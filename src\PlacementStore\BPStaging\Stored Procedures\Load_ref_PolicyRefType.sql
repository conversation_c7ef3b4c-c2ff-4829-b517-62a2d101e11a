/*
Lineage
ref.PolicyRefType.PolicyRefTypeKey=BPStaging.PolicyRefType.Id
ref.PolicyRefType.TranslationKey=BPStaging.PolicyRefType.LabelTranslationKey
ref.PolicyRefType.Text=BPStaging.PolicyRefType.Text
ref.PolicyRefType.IsDeprecated=BPStaging.PolicyRefType.IsDeprecated
ref.PolicyRefType.SourceUpdatedDate=BPStaging.PolicyRefType.ValidTo
ref.PolicyRefType.SourceUpdatedDate=BPStaging.PolicyRefType.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_ref_PolicyRefType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.PolicyRefType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.PolicyRefType t
    USING (
        SELECT
            Id
          , DataSourceInstanceId = 50366
          , TranslationKey
          , Text
          , SupportedLanguageId
          , IsDeprecated
          , SourceUpdatedDate = IIF(YEAR(ValidTo) < 9999, ValidTo, ValidFrom)
        FROM (
        SELECT
            Id
          , TranslationKey = LabelTranslationKey
          , Text
          , SupportedLanguageId
          , IsDeprecated
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.PolicyRefType
    ) s
        WHERE
            s.RowNo = 1
            AND s.SupportedLanguageId = 1 /* English only */
    ) s
    ON s.Id = t.PolicyRefTypeKey
       AND s.DataSourceInstanceId = t.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 PolicyRefTypeKey
               , TranslationKey
               , Text
               , IsDeprecated
               , DataSourceInstanceId
               , SourceUpdatedDate
             )
             VALUES
                 (
                     s.Id
                   , s.TranslationKey
                   , s.Text
                   , s.IsDeprecated
                   , s.DataSourceInstanceId
                   , s.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.TranslationKey
                               , s.Text
                               , s.IsDeprecated
                               , s.SourceUpdatedDate
                             INTERSECT
                             SELECT
                                 t.TranslationKey
                               , t.Text
                               , t.IsDeprecated
                               , t.SourceUpdatedDate
                         )
        THEN UPDATE SET
                 t.TranslationKey = s.TranslationKey
               , t.Text = s.Text
               , t.IsDeprecated = s.IsDeprecated
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.SourceUpdatedDate = s.SourceUpdatedDate
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;