/*
Lineage
dbo.PlacementRiskDefinitionElement.ElementId=dbo.ElementCache.ElementId
dbo.PlacementRiskDefinitionElement.Level=dbo.ElementCache.HierarchyId
dbo.PlacementRiskDefinitionElement.SourceUpdatedDate=dbo.ElementCache.LastUpdatedUTCDate
*/
/*
Updates the Placement Risk Definition Element table based on the element cache
*/
CREATE PROCEDURE BPStaging.UpdatePlacementRiskDefinitionElement
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementRiskDefinitionElement';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE INTO dbo.PlacementRiskDefinitionElement T
    USING (
        SELECT
            prde.PlacementId
          , ec2.ElementId
          , Level = ec2.HierarchyId.GetLevel()
          , SourceUpdatedDate = ec2.LastUpdatedUTCDate
          , DataSourceInstanceId = 50366
          , IsDeleted = 0
        FROM
            dbo.PlacementRiskDefinitionElement prde
            INNER JOIN dbo.ElementCache ec
                ON ec.ElementId = prde.ElementId

            INNER JOIN dbo.ElementCache ec2
                ON ec.ElementId = ec2.RootElementId
                   AND ISNULL(ec2.ElementBranchId, -1) = ISNULL(ec.ElementBranchId, -1)
        WHERE
            prde.Level = 1
            AND prde.IsDeleted = 0
    ) S
    ON S.PlacementId = T.PlacementId
       AND S.ElementId = T.ElementId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 DataSourceInstanceId
               , PlacementId
               , ElementId
               , Level
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.PlacementId
                   , S.ElementId
                   , S.Level
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.PlacementId
                               , T.ElementId
                               , T.Level
                               , T.IsDeleted
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.PlacementId
                               , S.ElementId
                               , S.Level
                               , S.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PlacementId = S.PlacementId
               , T.ElementId = S.ElementId
               , T.Level = S.Level
               , T.IsDeleted = S.IsDeleted
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
