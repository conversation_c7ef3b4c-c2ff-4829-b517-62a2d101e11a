/*
Lineage
BP.BoundPosition.Id=BPStaging.BoundPosition.Id
BP.BoundPosition.PlacementId=BPStaging.BoundPosition.PlacementId
BP.BoundPosition.ResponseManagementElementId=BPStaging.BoundPosition.ResponseManagementElementId
BP.BoundPosition.ElementBranchId=BPStaging.BoundPosition.ElementBranchId
BP.BoundPosition.BoundPositionTypeId=BPStaging.BoundPosition.BoundPositionTypeId
BP.BoundPosition.SourceUpdatedDate=BPStaging.BoundPosition.ValidTo
BP.BoundPosition.SourceUpdatedDate=BPStaging.BoundPosition.ValidFrom
BP.BoundPosition.IsDeleted=BPStaging.BoundPosition.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_BoundPosition
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.BoundPosition';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.BoundPosition)
BEGIN
    BEGIN TRY
        MERGE BP.BoundPosition T
        USING (
            SELECT
                inner_select.Id
              , inner_select.PlacementId
              , inner_select.ResponseManagementElementId
              , inner_select.ElementBranchId
              , inner_select.BoundPositionTypeId
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                     THEN 1
                                 ELSE 0 END
            FROM (
            SELECT
                Id
              , PlacementId
              , ResponseManagementElementId
              , ElementBranchId
              , BoundPositionTypeId
              , ValidFrom
              , ValidTo
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.BoundPosition
        ) inner_select
            WHERE
                inner_select.RowNo = 1
        ) S
        ON T.Id = S.Id
        WHEN NOT MATCHED
            THEN INSERT (
                     Id
                   , PlacementId
                   , ResponseManagementElementId
                   , ElementBranchId
                   , BoundPositionTypeId
                   , SourceUpdatedDate
                   , IsDeleted
                 )
                 VALUES
                     (
                         S.Id
                       , S.PlacementId
                       , S.ResponseManagementElementId
                       , S.ElementBranchId
                       , S.BoundPositionTypeId
                       , S.SourceUpdatedDate
                       , S.IsDeleted
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.PlacementId
      , T.ResponseManagementElementId
      , T.ElementBranchId
      , T.BoundPositionTypeId
      , T.SourceUpdatedDate
      , T.IsDeleted
    INTERSECT
    SELECT
        S.PlacementId
      , S.ResponseManagementElementId
      , S.ElementBranchId
      , S.BoundPositionTypeId
      , S.SourceUpdatedDate
      , S.IsDeleted
)
            THEN UPDATE SET
                     T.PlacementId = S.PlacementId
                   , T.ResponseManagementElementId = S.ResponseManagementElementId
                   , T.ElementBranchId = S.ElementBranchId
                   , T.BoundPositionTypeId = S.BoundPositionTypeId
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.ETLUpdatedDate = GETUTCDATE()
                   , T.IsDeleted = S.IsDeleted
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);