﻿namespace PsDb.Tests.rpt.Views;

public class vw_as_PolicyTests : PlacementStoreTestBase
{
    const string ViewName = @"rpt.vw_as_Policy";

    [Fact]
    public void NoDataTest()
    {
        dynamic result = GetResultRow(tableName: ViewName);
        Assert.Null(result);
    }

    [Fact]
    public void MinimalDataTest()
    {
        dynamic policyRecord = CreateRow(tableName: "dbo.Policy", new
        {
            InceptionDate = DateTime.UtcNow
        });

        dynamic result = GetResultRow(tableName: ViewName);
        Assert.NotNull(result);
        Assert.Equal(expected: policyRecord.PolicyId, actual: result.PolicyId);
    }

    [Fact]
    public void ReturnsDataTest()
    {
        dynamic dataSourceInstanceRecord, opportunityTypeRecord, policyStatusRecord, policyTypeRecord, pasRefPolicyStatusRecord, policyRecord, placementPolicyRecord, clientUnderwriterPremiumRecord;

        SetupData(out dataSourceInstanceRecord, out opportunityTypeRecord, out policyStatusRecord, out policyTypeRecord, out pasRefPolicyStatusRecord, out policyRecord, out placementPolicyRecord, out clientUnderwriterPremiumRecord);

        dynamic row = GetResultRow(tableName: ViewName);
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: policyRecord.PolicyReference, actual: row.PolicyReference);
        Assert.Equal(expected: policyRecord.PolicyKey, actual: row.PolicyKey);
        Assert.Equal(expected: policyRecord.PolicyDescription, actual: row.PolicyDescription);
        Assert.Equal(expected: policyRecord.InceptionDate, actual: row.InceptionDate);
        Assert.Equal(expected: policyRecord.ExpiryDate, actual: row.ExpiryDate);
        Assert.Equal(expected: policyStatusRecord.PolicyStatus, actual: row.PolicyStatus);
        Assert.Equal(expected: policyTypeRecord.PolicyType, actual: row.PolicyType);
        Assert.Equal(expected: pasRefPolicyStatusRecord.RefPolicyStatus, actual: row.RefPolicyStatus);
        Assert.Equal(expected: opportunityTypeRecord.OpportunityType, actual: row.OpportunityType);
        Assert.Equal(expected: placementPolicyRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: dataSourceInstanceRecord.DataSourceInstanceName, actual: row.ServicingPlatform);
        Assert.Equal(expected: clientUnderwriterPremiumRecord.GrossPremiumUSD, actual: row.GrossPremiumUSD);
        Assert.Equal(expected: clientUnderwriterPremiumRecord.NetPremiumtoUWUSD, actual: row.NetPremiumtoUWUSD);
    }

    [Fact]
    public void ReturnsAggregatedDataTest()
    {
        dynamic dataSourceInstanceRecord, opportunityTypeRecord, policyStatusRecord, policyTypeRecord, pasRefPolicyStatusRecord, policyRecord, placementPolicyRecord, clientUnderwriterPremiumRecord;

        SetupData(out dataSourceInstanceRecord, out opportunityTypeRecord, out policyStatusRecord, out policyTypeRecord, out pasRefPolicyStatusRecord, out policyRecord, out placementPolicyRecord, out clientUnderwriterPremiumRecord);

        // Creating an additional CUP record to test the Group BY Clause (SUM of the Premium)
        dynamic clientUnderwriterPremiumRecord2 = CreateRow(tableName: "dbo.ClientUnderwriterPremium", new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyId = policyRecord.PolicyId,
            LineSlipRef = "B",
            GrossPremiumUSD = 10000,
            NetPremiumtoUWUSD = 80000,
            IsDeleted = false
        });

        dynamic row = GetResultRow(tableName: ViewName);
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: clientUnderwriterPremiumRecord.GrossPremiumUSD + clientUnderwriterPremiumRecord2.GrossPremiumUSD, actual: row.GrossPremiumUSD);
        Assert.Equal(expected: clientUnderwriterPremiumRecord.NetPremiumtoUWUSD + clientUnderwriterPremiumRecord2.NetPremiumtoUWUSD, actual: row.NetPremiumtoUWUSD);
    }

    private void SetupData(out dynamic dataSourceInstanceRecord, out dynamic opportunityTypeRecord, out dynamic policyStatusRecord, out dynamic policyTypeRecord, out dynamic pasRefPolicyStatusRecord
        , out dynamic policyRecord, out dynamic placementPolicyRecord, out dynamic clientUnderwriterPremiumRecord)
    {
        dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            DataSourceInstanceName = "Eclipse"
        });

        opportunityTypeRecord = CreateRow(tableName: "ref.OpportunityType", new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            OpportunityTypeKey = "REN",
            OpportunityType = "Renewal",
            IsDeprecated = false
        });

        policyStatusRecord = CreateRow(tableName: "PAS.PolicyStatus", new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyStatusKey = "L",
            PolicyStatus = "Live",
            IsDeleted = false
        });

        policyTypeRecord = CreateRow(tableName: "PAS.PolicyType", new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyTypeKey = "N",
            PolicyType = "Non-Complex",
            IsDeleted = false
        });

        pasRefPolicyStatusRecord = CreateRow(tableName: "PAS.RefPolicyStatus", new
        {
            RefPolicyStatusKey = "102",
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            RefPolicyStatus = "Live",
            RefPolicyStatusId = 102,
            IsDeleted = false
        });

        policyRecord = CreateRow(tableName: "dbo.Policy", new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyReference = "PolicyRef",
            PolicyKey = "ABC",
            PolicyDescription = "This is a test Policy",
            InceptionDate = DateTime.UtcNow,
            ExpiryDate = DateTime.UtcNow.AddYears(1),
            PolicyStatusKey = policyStatusRecord.PolicyStatusKey,
            PolicyTypeKey = policyTypeRecord.PolicyTypeKey,
            RefPolicyStatusId = pasRefPolicyStatusRecord.RefPolicyStatusId,
            OpportunityTypeId = opportunityTypeRecord.OpportunityTypeId,
        });

        placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyId = policyRecord.PolicyId,
            PlacementId = 1000,
            PlacementPolicyRelationshipTypeId = 1,
            IsDeleted = false
        });

        clientUnderwriterPremiumRecord = CreateRow(tableName: "dbo.ClientUnderwriterPremium", new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyId = policyRecord.PolicyId,
            LineSlipRef = "A",
            GrossPremiumUSD = 5000,
            NetPremiumtoUWUSD = 3500,
            IsDeleted = false
        });
    }


    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public vw_as_PolicyTests(DatabaseFixture fixture) : base(fixture)
    {
    }

    #endregion
}
