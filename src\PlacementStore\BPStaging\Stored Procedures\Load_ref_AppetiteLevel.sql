/*
Lineage
ref.AppetiteLevel.AppetiteLevelId=BPStaging.AppetiteLevel.Id
ref.AppetiteLevel.AppetiteLevelKey=BPStaging.AppetiteLevel.LabelTranslationKey
ref.AppetiteLevel.AppetiteLevel=BPStaging.AppetiteLevel.Text
ref.AppetiteLevel.SourceUpdatedDate=BPStaging.AppetiteLevel.ValidFrom
ref.AppetiteLevel.IsDeprecated=BPStaging.AppetiteLevel.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_AppetiteLevel
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.AppetiteLevel';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.AppetiteLevel T
    USING (
        SELECT
            AppetiteLevelId = Id
          , AppetiteLevelKey = LabelTranslationKey
          , AppetiteLevel = Text
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.AppetiteLevel
    ) S
    ON T.AppetiteLevelId = S.AppetiteLevelId
    WHEN NOT MATCHED
        THEN INSERT (
                 AppetiteLevelId
               , DataSourceInstanceId
               , AppetiteLevelKey
               , AppetiteLevel
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.AppetiteLevelId
                   , S.DataSourceInstanceId
                   , S.AppetiteLevelKey
                   , S.AppetiteLevel
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.AppetiteLevelId
                               , T.DataSourceInstanceId
                               , T.AppetiteLevelKey
                               , T.AppetiteLevel
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.AppetiteLevelId
                               , S.DataSourceInstanceId
                               , S.AppetiteLevelKey
                               , S.AppetiteLevel
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.AppetiteLevelKey = S.AppetiteLevelKey
               , T.AppetiteLevel = S.AppetiteLevel
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);