﻿CREATE TABLE BP.PlacementAffirmationQuestion (
    Id                    INT          NOT NULL
  , PlacementId           INT          NOT NULL
  , AffirmationQuestionId INT          NOT NULL
  , Answer                BIT          NULL
  , AnsweredByUserId      INT          NULL
  , SourceUpdatedDate     DATETIME2(7) NOT NULL
  , ETLCreatedDate        DATETIME2(7) NOT NULL
        DEFAULT GETUTCDATE()
  , ETLUpdatedDate        DATETIME2(7) NOT NULL
        DEFAULT GETUTCDATE()
  , IsDeleted             BIT          NOT NULL
        DEFAULT (0)
  , CONSTRAINT PK_BP_PlacementAffirmationQuestion
        PRIMARY KEY
        (
            Id
        )
);