/* 
Lineage
Reference.VerticalIndustry.VerticalIndustryId=BPStaging.VerticalIndustry.Id
Reference.VerticalIndustry.DataSourceInstanceId=BPStaging.VerticalIndustry.ServicingPlatformId
Reference.VerticalIndustry.VerticalIndustryKey=BPStaging.VerticalIndustry.LabelTranslationKey
Reference.VerticalIndustry.VerticalIndustry=BPStaging.VerticalIndustry.Text
Reference.VerticalIndustry.SourceUpdatedDate=BPStaging.VerticalIndustry.ValidFrom
Reference.VerticalIndustry.IsDeprecated=BPStaging.VerticalIndustry.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_Reference_VerticalIndustry
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'Reference.VerticalIndustry';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    IF EXISTS (SELECT * FROM BPStaging.VerticalIndustry)
    BEGIN
        MERGE Reference.VerticalIndustry T
        USING (
            SELECT
                VerticalIndustryId = Id
              , DataSourceInstanceId = ServicingPlatformId
              , VerticalIndustryKey = LabelTranslationKey
              , VerticalIndustry = Text
              , SourceUpdatedDate = ValidFrom
              , IsDeprecated
            FROM
                BPStaging.VerticalIndustry
        ) S
        ON T.DataSourceInstanceId = S.DataSourceInstanceId
           AND T.VerticalIndustryId = S.VerticalIndustryId
        WHEN NOT MATCHED
            THEN INSERT (
                     VerticalIndustryId
                   , DataSourceInstanceId
                   , VerticalIndustryKey
                   , VerticalIndustry
                   , SourceUpdatedDate
                   , IsDeprecated
                 )
                 VALUES
                     (
                         S.VerticalIndustryId
                       , S.DataSourceInstanceId
                       , S.VerticalIndustryKey
                       , S.VerticalIndustry
                       , S.SourceUpdatedDate
                       , S.IsDeprecated
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT
            S.VerticalIndustryId
          , S.DataSourceInstanceId
          , S.VerticalIndustryKey
          , S.VerticalIndustry
          , S.SourceUpdatedDate
          , S.IsDeprecated
        INTERSECT
        SELECT
            T.VerticalIndustryId
          , T.DataSourceInstanceId
          , T.VerticalIndustryKey
          , T.VerticalIndustry
          , T.SourceUpdatedDate
          , T.IsDeprecated
    )
            THEN UPDATE SET
                     T.VerticalIndustryId = S.VerticalIndustryId
                   , T.DataSourceInstanceId = S.DataSourceInstanceId
                   , T.VerticalIndustryKey = S.VerticalIndustryKey
                   , T.VerticalIndustry = S.VerticalIndustry
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.IsDeprecated = S.IsDeprecated
                   , T.ETLUpdatedDate = GETUTCDATE()
        WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
                                       AND T.DataSourceInstanceId = 50366
            THEN UPDATE SET
                     T.IsDeprecated = 1
                   , T.ETLUpdatedDate = GETUTCDATE()
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;