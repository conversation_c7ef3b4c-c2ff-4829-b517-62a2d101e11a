/*
Lineage
dbo.RiskLocation.RiskLocationId=BPStaging.RiskLocation.Id
dbo.RiskLocation.RiskLocation=BPStaging.RiskLocation.Text
dbo.RiskLocation.IsDeprecated=BPStaging.RiskLocation.IsDeprecated
dbo.RiskLocation.SourceUpdatedDate=BPStaging.RiskLocation.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadRiskLocation
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.RiskLocation';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.RiskLocation T
    USING (
        SELECT
            RiskLocationId = Id
          , RiskLocation = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated = IsDeprecated
        FROM
            BPStaging.RiskLocation
    ) S
    ON T.RiskLocationId = S.RiskLocationId
    WHEN NOT MATCHED
        THEN INSERT (
                 RiskLocationId
               , RiskLocation
               , LastUpdateTime
               , LastUpdateBy
               , IsDeprecated
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.RiskLocationId
                   , S.RiskLocation
                   , GETUTCDATE()
                   , 'FMAImport'
                   , S.IsDeprecated
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.RiskLocation
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.RiskLocation
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.RiskLocation = S.RiskLocation
               , T.LastUpdateTime = GETUTCDATE()
               , T.LastUpdateBy = 'FMAImport'
               , T.IsDeprecated = S.IsDeprecated
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.LastUpdateBy = 'FMAImport'
               , T.LastUpdateTime = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
