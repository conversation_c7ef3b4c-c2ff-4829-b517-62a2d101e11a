/*
Lineage
PS.PlacementStatusHistory.PlacementId=dbo.Placement.PlacementId
PS.PlacementStatusHistory.SourceUpdatedDate=BPStaging.Placement.ValidFrom
PS.PlacementStatusHistory.PlacementStatusId=ref.PlacementStatus.PlacementStatusId
*/
CREATE PROCEDURE BPStaging.Load_PS_PlacementStatusHistory
AS
BEGIN
    DECLARE @MaxLastUpdatedUTCDate DATETIME2(7);
    DECLARE @InsertedCount INT = 0;
    DECLARE @UpdatedCount INT = 0;
    DECLARE @DeletedCount INT = 0;
    DECLARE @RejectedCount INT = 0;
    DECLARE @TargetTable VARCHAR(50) = 'PS.PlacementStatusHistory';
    DECLARE @SprocName VARCHAR(255);
    DECLARE @Action NVARCHAR(255);

    SET NOCOUNT ON;

    SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

    EXEC ADF.StoredProcStartLog @SprocName;

    BEGIN TRY
        /* Because we have to send out message when the Placement changes to certain states */
        /* this stored procedure has to gather all the states the Placement has been in.    */
        /* Ideally it won't find duplicates, but it is possible because of differences in   */
        /* dates that it could. The processing using PS.PlacementStatusHistory will have to */
        /* handle this.                                                                     */
        /* This code only inserts. Matching records will be dropped for a reload scenario.  */
        /* PS.PlacementStatusHistory does have a created and updated date on it. The only   */
        /* update is when the associated PlacementServiceBusId is added.                    */
        /* For consumption the SourceUpdatedDate value is key.                              */
        /* Duplicate consecutive states should be ignored.                                  */
        /* This only process Broking Platform placements.                                   */
        /* The precision of the dates is not the same which may also prevent the same event */
        /* appearing to be at the same time. It may just be a fraction of a second out.     */
        /* It doesn't insert a record if the status is the same on a record that is the     */
        /* same time or just before. It does not handle if the record has the same status   */
        /* as the one immediately after. This would have to update the time and move it     */
        /* forwards but I don't think this is a realistic scenario.                         */
        /* Has to UNION the existing records and staged records to ascertain any gaps.      */
        /* Sounds expensive but because PS.PlacementStatusHistory is a narrow table and we  */
        /* aren't processing many columns performance seems acceptable.                     */
        INSERT INTO
            PS.PlacementStatusHistory
            (
                PlacementId
              , PlacementStatusId
              , SourceUpdatedDate
              , DataSourceInstanceId
              , ETLCreatedDate
              , ETLUpdatedDate
            )
        SELECT
            c.PlacementId
          , c.PlacementStatusId
          , c.SourceUpdatedDate
          , DataSourceInstanceId = 50366
          , ETLCreatedDate = GETUTCDATE()
          , ETLUpdatedDate = GETUTCDATE()
        FROM (
            SELECT
                b.PlacementId
              , b.SourceUpdatedDate
              , b.PlacementStatusId
              , b.FromCurrent
            FROM (
                SELECT
                    a.PlacementId
                  , a.SourceUpdatedDate
                  , a.PlacementStatusId
                  , a.FromCurrent /* The order on the PARTITION to make these win over staged versions as current don't need inserting. */
                  , PrevPlacementStatusId = ISNULL(
                                                LEAD(a.PlacementStatusId) OVER (PARTITION BY a.PlacementId ORDER BY a.SourceUpdatedDate DESC, a.FromCurrent ASC)
                                              , -1
                                            )
                FROM (
                    SELECT
                        PlacementId = pl.PlacementId
                      , SourceUpdatedDate = sp.ValidFrom
                      , ps.PlacementStatusId
                      , FromCurrent = 0
                    FROM
                        BPStaging.Placement sp
                        INNER JOIN dbo.Placement pl
                            ON pl.PlacementSystemId = sp.Id
                               AND pl.DataSourceInstanceId = 50366

                        LEFT JOIN ref.PlacementStatus ps
                            ON ps.DataSourceInstanceId = 50366
                               AND ps.PlacementStatusKey = CAST(sp.PlacementStatusId AS NVARCHAR(50))
                    UNION ALL
                    SELECT
                        PlacementId
                      , SourceUpdatedDate
                      , PlacementStatusId
                      , FromCurrent = 1
                    FROM
                        PS.PlacementStatusHistory
                ) a
            ) b
            WHERE
                b.PrevPlacementStatusId <> b.PlacementStatusId /* To get rid of adjacent statuses. */
        ) c
        WHERE
            c.FromCurrent = 0 /* If there is a current then it is there and can't be stored again. */
        ORDER BY
            c.SourceUpdatedDate ASC;

        SELECT @InsertedCount = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;

    SET @Action = CONCAT(N'Load ', @TargetTable, N' from BPStaging.Placement.');

    EXEC ADF.StoredProcSetSqlLog
        @SprocName
      , @InsertedCount
      , @UpdatedCount
      , @DeletedCount
      , @RejectedCount
      , @Action
      , NULL;

    EXEC ADF.StoredProcEndLog @SprocName;

    SELECT
        InsertedCount = ISNULL(@InsertedCount, 0)
      , UpdatedCount = ISNULL(@UpdatedCount, 0)
      , DeletedCount = ISNULL(@DeletedCount, 0)
      , RejectedCount = ISNULL(@RejectedCount, 0);

    RETURN 0;
END;