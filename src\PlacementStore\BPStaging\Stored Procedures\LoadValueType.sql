/*
Lineage
dbo.ValueType.ValueTypeId=BPStaging.ValueType.Id
dbo.ValueType.Text=BPStaging.ValueType.Text
dbo.ValueType.StorageType=BPStaging.ValueType.StorageType
dbo.ValueType.IsDeprecated=BPStaging.ValueType.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadValueType
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.ValueType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ValueType)
BEGIN TRY
    MERGE INTO dbo.ValueType T
    USING (SELECT ValueTypeId = Id, Text, StorageType, IsDeprecated, DataSourceInstanceId = 50366 FROM BPStaging.ValueType) S
    ON T.ValueTypeId = S.ValueTypeId
    WHEN NOT MATCHED
        THEN INSERT (
                 ValueTypeId
               , Text
               , StorageType
               , IsDeprecated
               , CreatedUTCDate
               , LastUpdatedUTCDate
               , DataSourceInstanceId
             )
             VALUES
                 (
                     S.ValueTypeId
                   , S.Text
                   , S.StorageType
                   , S.IsDeprecated
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.DataSourceInstanceId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.Text
      , S.StorageType
      , S.IsDeprecated
      , S.DataSourceInstanceId
    INTERSECT
    SELECT
        T.Text
      , T.StorageType
      , T.IsDeprecated
      , T.DataSourceInstanceId
)
        THEN UPDATE SET
                 T.Text = S.Text
               , T.StorageType = S.StorageType
               , T.IsDeprecated = S.IsDeprecated
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
