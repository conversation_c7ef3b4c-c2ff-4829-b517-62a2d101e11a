/*
Lineage
PS.SubmissionPortalSummary.SubmissionId=BP.SubmissionPortalUser.SubmissionId
PS.SubmissionPortalSummary.SubmissionContainerMarketId=BP.SubmissionPortalUser.SubmissionContainerMarketId
PS.SubmissionPortalSummary.SubmissionCount=BP.SubmissionPortalUser.Id
PS.SubmissionPortalSummary.NumberOfPortalUsers=BP.SubmissionPortalUser.PortalUserId
PS.SubmissionPortalSummary.NotifiedCount=BP.SubmissionPortalUser.NotificationSent
PS.SubmissionPortalSummary.OpenedCount=BP.SubmissionPortalUser.SubmissionOpened
PS.SubmissionPortalSummary.FirstOpened=BP.SubmissionPortalUser.SubmissionOpenedTimestamp
PS.SubmissionPortalSummary.LastOpened=BP.SubmissionPortalUser.SubmissionOpenedTimestamp
PS.SubmissionPortalSummary.FirstNotified=BP.SubmissionPortalUser.NotificationSentTimestamp
PS.SubmissionPortalSummary.LastNotified=BP.SubmissionPortalUser.NotificationSentTimestamp
*/
CREATE PROCEDURE BPStaging.Load_PS_SubmissionPortalSummary
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.SubmissionPortalSummary';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXECUTE ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BP.SubmissionPortalUser
        );

BEGIN TRY
    IF @CheckCount <> 0
    BEGIN
        MERGE PS.SubmissionPortalSummary T
        USING (
            SELECT
                spu.SubmissionId
              , spu.SubmissionContainerMarketId
              , SubmissionCount = COUNT(spu.Id)
              , NumberOfPortalUsers = COUNT(DISTINCT spu.PortalUserId)
              , NotifiedCount = SUM(CASE WHEN spu.NotificationSent = 1
                                             THEN 1
                                         ELSE 0 END
                                )
              , OpenedCount = SUM(CASE WHEN spu.SubmissionOpened = 1
                                           THEN 1
                                       ELSE 0 END
                              )
              , FirstOpened = MIN(spu.SubmissionOpenedTimestamp)
              , LastOpened = MAX(spu.SubmissionOpenedTimestamp)
              , FirstNotified = MIN(spu.NotificationSentTimestamp)
              , LastNotified = MAX(spu.NotificationSentTimestamp)
            FROM
                BP.SubmissionPortalUser spu
                INNER JOIN dbo.Submission s
                    ON s.SubmissionId = spu.SubmissionId

                INNER JOIN PS.NegotiationMarket PNM
                    ON PNM.NegotiationMarketKey = CONCAT('SUBCONMKT|', spu.SubmissionContainerMarketId)
            GROUP BY
                spu.SubmissionId
              , spu.SubmissionContainerMarketId
        ) S
        ON S.SubmissionId = T.SubmissionId
           AND S.SubmissionContainerMarketId = T.SubmissionContainerMarketId
        WHEN NOT MATCHED BY TARGET
            THEN INSERT (
                     SubmissionId
                   , SubmissionContainerMarketId
                   , SubmissionCount
                   , NumberOfPortalUsers
                   , NotifiedCount
                   , OpenedCount
                   , FirstOpened
                   , LastOpened
                   , FirstNotified
                   , LastNotified
                 )
                 VALUES
                     (
                         S.SubmissionId
                       , S.SubmissionContainerMarketId
                       , S.SubmissionCount
                       , S.NumberOfPortalUsers
                       , S.NotifiedCount
                       , S.OpenedCount
                       , S.FirstOpened
                       , S.LastOpened
                       , S.FirstNotified
                       , S.LastNotified
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT
            T.SubmissionCount
          , T.NumberOfPortalUsers
          , T.NotifiedCount
          , T.OpenedCount
          , T.FirstOpened
          , T.LastOpened
          , T.FirstNotified
          , T.LastNotified
        INTERSECT
        SELECT
            S.SubmissionCount
          , S.NumberOfPortalUsers
          , S.NotifiedCount
          , S.OpenedCount
          , S.FirstOpened
          , S.LastOpened
          , S.FirstNotified
          , S.LastNotified
    )
            THEN UPDATE SET
                     T.SubmissionCount = S.SubmissionCount
                   , T.NumberOfPortalUsers = S.NumberOfPortalUsers
                   , T.NotifiedCount = S.NotifiedCount
                   , T.OpenedCount = S.OpenedCount
                   , T.FirstOpened = S.FirstOpened
                   , T.LastOpened = S.LastOpened
                   , T.FirstNotified = S.FirstNotified
                   , T.LastNotified = S.LastNotified
        WHEN NOT MATCHED BY SOURCE
            THEN DELETE
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;