/*
Lineage
dbo.PlacementOpportunity.PlacementOpportunityKey=BPStaging.OpportunityPlacement.Id
dbo.PlacementOpportunity.PlacementId=dbo.Placement.PlacementId
dbo.PlacementOpportunity.OpportunityId=BPStaging.OpportunityPlacement.OpportunityId
dbo.PlacementOpportunity.SourceUpdatedDate=BPStaging.OpportunityPlacement.ValidTo
dbo.PlacementOpportunity.SourceUpdatedDate=BPStaging.OpportunityPlacement.ValidFrom
dbo.PlacementOpportunity.IsDeleted=BPStaging.OpportunityPlacement.ValidTo
*/
CREATE PROCEDURE BPStaging.LoadPlacementOpportunity
AS
BEGIN
    DECLARE @InsertedCount INT = 0;
    DECLARE @UpdatedCount INT = 0;
    DECLARE @DeletedCount INT = 0;
    DECLARE @RejectedCount INT = 0;
    DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementOpportunity';

    DECLARE @Actions TABLE (
        Change VARCHAR(20)
    );

    DECLARE @SprocName VARCHAR(255);
    DECLARE @Action NVARCHAR(255);

    SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

    EXEC ADF.StoredProcStartLog @SprocName;

    IF EXISTS (SELECT * FROM BPStaging.OpportunityPlacement)
    BEGIN TRY
        MERGE dbo.PlacementOpportunity T
        USING (
            SELECT
                PlacementOpportunityKey = CAST(inner_select.Id AS NVARCHAR(50))
              , DataSourceInstanceId = 50366
              , pl.PlacementId
              , inner_select.OpportunityId
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                     THEN 1
                                 ELSE 0 END
            FROM (
            SELECT
                Id
              , PlacementId
              , OpportunityId
              , ValidFrom
              , ValidTo
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.OpportunityPlacement
        ) inner_select
                 LEFT JOIN dbo.Placement pl
                     ON pl.PlacementSystemId = inner_select.PlacementId
                        AND pl.DataSourceInstanceId = 50366
                        AND pl.IsDeleted = 0
            WHERE
                inner_select.RowNo = 1
        ) S
        ON T.PlacementOpportunityKey = S.PlacementOpportunityKey
           AND T.DataSourceInstanceId = S.DataSourceInstanceId
        WHEN NOT MATCHED
            THEN INSERT (
                     PlacementOpportunityKey
                   , DataSourceInstanceId
                   , PlacementId
                   , OpportunityId
                   , SourceUpdatedDate
                   , IsDeleted
                 )
                 VALUES
                     (
                         S.PlacementOpportunityKey
                       , S.DataSourceInstanceId
                       , S.PlacementId
                       , S.OpportunityId
                       , S.SourceUpdatedDate
                       , S.IsDeleted
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT T.PlacementId, T.OpportunityId, T.IsDeleted INTERSECT SELECT S.PlacementId, S.OpportunityId, S.IsDeleted
    )
            THEN UPDATE SET
                     T.PlacementId = S.PlacementId
                   , T.OpportunityId = S.OpportunityId
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.ETLUpdatedDate = GETUTCDATE()
                   , T.IsDeleted = S.IsDeleted
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;

    SET @Action = N'Merge ' + @TargetTable;

    EXEC ADF.StoredProcSetSqlLog
        @SprocName
      , @InsertedCount
      , @UpdatedCount
      , @DeletedCount
      , @RejectedCount
      , @Action
      , NULL;

    EXEC ADF.StoredProcEndLog @SprocName;

    SELECT
        InsertedCount = ISNULL(@InsertedCount, 0)
      , UpdatedCount = ISNULL(@UpdatedCount, 0)
      , DeletedCount = ISNULL(@DeletedCount, 0)
      , RejectedCount = ISNULL(@RejectedCount, 0);
END;