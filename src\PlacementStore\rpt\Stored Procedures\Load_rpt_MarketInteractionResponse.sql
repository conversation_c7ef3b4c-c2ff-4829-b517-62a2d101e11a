/*
Lineage
rpt.MarketInteractionResponse.CarrierResponseId=dbo.MarketInteraction.CarrierResponseId
rpt.MarketInteractionResponse.CarrierResponseId=PS.MarketResponse.MarketResponseKey
rpt.MarketInteractionResponse.MarketResponseKey=dbo.MarketInteraction.CarrierResponseId
rpt.MarketInteractionResponse.MarketResponseKey=PS.MarketResponse.MarketResponseKey
rpt.MarketInteractionResponse.ParentResponseKey=PS.MarketResponse.ParentKey
rpt.MarketInteractionResponse.MarketSelectionId=dbo.MarketInteraction.MarketSelectionId
rpt.MarketInteractionResponse.MarketSelectionId=PS.NegotiationMarket.NegotiationMarketKey
rpt.MarketInteractionResponse.NegotiationMarketKey=dbo.MarketSelection.PlacementSystemMarketSelectionId
rpt.MarketInteractionResponse.NegotiationMarketKey=PS.NegotiationMarket.NegotiationMarketKey
rpt.MarketInteractionResponse.MarketSelectionKey=dbo.PlacementSystemTable.TableId
rpt.MarketInteractionResponse.MarketSelectionKey=dbo.MarketInteraction.MarketSelectionId
rpt.MarketInteractionResponse.MarketSelectionKey=PS.NegotiationMarket.NegotiationMarketKey
rpt.MarketInteractionResponse.InteractionName=dbo.MarketInteraction.InteractionName
rpt.MarketInteractionResponse.InteractionName=PS.Negotiation.SubmissionName
rpt.MarketInteractionResponse.SubmittedDate=dbo.MarketInteraction.SubmittedDate
rpt.MarketInteractionResponse.SubmittedDate=BP.Submission.Sent
rpt.MarketInteractionResponse.ResponseDate=dbo.MarketInteraction.ResponseDate
rpt.MarketInteractionResponse.ResponseDate=PS.MarketResponse.ResponseDate
rpt.MarketInteractionResponse.ResponseTimeDays=dbo.MarketInteraction.SubmittedDate
rpt.MarketInteractionResponse.ResponseTimeDays=dbo.MarketInteraction.ResponseDate
rpt.MarketInteractionResponse.ResponseTimeDays=BP.Submission.Sent
rpt.MarketInteractionResponse.ResponseTimeDays=PS.MarketResponse.ResponseDate
rpt.MarketInteractionResponse.PendingActionReason=ref.PendingActionReason.PendingActionReason
rpt.MarketInteractionResponse.ResponseType=ref.ResponseType.ResponseType
rpt.MarketInteractionResponse.DeclinationReason=ref.DeclinationReason.DeclinationReason
rpt.MarketInteractionResponse.DeclinationReasonGroupName=dbo.ReasonGroup.GroupName
rpt.MarketInteractionResponse.OutcomeReason=ref.OutcomeReason.OutcomeReason
rpt.MarketInteractionResponse.OutcomeReasonGroupName=dbo.ReasonGroup.GroupName
rpt.MarketInteractionResponse.OutcomeStatus=ref.OutcomeStatus.OutcomeStatus
rpt.MarketInteractionResponse.UnderwriterName=dbo.MarketInteraction.UnderwriterName
rpt.MarketInteractionResponse.UnderwriterName=PS.MarketResponse.UnderwriterName
rpt.MarketInteractionResponse.UnderwriterEmail=PS.MarketResponse.UnderwriterEmail
rpt.MarketInteractionResponse.ProductKey=rpt.ProductHierarchy.ProductKey
rpt.MarketInteractionResponse.ProductId=PS.RiskProfile.ProductId
rpt.MarketInteractionResponse.ClassId=PS.RiskProfile.ClassId
rpt.MarketInteractionResponse.LayerType=ref.LayerType.LayerType
rpt.MarketInteractionResponse.LeadCarrier=rpt.Market.MarketName
rpt.MarketInteractionResponse.LeadSignedLineRate=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.QuotedToLead=dbo.MarketInteraction.QuotedToLead
rpt.MarketInteractionResponse.QuotedToLead=PS.MarketQuoteResponse.QuotedToLead
rpt.MarketInteractionResponse.QuotedToLead=dbo.MarketResponseSecurity.IsFacilityLead
rpt.MarketInteractionResponse.Premium_Currency=PS.vwToAverageExchangeRate.FromCurrencyAlphaCode
rpt.MarketInteractionResponse.Premium=dbo.MarketInteraction.Premium
rpt.MarketInteractionResponse.Premium=dbo.MarketInteraction.PremiumRate
rpt.MarketInteractionResponse.Premium=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.Premium=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.Premium=PS.MarketQuoteResponse.PremiumRate
rpt.MarketInteractionResponse.Premium=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.Premium_USD=dbo.MarketInteraction.Premium
rpt.MarketInteractionResponse.Premium_USD=dbo.MarketInteraction.PremiumRate
rpt.MarketInteractionResponse.Premium_USD=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.Premium_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.Premium_USD=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.Premium_USD=PS.MarketQuoteResponse.PremiumRate
rpt.MarketInteractionResponse.Premium_USD=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.OfferedLine=dbo.MarketInteraction.OfferedLine
rpt.MarketInteractionResponse.OfferedLine=dbo.MarketInteraction.OfferedLineRate
rpt.MarketInteractionResponse.OfferedLine=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.OfferedLine=PS.MarketQuoteResponse.OfferedLine
rpt.MarketInteractionResponse.OfferedLine=PS.MarketQuoteResponse.OfferedLineRate
rpt.MarketInteractionResponse.OfferedLine=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.OfferedLine=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.OfferedLine_USD=dbo.MarketInteraction.OfferedLine
rpt.MarketInteractionResponse.OfferedLine_USD=dbo.MarketInteraction.OfferedLineRate
rpt.MarketInteractionResponse.OfferedLine_USD=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.OfferedLine_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.OfferedLine_USD=PS.MarketQuoteResponse.OfferedLine
rpt.MarketInteractionResponse.OfferedLine_USD=PS.MarketQuoteResponse.OfferedLineRate
rpt.MarketInteractionResponse.OfferedLine_USD=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.OfferedLine_USD=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.OfferedLineRate=dbo.MarketInteraction.OfferedLineRate
rpt.MarketInteractionResponse.OfferedLineRate=dbo.MarketInteraction.OfferedLine
rpt.MarketInteractionResponse.OfferedLineRate=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.OfferedLineRate=PS.MarketQuoteResponse.OfferedLineRate
rpt.MarketInteractionResponse.OfferedLineRate=PS.MarketQuoteResponse.OfferedLine
rpt.MarketInteractionResponse.OfferedLineRate=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.OfferedLineRate=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.PremiumRate=dbo.MarketInteraction.PremiumRate
rpt.MarketInteractionResponse.PremiumRate=PS.MarketQuoteResponse.PremiumRate
rpt.MarketInteractionResponse.CommissionRate=dbo.MarketInteraction.CommissionRate
rpt.MarketInteractionResponse.CommissionRate=PS.MarketQuoteResponse.CommissionRate
rpt.MarketInteractionResponse.Limit_Currency=PS.vwToAverageExchangeRate.FromCurrencyAlphaCode
rpt.MarketInteractionResponse.Limit=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.Limit=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.Limit_USD=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.Limit_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.Limit_USD=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.AttachmentPoint_Currency=PS.vwToAverageExchangeRate.FromCurrencyAlphaCode
rpt.MarketInteractionResponse.AttachmentPoint=dbo.MarketInteraction.AttachmentPoint
rpt.MarketInteractionResponse.AttachmentPoint=PS.MarketResponse.AttachmentPoint
rpt.MarketInteractionResponse.AttachmentPoint_USD=dbo.MarketInteraction.AttachmentPoint
rpt.MarketInteractionResponse.AttachmentPoint_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.AttachmentPoint_USD=PS.MarketResponse.AttachmentPoint
rpt.MarketInteractionResponse.Comments=dbo.MarketInteraction.Comments
rpt.MarketInteractionResponse.Comments=PS.MarketResponse.Comments
rpt.MarketInteractionResponse.Subjectivity=dbo.MarketInteraction.Subjectivity
rpt.MarketInteractionResponse.Subjectivity=PS.MarketQuoteResponse.Subjectivity
rpt.MarketInteractionResponse.ResponseDateKey=dbo.MarketInteraction.ResponseDate
rpt.MarketInteractionResponse.ResponseDateKey=PS.MarketResponse.ResponseDate
rpt.MarketInteractionResponse.ResponseKey=dbo.PlacementSystemTable.TableId
rpt.MarketInteractionResponse.ResponseKey=dbo.MarketInteraction.CarrierResponseId
rpt.MarketInteractionResponse.ResponseKey=PS.MarketResponse.MarketResponseKey
rpt.MarketInteractionResponse.WrittenPremium=dbo.MarketInteraction.OfferedLineRate
rpt.MarketInteractionResponse.WrittenPremium=dbo.MarketInteraction.Premium
rpt.MarketInteractionResponse.WrittenPremium=dbo.MarketInteraction.OfferedLine
rpt.MarketInteractionResponse.WrittenPremium=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.WrittenPremium=PS.MarketQuoteResponse.OfferedLineRate
rpt.MarketInteractionResponse.WrittenPremium=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.WrittenPremium=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.WrittenPremium_USD=dbo.MarketInteraction.OfferedLineRate
rpt.MarketInteractionResponse.WrittenPremium_USD=dbo.MarketInteraction.Premium
rpt.MarketInteractionResponse.WrittenPremium_USD=dbo.MarketInteraction.OfferedLine
rpt.MarketInteractionResponse.WrittenPremium_USD=dbo.MarketInteraction.Limit
rpt.MarketInteractionResponse.WrittenPremium_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.WrittenPremium_USD=PS.MarketQuoteResponse.OfferedLineRate
rpt.MarketInteractionResponse.WrittenPremium_USD=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.WrittenPremium_USD=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.MultiRisk=PS.MarketResponseBasis.SpecificationId
rpt.MarketInteractionResponse.MultiRisk=PS.MarketResponseBasis.RiskProfileId
rpt.MarketInteractionResponse.MultiRisk=PS.ContractRiskProfile.ContractId
rpt.MarketInteractionResponse.MarketSecurityCarrierKey=ref.Facility.PSFacilityPolicyId
rpt.MarketInteractionResponse.MarketSecurityCarrierKey=ref.FacilitySection.FacilitySectionId
rpt.MarketInteractionResponse.MarketSecurityCarrierKey=dbo.MarketResponseSecurity.CarrierId
rpt.MarketInteractionResponse.MarketSecurityCarrierKey=ref.FacilitySectionCarrier.FacilitySectionId
rpt.MarketInteractionResponse.MarketSecurityCarrierKey=ref.FacilitySectionCarrier.CarrierId
rpt.MarketInteractionResponse.Split=dbo.MarketInteraction.OfferedLineRate
rpt.MarketInteractionResponse.Split=PS.MarketQuoteResponse.OfferedLineRate
rpt.MarketInteractionResponse.Split=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.SignedLine=PS.MarketQuoteResponse.SignedLine
rpt.MarketInteractionResponse.SignedLine=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.SignedLine=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.SignedLine=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.SignedLine_Currency=PS.vwToAverageExchangeRate.FromCurrencyAlphaCode
rpt.MarketInteractionResponse.SignedLine_USD=PS.MarketQuoteResponse.SignedLine
rpt.MarketInteractionResponse.SignedLine_USD=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.SignedLine_USD=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.SignedLine_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.SignedLine_USD=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.SignedLineRate=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.SignedLineRate=PS.MarketQuoteResponse.SignedLine
rpt.MarketInteractionResponse.SignedLineRate=PS.MarketResponse.Limit
rpt.MarketInteractionResponse.SignedLineRate=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.ExpiringSignedLineRate=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.SignedPremium=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.SignedPremium=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.SignedPremium=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.SignedPremium_USD=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.SignedPremium_USD=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.SignedPremium_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.SignedPremium_USD=dbo.MarketResponseSecurity.Split
rpt.MarketInteractionResponse.ExpiringSignedPremium=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.ExpiringSignedPremium=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.ExpiringSignedPremium_USD=PS.MarketQuoteResponse.SignedLineRate
rpt.MarketInteractionResponse.ExpiringSignedPremium_USD=PS.MarketQuoteResponse.Premium
rpt.MarketInteractionResponse.ExpiringSignedPremium_USD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.MarketSecurityCarrierId=dbo.MarketResponseSecurity.CarrierId
rpt.MarketInteractionResponse.MarketResponseSecurityId=dbo.MarketResponseSecurity.MarketResponseSecurityId
rpt.MarketInteractionResponse.ResponseCreatedDate=dbo.MarketInteraction.ResponseDate
rpt.MarketInteractionResponse.ResponseCreatedDate=PS.MarketResponse.ResponseCreatedDate
rpt.MarketInteractionResponse.FirstResponseAcceptedDate=PS.MarketQuoteResponse.FirstResponseAcceptedDate
rpt.MarketInteractionResponse.LastResponseAcceptedDate=PS.MarketQuoteResponse.LastResponseAcceptedDate
rpt.MarketInteractionResponse.Deductible=PS.MarketQuoteResponse.Deductible
rpt.MarketInteractionResponse.DeductibleCurrency=PS.vwToAverageExchangeRate.FromCurrencyAlphaCode
rpt.MarketInteractionResponse.DeductibleUSD=PS.MarketQuoteResponse.Deductible
rpt.MarketInteractionResponse.DeductibleUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketInteractionResponse.FollowType=dbo.MarketInteraction.QuotedToLead
rpt.MarketInteractionResponse.FollowType=ref.FollowType.FollowType
rpt.MarketInteractionResponse.FollowType=PS.MarketQuoteResponse.FollowTypeId
rpt.MarketInteractionResponse.FollowType=PS.MarketQuoteResponse.QuotedToLead
rpt.MarketInteractionResponse.CurrentPolicyList=dbo.Policy.PolicyReference
rpt.MarketInteractionResponse.CurrentPolicyCount=BP.BoundPositionPlacementPolicy.PlacementPolicyId
rpt.MarketInteractionResponse.ExpiringPolicyList=dbo.Policy.PolicyReference
rpt.MarketInteractionResponse.ExpiringPolicyCount=BP.BoundPositionPlacementPolicy.PlacementPolicyId
rpt.MarketInteractionResponse.ContractId=PS.MarketResponseBasis.ContractId
rpt.MarketInteractionResponse.IsInvalid=PS.MarketResponse.IsInvalid
rpt.MarketInteractionResponse.SourceUpdatedDate=dbo.MarketInteraction.LastUpdatedUTCDate
rpt.MarketInteractionResponse.SourceUpdatedDate=PS.Negotiation.ETLUpdatedDate
rpt.MarketInteractionResponse.SourceUpdatedDate=PS.Negotiation.ETLCreatedDate
*/
CREATE PROCEDURE rpt.Load_rpt_MarketInteractionResponse
AS
/* WARNING - SQL Prompt will likely change the case of a ProductID/ProductId in here causing compile errors. */
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(255) = 'rpt.MarketInteractionResponse';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY

    -- Calculating the Lead Carrier Market Responses
    DROP TABLE IF EXISTS #ParentMarketResponse;

    CREATE TABLE #ParentMarketResponse (
        MarketResponseKey VARCHAR(50)    NULL
      , MarketName        NVARCHAR(550)  NULL
      , SignedLineRate    DECIMAL(38, 7) NULL
    );

    INSERT INTO
        #ParentMarketResponse
        (
            MarketResponseKey
          , MarketName
          , SignedLineRate
        )
    SELECT
        mr.MarketResponseKey
      , m.MarketName
      , mqr.SignedLineRate
    FROM
        PS.MarketResponse mr
        INNER JOIN PS.NegotiationMarket nm
            ON nm.NegotiationMarketId = mr.NegotiationMarketId
               AND nm.IsDeleted = 0

        LEFT JOIN PS.MarketQuoteResponse mqr
            ON mqr.MarketResponseId = mr.MarketResponseId
               AND mqr.IsDeleted = 0
               AND mqr.IsOverride = 0
               AND mqr.QuotedToLead = 1

        INNER JOIN rpt.Market m
            ON m.MarketKey = nm.MarketKey
    WHERE
        mr.MarketResponseKey LIKE 'MKTRES|%'
        AND mr.IsDeleted = 0
        AND mqr.OutcomeStatusId = 1;

    DROP TABLE IF EXISTS #AllExpiringHistoricQuotes;

    -- Get MAX expiring and historic quotes
    SELECT
        n.PlacementId
      , rp.LineOfBusiness
      , nm.CarrierId
      , LayerTypeId = COALESCE(mr.LayerTypeId, -1)
      , LayerType = ISNULL(lt.LayerType, 'None')
      , nt.NegotiationType
      , Premium = MAX(mqr.Premium)
      , PremiumRate = MAX(mqr.PremiumRate)
      , PremiumCurrencyId = MAX(mqr.PremiumCurrencyId)
      , SignedLineRate = MAX(mqr.SignedLineRate)
    INTO #AllExpiringHistoricQuotes
    FROM
        PS.MarketResponse mr
        LEFT JOIN PS.MarketQuoteResponse mqr
            ON mqr.MarketResponseId = mr.MarketResponseId
               AND mqr.IsDeleted = 0
               AND mqr.IsOverride = 0

        LEFT JOIN ref.LayerType lt
            ON lt.LayerTypeId = mr.LayerTypeId

        INNER JOIN PS.MarketResponseBasis mrb
            ON mrb.MarketResponseId = mr.MarketResponseId

        INNER JOIN PS.RiskProfile rp
            ON rp.RiskProfileId = mrb.RiskProfileId

        INNER JOIN PS.NegotiationMarket nm
            ON nm.NegotiationMarketId = mr.NegotiationMarketId
               AND nm.IsDeleted = 0
               AND nm.NegotiationMarketKey LIKE 'EXPRESPMKT|%'

        INNER JOIN PS.Negotiation n
            ON n.NegotiationId = nm.NegotiationId
               AND n.IsDeleted = 0
               AND n.NegotiationKey LIKE 'EXPRESP|%'

        INNER JOIN ref.NegotiationType nt
            ON nt.NegotiationTypeId = n.NegotiationTypeKey
    GROUP BY
        n.PlacementId
      , rp.LineOfBusiness
      , nm.CarrierId
      , COALESCE(mr.LayerTypeId, -1)
      , lt.LayerType
      , nt.NegotiationType;

    DROP TABLE IF EXISTS #UniqueQuoteList;

    -- Get unique list of quotes excluding negotiation type
    SELECT
        aq.PlacementId
      , aq.LineOfBusiness
      , aq.CarrierId
      , aq.LayerTypeId
      , aq.LayerType
    INTO #UniqueQuoteList
    FROM
        #AllExpiringHistoricQuotes aq
    GROUP BY
        aq.PlacementId
      , aq.LineOfBusiness
      , aq.CarrierId
      , aq.LayerTypeId
      , aq.LayerType;

    DROP TABLE IF EXISTS #MergedExpiringHistoricQuotes;

    -- Create final quote data to link to current quotes
    SELECT
        aq.PlacementId
      , aq.LineOfBusiness
      , aq.CarrierId
      , aq.LayerTypeId
      , aq.LayerType
      , Premium = COALESCE(eq.Premium, hq.Premium)
      , PremiumRate = COALESCE(eq.PremiumRate, hq.PremiumRate)
      , PremiumCurrencyId = COALESCE(eq.PremiumCurrencyId, hq.PremiumCurrencyId)
      , SignedLineRate = COALESCE(eq.SignedLineRate, hq.SignedLineRate)
    INTO #MergedExpiringHistoricQuotes
    FROM
        #UniqueQuoteList aq
        LEFT JOIN #AllExpiringHistoricQuotes eq
            ON eq.PlacementId = aq.PlacementId
               AND eq.LineOfBusiness = aq.LineOfBusiness
               AND eq.CarrierId = aq.CarrierId
               AND eq.LayerType = aq.LayerType
               AND eq.NegotiationType = 'Expiring'

        LEFT JOIN #AllExpiringHistoricQuotes hq
            ON hq.PlacementId = aq.PlacementId
               AND hq.LineOfBusiness = aq.LineOfBusiness
               AND hq.CarrierId = aq.CarrierId
               AND hq.LayerType = aq.LayerType
               AND hq.NegotiationType = 'Historic';

    DROP TABLE IF EXISTS #BoundResposeCurrentPolicy;

    SELECT
        mr.MarketResponseId
      , CurrentPolicyList = LEFT(STRING_AGG(po.PolicyReference, ' || '), 1000)
      , CurrentPolicyCount = COUNT(bppp.PlacementPolicyId)
    INTO #BoundResposeCurrentPolicy
    FROM
        PS.MarketResponse mr
        INNER JOIN BP.MarketResponseElement mre
            ON CONCAT('MKTRES|', mre.MarketResponseId) = mr.MarketResponseKey

        INNER JOIN BP.BoundPosition bp
            ON bp.ResponseManagementElementId = mre.ResponseManagementElementId
               AND ISNULL(bp.ElementBranchId, -1) = ISNULL(mre.ElementBranchId, -1)
               AND bp.BoundPositionTypeId = 1 /* Current */
               AND bp.IsDeleted = 0

        INNER JOIN BP.BoundPositionPlacementPolicy bppp
            ON bppp.BoundPositionId = bp.Id
               AND bppp.IsDeleted = 0

        INNER JOIN dbo.PlacementPolicy pp
            ON bppp.PlacementPolicyId = pp.SourcePlacementPolicyId

        INNER JOIN dbo.Policy po
            ON po.PolicyId = pp.PolicyId
    WHERE
        mr.DataSourceInstanceId = 50366
    GROUP BY
        mr.MarketResponseId;

    DROP TABLE IF EXISTS #BoundResposeExpiringPolicy;

    SELECT
        mr.MarketResponseId
      , ExpiringPolicyList = LEFT(STRING_AGG(po.PolicyReference, ' || '), 1000)
      , ExpiringPolicyCount = COUNT(bppp.PlacementPolicyId)
    INTO #BoundResposeExpiringPolicy
    FROM
        PS.MarketResponse mr
        INNER JOIN BP.MarketResponseElement mre
            ON CONCAT('MKTRES|', mre.MarketResponseId) = mr.MarketResponseKey

        INNER JOIN BP.BoundPosition bp
            ON bp.ResponseManagementElementId = mre.ResponseManagementElementId
               AND ISNULL(bp.ElementBranchId, -1) = ISNULL(mre.ElementBranchId, -1)
               AND bp.BoundPositionTypeId = 2 /* Expiring */
               AND bp.IsDeleted = 0

        INNER JOIN BP.BoundPositionPlacementPolicy bppp
            ON bppp.BoundPositionId = bp.Id
               AND bppp.IsDeleted = 0

        INNER JOIN dbo.PlacementPolicy pp
            ON bppp.PlacementPolicyId = pp.SourcePlacementPolicyId

        INNER JOIN dbo.Policy po
            ON po.PolicyId = pp.PolicyId
    WHERE
        mr.DataSourceInstanceId = 50366
    GROUP BY
        mr.MarketResponseId;

    -- Create SourceForMerge temp table
    CREATE TABLE #SourceForMerge (
        MarketCategory             NVARCHAR(30)   NOT NULL
      , CarrierResponseId          INT            NOT NULL
      , MarketResponseKey          NVARCHAR(50)   NOT NULL
      , ParentResponseKey          NVARCHAR(50)   NULL
      , MarketSelectionId          INT            NULL
      , NegotiationMarketKey       NVARCHAR(30)   NULL
      , MarketSelectionKey         NVARCHAR(30)   NULL
      , InteractionName            NVARCHAR(250)  NULL
      , SubmittedDate              DATETIME       NULL
      , ResponseDate               DATETIME2      NULL
      , ResponseTimeDays           INT            NULL
      , PendingActionReason        NVARCHAR(200)  NULL
      , ResponseType               NVARCHAR(200)  NULL
      , DeclinationReason          NVARCHAR(200)  NULL
      , DeclinationReasonGroupName NVARCHAR(500)  NULL
      , OutcomeReason              NVARCHAR(200)  NULL
      , OutcomeReasonGroupName     NVARCHAR(500)  NULL
      , OutcomeStatus              NVARCHAR(200)  NULL
      , UnderwriterName            NVARCHAR(200)  NULL
      , UnderwriterEmail           NVARCHAR(200)  NULL
      , ProductKey                 NVARCHAR(200)  NULL
      , ProductId                  INT            NULL
      , ClassId                    INT            NULL
      , LayerType                  NVARCHAR(200)  NULL
      , LeadCarrier                NVARCHAR(550)  NULL
      , LeadSignedLineRate         DECIMAL(38, 7) NULL
      , QuotedToLead               BIT            NULL
      , Premium_Currency           NVARCHAR(3)    NULL
      , Premium                    DECIMAL(19, 4) NULL
      , Premium_USD                DECIMAL(38, 6) NULL
      , OfferedLine                DECIMAL(38, 7) NULL
      , OfferedLine_USD            DECIMAL(38, 6) NULL
      , OfferedLineRate            DECIMAL(38, 7) NULL
      , PremiumRate                DECIMAL(21, 6) NULL
      , CommissionRate             DECIMAL(21, 6) NULL
      , Limit_Currency             NVARCHAR(3)    NULL
      , Limit                      DECIMAL(19, 4) NULL
      , Limit_USD                  DECIMAL(38, 6) NULL
      , AttachmentPoint_Currency   NVARCHAR(3)    NULL
      , AttachmentPoint            DECIMAL(19, 4) NULL
      , AttachmentPoint_USD        DECIMAL(38, 6) NULL
      , Comments                   NVARCHAR(3000) NULL
      , Subjectivity               NVARCHAR(3000) NULL
      , ResponseDateKey            INT            NULL
      , ResponseKey                NVARCHAR(26)   NULL
      , WrittenPremium             DECIMAL(38, 6) NULL
      , WrittenPremium_USD         DECIMAL(38, 6) NULL
      , MultiRisk                  INT            NOT NULL
      , MarketSecurityCarrierKey   NVARCHAR(60)   NULL
      , Split                      DECIMAL(21, 6) NULL
      , SignedLine                 DECIMAL(38, 6) NULL
      , SignedLine_Currency        NVARCHAR(3)    NULL
      , SignedLine_USD             DECIMAL(38, 6) NULL
      , SignedLineRate             DECIMAL(38, 7) NULL
      , ExpiringSignedLineRate     DECIMAL(38, 7) NULL
      , SignedPremium              DECIMAL(38, 6) NULL
      , SignedPremium_USD          DECIMAL(38, 6) NULL
      , ExpiringSignedPremium      DECIMAL(38, 6) NULL
      , ExpiringSignedPremium_USD  DECIMAL(38, 6) NULL
      , MarketSecurityCarrierId    INT            NULL
      , MarketResponseSecurityId   INT            NULL
      , LastUpdatedUTCDate         DATETIME2      NULL
      , ResponseCreatedDate        DATETIME2      NULL
      , FirstResponseAcceptedDate  DATETIME2      NULL
      , LastResponseAcceptedDate   DATETIME2      NULL
      , Deductible                 DECIMAL(38, 6) NULL
      , DeductibleCurrency         NVARCHAR(3)    NULL
      , DeductibleUSD              DECIMAL(38, 6) NULL
      , FollowType                 NVARCHAR(30)   NULL
      , CurrentPolicyList          NVARCHAR(1000) NULL
      , CurrentPolicyCount         INT            NOT NULL
      , ExpiringPolicyList         NVARCHAR(1000) NULL
      , ExpiringPolicyCount        INT            NOT NULL
      , ContractId                 INT            NULL
      , IsInvalid                  BIT            NOT NULL
    );

    INSERT INTO
        #SourceForMerge
    SELECT
        MarketCategory = 'MarketSelection'
      , mi.CarrierResponseId
      , MarketResponseKey = CONCAT('CARRES|', mi.CarrierResponseId)
      , ParentResponseKey = NULL
      , mi.MarketSelectionId
      , NegotiationMarketKey = CONCAT('MKTSEL|', pla.PlacementSystemMarketSelectionId)
      , MarketSelectionKey = (
                                 SELECT CAST(MIN(t.TableId) AS NVARCHAR(5))
                                 FROM
                                     dbo.PlacementSystemTable t
                                 WHERE
                                     t.SchemaName = 'app'
                                     AND t.TableName = 'PlacementCarrier'
                             ) + '|' + CAST(mi.MarketSelectionId AS NVARCHAR(20))
      , mi.InteractionName
      , mi.SubmittedDate
      , ResponseDate = CASE WHEN mi.ResponseDate < '1900-01-01 00:00:00'
                                THEN '1900-01-01 00:00:00.0000000'
                            ELSE mi.ResponseDate END
      , ResponseTimeDays = DATEDIFF(DAY, mi.SubmittedDate, mi.ResponseDate)
      , par.PendingActionReason
      , rt.ResponseType
      , dr.DeclinationReason
      , DeclinationReasonGroupName = rgmdr.GroupName
      , o.OutcomeReason
      , OutcomeReasonGroupName = rgmor.GroupName
      , s.OutcomeStatus
      , mi.UnderwriterName
      , UnderwriterEmail = CAST(NULL AS NVARCHAR(320))
      , ph.ProductKey
      , prod.ProductId
      , prod.ClassId
      , lt.LayerType
      , LeadCarrier = NULL
      , LeadSignedLineRate = NULL
      , mi.QuotedToLead
      , Premium_Currency = p_er.FromCurrencyAlphaCode
      , Premium = CAST(COALESCE(
                           mi.Premium
                         , CASE WHEN mi.PremiumRate > 1
                                     AND mi.PremiumRate <= 100
                                     AND mi.Limit > 0
                                    THEN (mi.PremiumRate / 100) * mi.Limit END
                         , CASE WHEN mi.PremiumRate BETWEEN 0 AND 1
                                     AND mi.Limit > 0
                                    THEN mi.PremiumRate * mi.Limit END
                       ) AS DECIMAL(19, 4))
      , Premium_USD = CAST(COALESCE(
                               mi.Premium
                             , CASE WHEN mi.PremiumRate > 1
                                         AND mi.PremiumRate <= 100
                                         AND mi.Limit > 0
                                        THEN (mi.PremiumRate / 100) * mi.Limit END
                             , CASE WHEN mi.PremiumRate BETWEEN 0 AND 1
                                         AND mi.Limit > 0
                                        THEN mi.PremiumRate * mi.Limit END
                           ) AS DECIMAL(19, 4)) * p_er.ExchangeRate
      , OfferedLine = CAST(COALESCE(mi.OfferedLine
                                  , CASE WHEN mi.OfferedLineRate > 0
                                              AND mi.Limit > 0
                                             THEN (mi.OfferedLineRate / 100) * mi.Limit END
                           ) AS DECIMAL(19, 4))
      , OfferedLine_USD = CAST(COALESCE(mi.OfferedLine
                                      , CASE WHEN mi.OfferedLineRate > 0
                                                  AND mi.Limit > 0
                                                 THEN (mi.OfferedLineRate / 100) * mi.Limit END
                               ) AS DECIMAL(19, 4)) * l_er.ExchangeRate
      , OfferedLineRate = CAST(COALESCE(mi.OfferedLineRate
                                      , CASE WHEN mi.OfferedLine > 0
                                                  AND mi.Limit > 0
                                                 THEN (mi.OfferedLine / mi.Limit) * 100
                                             WHEN mi.OfferedLine > 0
                                                  AND mi.Limit IS NULL
                                                 THEN 100 END
                                      , 0
                               ) AS DECIMAL(21, 6))
      , mi.PremiumRate
      , mi.CommissionRate
      , Limit_Currency = l_er.FromCurrencyAlphaCode
      , mi.Limit
      , Limit_USD = mi.Limit * l_er.ExchangeRate
      , AttachmentPoint_Currency = a_er.FromCurrencyAlphaCode
      , mi.AttachmentPoint
      , AttachmentPoint_USD = mi.AttachmentPoint * a_er.ExchangeRate
      , Comments = SUBSTRING(CASE WHEN LEN(LTRIM(RTRIM(mi.Comments))) = 0
                                      THEN NULL
                                  ELSE LTRIM(RTRIM(mi.Comments)) END
                           , 0
                           , 3000
                   ) --long records are no use in reporting
      , Subjectivity = CASE WHEN LEN(LTRIM(RTRIM(mi.Subjectivity))) = 0
                                THEN NULL
                            ELSE LTRIM(RTRIM(mi.Subjectivity)) END
      , ResponseDateKey = CASE WHEN mi.ResponseDate >= '21000101'
                                   THEN 21000101
                               ELSE CONVERT(INT, CONVERT(CHAR(8), mi.ResponseDate, 112)) END
      , ResponseKey = (
                          SELECT CAST(MIN(t.TableId) AS NVARCHAR(5))
                          FROM
                              dbo.PlacementSystemTable t
                          WHERE
                              t.SchemaName = 'app'
                              AND t.TableName = 'CarrierResponse'
                      ) + '|' + CAST(mi.CarrierResponseId AS NVARCHAR(20))
      , WrittenPremium = CAST(CASE WHEN mi.OfferedLineRate > 0
                                        AND mi.Premium > 0
                                       THEN (mi.OfferedLineRate / 100) * mi.Premium
                                   WHEN mi.OfferedLine > 0
                                        AND mi.Limit > 0
                                        AND mi.Premium > 0
                                       THEN (mi.OfferedLine / mi.Limit) * mi.Premium END AS DECIMAL(30, 8))
      , WrittenPremium_USD = CAST(CASE WHEN mi.OfferedLineRate > 0
                                            AND mi.Premium > 0
                                           THEN (mi.OfferedLineRate / 100) * mi.Premium
                                       WHEN mi.OfferedLine > 0
                                            AND mi.Limit > 0
                                            AND mi.Premium > 0
                                           THEN (mi.OfferedLine / mi.Limit) * mi.Premium END AS DECIMAL(30, 8))
                             * p_er.ExchangeRate
      , MultiRisk = 0
      , MarketSecurityCarrierKey = CAST(NULL AS NVARCHAR(60))
      , Split = mi.OfferedLineRate
      , SignedLine = NULL
      , SignedLine_Currency = NULL
      , SignedLine_USD = NULL
      , SignedLineRate = NULL
      , ExpiringSignedLineRate = NULL
      , SignedPremium = NULL
      , SignedPremium_USD = NULL
      , ExpiringSignedPremium = NULL
      , ExpiringSignedPremium_USD = NULL
      , MarketSecurityCarrierId = NULL
      , MarketResponseSecurityId = NULL
      , mi.LastUpdatedUTCDate
      , ResponseCreatedDate = CASE WHEN mi.ResponseDate < '1900-01-01 00:00:00'
                                       THEN '1900-01-01 00:00:00.0000000'
                                   ELSE mi.ResponseDate END
      , FirstResponseAcceptedDate = NULL
      , LastResponseAcceptedDate = NULL
      , Deductible = NULL
      , DeductibleCurrency = NULL
      , DeductibleUSD = NULL
      , FollowType = CASE WHEN mi.QuotedToLead = 1
                              THEN 'Lead'
                          ELSE 'Follow' END
      , CurrentPolicyList = NULL
      , CurrentPolicyCount = 0
      , ExpiringPolicyList = NULL
      , ExpiringPolicyCount = 0
      , ContractId = NULL
      , IsInvalid = CAST(0 AS BIT)
    FROM
        dbo.MarketInteraction mi
        INNER JOIN (
            SELECT
                ms.MarketSelectionId
              , pl.PlacementId
              , ms.CarrierId
              , ms.PlacementSystemMarketSelectionId
              , LastUpdatedUTCDate = ms.SourceUpdatedDate
            FROM
                dbo.MarketSelection ms
                JOIN dbo.Placement pl
                    ON ms.PlacementId = pl.PlacementId
                       AND pl.IsDeleted = 0

                INNER JOIN ref.PlacementStatus ps
                    ON ps.PlacementStatusId = pl.PlacementStatusId
            WHERE
                (
                ps.PlacementStatusKey <> '6'
                OR pl.CancellationReasonId <> 9
            )
                AND ms.IsDeleted = 0
        ) pla --only include non-migrated business
            ON mi.MarketSelectionId = pla.MarketSelectionId

        LEFT JOIN (SELECT DISTINCT ProductId, ClassId FROM PS.RiskProfile WHERE IsDeleted = 0) prod
            ON prod.ProductId = mi.ProductId

        LEFT JOIN rpt.ProductHierarchy ph
            ON ph.ProductId = prod.ProductId

        LEFT JOIN ref.OutcomeReason o
            ON o.OutcomeReasonId = mi.OutcomeReasonId

        LEFT JOIN dbo.ReasonGroup rgmor
            ON o.ReasonGroupId = rgmor.ReasonGroupId

        LEFT JOIN ref.ResponseType rt
            ON rt.ResponseTypeId = mi.ResponseTypeId

        LEFT JOIN ref.OutcomeStatus s
            ON s.OutcomeStatusId = mi.OutcomeStatusId

        LEFT JOIN ref.LayerType lt
            ON lt.LayerTypeId = mi.LayerTypeId

        LEFT JOIN ref.DeclinationReason dr
            ON dr.DeclinationReasonId = mi.DeclinationReasonId

        LEFT JOIN dbo.ReasonGroup rgmdr
            ON dr.ReasonGroupId = rgmdr.ReasonGroupId

        LEFT JOIN ref.PendingActionReason par
            ON par.PendingActionReasonId = mi.PendingActionReasonId

        LEFT JOIN PS.vwToAverageExchangeRate p_er
            ON p_er.FromCurrencyId = mi.PremiumCurrencyId
               AND p_er.EffectiveFromDate <= mi.ResponseDate
               AND mi.ResponseDate < p_er.EffectiveToDate
               AND p_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate l_er
            ON l_er.FromCurrencyId = mi.LimitCurrencyId
               AND l_er.EffectiveFromDate <= mi.ResponseDate
               AND mi.ResponseDate < l_er.EffectiveToDate
               AND l_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate a_er
            ON a_er.FromCurrencyId = mi.AttachmentCurrencyId
               AND a_er.EffectiveFromDate <= mi.ResponseDate
               AND mi.ResponseDate < a_er.EffectiveToDate
               AND a_er.ToCurrencyAlphaCode = 'USD'
    WHERE
        mi.IsDeleted = 0;

    INSERT INTO
        #SourceForMerge
    SELECT
        MarketCategory = 'SubmissionMarket'
      , CarrierResponseId = TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT)
      , mr.MarketResponseKey
      , ParentResponseKey = mr.ParentKey
      , MarketSelectionID = TRY_CAST(SUBSTRING(nm.NegotiationMarketKey, 11, 11) AS INT)
      , nm.NegotiationMarketKey
      , MarketSelectionKey = (
                                 SELECT CAST(MIN(t.TableId) AS NVARCHAR(5))
                                 FROM
                                     dbo.PlacementSystemTable t
                                 WHERE
                                     t.SchemaName = 'app'
                                     AND t.TableName = 'SubmissionContainerMarket'
                             ) + '|'
                             + CAST(TRY_CAST(SUBSTRING(nm.NegotiationMarketKey, 11, 11) AS INT) AS NVARCHAR(20))
      , InteractionName = n.SubmissionName
      , SubmittedDate = su.SentDate
      , ResponseDate = CASE WHEN mr.ResponseDate < '1900-01-01 00:00:00'
                                THEN '1900-01-01 00:00:00.0000000'
                            ELSE mr.ResponseDate END
      , ResponseTimeDays = DATEDIFF(DAY, su.SentDate, mr.ResponseDate)
      , par.PendingActionReason
      , rt.ResponseType
      , dr.DeclinationReason
      , DeclinationReasonGroupName = rgmdr.GroupName
      , o.OutcomeReason
      , OutcomeReasonGroupName = rgmor.GroupName
      , os.OutcomeStatus
      , Underwriter = mr.UnderwriterName
      , mr.UnderwriterEmail
      , ProductKey = COALESCE(ph.ProductKey, ph2.ProductKey, ph3.ProductKey)
      , ProductId = COALESCE(prod.ProductId, prod2.ProductId, prod3.ProductId)
      , ClassId = COALESCE(prod.ClassId, prod2.ClassId, prod3.ClassId)
      , lt.LayerType
      , LeadCarrier = pmr.MarketName
      , LeadSignedLineRate = pmr.SignedLineRate
      , mqr.QuotedToLead
      , Premium_Currency = p_er.FromCurrencyAlphaCode
      , Premium = CAST(COALESCE(
                           mqr.Premium
                         , CASE WHEN mqr.PremiumRate > 1
                                     AND mqr.PremiumRate <= 100
                                     AND mr.Limit > 0
                                    THEN (mqr.PremiumRate / 100) * mr.Limit END
                         , CASE WHEN mqr.PremiumRate BETWEEN 0 AND 1
                                     AND mr.Limit > 0
                                    THEN mqr.PremiumRate * mr.Limit END
                       ) AS DECIMAL(19, 4))
      , Premium_USD = CAST(COALESCE(
                               mqr.Premium
                             , CASE WHEN mqr.PremiumRate > 1
                                         AND mqr.PremiumRate <= 100
                                         AND mr.Limit > 0
                                        THEN (mqr.PremiumRate / 100) * mr.Limit END
                             , CASE WHEN mqr.PremiumRate BETWEEN 0 AND 1
                                         AND mr.Limit > 0
                                        THEN mqr.PremiumRate * mr.Limit END
                           ) AS DECIMAL(19, 4)) * p_er.ExchangeRate
      , OfferedLine = CAST(COALESCE(mqr.OfferedLine
                                  , CASE WHEN mqr.OfferedLineRate > 0
                                              AND mr.Limit > 0
                                             THEN (mqr.OfferedLineRate / 100) * mr.Limit END
                           ) AS DECIMAL(19, 4))
      , OfferedLine_USD = CAST(COALESCE(mqr.OfferedLine
                                      , CASE WHEN mqr.OfferedLineRate > 0
                                                  AND mr.Limit > 0
                                                 THEN (mqr.OfferedLineRate / 100) * mr.Limit END
                               ) AS DECIMAL(19, 4)) * l_er.ExchangeRate
      , OfferedLineRate = CAST(COALESCE(mqr.OfferedLineRate
                                      , CASE WHEN mqr.OfferedLine > 0
                                                  AND mr.Limit > 0
                                                 THEN (mqr.OfferedLine / mr.Limit) * 100
                                             WHEN mqr.OfferedLine > 0
                                                  AND mr.Limit IS NULL
                                                 THEN 100 END
                                      , 0
                               ) AS DECIMAL(21, 6))
      , mqr.PremiumRate
      , mqr.CommissionRate
      , Limit_Currency = l_er.FromCurrencyAlphaCode
      , Limit = mr.Limit
      , Limit_USD = mr.Limit * l_er.ExchangeRate
      , AttachmentPoint_Currency = ap_er.FromCurrencyAlphaCode
      , AttachmentPoint = mr.AttachmentPoint
      , AttachmentPoint_USD = mr.AttachmentPoint * ap_er.ExchangeRate
      , Comments = SUBSTRING(CASE WHEN LEN(LTRIM(RTRIM(mr.Comments))) = 0
                                      THEN NULL
                                  ELSE LTRIM(RTRIM(mr.Comments)) END
                           , 0
                           , 3000
                   )
      , Subjectivity = SUBSTRING(CASE WHEN LEN(LTRIM(RTRIM(mqr.Subjectivity))) = 0
                                          THEN NULL
                                      ELSE LTRIM(RTRIM(mqr.Subjectivity)) END
                               , 0
                               , 3000
                       )
      , ResponseDateKey = CASE WHEN mr.ResponseDate >= '21000101'
                                   THEN 21000101
                               ELSE CONVERT(INT, CONVERT(CHAR(8), mr.ResponseDate, 112)) END
      , ResponseKey = (
                          SELECT CAST(MIN(t.TableId) AS NVARCHAR(5))
                          FROM
                              dbo.PlacementSystemTable t
                          WHERE
                              t.SchemaName = 'app'
                              AND t.TableName = 'MarketResponse'
                      ) + '|' + CAST(TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT) AS NVARCHAR(20))
      , WrittenPremium = CAST(CASE WHEN mqr.OfferedLineRate > 0
                                        AND mqr.Premium > 0
                                       THEN (mqr.OfferedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
      , WrittenPremium_USD = CAST(CASE WHEN mqr.OfferedLineRate > 0
                                            AND mqr.Premium > 0
                                           THEN (mqr.OfferedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
                             * p_er.ExchangeRate
      , MultiRisk = CASE WHEN mrb.SpecificationCount > 1
                              OR mrb.RiskProfileCount > 1
                             THEN 1
                         ELSE 0 END
      , MarketSecurityCarrierKey = NULL
      , Split = mqr.OfferedLineRate
      , SignedLine = CAST(COALESCE(mqr.SignedLine
                                 , CASE WHEN mqr.SignedLineRate > 0
                                             AND mr.Limit > 0
                                            THEN (mqr.SignedLineRate / 100) * mr.Limit END
                          ) AS DECIMAL(19, 4))
      , SignedLine_Currency = l_er.FromCurrencyAlphaCode
      , SignedLine_USD = CAST(COALESCE(mqr.SignedLine
                                     , CASE WHEN mqr.SignedLineRate > 0
                                                 AND mr.Limit > 0
                                                THEN (mqr.SignedLineRate / 100) * mr.Limit END
                              ) AS DECIMAL(19, 4)) * l_er.ExchangeRate
      , SignedLineRate = CAST(COALESCE(mqr.SignedLineRate
                                     , CASE WHEN mqr.SignedLine > 0
                                                 AND mr.Limit > 0
                                                THEN (mqr.SignedLine / mr.Limit) * 100
                                            WHEN mqr.SignedLine > 0
                                                 AND mr.Limit IS NULL
                                                THEN 100 END
                                     , 0
                              ) AS DECIMAL(21, 6))
      , ExpiringSignedLineRate = CAST(emqr.SignedLineRate AS DECIMAL(21, 6))
      , SignedPremium = CAST(CASE WHEN mqr.SignedLineRate > 0
                                       AND mqr.Premium > 0
                                      THEN (mqr.SignedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
      , SignedPremium_USD = CAST(CASE WHEN mqr.SignedLineRate > 0
                                           AND mqr.Premium > 0
                                          THEN (mqr.SignedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
                            * p_er.ExchangeRate
      , ExpiringSignedPremium = CAST(CASE WHEN emqr.SignedLineRate > 0
                                               AND emqr.Premium > 0
                                              THEN (emqr.SignedLineRate / 100) * emqr.Premium END AS DECIMAL(30, 8))
      , ExpiringSignedPremium_USD = CAST(CASE WHEN emqr.SignedLineRate > 0
                                                   AND emqr.Premium > 0
                                                  THEN (emqr.SignedLineRate / 100) * emqr.Premium END AS DECIMAL(30, 8))
                                    * ep_er.ExchangeRate
      , MarketSecurityCarrierId = NULL
      , MarketResponseSecurityId = NULL
      , LastUpdatedUTCDate = ISNULL(n.ETLUpdatedDate, n.ETLCreatedDate)
      , ResponseCreatedDate = mr.ResponseCreatedDate
      , FirstResponseAcceptedDate = mqr.FirstResponseAcceptedDate
      , LastResponseAcceptedDate = mqr.LastResponseAcceptedDate
      , Deductible = mqr.Deductible
      , DeductibleCurrencyCode = d_er.FromCurrencyAlphaCode
      , Deductible_USD = CAST(CASE WHEN mqr.Deductible > 0
                                       THEN mqr.Deductible * d_er.ExchangeRate
                                   ELSE 0 END AS DECIMAL(38, 6))
      , FollowType = CASE WHEN mqr.FollowTypeId IS NULL
                               AND mqr.QuotedToLead = 1
                              THEN 'LEAD'
                          ELSE ft.FollowType END
      , brcp.CurrentPolicyList
      , CurrentPolicyCount = ISNULL(brcp.CurrentPolicyCount, 0)
      , brep.ExpiringPolicyList
      , ExpiringPolicyCount = ISNULL(brep.ExpiringPolicyCount, 0)
      , mrb.ContractId
      , mr.IsInvalid
    FROM
        PS.MarketResponse mr
        LEFT JOIN PS.MarketQuoteResponse mqr
            ON mqr.MarketResponseId = mr.MarketResponseId
               AND mqr.IsDeleted = 0
               AND mqr.IsOverride = 0

        INNER JOIN PS.NegotiationMarket nm
            ON nm.NegotiationMarketId = mr.NegotiationMarketId
               AND nm.IsDeleted = 0
               AND nm.NegotiationMarketKey LIKE 'SUBCONMKT|%'

        INNER JOIN PS.Negotiation n
            ON n.NegotiationId = nm.NegotiationId
               AND n.IsDeleted = 0
               AND n.NegotiationKey LIKE 'SUBC|%'

        LEFT JOIN ref.FollowType ft
            ON mqr.FollowTypeId = ft.FollowTypeId

        LEFT JOIN (SELECT SubmissionContainerId, SentDate = MIN(Sent) FROM BP.Submission GROUP BY SubmissionContainerId) su
            ON TRY_CAST(SUBSTRING(n.NegotiationKey, 6, 11) AS INT) = su.SubmissionContainerId

        LEFT JOIN (
            SELECT
                MarketResponseId = TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT)
              , mr.MarketResponseKey
              , SpecificationId = MAX(ISNULL(mrb.SpecificationId, 0))
              , RiskProfileId = MAX(ISNULL(mrb.RiskProfileId, 0))
              , ContractId = MAX(ISNULL(mrb.ContractId, 0))
              , SpecificationCount = SUM(CASE WHEN mrb.SpecificationId IS NOT NULL
                                                  THEN 1
                                              ELSE 0 END
                                     )
              , RiskProfileCount = SUM(CASE WHEN mrb.RiskProfileId IS NOT NULL
                                                THEN 1
                                            WHEN cr.ContractId IS NOT NULL
                                                THEN 1
                                            ELSE 0 END
                                   )
            FROM
                PS.MarketResponseBasis mrb
                INNER JOIN PS.MarketResponse mr
                    ON mr.MarketResponseId = mrb.MarketResponseId
                       AND mr.IsDeleted = 0
                       AND mr.MarketResponseKey LIKE 'MKTRES|%'

                LEFT JOIN PS.ContractRiskProfile cr
                    ON mrb.ContractId = cr.ContractId
                       AND cr.IsDeleted = 0
            WHERE
                mrb.IsDeleted = 0
            GROUP BY
                mr.MarketResponseKey
        ) mrb
            ON mr.MarketResponseKey = mrb.MarketResponseKey --get max Specification as we want a product to associate

        LEFT JOIN dbo.Specification s
            ON mrb.SpecificationId = s.SpecificationId
               AND s.IsDeleted = 0
        --Multiple risks can appear on one contract.  Therefore using one.  
        -- If we wanted to support multiple products, we'd also need to change the premiums to avoid double counting
        -- This only affects Contract Builder

        LEFT JOIN (
            SELECT
                cr.ContractId
              , cr.RiskProfileId
              , RowNo = ROW_NUMBER() OVER (PARTITION BY cr.ContractId ORDER BY cr.ETLUpdatedDate DESC, cr.ContractRiskId DESC)
            FROM
                PS.ContractRiskProfile cr
            WHERE
                cr.IsDeleted = 0
        ) cr
            ON mrb.ContractId = cr.ContractId
               AND cr.RowNo = 1

        LEFT JOIN PS.RiskProfile prod
            ON prod.RiskProfileId = mrb.RiskProfileId
               AND prod.IsDeleted = 0

        LEFT JOIN PS.RiskProfile prod2
            ON prod2.RiskProfileKey = CONCAT('SPEC|', mrb.SpecificationId)
               AND prod2.IsDeleted = 0

        LEFT JOIN PS.RiskProfile prod3
            ON prod3.RiskProfileId = cr.RiskProfileId
               AND prod3.IsDeleted = 0

        LEFT JOIN rpt.ProductHierarchy ph
            ON ph.ProductId = prod.ProductId

        LEFT JOIN rpt.ProductHierarchy ph2
            ON ph2.ProductId = prod2.ProductId

        LEFT JOIN rpt.ProductHierarchy ph3
            ON ph3.ProductId = prod3.ProductId

        LEFT JOIN ref.OutcomeReason o
            ON o.OutcomeReasonId = mqr.OutcomeReasonId

        LEFT JOIN dbo.ReasonGroup rgmor
            ON o.ReasonGroupId = rgmor.ReasonGroupId

        LEFT JOIN ref.ResponseType rt
            ON rt.ResponseTypeId = mr.ResponseTypeId

        LEFT JOIN ref.OutcomeStatus os
            ON os.OutcomeStatusId = mqr.OutcomeStatusId

        LEFT JOIN ref.LayerType lt
            ON lt.LayerTypeId = mr.LayerTypeId

        LEFT JOIN #MergedExpiringHistoricQuotes emqr
            ON emqr.PlacementId = n.PlacementId
               AND emqr.LineOfBusiness = prod.LineOfBusiness
               AND emqr.CarrierId = nm.CarrierId
               AND emqr.LayerTypeId = ISNULL(mr.LayerTypeId, -1)

        LEFT JOIN ref.DeclinationReason dr
            ON dr.DeclinationReasonId = mr.DeclinationReasonId

        LEFT JOIN dbo.ReasonGroup rgmdr
            ON dr.ReasonGroupId = rgmdr.ReasonGroupId

        LEFT JOIN ref.PendingActionReason par
            ON par.PendingActionReasonId = mr.PendingActionReasonId

        LEFT JOIN PS.vwToAverageExchangeRate p_er
            ON p_er.FromCurrencyId = mqr.PremiumCurrencyId
               AND p_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < p_er.EffectiveToDate
               AND p_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate ep_er
            ON ep_er.FromCurrencyId = emqr.PremiumCurrencyId
               AND ep_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < ep_er.EffectiveToDate
               AND ep_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate l_er
            ON l_er.FromCurrencyId = mr.LimitCurrencyId
               AND l_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < l_er.EffectiveToDate
               AND l_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate ap_er
            ON ap_er.FromCurrencyId = mr.AttachmentPointCurrencyId
               AND ap_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < ap_er.EffectiveToDate
               AND ap_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate d_er
            ON d_er.FromCurrencyId = mqr.DeductibleCurrencyId
               AND d_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < d_er.EffectiveToDate
               AND d_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN #BoundResposeCurrentPolicy brcp
            ON brcp.MarketResponseId = mr.MarketResponseId

        LEFT JOIN #BoundResposeExpiringPolicy brep
            ON brep.MarketResponseId = mr.MarketResponseId

        LEFT JOIN #ParentMarketResponse pmr
            ON mr.ParentKey = pmr.MarketResponseKey --Joining the Current Response from Market Interactions Response through its Parent Response key with the subquery that contains all the lead Responses (MKR Key).
    WHERE
        mr.MarketResponseKey LIKE 'MKTRES|%'
        AND mr.DataSourceInstanceId = 50366
        AND mr.IsDeleted = 0;

    INSERT INTO
        #SourceForMerge
    SELECT
        MarketCategory = 'MarketSecurity'
      , CarrierResponseId = TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT)
      , MarketResponseKey = mr.MarketResponseKey
      , ParentResponseKey = NULL
      , MarketSelectionID = TRY_CAST(SUBSTRING(nm.NegotiationMarketKey, 11, 11) AS INT)
      , NegotiationMarketKey = CONCAT('MKTSEC|', TRY_CAST(SUBSTRING(nm.NegotiationMarketKey, 11, 11) AS INT)) --Replacing SUBCONMKT with MKTSEC for the MarketSecurity section of code
      , MarketSelectionKey = (
                                 SELECT CAST(MIN(t.TableId) AS NVARCHAR(5))
                                 FROM
                                     dbo.PlacementSystemTable t
                                 WHERE
                                     t.SchemaName = 'app'
                                     AND t.TableName = 'SubmissionContainerMarket'
                             ) + '|'
                             + CAST(TRY_CAST(SUBSTRING(nm.NegotiationMarketKey, 11, 11) AS INT) AS NVARCHAR(20))
      , InteractionName = n.SubmissionName
      , SubmittedDate = su.SentDate
      , ResponseDate = CASE WHEN mr.ResponseDate < '1900-01-01 00:00:00'
                                THEN '1900-01-01 00:00:00.0000000'
                            ELSE mr.ResponseDate END
      , ResponseTimeDays = DATEDIFF(DAY, su.SentDate, mr.ResponseDate)
      , par.PendingActionReason
      , rt.ResponseType
      , dr.DeclinationReason
      , DeclinationReasonGroupName = rgmdr.GroupName
      , o.OutcomeReason
      , OutcomeReasonGroupName = rgmor.GroupName
      , os.OutcomeStatus
      , Underwriter = mr.UnderwriterName
      , mr.UnderwriterEmail
      , ProductKey = COALESCE(ph.ProductKey, ph2.ProductKey, ph3.ProductKey)
      , ProductId = COALESCE(prod.ProductId, prod2.ProductId, prod3.ProductId)
      , ClassId = COALESCE(prod.ClassId, prod2.ClassId, prod3.ClassId)
      , lt.LayerType
      , LeadCarrier = NULL
      , LeadSignedLineRate = NULL
      , QuotedToLead = sec.IsFacilityLead
      , Premium_Currency = p_er.FromCurrencyAlphaCode
      , Premium = CAST(COALESCE(
                           mqr.Premium
                         , CASE WHEN mqr.PremiumRate > 1
                                     AND mqr.PremiumRate <= 100
                                     AND mr.Limit > 0
                                    THEN (mqr.PremiumRate / 100) * mr.Limit END
                         , CASE WHEN mqr.PremiumRate BETWEEN 0 AND 1
                                     AND mr.Limit > 0
                                    THEN mqr.PremiumRate * mr.Limit END
                       ) AS DECIMAL(19, 4))
      , Premium_USD = CAST(COALESCE(
                               mqr.Premium
                             , CASE WHEN mqr.PremiumRate > 1
                                         AND mqr.PremiumRate <= 100
                                         AND mr.Limit > 0
                                        THEN (mqr.PremiumRate / 100) * mr.Limit END
                             , CASE WHEN mqr.PremiumRate BETWEEN 0 AND 1
                                         AND mr.Limit > 0
                                        THEN mqr.PremiumRate * mr.Limit END
                           ) AS DECIMAL(19, 4)) * p_er.ExchangeRate
      , OfferedLine = (sec.Split / 100)
                      * CAST(COALESCE(mqr.OfferedLine
                                    , CASE WHEN mqr.OfferedLineRate > 0
                                                AND mr.Limit > 0
                                               THEN (mqr.OfferedLineRate / 100) * mr.Limit END
                             ) AS DECIMAL(19, 4))
      , OfferedLine_USD = (sec.Split / 100)
                          * CAST(COALESCE(mqr.OfferedLine
                                        , CASE WHEN mqr.OfferedLineRate > 0
                                                    AND mr.Limit > 0
                                                   THEN (mqr.OfferedLineRate / 100) * mr.Limit END
                                 ) AS DECIMAL(19, 4)) * l_er.ExchangeRate
      , OfferedLineRate = (sec.Split / 100)
                          * CAST(COALESCE(mqr.OfferedLineRate
                                        , CASE WHEN mqr.OfferedLine > 0
                                                    AND mr.Limit > 0
                                                   THEN (mqr.OfferedLine / mr.Limit) * 100
                                               WHEN mqr.OfferedLine > 0
                                                    AND mr.Limit IS NULL
                                                   THEN 100 END
                                        , 0
                                 ) AS DECIMAL(21, 6))
      , mqr.PremiumRate
      , mqr.CommissionRate
      , Limit_Currency = l_er.FromCurrencyAlphaCode
      , Limit = mr.Limit
      , Limit_USD = mr.Limit * l_er.ExchangeRate
      , AttachmentPoint_Currency = ap_er.FromCurrencyAlphaCode
      , AttachmentPoint = mr.AttachmentPoint
      , AttachmentPoint_USD = mr.AttachmentPoint * ap_er.ExchangeRate
      , Comments = SUBSTRING(CASE WHEN LEN(LTRIM(RTRIM(mr.Comments))) = 0
                                      THEN NULL
                                  ELSE LTRIM(RTRIM(mr.Comments)) END
                           , 0
                           , 3000
                   )
      , Subjectivity = SUBSTRING(CASE WHEN LEN(LTRIM(RTRIM(mqr.Subjectivity))) = 0
                                          THEN NULL
                                      ELSE LTRIM(RTRIM(mqr.Subjectivity)) END
                               , 0
                               , 3000
                       )
      , ResponseDateKey = CASE WHEN mr.ResponseDate >= '21000101'
                                   THEN 21000101
                               ELSE CONVERT(INT, CONVERT(CHAR(8), mr.ResponseDate, 112)) END
      , ResponseKey = (
                          SELECT CAST(MIN(t.TableId) AS NVARCHAR(5))
                          FROM
                              dbo.PlacementSystemTable t
                          WHERE
                              t.SchemaName = 'app'
                              AND t.TableName = 'MarketResponse'
                      ) + '|' + CAST(TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT) AS NVARCHAR(20))
      , WrittenPremium = (sec.Split / 100)
                         * CAST(CASE WHEN mqr.OfferedLineRate > 0
                                          AND mqr.Premium > 0
                                         THEN (mqr.OfferedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
      , WrittenPremium_USD = (sec.Split / 100)
                             * CAST(CASE WHEN mqr.OfferedLineRate > 0
                                              AND mqr.Premium > 0
                                             THEN (mqr.OfferedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
                             * p_er.ExchangeRate
      , MultiRisk = CASE WHEN mrb.SpecificationCount > 1
                              OR mrb.RiskProfileCount > 1
                             THEN 1
                         ELSE 0 END
      , MarketSecurityCarrierKey = CASE WHEN mr.FacilitySectionId IS NOT NULL -- This field is required to create the key
                                            THEN 'FacilityMember-' + CONVERT(VARCHAR(20), f.PSFacilityPolicyId) + '-'
                                                 + CONVERT(VARCHAR(20), fs.FacilitySectionId) + '-'
                                                 + CONVERT(VARCHAR(20), sec.CarrierId) -- The security carrier is used here in case there is no linked facility section carrier
                                        WHEN fsc.FacilitySectionId IS NOT NULL -- This field is required to create the key
                                            THEN 'FacilityMember-' + CONVERT(VARCHAR(20), f.PSFacilityPolicyId) + '-'
                                                 + CONVERT(VARCHAR(20), fsc.FacilitySectionId) + '-'
                                                 + CONVERT(VARCHAR(20), fsc.CarrierId)
                                        ELSE 'Carrier-' + CONVERT(VARCHAR(20), sec.CarrierId) END
      , sec.Split
      , SignedLine = (sec.Split / 100)
                     * CAST(COALESCE(mqr.SignedLine
                                   , CASE WHEN mqr.SignedLineRate > 0
                                               AND mr.Limit > 0
                                              THEN (mqr.SignedLineRate / 100) * mr.Limit END
                            ) AS DECIMAL(19, 4))
      , SignedLine_Currency = l_er.FromCurrencyAlphaCode
      , SignedLine_USD = (sec.Split / 100)
                         * CAST(COALESCE(mqr.SignedLine
                                       , CASE WHEN mqr.SignedLineRate > 0
                                                   AND mr.Limit > 0
                                                  THEN (mqr.SignedLineRate / 100) * mr.Limit END
                                ) AS DECIMAL(19, 4)) * l_er.ExchangeRate
      , SignedLineRate = (sec.Split / 100)
                         * CAST(COALESCE(mqr.SignedLineRate
                                       , CASE WHEN mqr.SignedLine > 0
                                                   AND mr.Limit > 0
                                                  THEN (mqr.SignedLine / mr.Limit) * 100
                                              WHEN mqr.SignedLine > 0
                                                   AND mr.Limit IS NULL
                                                  THEN 100 END
                                       , 0
                                ) AS DECIMAL(21, 6))
      , ExpiringSignedLineRate = CAST(emqr.SignedLineRate AS DECIMAL(21, 6))
      , SignedPremium = (sec.Split / 100)
                        * CAST(CASE WHEN mqr.SignedLineRate > 0
                                         AND mqr.Premium > 0
                                        THEN (mqr.SignedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
      , SignedPremium_USD = (sec.Split / 100)
                            * CAST(CASE WHEN mqr.SignedLineRate > 0
                                             AND mqr.Premium > 0
                                            THEN (mqr.SignedLineRate / 100) * mqr.Premium END AS DECIMAL(30, 8))
                            * p_er.ExchangeRate
      , ExpiringSignedPremium = CAST(CASE WHEN emqr.SignedLineRate > 0
                                               AND emqr.Premium > 0
                                              THEN (emqr.SignedLineRate / 100) * emqr.Premium END AS DECIMAL(30, 8))
      , ExpiringSignedPremium_USD = CAST(CASE WHEN emqr.SignedLineRate > 0
                                                   AND emqr.Premium > 0
                                                  THEN (emqr.SignedLineRate / 100) * emqr.Premium END AS DECIMAL(30, 8))
                                    * ep_er.ExchangeRate
      , MarketSecurityCarrierId = sec.CarrierId
      , sec.MarketResponseSecurityId
      , LastUpdatedUTCDate = ISNULL(n.ETLUpdatedDate, n.ETLCreatedDate)
      , ResponseCreatedDate = mr.ResponseCreatedDate
      , FirstResponseAcceptedDate = mqr.FirstResponseAcceptedDate
      , LastResponseAcceptedDate = mqr.LastResponseAcceptedDate
      , Deductible = mqr.Deductible
      , DeductibleCurrencyCode = d_er.FromCurrencyAlphaCode
      , Deductible_USD = CAST(CASE WHEN mqr.Deductible > 0
                                       THEN mqr.Deductible * d_er.ExchangeRate
                                   ELSE 0 END AS DECIMAL(38, 6))
      , FollowType = CASE WHEN mqr.FollowTypeId IS NULL
                               AND mqr.QuotedToLead = 1
                              THEN 'LEAD'
                          ELSE ft.FollowType END
      , brcp.CurrentPolicyList
      , CurrentPolicyCount = ISNULL(brcp.CurrentPolicyCount, 0)
      , brep.ExpiringPolicyList
      , ExpiringPolicyCount = ISNULL(brep.ExpiringPolicyCount, 0)
      , mrb.ContractId
      , mr.IsInvalid
    FROM
        PS.MarketResponse mr
        LEFT JOIN PS.MarketQuoteResponse mqr
            ON mqr.MarketResponseId = mr.MarketResponseId
               AND mqr.IsDeleted = 0
               AND mqr.IsOverride = 0 -- Adding to align with existing code

        INNER JOIN PS.NegotiationMarket nm
            ON nm.NegotiationMarketId = mr.NegotiationMarketId
               AND nm.IsDeleted = 0
               AND nm.NegotiationMarketKey LIKE 'SUBCONMKT|%'

        INNER JOIN PS.Negotiation n
            ON n.NegotiationId = nm.NegotiationId
               AND n.IsDeleted = 0
               AND n.NegotiationKey LIKE 'SUBC|%'

        LEFT JOIN ref.FacilitySection fs
            ON fs.FacilitySectionId = mr.FacilitySectionId

        LEFT JOIN ref.Facility f
            ON f.FacilityId = fs.FacilityId

        LEFT JOIN ref.FollowType ft
            ON mqr.FollowTypeId = ft.FollowTypeId

        INNER JOIN dbo.MarketResponseSecurity sec
            ON TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT) = sec.MarketResponseId

        LEFT JOIN (
            SELECT
                FacilitySectionId
              , CarrierId
              , RowNo = ROW_NUMBER() OVER (PARTITION BY FacilitySectionId, CarrierId ORDER BY IsDeprecated ASC, SourceUpdatedDate DESC)
            FROM
                ref.FacilitySectionCarrier
        ) fsc
            ON fsc.FacilitySectionId = fs.FacilitySectionId
               AND fsc.CarrierId = sec.CarrierId
               AND fsc.RowNo = 1 -- Added Row Number to pick the most recent non deprecated record or the latest deprecated record

        LEFT JOIN (SELECT SubmissionContainerId, SentDate = MIN(Sent) FROM BP.Submission GROUP BY SubmissionContainerId) su
            ON TRY_CAST(SUBSTRING(n.NegotiationKey, 6, 11) AS INT) = su.SubmissionContainerId

        LEFT JOIN (
            SELECT
                MarketResponseId = TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT)
              , mr.MarketResponseKey
              , SpecificationId = MAX(ISNULL(mrb.SpecificationId, 0))
              , RiskProfileId = MAX(ISNULL(mrb.RiskProfileId, 0))
              , ContractId = MAX(ISNULL(mrb.ContractId, 0))
              , SpecificationCount = SUM(CASE WHEN mrb.SpecificationId IS NOT NULL
                                                  THEN 1
                                              ELSE 0 END
                                     )
              , RiskProfileCount = SUM(CASE WHEN mrb.RiskProfileId IS NOT NULL
                                                THEN 1
                                            WHEN cr.ContractId IS NOT NULL
                                                THEN 1
                                            ELSE 0 END
                                   )
            FROM
                PS.MarketResponseBasis mrb
                INNER JOIN PS.MarketResponse mr
                    ON mr.MarketResponseId = mrb.MarketResponseId
                       AND mr.IsDeleted = 0
                       AND mr.MarketResponseKey LIKE 'MKTRES|%'

                LEFT JOIN PS.ContractRiskProfile cr
                    ON mrb.ContractId = cr.ContractId
                       AND cr.IsDeleted = 0
            WHERE
                mrb.IsDeleted = 0
            GROUP BY
                mr.MarketResponseKey
        ) mrb
            ON mr.MarketResponseKey = mrb.MarketResponseKey --get max Specification as we want a product to associate 

        LEFT JOIN dbo.Specification s
            ON mrb.SpecificationId = s.SpecificationId
               AND s.IsDeleted = 0

        --Multiple risks can appear on one contract.  Therefore using one.  
        -- If we wanted to support multiple products, we'd also need to change the premiums to avoid double counting
        -- This only affects Contract Builder

        LEFT JOIN (
            SELECT
                cr.ContractId
              , cr.RiskProfileId
              , RowNo = ROW_NUMBER() OVER (PARTITION BY cr.ContractId ORDER BY cr.ETLUpdatedDate DESC, cr.ContractRiskId DESC)
            FROM
                PS.ContractRiskProfile cr
            WHERE
                cr.IsDeleted = 0
        ) cr
            ON mrb.ContractId = cr.ContractId
               AND cr.RowNo = 1

        LEFT JOIN PS.RiskProfile prod
            ON prod.RiskProfileId = mrb.RiskProfileId
               AND prod.IsDeleted = 0

        LEFT JOIN PS.RiskProfile prod2
            ON prod2.RiskProfileKey = CONCAT('SPEC|', mrb.SpecificationId)
               AND prod2.IsDeleted = 0

        LEFT JOIN PS.RiskProfile prod3
            ON prod3.RiskProfileId = cr.RiskProfileId
               AND prod3.IsDeleted = 0

        LEFT JOIN rpt.ProductHierarchy ph
            ON ph.ProductId = prod.ProductId

        LEFT JOIN rpt.ProductHierarchy ph2
            ON ph2.ProductId = prod2.ProductId

        LEFT JOIN rpt.ProductHierarchy ph3
            ON ph3.ProductId = prod3.ProductId

        LEFT JOIN ref.OutcomeReason o
            ON o.OutcomeReasonId = mqr.OutcomeReasonId

        LEFT JOIN dbo.ReasonGroup rgmor
            ON o.ReasonGroupId = rgmor.ReasonGroupId

        LEFT JOIN ref.ResponseType rt
            ON rt.ResponseTypeId = mr.ResponseTypeId

        LEFT JOIN ref.OutcomeStatus os
            ON os.OutcomeStatusId = mqr.OutcomeStatusId

        LEFT JOIN ref.DeclinationReason dr
            ON dr.DeclinationReasonId = mr.DeclinationReasonId

        LEFT JOIN dbo.ReasonGroup rgmdr
            ON dr.ReasonGroupId = rgmdr.ReasonGroupId

        LEFT JOIN ref.LayerType lt
            ON lt.LayerTypeId = mr.LayerTypeId

        LEFT JOIN #MergedExpiringHistoricQuotes emqr
            ON emqr.PlacementId = n.PlacementId
               AND emqr.LineOfBusiness = prod.LineOfBusiness
               AND emqr.CarrierId = nm.CarrierId
               AND emqr.LayerTypeId = ISNULL(mr.LayerTypeId, -1)

        LEFT JOIN ref.PendingActionReason par
            ON par.PendingActionReasonId = mr.PendingActionReasonId

        LEFT JOIN PS.vwToAverageExchangeRate p_er
            ON p_er.FromCurrencyId = mqr.PremiumCurrencyId
               AND p_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < p_er.EffectiveToDate
               AND p_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate ep_er
            ON ep_er.FromCurrencyId = emqr.PremiumCurrencyId
               AND ep_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < ep_er.EffectiveToDate
               AND ep_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate l_er
            ON l_er.FromCurrencyId = mr.LimitCurrencyId
               AND l_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < l_er.EffectiveToDate
               AND l_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate ap_er
            ON ap_er.FromCurrencyId = mr.AttachmentPointCurrencyId
               AND ap_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < ap_er.EffectiveToDate
               AND ap_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN PS.vwToAverageExchangeRate d_er
            ON d_er.FromCurrencyId = mqr.DeductibleCurrencyId
               AND d_er.EffectiveFromDate <= mr.ResponseDate
               AND mr.ResponseDate < d_er.EffectiveToDate
               AND d_er.ToCurrencyAlphaCode = 'USD'

        LEFT JOIN #BoundResposeCurrentPolicy brcp
            ON brcp.MarketResponseId = mr.MarketResponseId

        LEFT JOIN #BoundResposeExpiringPolicy brep
            ON brep.MarketResponseId = mr.MarketResponseId
    WHERE
        mr.MarketResponseKey LIKE 'MKTRES|%'
        AND mr.DataSourceInstanceId = 50366
        AND mr.IsDeleted = 0;

    ----------------------------------------------------------------------------------
    TRUNCATE TABLE rpt.MarketInteractionResponse;

    INSERT INTO
        rpt.MarketInteractionResponse
        (
            MarketCategory
          , CarrierResponseId
          , MarketResponseKey
          , ParentResponseKey
          , MarketSelectionId
          , NegotiationMarketKey
          , MarketSelectionKey
          , InteractionName
          , SubmittedDate
          , ResponseDate
          , ResponseTimeDays
          , PendingActionReason
          , ResponseType
          , DeclinationReason
          , DeclinationReasonGroupName
          , OutcomeReason
          , OutcomeReasonGroupName
          , OutcomeStatus
          , UnderwriterName
          , UnderwriterEmail
          , ProductKey
          , ProductId
          , ClassId
          , LayerType
          , LeadCarrier
          , LeadSignedLineRate
          , QuotedToLead
          , Premium_Currency
          , Premium
          , Premium_USD
          , OfferedLine
          , OfferedLine_USD
          , OfferedLineRate
          , PremiumRate
          , CommissionRate
          , Limit_Currency
          , Limit
          , Limit_USD
          , AttachmentPoint_Currency
          , AttachmentPoint
          , AttachmentPoint_USD
          , Comments
          , Subjectivity
          , ResponseDateKey
          , ResponseKey
          , WrittenPremium
          , WrittenPremium_USD
          , MultiRisk
          , MarketSecurityCarrierKey
          , Split
          , SignedLine
          , SignedLine_Currency
          , SignedLine_USD
          , SignedLineRate
          , ExpiringSignedLineRate
          , SignedPremium
          , SignedPremium_USD
          , ExpiringSignedPremium
          , ExpiringSignedPremium_USD
          , MarketSecurityCarrierId
          , MarketResponseSecurityId
          , ResponseCreatedDate
          , FirstResponseAcceptedDate
          , LastResponseAcceptedDate
          , Deductible
          , DeductibleCurrency
          , DeductibleUSD
          , FollowType
          , CurrentPolicyList
          , CurrentPolicyCount
          , ExpiringPolicyList
          , ExpiringPolicyCount
          , ContractId
          , IsInvalid
          , ETLCreatedDate
          , ETLUpdatedDate
          , SourceUpdatedDate
        )
    SELECT
        s.MarketCategory
      , s.CarrierResponseId
      , s.MarketResponseKey
      , s.ParentResponseKey
      , s.MarketSelectionId
      , s.NegotiationMarketKey
      , s.MarketSelectionKey
      , s.InteractionName
      , s.SubmittedDate
      , s.ResponseDate
      , s.ResponseTimeDays
      , s.PendingActionReason
      , s.ResponseType
      , s.DeclinationReason
      , s.DeclinationReasonGroupName
      , s.OutcomeReason
      , s.OutcomeReasonGroupName
      , s.OutcomeStatus
      , s.UnderwriterName
      , s.UnderwriterEmail
      , s.ProductKey
      , s.ProductId
      , s.ClassId
      , s.LayerType
      , s.LeadCarrier
      , s.LeadSignedLineRate
      , s.QuotedToLead
      , s.Premium_Currency
      , s.Premium
      , s.Premium_USD
      , s.OfferedLine
      , s.OfferedLine_USD
      , s.OfferedLineRate
      , s.PremiumRate
      , s.CommissionRate
      , s.Limit_Currency
      , s.Limit
      , s.Limit_USD
      , s.AttachmentPoint_Currency
      , s.AttachmentPoint
      , s.AttachmentPoint_USD
      , s.Comments
      , s.Subjectivity
      , s.ResponseDateKey
      , s.ResponseKey
      , s.WrittenPremium
      , s.WrittenPremium_USD
      , s.MultiRisk
      , s.MarketSecurityCarrierKey
      , s.Split
      , s.SignedLine
      , s.SignedLine_Currency
      , s.SignedLine_USD
      , s.SignedLineRate
      , s.ExpiringSignedLineRate
      , s.SignedPremium
      , s.SignedPremium_USD
      , s.ExpiringSignedPremium
      , s.ExpiringSignedPremium_USD
      , s.MarketSecurityCarrierId
      , s.MarketResponseSecurityId
      , s.ResponseCreatedDate
      , s.FirstResponseAcceptedDate
      , s.LastResponseAcceptedDate
      , s.Deductible
      , s.DeductibleCurrency
      , s.DeductibleUSD
      , s.FollowType
      , s.CurrentPolicyList
      , s.CurrentPolicyCount
      , s.ExpiringPolicyList
      , s.ExpiringPolicyCount
      , s.ContractId
      , s.IsInvalid
      , GETUTCDATE()
      , GETUTCDATE()
      , s.LastUpdatedUTCDate
    FROM
        #SourceForMerge s;

    SET @InsertedCount = @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

DROP TABLE IF EXISTS #SourceForMerge;

SET @Action = N'Insert into ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;