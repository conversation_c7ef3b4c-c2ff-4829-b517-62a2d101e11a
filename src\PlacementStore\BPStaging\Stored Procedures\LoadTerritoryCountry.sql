/*
Lineage
dbo.TerritoryCountry.TerritoryId=BPStaging.TerritoryCountry.TerritoryId
dbo.TerritoryCountry.CountryCode=BPStaging.TerritoryCountry.CountryCode
*/
CREATE PROCEDURE BPStaging.LoadTerritoryCountry
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.TerritoryCountry';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.TerritoryCountry T
    USING (
        SELECT DISTINCT
               TerritoryId = TerritoryId
             , CountryCode = CountryCode
             , IsDeprecated = 0
        FROM
            BPStaging.TerritoryCountry
    ) S
    ON T.TerritoryId = S.TerritoryId
       AND T.CountryCode = S.CountryCode
    WHEN NOT MATCHED
        THEN INSERT (
                 TerritoryId
               , CountryCode
               , CreatedTime
               , LastUpdateTime
               , IsDeprecated
             )
             VALUES
                 (
                     S.TerritoryId
                   , S.CountryCode
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT T.IsDeprecated INTERSECT SELECT S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.LastUpdateTime = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.LastUpdateTime = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
