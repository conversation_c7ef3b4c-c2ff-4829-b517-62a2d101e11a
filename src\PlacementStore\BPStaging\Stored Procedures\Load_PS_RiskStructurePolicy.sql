/*
Lineage
PS.RiskStructurePolicy.DataSourceInstanceId=PS.RiskStructure.DataSourceInstanceId
PS.RiskStructurePolicy.RiskStructurePolicyKey=PS.RiskStructure.RiskStructureId
PS.RiskStructurePolicy.RiskStructurePolicyKey=BPStaging.PS_Staging_RiskStructurePolicy.PolicyId
PS.RiskStructurePolicy.RiskStructureId=PS.RiskStructure.RiskStructureId
PS.RiskStructurePolicy.PolicyId=BPStaging.PS_Staging_RiskStructurePolicy.PolicyId
PS.RiskStructurePolicy.SourceUpdatedDate=BPStaging.PS_Staging_RiskStructurePolicy.ValidFrom
PS.RiskStructurePolicy.SourceUpdatedDate=PS.RiskStructure.SourceUpdatedDate
PS.RiskStructurePolicy.IsDeleted=BPStaging.PS_Staging_RiskStructurePolicy.IsDeleted
PS.RiskStructurePolicy.IsDeleted=PS.RiskStructure.IsDeleted
*/
CREATE PROCEDURE BPStaging.Load_PS_RiskStructurePolicy
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.RiskStructurePolicy';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.RiskStructurePolicy T
    USING (
        SELECT DISTINCT /* Need to squash the ContractRiskProfile records so we get a single record for a Contract */
               RS.DataSourceInstanceId
             , RiskStructurePolicyKey = CONCAT('RS|', RS.RiskStructureId, '|POL|', CP.PolicyId)
             , RS.RiskStructureId
             , CP.PolicyId
             , SourceUpdatedDate = GREATEST(CP.ValidFrom, RS.SourceUpdatedDate)
             , IsDeleted = CP.IsDeleted | RS.IsDeleted
        FROM
            BPStaging.PS_Staging_RiskStructurePolicy CP
            INNER JOIN PS.RiskStructure RS
                ON RS.RiskStructureKey = CONCAT('CON|', CP.ContractId, '|RP|', CP.RiskProfileId)
                   AND RS.DataSourceInstanceId = CP.DataSourceInstanceId
        WHERE
            CP.PolicyId IS NOT NULL
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.RiskStructureId = S.RiskStructureId
       AND T.PolicyId = S.PolicyId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , RiskStructurePolicyKey
               , RiskStructureId
               , PolicyId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.RiskStructurePolicyKey
                   , S.RiskStructureId
                   , S.PolicyId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.DataSourceInstanceId
                               , S.RiskStructurePolicyKey
                               , S.RiskStructureId
                               , S.PolicyId
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.DataSourceInstanceId
                               , T.RiskStructurePolicyKey
                               , T.RiskStructureId
                               , T.PolicyId
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RiskStructurePolicyKey = S.RiskStructurePolicyKey
               , T.RiskStructureId = S.RiskStructureId
               , T.PolicyId = S.PolicyId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

RETURN 0;