CREATE PROCEDURE Task.GetTaskMetadata
    @policyId            BIGINT
  , @policyRenewalTaskId UNIQUEIDENTIFIER
AS
DECLARE @classOfBusiness NVARCHAR(200) = N'';
DECLARE @lineOfBusiness NVARCHAR(200) = N'';
DECLARE @countOfLob INT = 0;

SELECT
    @classOfBusiness = @classOfBusiness + CASE WHEN @classOfBusiness = ''
                                                   THEN ''
                                               ELSE ',' END + sp.SourceProductName
  , @lineOfBusiness = @lineOfBusiness + CASE WHEN @lineOfBusiness = ''
                                                 THEN ''
                                             ELSE ',' END + sp.ProductLine
  , @countOfLob = CASE WHEN sp.ProductLine IS NOT NULL
                           THEN @countOfLob + 1
                       ELSE @countOfLob END
FROM (
    SELECT DISTINCT
           pr.SourceProductName
         , pr.ProductLine
    FROM
        dbo.Policy p WITH (NOLOCK)
        INNER JOIN dbo.PolicySection ps WITH (NOLOCK)
            ON p.PolicyId = ps.PolicyId
               AND p.IsDeleted = 0

        INNER JOIN dbo.PolicySectionProduct psp WITH (NOLOCK)
            ON psp.PolicySectionId = ps.PolicySectionId
               AND psp.IsDeleted = 0

        INNER JOIN dbo.Product pr WITH (NOLOCK)
            ON pr.ProductId = psp.ProductId
               AND pr.IsDeleted = 0
    WHERE
        p.PolicyId = @policyId
) sp
ORDER BY
    sp.SourceProductName;

DECLARE @carrier NVARCHAR(200) = N'';

SELECT @carrier = (@carrier + CASE WHEN @carrier = ''
                                       THEN ''
                                   ELSE ',' END + p.PartyName
                  )
FROM
    APIv1.fn_PolicyCarriers(@policyId) pc
    INNER JOIN dbo.Party p WITH (NOLOCK)
        ON p.PartyId = pc.PartyId;

DECLARE @placementLink NVARCHAR(200);

SELECT @placementLink = Value
FROM
    Task.Config WITH (NOLOCK)
WHERE
    Name = 'PlacementLink';

SELECT TOP 1
       PolicyRenewalTaskId = @policyRenewalTaskId
     , po.PolicyId
     , DataSourceInstanceId = po.DataSourceInstanceId
     , po.PolicyKey
     , po.PolicyReference
     , po.InceptionDate
     , po.ExpiryDate
     , po.ParentKey
     , ps.PolicyStatus
     , pt.PolicyType
     , le.LegalEntity
     , EffectiveDate = DATEADD(DAY, 1, po.ExpiryDate)
     , ClassOfBusiness = @classOfBusiness
     , CurrentDateTime = GETUTCDATE()
     , PlacementLink = REPLACE(@placementLink, '{PlacementId}', pp.PlacementId)
     , RenewedFromPlacementLink = REPLACE(@placementLink, '{PlacementId}', pl.RenewedFromPlacementId)
     , PlacementSystemId = CASE WHEN pl.DataSourceInstanceId = 50366
                                    THEN CAST(pl.PlacementSystemId AS NVARCHAR(100))
                                ELSE NULL END
     , LineOfBusiness = @lineOfBusiness
     , CountOfLOBs = CONVERT(NVARCHAR(10), @countOfLob)
     , Carrier = @carrier
FROM
    dbo.Policy po WITH (NOLOCK)
    LEFT JOIN dbo.PlacementPolicy pp WITH (NOLOCK)
        ON pp.PolicyId = po.PolicyId
           AND pp.IsDeleted = 0
           AND pp.PlacementPolicyRelationshipTypeId = 1
           AND pp.DataSourceInstanceId = 50366

    LEFT JOIN dbo.Placement pl WITH (NOLOCK)
        ON pl.PlacementId = pp.PlacementId
           AND pl.IsDeleted = 0

    LEFT JOIN PAS.PolicyType pt WITH (NOLOCK)
        ON po.PolicyTypeKey = pt.PolicyTypeKey
            AND po.DataSourceInstanceId = pt.DataSourceInstanceId

    LEFT JOIN PAS.PolicyStatus ps WITH (NOLOCK)
        ON po.PolicyStatusKey = ps.PolicyStatusKey
            AND po.DataSourceInstanceId = ps.DataSourceInstanceId

    LEFT JOIN ref.LegalEntity le WITH (NOLOCK)
        ON po.FinancialLegalEntityId = le.LegalEntityId
WHERE
    po.IsDeleted = 0
    AND po.InceptionDate IS NOT NULL
    AND po.PolicyId = @policyId;

SELECT DISTINCT
       poh.Level
     , poh.Role
     , Organisation = CASE WHEN o.DataSourceInstanceId = 50001
                               THEN o.RelatedOrganisation
                           ELSE poh.Organisation END
FROM
    APIv1.PolicyOrganisationHierarchy poh WITH (NOLOCK)
    LEFT JOIN PS.OrganisationRelationship o WITH (NOLOCK)
        ON o.RelatedOrganisationKey = poh.Organisation
           AND o.OrganisationRelationshipType = poh.Role
           AND o.DataSourceInstanceId = poh.DataSourceInstanceId
WHERE
    poh.PolicyId = @policyId
ORDER BY
    poh.Level;

SELECT
    a.GlobalServicingRoleId
  , a.ServicingRole
  , WorkerId = a.WorkerId
  , WorkerName
  , WorkerAccount
  , a.UserPrincipalName
  , a.EmailAddress
  , a.IsActive
FROM (
    SELECT
        sr.GlobalServicingRoleId
      , sr.ServicingRole
      , WorkerId = psw.WorkerSK
      , w.WorkerName
      , w.WorkerAccount
      , ad.UserPrincipalName
      , ad.EmailAddress
      , IsActive = ISNULL(pw.IsActive, 0)
      , WorkerOrder = ROW_NUMBER() OVER (PARTITION BY sr.GlobalServicingRoleId ORDER BY ISNULL(pw.IsActive, 0) DESC, w.WorkerName)
    FROM
        dbo.Policy p WITH (NOLOCK)
        INNER JOIN dbo.PolicyWorker pw WITH (NOLOCK)
            ON pw.PolicyId = p.PolicyId
               AND pw.IsDeleted = 0

        INNER JOIN PAS.ServicingRole sr WITH (NOLOCK)
            ON sr.ServicingRoleKey = pw.ServicingRoleKey
               AND sr.DataSourceInstanceId = pw.DataSourceInstanceId
               AND sr.IsDeleted = 0

        INNER JOIN PS.Worker psw WITH (NOLOCK)
            ON psw.WorkerSK = pw.WorkerId

        INNER JOIN PAS.Worker w WITH (NOLOCK)
            ON w.WorkerKey = psw.WorkerKey
               AND w.DataSourceInstanceId = psw.DataSourceInstanceId
               AND w.IsDeleted = 0

        INNER JOIN Reference.Worker rw WITH (NOLOCK)
            ON w.ReferenceWorkerId = rw.WorkerId
               AND rw.IsDeleted = 0

        INNER JOIN Reference.WorkerAccount rwa WITH (NOLOCK)
            ON rw.WorkerId = rwa.WorkerId
               AND rwa.IsDeleted = 0

        INNER JOIN Reference.ActiveDirectory ad WITH (NOLOCK)
            ON rwa.ActiveDirectoryId = ad.ActiveDirectoryId
    WHERE
        p.PolicyId = @policyId
) a
WHERE
    a.WorkerOrder = 1;

SELECT
    GlobalPartyRoleId = a.GlobalPartyRoleId
  , a.PartyKey
  , a.PartyName
  , a.FCAClassification
FROM (
    SELECT
        prl.GlobalPartyRoleId
      , py.PartyKey
      , py.PartyName
      , PartyOrder = ROW_NUMBER() OVER (PARTITION BY prl.GlobalPartyRoleId ORDER BY ppr.IsPrimaryParty DESC, py.PartyName)
      , FCAClassification = pa.EclipseFSAClassific
    FROM
        dbo.Policy p WITH (NOLOCK)
        INNER JOIN dbo.PolicyPartyRelationship ppr WITH (NOLOCK)
            ON ppr.PolicyId = p.PolicyId
               AND ppr.IsDeleted = 0

        INNER JOIN ref.PartyRole prl WITH (NOLOCK)
            ON prl.PartyRoleId = ppr.PartyRoleId
               AND prl.IsDeprecated = 0

        INNER JOIN dbo.Party py WITH (NOLOCK)
            ON py.PartyId = ppr.PartyId
               AND py.IsDeleted = 0

        LEFT JOIN (
            SELECT
                PACTPartyId = p.PASPartyId
              , ppa.EclipseFSAClassific
            FROM
                PAS.PartyAttribute ppa WITH (NOLOCK)
                INNER JOIN PAS.Party p WITH (NOLOCK)
                    ON p.PartyKey = ppa.PartyKey
                       AND p.DataSourceInstanceId = ppa.DataSourceInstanceId
            WHERE
                ppa.IsDeleted = 0
        ) pa
            ON pa.PACTPartyId = py.PACTPartyId
    WHERE
        p.PolicyId = @policyId
) a
WHERE
    a.PartyOrder = 1;

SELECT
    a.SourceProductName
  , a.ProductClass
  , a.ProductLine
  , a.PolicySectionKey
  , a.WrittenLine
  , a.SignedLine
  , a.BrokerOrder1
  , a.WholePartOrder
  , a.LineSlipRef
  , a.PolicySection
FROM (
    SELECT DISTINCT
           pr.SourceProductName
         , pr.ProductClass
         , pr.ProductLine
         , ps.PolicySectionKey
         , pm.WrittenLine
         , pm.SignedLine
         , pm.BrokerOrder1
         , pm.WholePartOrder
         , pm.LineSlipRef
         , PolicySection = pasps.PolicySectionCode
         , OrderNumber = ROW_NUMBER() OVER (ORDER BY ps.PolicySectionId)
    FROM
        dbo.Policy p WITH (NOLOCK)
        LEFT JOIN dbo.PolicySection ps WITH (NOLOCK)
            ON p.PolicyId = ps.PolicyId
               AND p.IsDeleted = 0

        LEFT JOIN PAS.PolicySection pasps WITH (NOLOCK)
            ON pasps.PASPolicySectionId = ps.PACTPolicySectionId
                AND pasps.IsDeleted = 0

        LEFT JOIN dbo.PolicySectionProduct psp WITH (NOLOCK)
            ON psp.PolicySectionId = ps.PolicySectionId
               AND psp.IsDeleted = 0

        LEFT JOIN dbo.Product pr WITH (NOLOCK)
            ON pr.ProductId = psp.ProductId
               AND pr.IsDeleted = 0

        LEFT JOIN dbo.PolicyMarket pm WITH (NOLOCK)
            ON pm.PolicySectionId = ps.PolicySectionId
               AND pm.IsDeleted = 0
    WHERE
        p.PolicyId = @policyId
) a
WHERE
    a.OrderNumber = 1;
