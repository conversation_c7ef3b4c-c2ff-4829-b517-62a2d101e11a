/*
Lineage
dbo.SubmissionCoverageGroup.SubmissionCoverageGroupId=BPStaging.SubmissionCoverageGroup.Id
dbo.SubmissionCoverageGroup.SubmissionContainerId=BPStaging.SubmissionCoverageGroup.SubmissionContainerId
dbo.SubmissionCoverageGroup.CoverageGroupId=BPStaging.SubmissionCoverageGroup.CoverageGroupId
dbo.SubmissionCoverageGroup.DisplayIndex=BPStaging.SubmissionCoverageGroup.DisplayIndex
*/
CREATE PROCEDURE BPStaging.LoadSubmissionCoverageGroup
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.SubmissionCoverageGroup';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.SubmissionCoverageGroup
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE INTO dbo.SubmissionCoverageGroup T
    USING (
        SELECT
            SubmissionCoverageGroupId = stgSCG.Id
          , DataSourceInstanceId = 50366
          , stgSCG.SubmissionContainerId
          , stgSCG.CoverageGroupId
          , stgSCG.DisplayIndex
          , SourceUpdatedDate = CAST(NULL AS DATETIME2(7))/* No date from BP */
          , IsDeleted = 0
        FROM
            BPStaging.SubmissionCoverageGroup stgSCG
            INNER JOIN PS.Negotiation SC
                ON CONCAT('SUBC|', stgSCG.SubmissionContainerId) = SC.NegotiationKey
                   AND SC.DataSourceInstanceId = 50366

            INNER JOIN dbo.CoverageGroup cg
                ON stgSCG.CoverageGroupId = cg.CoverageGroupId
    ) S
    ON S.SubmissionCoverageGroupId = T.SubmissionCoverageGroupId
    WHEN NOT MATCHED
        THEN INSERT (
                 SubmissionCoverageGroupId
               , DataSourceInstanceId
               , SubmissionContainerId
               , CoverageGroupId
               , DisplayIndex
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.SubmissionCoverageGroupId
                   , S.DataSourceInstanceId
                   , S.SubmissionContainerId
                   , S.CoverageGroupId
                   , S.DisplayIndex
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.DataSourceInstanceId
      , S.SubmissionContainerId
      , S.CoverageGroupId
      , S.DisplayIndex
      , S.SourceUpdatedDate
      , S.IsDeleted
    INTERSECT
    SELECT
        T.DataSourceInstanceId
      , T.SubmissionContainerId
      , T.CoverageGroupId
      , T.DisplayIndex
      , T.SourceUpdatedDate
      , T.IsDeleted
)
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.SubmissionContainerId = S.SubmissionContainerId
               , T.CoverageGroupId = S.CoverageGroupId
               , T.DisplayIndex = S.DisplayIndex
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
