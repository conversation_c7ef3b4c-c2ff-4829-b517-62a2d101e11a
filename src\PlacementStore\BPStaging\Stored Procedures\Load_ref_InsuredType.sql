/*
Lineage
ref.InsuredType.InsuredTypeKey=BPStaging.InsuredType.Id
ref.InsuredType.TranslationKey=BPStaging.InsuredType.LabelTranslationKey
ref.InsuredType.InsuredType=BPStaging.InsuredType.DescriptionTranslationKey
ref.InsuredType.IsDeprecated=BPStaging.InsuredType.IsDeprecated
ref.InsuredType.SourceUpdatedDate=BPStaging.InsuredType.ValidTo
ref.InsuredType.SourceUpdatedDate=BPStaging.InsuredType.ValidFrom
ref.InsuredType.IsDeleted=BPStaging.InsuredType.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_ref_InsuredType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.InsuredType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.InsuredType t
    USING (
        SELECT
            InsuredTypeKey = s.Id
          , DataSourceInstanceId = 50366
          , TranslationKey = s.LabelTranslationKey
          , InsuredType = s.DescriptionTranslationKey
          , s.IsDeprecated
          , SourceUpdatedDate = IIF(YEAR(s.ValidTo) < 9999, s.ValidTo, s.ValidFrom)
          , IsDeleted = IIF(YEAR(s.ValidTo) < 9999, 1, 0)
        FROM (
        SELECT
            Id
          , LabelTranslationKey
          , DescriptionTranslationKey
          , IsDeprecated
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.InsuredType
    ) s
        WHERE
            s.RowNo = 1
    ) s
    ON s.InsuredTypeKey = t.InsuredTypeKey
       AND s.DataSourceInstanceId = t.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 InsuredTypeKey
               , TranslationKey
               , InsuredType
               , IsDeprecated
               , DataSourceInstanceId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.InsuredTypeKey
                   , s.TranslationKey
                   , s.InsuredType
                   , s.IsDeprecated
                   , s.DataSourceInstanceId
                   , s.SourceUpdatedDate
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.TranslationKey
                               , s.InsuredType
                               , s.IsDeprecated
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.TranslationKey
                               , t.InsuredType
                               , t.IsDeprecated
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.TranslationKey = s.TranslationKey
               , t.InsuredType = s.InsuredType
               , t.IsDeprecated = s.IsDeprecated
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.IsDeleted = s.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;