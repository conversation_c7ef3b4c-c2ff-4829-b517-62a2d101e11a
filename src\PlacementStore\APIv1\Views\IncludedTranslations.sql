/*
Lineage
LocaleCode=RefConfig.IncludedLocales.LocaleCode
LanguageName=Reference.Language.LanguageName
LanguageCode=Reference.Language.LanguageCode
CountryName=Reference.Country.CountryName
FMALanguageId=RefConfig.IncludedLocales.FMALanguageId
IsDeleted=RefConfig.IncludedLocales.IsDeleted
*/
CREATE VIEW APIv1.IncludedTranslations
AS
SELECT
    i.LocaleCode
  , l.LanguageName
  , l.LanguageCode
  , c.CountryName
  , i.FMALanguageId
  , i.IsDeleted
FROM
    RefConfig.IncludedLocales i
    INNER JOIN Reference.Language l
        ON SUBSTRING(i.LocaleCode, 1, CHARINDEX('-', i.LocaleCode) - 1) = l.LanguageCode

    INNER JOIN Reference.Country c
        ON SUBSTRING(i.LocaleCode, CHARINDEX('-', i.LocaleCode) + 1, 2) = c.CountryAlpha2Code;