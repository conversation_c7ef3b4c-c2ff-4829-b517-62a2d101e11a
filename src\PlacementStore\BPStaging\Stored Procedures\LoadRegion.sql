/*
Lineage
ref.BrokingRegion.BrokingRegionId=BPStaging.Region.Id
ref.BrokingRegion.BrokingRegionKey=BPStaging.Region.Id
ref.BrokingRegion.BrokingRegion=BPStaging.Region.LabelTranslationDescription
ref.BrokingRegion.BrokingRegionParentId=BPStaging.Region.Id
ref.BrokingRegion.IsDeprecated=BPStaging.Region.IsDeprecated
ref.BrokingRegion.SourceUpdatedDate=BPStaging.Region.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadRegion
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.BrokingRegion';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.BrokingRegion T
    USING (
        SELECT
            BrokingRegionId = c.Id
          , DataSourceInstanceId = 50366
          , BrokingRegionKey = c.Id
          , BrokingRegion = c.LabelTranslationDescription
          , BrokingRegionParentId = p.Id
          , ServicingPlatformId = 50366
          , c.IsDeprecated
          , SourceUpdatedDate = c.ValidFrom
        FROM
            BPStaging.Region c
            LEFT JOIN BPStaging.Region p
                ON p.RegionNode = c.RegionNode.GetAncestor(1)
    ) S
    ON S.BrokingRegionId = T.BrokingRegionId
    WHEN NOT MATCHED
        THEN INSERT (
                 BrokingRegionId
               , DataSourceInstanceId
               , BrokingRegionKey
               , BrokingRegion
               , BrokingRegionParentId
               , ServicingPlatformId
               , IsDeprecated
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.BrokingRegionId
                   , S.DataSourceInstanceId
                   , S.BrokingRegionKey
                   , S.BrokingRegion
                   , S.BrokingRegionParentId
                   , S.ServicingPlatformId
                   , S.IsDeprecated
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.DataSourceInstanceId
                               , S.BrokingRegionKey
                               , S.BrokingRegion
                               , S.BrokingRegionParentId
                               , S.ServicingPlatformId
                               , S.IsDeprecated
                               , S.SourceUpdatedDate
                             INTERSECT
                             SELECT
                                 T.DataSourceInstanceId
                               , T.BrokingRegionKey
                               , T.BrokingRegion
                               , T.BrokingRegionParentId
                               , T.ServicingPlatformId
                               , T.IsDeprecated
                               , T.SourceUpdatedDate
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.BrokingRegionKey = S.BrokingRegionKey
               , T.BrokingRegion = S.BrokingRegion
               , T.BrokingRegionParentId = S.BrokingRegionParentId
               , T.ServicingPlatformId = S.ServicingPlatformId
               , T.IsDeprecated = S.IsDeprecated
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;