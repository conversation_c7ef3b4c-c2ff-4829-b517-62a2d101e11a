/*
NB: This is the same logic as in the view APIv1.PolicyCarriers, but this object takes a @PolicyId parameter
    to make the query more efficient when called from Task.GetTaskMetadata.

!!!!!WHEN CHANGING THIS LOGIC, ENSURE THAT THE VIEW APIv1.PolicyCarriers IS ALSO UPDATED!!!!!
*/
CREATE FUNCTION APIv1.fn_PolicyCarriers (
    @PolicyId BIGINT
)
RETURNS TABLE
AS
RETURN
SELECT
    PolicyId
  , LastUpdatedUTCDate = MAX(ETLUpdatedDate)
  , pc.PartyId
  , ExpiryDate = MAX(ExpiryDate)
  , FacilityId
  , FacilitySectionId
  , pc.MarketKindId
  , Incumbent = MAX(pc.Incumbent)
  , IsLead = MAX(pc.IsLead)
  , IsLapsed = MAX(pc.IsLapsed)
FROM (
    SELECT DISTINCT
           p.PolicyId
         , ppr.ETLUpdatedDate --This should be based on Policy Party Relationship dates and its added in PPR table
         , PartyId = CASE WHEN f.PSFacilityPolicyId IS NULL
                              THEN ppr.PartyId END
         , p.ExpiryDate
         , FacilityId = f.PSFacilityPolicyId
         , FacilitySectionId = fs.PSFacilityPolicySectionId
         , MarketKindId = CASE WHEN f.PSFacilityPolicyId IS NOT NULL
                                   THEN 2
                               ELSE 1 END
         , Incumbent = CASE WHEN ppr.PACTPolicyPartyRoleId IS NOT NULL
                                THEN 1
                            ELSE 0 END
         , IsLead = CASE WHEN f.PSFacilityPolicyId IS NOT NULL
                             THEN 0
                         ELSE ppr.IsPrimaryParty END --we can't tell if facility is lead overall
         , IsLapsed = CASE WHEN f.PSFacilityPolicyId IS NULL
                               THEN 0 --mark open market as valid
                           WHEN f.PSFacilityPolicyId IS NOT NULL
                                AND DATEADD(DAY, 1, p.ExpiryDate) BETWEEN f.InceptionDate AND f.ExpiryDate
                               THEN 0 --valid renewal facility
                           ELSE 1 END
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyPartyRelationship ppr
            ON ppr.PolicyId = p.PolicyId

        INNER JOIN ref.PartyRole pr
            ON ppr.PartyRoleId = pr.PartyRoleId
               AND pr.IsDeprecated = 0

        LEFT JOIN ref.PartyRole parpr
            ON ppr.ParentPartyRoleId = parpr.PartyRoleId
               AND parpr.IsDeprecated = 0

        LEFT JOIN dbo.PolicyCarrierDetails pcd
            ON ppr.PartyId = pcd.PartyId
               AND ppr.PolicyId = pcd.PolicyId
               AND ppr.PartyRoleId = pcd.PartyRoleId
               AND pcd.IsDeleted = 0

        LEFT JOIN ref.FacilitySection fs
            ON pcd.LinkedFacilityPolicySectionId = fs.PSFacilityPolicySectionId

        LEFT JOIN ref.Facility f
            ON f.FacilityId = fs.FacilityId

        LEFT JOIN PAS.RefPolicyStatus rps
            ON rps.RefPolicyStatusId = f.RefPolicyStatusId
               AND rps.IsDeleted = 0
    WHERE
        pr.GlobalPartyRoleId = '102' --carrier
        AND p.IsDeleted = 0
        AND pr.PartyRoleKey NOT IN (
                'BUREAU', 'BILLCO', 'SHAREBROKER'
            ) --exclude bureau and billing co
        AND ISNULL(parpr.PartyRoleKey, 'N/A') NOT IN (
                'CONSORTIUM', 'POOL'
            ) --exclude children of pool or consortium
        AND ppr.IsDeleted = 0 --AND ppr.Incumbent = 1
                              --AND (ISNULL(ppr.FacilityId,0) = 0 OR (ppr.FacilityId > 0 AND ppr.CreatedUser != 'FMAImport')) --ignore FMA imported facilities
        AND p.RuleId > 0 --in scope
        AND p.PolicyId = @PolicyId
    UNION ALL

    --incumbent facility members
    SELECT DISTINCT
           p.PolicyId
         , LastUpdatedUTCDate = ppr.ETLUpdatedDate --This should be based on Policy Party Relationship dates and its added in PPR table
         , ppr.PartyId
         , p.ExpiryDate
         , FacilityId = f.PSFacilityPolicyId
         , FacilitySectionId = fs.PSFacilityPolicySectionId
         , MarketKindId = 3
         , Incumbent = CASE WHEN ppr.PACTPolicyPartyRoleId IS NOT NULL
                                THEN 1
                            ELSE 0 END
         , IsLead = MAX(CAST(ppr.IsPrimaryParty AS INT)) OVER (PARTITION BY
                                                                   p.PolicyId
                                                                 , f.PSFacilityPolicyId
                                                                 , fs.PSFacilityPolicySectionId
                                                                 , ppr.PartyId
                                                               ORDER BY
                                                                   p.PolicyId
                                                                 , f.PSFacilityPolicyId
                                                                 , fs.PSFacilityPolicySectionId
                                                                 , ppr.PartyId
                                                         )
         , IsLapsed = CASE WHEN f.PSFacilityPolicyId IS NOT NULL
                                AND DATEADD(DAY, 1, p.ExpiryDate) BETWEEN f.InceptionDate AND f.ExpiryDate
                               THEN 0 --valid facility
                           ELSE 1 END
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyPartyRelationship ppr
            ON ppr.PolicyId = p.PolicyId

        INNER JOIN ref.PartyRole pr
            ON ppr.PartyRoleId = pr.PartyRoleId
               AND pr.IsDeprecated = 0

        LEFT JOIN ref.PartyRole parpr
            ON ppr.ParentPartyRoleId = parpr.PartyRoleId
               AND parpr.IsDeprecated = 0

        LEFT JOIN dbo.PolicyCarrierDetails pcd
            ON ppr.PartyId = pcd.PartyId
               AND ppr.PolicyId = pcd.PolicyId
               AND ppr.PartyRoleId = pcd.PartyRoleId
               AND pcd.IsDeleted = 0

        INNER JOIN ref.FacilitySection fs
            ON pcd.LinkedFacilityPolicySectionId = fs.PSFacilityPolicySectionId

        LEFT JOIN ref.Facility f
            ON f.FacilityId = fs.FacilityId

        LEFT JOIN PAS.RefPolicyStatus rps
            ON rps.RefPolicyStatusId = f.RefPolicyStatusId
               AND rps.IsDeleted = 0

        LEFT JOIN APIv1.FacilityCarriers fc
            ON f.PSFacilityPolicyId = fc.FacilityId
               AND fc.CarrierId = ppr.PartyId
               AND fc.IsDeleted = 0
    WHERE
        pr.GlobalPartyRoleId = '102' --carrier
        AND p.IsDeleted = 0
        AND pr.PartyRoleKey NOT IN (
                'BUREAU', 'BILLCO', 'SHAREBROKER'
            ) --exclude bureau and billing co
        AND ISNULL(parpr.PartyRoleKey, 'N/A') NOT IN (
                'CONSORTIUM', 'POOL'
            ) --exclude children of pool or consortium
        AND ppr.IsDeleted = 0 --AND ppr.Incumbent = 1
        AND p.RuleId > 0 --in scope
        AND p.PolicyId = @PolicyId
) pc
GROUP BY
    PolicyId
  , pc.PartyId
  , pc.MarketKindId
  , FacilityId
  , FacilitySectionId;
