/*
Lineage
PS.MarketResponseBasis.MarketResponseBasisKey=BPStaging.MarketResponseBasis.Id
PS.MarketResponseBasis.MarketResponseBasisKey=BPStaging.CarrierResponse.Id
PS.MarketResponseBasis.MarketResponseBasisKey=BP.ExpiringResponseElement.ElementBranchId
PS.MarketResponseBasis.MarketResponseBasisKey=BP.AdjustmentResponseElement.ElementBranchId
PS.MarketResponseBasis.MarketResponseId=PS.MarketResponse.MarketResponseId
PS.MarketResponseBasis.SpecificationId=BPStaging.MarketResponseBasis.SpecificationId
PS.MarketResponseBasis.CoverageGroupId=BPStaging.MarketResponseBasis.CoverageGroupId
PS.MarketResponseBasis.CoverageGroupSectionId=BPStaging.MarketResponseBasis.CoverageGroupSectionId
PS.MarketResponseBasis.RiskProfileId=PS.RiskProfile.RiskProfileId
PS.MarketResponseBasis.ContractId=BPStaging.MarketResponseBasis.ContractId
PS.MarketResponseBasis.ContractSectionId=BPStaging.MarketResponseBasis.ContractSectionId
PS.MarketResponseBasis.SourceUpdatedDate=BPStaging.MarketResponseBasis.ValidTo
PS.MarketResponseBasis.SourceUpdatedDate=BPStaging.CarrierResponse.ValidTo
PS.MarketResponseBasis.SourceUpdatedDate=BPStaging.MarketResponseBasis.ValidFrom
PS.MarketResponseBasis.SourceUpdatedDate=BPStaging.CarrierResponse.ValidFrom
PS.MarketResponseBasis.IsDeleted=BPStaging.MarketResponseBasis.ValidTo
PS.MarketResponseBasis.IsDeleted=BPStaging.CarrierResponse.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_MarketResponseBasis
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketResponseBasis';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE INTO PS.MarketResponseBasis T
    USING (
        SELECT
            inner_select.MarketResponseBasisKey
          , MarketResponseId
          , SpecificationId
          , CoverageGroupId
          , CoverageGroupSectionId
          , RiskProfileId
          , ContractId
          , ContractSectionId
          , inner_select.IsDeleted
          , inner_select.SourceUpdatedDate
          , inner_select.DataSourceInstanceId
          , inner_select.RowNo
        FROM (
        SELECT
            Sou.MarketResponseBasisKey
          , MarketResponseId
          , SpecificationId
          , CoverageGroupId
          , CoverageGroupSectionId
          , RiskProfileId
          , ContractId
          , ContractSectionId
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , Sou.DataSourceInstanceId
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Sou.DataSourceInstanceId, Sou.MarketResponseBasisKey ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM (
        SELECT
            MarketResponseBasisKey = CONCAT('MRB|', mrb.Id)
          , DataSourceInstanceId = 50366
          , mr.MarketResponseId
          , mrb.SpecificationId
          , mrb.CoverageGroupId
          , mrb.CoverageGroupSectionId
          , RiskProfileId = COALESCE(rp.RiskProfileId, srp.RiskProfileId)
          , mrb.ContractId
          , mrb.ContractSectionId
          , mrb.ValidFrom
          , mrb.ValidTo
        FROM
            BPStaging.MarketResponseBasis mrb
            INNER JOIN PS.MarketResponse mr
                ON mr.MarketResponseKey = CONCAT('MKTRES|', mrb.MarketResponseId)
                   AND mr.DataSourceInstanceId = 50366

            LEFT JOIN PS.RiskProfile rp
                ON rp.RiskProfileKey = CONCAT(N'ELEM|', mrb.RiskDefinitionElementId)
                   AND rp.DataSourceInstanceId = 50366

            LEFT JOIN PS.RiskProfile srp
                ON srp.RiskProfileKey = CONCAT(N'SPEC|', mrb.RiskDefinitionElementId)
                   AND srp.DataSourceInstanceId = 50366
        UNION ALL
        SELECT
            MarketResponseBasisKey = CONCAT('CARRES|', CR.Id)
          , DataSourceInstanceId = 50366
          , MR.MarketResponseId
          , SpecificationId = NULL
          , CoverageGroupId = NULL
          , CoverageGroupSectionId = NULL
          , RP.RiskProfileId
          , ContractId = NULL
          , ContractSectionId = NULL
          , CR.ValidFrom
          , CR.ValidTo
        FROM
            BPStaging.CarrierResponse CR
            INNER JOIN PS.MarketResponse MR
                ON MR.DataSourceInstanceId = 50366
                   AND MR.MarketResponseKey = CONCAT('CARRES|', CR.Id)

            INNER JOIN dbo.Placement PL
                ON PL.PlacementSystemId = CR.PlacementId
                   AND PL.DataSourceInstanceId = 50366

            LEFT JOIN (
                SELECT
                    RiskProfileId
                  , PlacementId
                  , ProductId
                  , IsDeleted
                  , RowNumber = ROW_NUMBER() OVER (PARTITION BY PlacementId, ProductId ORDER BY IsDeleted ASC, RiskProfileId)
                FROM
                    PS.RiskProfile
            ) RP
                ON RP.ProductId = CR.ProductId
                   AND RP.PlacementId = PL.PlacementId
                   AND RP.RowNumber = 1
                   AND RP.IsDeleted = 0
        UNION ALL
        SELECT DISTINCT
               MarketResponseBasisKey = CONCAT('EXPRESP|', ERG.ElementBranchId)
             , DataSourceInstanceId = 50366
             , mr.MarketResponseId
             , SpecificationId = NULL
             , CoverageGroupId = NULL
             , CoverageGroupSectionId = NULL
             , RiskProfileId = COALESCE(rp.RiskProfileId, srp.RiskProfileId)
             , ContractId = NULL
             , ContractSectionId = NULL
             , ValidFrom = NULL
             , ValidTo = NULL
        FROM
            BP.ExpiringResponseElement ERG
            INNER JOIN BP.ResponseManagementElement RME
                ON ERG.ResponseManagementElementId = RME.Id

            INNER JOIN dbo.ElementBranch eb
                ON eb.ElementBranchId = ERG.ElementBranchId
            --     AND eb.ClonedFromElementBranchId IS NULL --> Include only they haven't been cloned from last years placement.

            INNER JOIN PS.MarketResponse mr
                ON mr.MarketResponseKey = CONCAT('EXPRESP|', ERG.ExpiringResponseGroupId, '|', ERG.Id)
                   AND mr.DataSourceInstanceId = 50366

            INNER JOIN BP.ResponseManagementElementRiskDefinitionElement rmrd
                ON rmrd.ResponseManagementElementId = ERG.ResponseManagementElementId
                   AND rmrd.IsDeleted = 0

            LEFT JOIN PS.RiskProfile rp
                ON rp.RiskProfileKey = CONCAT(N'ELEM|', rmrd.RiskDefinitionElementId)
                   AND rp.DataSourceInstanceId = 50366

            LEFT JOIN PS.RiskProfile srp
                ON srp.RiskProfileKey = CONCAT(N'SPEC|', rmrd.RiskDefinitionElementId)
                   AND srp.DataSourceInstanceId = 50366
        UNION ALL
        SELECT DISTINCT
               MarketResponseBasisKey = CONCAT('ADJRESP|', ARG.ElementBranchId)
             , DataSourceInstanceId = 50366
             , mr.MarketResponseId
             , SpecificationId = NULL
             , CoverageGroupId = NULL
             , CoverageGroupSectionId = NULL
             , RiskProfileId = COALESCE(rp.RiskProfileId, srp.RiskProfileId)
             , ContractId = NULL
             , ContractSectionId = NULL
             , ValidFrom = NULL
             , ValidTo = NULL
        FROM
            BP.AdjustmentResponseElement ARG
            INNER JOIN BP.ResponseManagementElement RME
                ON ARG.ResponseManagementElementId = RME.Id

            INNER JOIN dbo.ElementBranch eb
                ON eb.ElementBranchId = ARG.ElementBranchId

            INNER JOIN PS.MarketResponse mr
                ON mr.MarketResponseKey = CONCAT('ADJRESP|', ARG.AdjustmentResponseGroupId, '|', ARG.Id)
                   AND mr.DataSourceInstanceId = 50366

            INNER JOIN BP.ResponseManagementElementRiskDefinitionElement rmrd
                ON rmrd.ResponseManagementElementId = ARG.ResponseManagementElementId
                   AND rmrd.IsDeleted = 0

            LEFT JOIN PS.RiskProfile rp
                ON rp.RiskProfileKey = CONCAT(N'ELEM|', rmrd.RiskDefinitionElementId)
                   AND rp.DataSourceInstanceId = 50366

            LEFT JOIN PS.RiskProfile srp
                ON srp.RiskProfileKey = CONCAT(N'SPEC|', rmrd.RiskDefinitionElementId)
                   AND srp.DataSourceInstanceId = 50366
    ) Sou
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON S.DataSourceInstanceId = T.DataSourceInstanceId
       AND S.MarketResponseBasisKey = T.MarketResponseBasisKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , MarketResponseBasisKey
               , MarketResponseId
               , SpecificationId
               , CoverageGroupId
               , CoverageGroupSectionId
               , RiskProfileId
               , ContractId
               , ContractSectionId
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.MarketResponseBasisKey
                   , S.MarketResponseId
                   , S.SpecificationId
                   , S.CoverageGroupId
                   , S.CoverageGroupSectionId
                   , S.RiskProfileId
                   , S.ContractId
                   , S.ContractSectionId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED
    -- Ignoring fields that are hardcoded in the view which only leaves DataSourceInstanceId
    AND NOT EXISTS (
                SELECT
                    S.MarketResponseId
                  , S.SpecificationId
                  , S.CoverageGroupId
                  , S.CoverageGroupSectionId
                  , S.RiskProfileId
                  , S.ContractId
                  , S.ContractSectionId
                  , S.IsDeleted
                INTERSECT
                SELECT
                    T.MarketResponseId
                  , T.SpecificationId
                  , T.CoverageGroupId
                  , T.CoverageGroupSectionId
                  , T.RiskProfileId
                  , T.ContractId
                  , T.ContractSectionId
                  , T.IsDeleted
            )
        THEN UPDATE SET
                 T.MarketResponseId = S.MarketResponseId
               , T.SpecificationId = S.SpecificationId
               , T.CoverageGroupId = S.CoverageGroupId
               , T.CoverageGroupSectionId = S.CoverageGroupSectionId
               , T.RiskProfileId = S.RiskProfileId
               , T.ContractId = S.ContractId
               , T.ContractSectionId = S.ContractSectionId
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    OUTPUT $ACTION
    INTO @Actions;

    -- Get number of records affected.
    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);