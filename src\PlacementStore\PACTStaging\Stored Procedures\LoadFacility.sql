/*
Lineage
ref.Facility.DataSourceInstanceId=dbo.Policy.DataSourceInstanceId
ref.Facility.DataSourceInstanceId=PACTStaging.eGlobal_PLH_POOLHEADER.SourceId
ref.Facility.DataSourceInstanceId=Eclipse.Role.SourceId
ref.Facility.FacilityKey=dbo.Policy.PolicyId
ref.Facility.FacilityKey=PACTStaging.eGlobal_PLH_POOLHEADER.PLH_CODE
ref.Facility.FacilityKey=PACTStaging.eGlobal_PLH_POOLHEADER.PLH_BRANCH
ref.Facility.FacilityKey=Eclipse.Role.RoleId
ref.Facility.FacilityKey=Eclipse.Role.Role
ref.Facility.FacilityName=dbo.Policy.PolicyDescription
ref.Facility.FacilityName=PACTStaging.eGlobal_PLH_POOLHEADER.PLH_FULLNAME
ref.Facility.FacilityName=Eclipse.Role.Role
ref.Facility.FacilityName=Eclipse.Role.OrgId
ref.Facility.FacilityName=Eclipse.Role.RoleDsc
ref.Facility.FacilityReference=ref.LegalEntity.BrokerCode
ref.Facility.FacilityReference=dbo.Policy.PolicyReference
ref.Facility.FacilityReference=PACTStaging.eGlobal_PLH_POOLHEADER.PLH_CODE
ref.Facility.FacilityReference=Eclipse.Role.RoleId
ref.Facility.InceptionDate=dbo.Policy.InceptionDate
ref.Facility.InceptionDate=PACTStaging.eGlobal_PLH_POOLHEADER.PLH_EFFDATE
ref.Facility.InceptionDate=Eclipse.Role.EffectiveDate
ref.Facility.ExpiryDate=dbo.Policy.ExpiryDate
ref.Facility.RenewedFromFacilityId=ref.Facility.FacilityId
ref.Facility.FacilityType=dbo.Policy.FacilityType
ref.Facility.FacilityType=Eclipse.Role.Role
ref.Facility.RefPolicyStatusId=dbo.Policy.RefPolicyStatusId
ref.Facility.BrokerCode=ref.LegalEntity.BrokerCode
ref.Facility.PolicyReference=dbo.Policy.PolicyReference
ref.Facility.PolicyReference=PACTStaging.eGlobal_PLH_POOLHEADER.PLH_CODE
ref.Facility.PolicyReference=Eclipse.Role.RoleId
ref.Facility.PolicyStatusKey=dbo.Policy.PolicyStatusKey
ref.Facility.RenewedFromPolicyId=dbo.Policy.RenewedFromPolicyId
ref.Facility.SourceUpdatedDate=dbo.Policy.SourceUpdatedDate
ref.Facility.SourceUpdatedDate=PACTStaging.eGlobal_PLH_POOLHEADER.ETLUpdatedDate
ref.Facility.SourceUpdatedDate=Eclipse.Role.ETLUpdatedDate
ref.Facility.IsDeprecated=dbo.Policy.IsDeleted
ref.Facility.IsDeprecated=dbo.Policy.RefPolicyStatusId
ref.Facility.IsDeprecated=PACTStaging.eGlobal_PLH_POOLHEADER.IsDeleted
ref.Facility.IsDeprecated=Eclipse.Role.IsDeleted
*/

CREATE PROCEDURE PACTStaging.LoadFacility
AS
SET XACT_ABORT, NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.Facility';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

DECLARE @AmountsActions TABLE (
    Change VARCHAR(20)
);

DECLARE @NullSegmentMarkedDeleted INT;
DECLARE @RenewedFromUpdateCount INT;
DECLARE @ParentUpdateCount INT;
DECLARE
    @AmountsInsertedCount INT
  , @AmountsUpdatedCount  INT
  , @AmountsDeletedCount  INT;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    BEGIN TRANSACTION;

    DROP TABLE IF EXISTS #Facility;

    SELECT
        PO.DataSourceInstanceId
      , FacilityKey = CAST(PO.PolicyId AS NVARCHAR(100))
      , FacilityName = LTRIM(RTRIM(PO.PolicyDescription))
      , LE.BrokerCode
      , PO.PolicyReference
      , FacilityReference = CONCAT(LE.BrokerCode, PO.PolicyReference)
      , PO.InceptionDate
      , PO.ExpiryDate
      , PO.PolicyStatusKey
      , PO.RenewedFromPolicyId
      , PO.FacilityType
      , PO.RefPolicyStatusId
      , PO.SourceUpdatedDate
      , IsDeprecated = CASE WHEN PO.RefPolicyStatusId = 101
                                THEN 1
                            ELSE PO.IsDeleted END
      , ROW_NO = ROW_NUMBER() OVER (PARTITION BY PORG.PolicyId
                                    ORDER BY
                                        PORG.IsDeleted ASC
                                      --, OHT.IsDeleted ASC
                                      --, LE.IsDeleted ASC
                                      , PORG.ETLUpdatedDate DESC
                              )
    INTO #Facility
    FROM
        dbo.Policy PO
        LEFT JOIN dbo.PolicyOrganisation PORG
            ON PORG.PolicyId = PO.PolicyId
               AND PORG.IsDeleted = 0

        LEFT JOIN APIv1.OrganisationHierarchyTable OHT
            ON OHT.OrganisationId = PORG.OrganisationId

        LEFT JOIN ref.OrganisationLegalEntity OLE
            ON OLE.OrganisationId = OHT.Level1OrgId
               AND OLE.IsDeprecated = 0

        LEFT JOIN ref.LegalEntity LE
            ON LE.LegalEntityKey = CAST(OLE.LegalEntityId AS NVARCHAR(100))
               AND LE.DataSourceInstanceId = 50366
    WHERE
        PO.IsFacility = 1
        AND PO.RefPolicyStatusId IN (
                100, 101, 102, 103
            ) --placement, NTU, live, lapsed;
        AND (
            EXISTS (
        SELECT 'members'
        FROM
            dbo.PolicyPartyRelationship PPR
            INNER JOIN ref.PartyRole PR
                ON PR.PartyRoleId = PPR.PartyRoleId
                   AND PR.GlobalPartyRoleId = 102 --carrier
        WHERE
            PPR.PolicyId = PO.PolicyId
    )
            OR EXISTS (
        SELECT 'members' FROM dbo.PolicyMarket PM WHERE PM.PolicyId = PO.PolicyId
    )
        )
    UNION ALL
    SELECT
        DataSourceInstanceId = SourceId
      , FacilityKey = PLH_CODE + '-' + PLH_BRANCH
      , FacilityName = PLH_FULLNAME
      , BrokerCode = NULL
      , PolicyReference = PLH_CODE
      , FacilityReference = PLH_CODE
      , InceptionDate = PLH_EFFDATE
      , ExpiryDate = '9999-12-31 23:59:59' -- hardcoding
      , PolicyStatusKey = NULL
      , RenewedFromPolicyId = NULL
      , FacilityType = 'POOL'
      , RefPolicyStatusId = 102
      , SourceLastUpdateDate = ETLUpdatedDate
      , IsDeprecated = IsDeleted
      , ROW_NO = 1
    FROM
        PACTStaging.eGlobal_PLH_POOLHEADER
    UNION ALL
    SELECT
        DataSourceInstanceId = R.SourceId
      , FacilityKey = CAST(R.RoleId AS VARCHAR(10)) + '-' + R.Role
      , FacilityName = CASE WHEN R.RoleDsc IS NULL
                                 AND R.IsDeleted = 0
                                THEN CONCAT(R.Role, ' ', R.OrgId)
                            WHEN R.RoleDsc IS NULL
                                 AND R.IsDeleted = 1
                                THEN CONCAT(R.Role, ' ', R.OrgId, ' (Deprecated)')
                            ELSE R.RoleDsc END
      , BrokerCode = NULL
      , PolicyReference = CAST(R.RoleId AS VARCHAR(10))
      , FacilityReference = CAST(R.RoleId AS VARCHAR(10))
      , InceptionDate = R.EffectiveDate
      , ExpiryDate = '9999-12-31 23:59:59'
      , PolicyStatusId = NULL
      , RenewedFromPolicyId = NULL
      , FacilityType = UPPER(R.Role)
      , RefPolicyStatusId = 102
      , SourceLastUpdateDate = ETLUpdatedDate
      , IsDeprecated = R.IsDeleted
      , ROW_NO = 1
    FROM
        Eclipse.Role R
    WHERE
        R.Role IN (
            'Consortium', 'Pool'
        );

    MERGE ref.Facility T
    USING (
        SELECT
            F.DataSourceInstanceId
          , F.FacilityKey
          , F.FacilityName
          , F.FacilityReference
          , F.InceptionDate
          , F.ExpiryDate
          , RenewedFromFacilityId = RFF.FacilityId
          , F.FacilityType
          , F.RefPolicyStatusId
          , F.BrokerCode
          , F.PolicyReference
          , F.PolicyStatusKey
          , F.RenewedFromPolicyId
          , F.SourceUpdatedDate
          , F.IsDeprecated
        FROM
            #Facility F
            LEFT JOIN ref.Facility RFF
                ON RFF.PSFacilityPolicyId = F.RenewedFromPolicyId
        WHERE
            F.ROW_NO = 1
    ) S
    ON T.FacilityKey = S.FacilityKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , FacilityKey
               , FacilityName
               , FacilityReference
               , InceptionDate
               , ExpiryDate
               , RenewedFromFacilityId
               , FacilityType
               , RefPolicyStatusId
               , BrokerCode
               , PolicyReference
               , PolicyStatusKey
               , RenewedFromPolicyId
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.FacilityKey
                   , S.FacilityName
                   , S.FacilityReference
                   , S.InceptionDate
                   , S.ExpiryDate
                   , S.RenewedFromFacilityId
                   , S.FacilityType
                   , S.RefPolicyStatusId
                   , S.BrokerCode
                   , S.PolicyReference
                   , S.PolicyStatusKey
                   , S.RenewedFromPolicyId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.FacilityKey
                               , T.FacilityName
                               , T.FacilityReference
                               , T.InceptionDate
                               , T.ExpiryDate
                               , T.RenewedFromFacilityId
                               , T.FacilityType
                               , T.RefPolicyStatusId
                               , T.BrokerCode
                               , T.PolicyReference
                               , T.PolicyStatusKey
                               , T.RenewedFromPolicyId
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.FacilityKey
                               , S.FacilityName
                               , S.FacilityReference
                               , S.InceptionDate
                               , S.ExpiryDate
                               , S.RenewedFromFacilityId
                               , S.FacilityType
                               , S.RefPolicyStatusId
                               , S.BrokerCode
                               , S.PolicyReference
                               , S.PolicyStatusKey
                               , S.RenewedFromPolicyId
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.FacilityKey = S.FacilityKey
               , T.FacilityName = S.FacilityName
               , T.FacilityReference = S.FacilityReference
               , T.InceptionDate = S.InceptionDate
               , T.ExpiryDate = S.ExpiryDate
               , T.RenewedFromFacilityId = S.RenewedFromFacilityId
               , T.FacilityType = S.FacilityType
               , T.RefPolicyStatusId = S.RefPolicyStatusId
               , T.BrokerCode = S.BrokerCode
               , T.PolicyReference = S.PolicyReference
               , T.PolicyStatusKey = S.PolicyStatusKey
               , T.RenewedFromPolicyId = S.RenewedFromPolicyId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = 1
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    COMMIT TRANSACTION;
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;

    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 0;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
GO