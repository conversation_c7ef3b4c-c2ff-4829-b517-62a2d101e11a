CREATE TABLE PS.FinancialSegmentHierarchyTable (
    DataSourceInstanceId  INT           NOT NULL
  , FinancialSegmentId    INT           NOT NULL
  , ParentId              INT           NULL
  , PASFinancialSegmentId INT           NULL
  , FinancialSegment      NVARCHAR(500) NULL
  , SegLevel1             NVARCHAR(500) NULL
  , SegLevel2             NVARCHAR(500) NULL
  , SegLevel3             NVARCHAR(500) NULL
  , SegLevel4             NVARCHAR(500) NULL
  , SegLevel5             NVARCHAR(500) NULL
  , LevelNum              INT           NULL
  , SegRole               NVARCHAR(50)  NULL
  , SegNode               HIERARCHYID   NULL
  , ETLCreatedDate        DATETIME2(7)  NOT NULL
        DEFAULT GETUTCDATE()
  , ETLUpdatedDate        DATETIME2(7)  NOT NULL
        DEFAULT GETUTCDATE()
  , Level1SegId           INT           NULL
  , Level2SegId           INT           NULL
  , Level3SegId           INT           NULL
  , Level4SegId           INT           NULL
  , Level5SegId           INT           NULL
  , IsDeprecated          BIT           NOT NULL
        DEFAULT 0
  , CONSTRAINT PK_PS_FinancialSegmentHierarchyTable
        PRIMARY KEY
        (
            FinancialSegmentId
        )
  , CONSTRAINT FK_PS_FinancialSegmentHierarchyTable_Reference_DataSourceInstance
        FOREIGN KEY
        (
            DataSourceInstanceId
        )
        REFERENCES Reference.DataSourceInstance
        (
            DataSourceInstanceId
        )
);
GO

CREATE NONCLUSTERED INDEX IX_PS_FinancialSegmentHierarchyTable_SegLevel5
ON PS.FinancialSegmentHierarchyTable
(
    SegLevel5
);
GO