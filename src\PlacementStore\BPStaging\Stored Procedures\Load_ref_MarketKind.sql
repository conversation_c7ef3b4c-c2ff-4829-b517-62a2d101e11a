/*
Lineage
ref.MarketKind.MarketKindId=BPStaging.MarketKind.Id
ref.MarketKind.MarketKindKey=BPStaging.MarketKind.Id
ref.MarketKind.MarketKind=BPStaging.MarketKind.Text
ref.MarketKind.IsChildMarketKind=BPStaging.MarketKind.IsChildMarketKind
ref.MarketKind.IsThirdParty=BPStaging.MarketKind.IsThirdParty
ref.MarketKind.CanDistribute=BPStaging.MarketKind.CanDistribute
ref.MarketKind.SourceUpdatedDate=BPStaging.MarketKind.ValidFrom
ref.MarketKind.IsDeprecated=BPStaging.MarketKind.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_MarketKind
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.MarketKind';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF (EXISTS (SELECT 1 FROM BPStaging.MarketKind))
BEGIN
    BEGIN TRY
        MERGE ref.MarketKind t
        USING (
            SELECT
                MarketKindId = Id
              , DataSourceInstanceId = 50366
              , MarketKindKey = CAST(Id AS NVARCHAR(50))
              , MarketKind = Text
              , IsChildMarketKind
              , IsThirdParty
              , CanDistribute
              , SourceUpdatedDate = ValidFrom
              , IsDeprecated
            FROM
                BPStaging.MarketKind
        ) s
        ON t.MarketKindId = s.MarketKindId
        WHEN NOT MATCHED
            THEN INSERT (
                     MarketKindId
                   , DataSourceInstanceId
                   , MarketKindKey
                   , MarketKind
                   , IsChildMarketKind
                   , IsThirdParty
                   , CanDistribute
                   , SourceUpdatedDate
                   , ETLCreatedDate
                   , ETLUpdatedDate
                   , IsDeprecated
                 )
                 VALUES
                     (
                         s.MarketKindId
                       , s.DataSourceInstanceId
                       , s.MarketKindKey
                       , s.MarketKind
                       , s.IsChildMarketKind
                       , s.IsThirdParty
                       , s.CanDistribute
                       , s.SourceUpdatedDate
                       , GETUTCDATE()
                       , GETUTCDATE()
                       , s.IsDeprecated
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        t.DataSourceInstanceId
      , t.MarketKindKey
      , t.MarketKind
      , t.IsChildMarketKind
      , t.IsThirdParty
      , t.CanDistribute
      , t.SourceUpdatedDate
      , t.IsDeprecated
    INTERSECT
    SELECT
        s.DataSourceInstanceId
      , s.MarketKindKey
      , s.MarketKind
      , s.IsChildMarketKind
      , s.IsThirdParty
      , s.CanDistribute
      , s.SourceUpdatedDate
      , s.IsDeprecated
)
            THEN UPDATE SET
                     t.DataSourceInstanceId = s.DataSourceInstanceId
                   , t.MarketKindKey = s.MarketKindKey
                   , t.MarketKind = s.MarketKind
                   , t.IsChildMarketKind = s.IsChildMarketKind
                   , t.IsThirdParty = s.IsThirdParty
                   , t.CanDistribute = s.CanDistribute
                   , t.SourceUpdatedDate = s.SourceUpdatedDate
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.IsDeprecated = s.IsDeprecated
        WHEN NOT MATCHED BY SOURCE AND t.IsDeprecated = 0
            THEN UPDATE SET
                     t.ETLUpdatedDate = GETUTCDATE()
                   , t.IsDeprecated = 1
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
