/*
Lineage
ref.ServiceLevel.ServiceLevelId=BPStaging.ServiceLevel.Id
ref.ServiceLevel.ServiceLevelKey=BPStaging.ServiceLevel.LabelTranslationKey
ref.ServiceLevel.ServiceLevel=BPStaging.ServiceLevel.Text
ref.ServiceLevel.SourceUpdatedDate=BPStaging.ServiceLevel.ValidFrom
ref.ServiceLevel.IsDeprecated=BPStaging.ServiceLevel.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadServiceLevel
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ServiceLevel';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.ServiceLevel T
    USING (
        SELECT
            ServiceLevelId = Id
          , DataSourceInstanceId = 50366
          , ServiceLevelKey = LabelTranslationKey
          , ServiceLevel = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.ServiceLevel
    ) S
    ON T.ServiceLevelId = S.ServiceLevelId
    WHEN NOT MATCHED
        THEN INSERT (
                 ServiceLevelId
               , DataSourceInstanceId
               , ServiceLevelKey
               , ServiceLevel
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.ServiceLevelId
                   , S.DataSourceInstanceId
                   , S.ServiceLevelKey
                   , S.ServiceLevel
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.ServiceLevelId
                               , T.DataSourceInstanceId
                               , T.ServiceLevelKey
                               , T.ServiceLevel
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.ServiceLevelId
                               , S.DataSourceInstanceId
                               , S.ServiceLevelKey
                               , S.ServiceLevel
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.ServiceLevelId = S.ServiceLevelId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ServiceLevelKey = S.ServiceLevelKey
               , T.ServiceLevel = S.ServiceLevel
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
