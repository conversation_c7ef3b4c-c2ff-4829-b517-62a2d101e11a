/*
Lineage
dbo.Placement.PlacementCompletionStatusId=dbo.PlacementCompletionStatus.Id
dbo.Placement.DateSentToServicingPlatform=Rules.PlacementServiceBus.RequestDateTime
*/
CREATE PROCEDURE APIv1.UpdatePlacementServiceBusCompletion (
    @PlacementSystemID INT
  , @CompletionStatus  NVARCHAR(50)
)
AS
DECLARE @UpdatedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'Rules.PlacementServiceBus';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

UPDATE P
SET
    P.PlacementCompletionStatusId = psc.Id
  , P.DateSentToServicingPlatform = psb.RequestDateTime
FROM
    Rules.PlacementServiceBus psb
    JOIN dbo.Placement P
        ON psb.PlacementSystemId = P.PlacementSystemId
           AND P.DataSourceInstanceId = 50366
    CROSS JOIN dbo.PlacementCompletionStatus psc
WHERE
    psc.Name = @CompletionStatus
    AND P.PlacementSystemId = @PlacementSystemID;

SELECT @UpdatedCount = @@ROWCOUNT;

SET @Action = N'Update ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , NULL
  , @UpdatedCount
  , NULL
  , NULL
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;
