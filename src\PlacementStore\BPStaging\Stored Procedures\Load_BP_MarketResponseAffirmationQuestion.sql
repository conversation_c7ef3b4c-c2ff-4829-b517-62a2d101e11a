/*
Lineage
BP.MarketResponseAffirmationQuestion.Id=BPStaging.MarketResponseAffirmationQuestion.Id
BP.MarketResponseAffirmationQuestion.MarketResponseId=BPStaging.MarketResponseAffirmationQuestion.MarketResponseId
BP.MarketResponseAffirmationQuestion.AffirmationQuestionId=BPStaging.MarketResponseAffirmationQuestion.AffirmationQuestionId
BP.MarketResponseAffirmationQuestion.Answer=BPStaging.MarketResponseAffirmationQuestion.Answer
BP.MarketResponseAffirmationQuestion.AnsweredByUserId=BPStaging.MarketResponseAffirmationQuestion.AnsweredByUserId
BP.MarketResponseAffirmationQuestion.SourceUpdatedDate=BPStaging.MarketResponseAffirmationQuestion.ValidTo
BP.MarketResponseAffirmationQuestion.SourceUpdatedDate=BPStaging.MarketResponseAffirmationQuestion.ValidFrom
BP.MarketResponseAffirmationQuestion.IsDeleted=BPStaging.MarketResponseAffirmationQuestion.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_MarketResponseAffirmationQuestion
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.MarketResponseAffirmationQuestion';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.MarketResponseAffirmationQuestion)
BEGIN
    BEGIN TRY
        MERGE BP.MarketResponseAffirmationQuestion t
        USING (
            SELECT
                inner_select.Id
              , inner_select.MarketResponseId
              , inner_select.AffirmationQuestionId
              , inner_select.Answer
              , inner_select.AnsweredByUserId
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                     THEN 1
                                 ELSE 0 END
            FROM (
            SELECT
                Id
              , MarketResponseId
              , AffirmationQuestionId
              , Answer
              , AnsweredByUserId
              , ValidFrom
              , ValidTo
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.MarketResponseAffirmationQuestion
        ) inner_select
            WHERE
                inner_select.RowNo = 1
        ) s
        ON t.Id = s.Id
        WHEN NOT MATCHED
            THEN INSERT (
                     Id
                   , MarketResponseId
                   , AffirmationQuestionId
                   , Answer
                   , AnsweredByUserId
                   , SourceUpdatedDate
                   , IsDeleted
                 )
                 VALUES
                     (
                         s.Id
                       , s.MarketResponseId
                       , s.AffirmationQuestionId
                       , s.Answer
                       , s.AnsweredByUserId
                       , s.SourceUpdatedDate
                       , s.IsDeleted
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        t.Id
      , t.MarketResponseId
      , t.AffirmationQuestionId
      , t.Answer
      , t.AnsweredByUserId
      , t.SourceUpdatedDate
      , t.IsDeleted
    INTERSECT
    SELECT
        s.Id
      , s.MarketResponseId
      , s.AffirmationQuestionId
      , s.Answer
      , s.AnsweredByUserId
      , s.SourceUpdatedDate
      , s.IsDeleted
)
            THEN UPDATE SET
                     t.MarketResponseId = s.MarketResponseId
                   , t.AffirmationQuestionId = s.AffirmationQuestionId
                   , t.Answer = s.Answer
                   , t.AnsweredByUserId = s.AnsweredByUserId
                   , t.SourceUpdatedDate = s.SourceUpdatedDate
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.IsDeleted = s.IsDeleted
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);