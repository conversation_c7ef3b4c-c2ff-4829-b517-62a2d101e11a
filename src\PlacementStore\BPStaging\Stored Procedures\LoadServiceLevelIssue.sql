/*
Lineage
ref.ServiceLevelIssue.ServiceLevelIssueId=BPStaging.ServiceLevelIssue.Id
ref.ServiceLevelIssue.ServiceLevelIssueKey=BPStaging.ServiceLevelIssue.LabelTranslationKey
ref.ServiceLevelIssue.ServiceLevelIssue=BPStaging.ServiceLevelIssue.Text
ref.ServiceLevelIssue.SourceUpdatedDate=BPStaging.ServiceLevelIssue.ValidFrom
ref.ServiceLevelIssue.IsDeprecated=BPStaging.ServiceLevelIssue.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadServiceLevelIssue
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ServiceLevelIssue';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.ServiceLevelIssue T
    USING (
        SELECT
            ServiceLevelIssueId = Id
          , DataSourceInstanceId = 50366
          , ServiceLevelIssueKey = LabelTranslationKey
          , ServiceLevelIssue = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.ServiceLevelIssue
    ) S
    ON T.ServiceLevelIssueId = S.ServiceLevelIssueId
    WHEN NOT MATCHED
        THEN INSERT (
                 ServiceLevelIssueId
               , DataSourceInstanceId
               , ServiceLevelIssueKey
               , ServiceLevelIssue
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.ServiceLevelIssueId
                   , S.DataSourceInstanceId
                   , S.ServiceLevelIssueKey
                   , S.ServiceLevelIssue
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.ServiceLevelIssueId
                               , T.DataSourceInstanceId
                               , T.ServiceLevelIssueKey
                               , T.ServiceLevelIssue
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.ServiceLevelIssueId
                               , S.DataSourceInstanceId
                               , S.ServiceLevelIssueKey
                               , S.ServiceLevelIssue
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.ServiceLevelIssueId = S.ServiceLevelIssueId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ServiceLevelIssueKey = S.ServiceLevelIssueKey
               , T.ServiceLevelIssue = S.ServiceLevelIssue
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
