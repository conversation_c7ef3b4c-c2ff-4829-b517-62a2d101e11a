/*
Lineage
PS.ClientProposalPlacement.Id=BPStaging.ClientProposalPlacement.Id
PS.ClientProposalPlacement.ClientProposalId=BPStaging.ClientProposalPlacement.ClientProposalId
PS.ClientProposalPlacement.PlacementId=BPStaging.ClientProposalPlacement.PlacementId
PS.ClientProposalPlacement.SourceUpdatedDate=BPStaging.ClientProposalPlacement.ValidTo
PS.ClientProposalPlacement.SourceUpdatedDate=BPStaging.ClientProposalPlacement.ValidFrom
PS.ClientProposalPlacement.IsDeleted=BPStaging.ClientProposalPlacement.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_ClientProposalPlacement
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ClientProposalPlacement';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ClientProposalPlacement)
BEGIN TRY
    MERGE PS.ClientProposalPlacement T
    USING (
        SELECT
            Id
          , ClientProposalId
          , PlacementId
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , ClientProposalId
          , PlacementId
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ClientProposalPlacement
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , ClientProposalId
               , PlacementId
               , SourceUpdatedDate
               , IsDeleted
               , DataSourceInstanceId
             )
             VALUES
                 (
                     S.Id
                   , S.ClientProposalId
                   , S.PlacementId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.ClientProposalId
      , T.PlacementId
      , T.SourceUpdatedDate
      , T.IsDeleted
      , T.DataSourceInstanceId
    INTERSECT
    SELECT
        S.ClientProposalId
      , S.PlacementId
      , S.SourceUpdatedDate
      , S.IsDeleted
      , S.DataSourceInstanceId
)
        THEN UPDATE SET
                 T.ClientProposalId = S.ClientProposalId
               , T.PlacementId = S.PlacementId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);