/*
Lineage
PS.MarketResponseElement.DataSourceInstanceId=PS.MarketResponse.DataSourceInstanceId
PS.MarketResponseElement.MarketResponseElementKey=PS.MarketResponse.MarketResponseId
PS.MarketResponseElement.MarketResponseElementKey=dbo.ElementCache.ElementId
PS.MarketResponseElement.MarketResponseElementKey=dbo.ElementCache.ElementBranchId
PS.MarketResponseElement.MarketResponseElementKey=dbo.ElementCache.ParentElementId
PS.MarketResponseElement.MarketResponseElementKey=PS.ElementCompositionLinkedElement.AnchorElementId
PS.MarketResponseElement.ParentDataSourceInstanceId=PS.MarketResponse.DataSourceInstanceId
PS.MarketResponseElement.ParentMarketResponseElementKey=PS.MarketResponse.MarketResponseId
PS.MarketResponseElement.ParentMarketResponseElementKey=dbo.ElementCache.ElementId
PS.MarketResponseElement.ParentMarketResponseElementKey=dbo.ElementCache.ElementBranchId
PS.MarketResponseElement.ParentMarketResponseElementKey=dbo.ElementCache.ParentElementId
PS.MarketResponseElement.ParentMarketResponseElementKey=PS.ElementCompositionLinkedElement.AnchorElementId
PS.MarketResponseElement.MarketResponseId=PS.MarketResponse.MarketResponseId
PS.MarketResponseElement.TypeKeyPath=dbo.ElementCache.TypeKeyPath
PS.MarketResponseElement.ElementLevel=dbo.ElementCache.HierarchyId
PS.MarketResponseElement.ElementDisplayOrder=dbo.ElementDelta.Index
PS.MarketResponseElement.ElementId=dbo.ElementCache.ElementId
PS.MarketResponseElement.ElementTypeId=dbo.ElementCache.ElementTypeId
PS.MarketResponseElement.ElementType=ref.ElementType.ElementType
PS.MarketResponseElement.ElementTypeOverride=dbo.ElementDelta.Label
PS.MarketResponseElement.ParentElementId=dbo.ElementCache.ParentElementId
PS.MarketResponseElement.ParentElementId=dbo.ElementCache.ElementId
PS.MarketResponseElement.ParentElementTypeId=dbo.ElementCache.ElementTypeId
PS.MarketResponseElement.ParentElementType=ref.ElementType.ElementType
PS.MarketResponseElement.ParentElementTypeOverride=dbo.ElementDelta.Label
PS.MarketResponseElement.SourceUpdatedDate=BP.MarketResponseElement.ETLUpdatedDate
PS.MarketResponseElement.SourceUpdatedDate=BP.ResponseManagementElement.ETLUpdatedDate
PS.MarketResponseElement.SourceUpdatedDate=BP.ExpiringResponseElement.ETLUpdatedDate
PS.MarketResponseElement.SourceUpdatedDate=BP.AdjustmentResponseElement.ETLUpdatedDate
PS.MarketResponseElement.SourceUpdatedDate=dbo.ElementCache.LastUpdatedUTCDate
PS.MarketResponseElement.IsDeleted=BP.MarketResponseElement.IsDeleted
PS.MarketResponseElement.IsDeleted=BP.ResponseManagementElement.IsDeleted
PS.MarketResponseElement.IsDeleted=BP.ExpiringResponseElement.IsDeleted
PS.MarketResponseElement.IsDeleted=BP.AdjustmentResponseElement.IsDeleted
PS.MarketResponseElement.ElementBranchId=dbo.ElementCache.ElementBranchId
*/

CREATE PROCEDURE BPStaging.Load_PS_MarketResponseElement
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketResponseElement';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @UpdateDate DATETIME2(7) = GETUTCDATE();

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #MarketResponse;
    DROP TABLE IF EXISTS #MarketResponseElement;

    CREATE TABLE #MarketResponse (
        DataSourceInstanceId INT          NOT NULL
      , MarketResponseId     INT          NOT NULL
      , ElementId            INT          NOT NULL
      , ElementBranchId      INT          NOT NULL
      , SourceUpdatedDate    DATETIME2(7) NULL
      , IsDeleted            BIT          NOT NULL
      ,
      UNIQUE CLUSTERED
      (
          MarketResponseId ASC
      )
    );

    CREATE TABLE #MarketResponseElement (
        DataSourceInstanceId           INT           NOT NULL
      , MarketResponseElementKey       NVARCHAR(50)  NOT NULL
      , ParentDataSourceInstanceId     INT           NULL
      , ParentMarketResponseElementKey NVARCHAR(50)  NULL
      , MarketResponseId               INT           NOT NULL
      , TypeKeyPath                    NVARCHAR(500) NULL
      , ElementLevel                   INT           NOT NULL
      , ElementDisplayOrder            INT           NULL
      , ElementId                      INT           NOT NULL
      , ElementTypeId                  INT           NOT NULL
      , ElementType                    NVARCHAR(500) NOT NULL
      , ElementTypeOverride            NVARCHAR(500) NULL
      , ParentElementId                INT           NULL
      , ParentElementTypeId            INT           NULL
      , ParentElementType              NVARCHAR(500) NULL
      , ParentElementTypeOverride      NVARCHAR(500) NULL
      , SourceUpdatedDate              DATETIME2(7)  NULL
      , IsDeleted                      BIT           NOT NULL
      , ElementBranchId                INT           NULL
      ,
      UNIQUE CLUSTERED
      (
          MarketResponseElementKey ASC
        , DataSourceInstanceId ASC
      )
    );

    /* Market Responses */
    INSERT INTO
        #MarketResponse
        (
            DataSourceInstanceId
          , MarketResponseId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , mr.MarketResponseId
      , rme.ElementId
      , mre.ElementBranchId
      , SourceUpdatedDate = GREATEST(mre.ETLUpdatedDate, rme.ETLUpdatedDate)
      , IsDeleted = (mre.IsDeleted | rme.IsDeleted)
    FROM
        BP.MarketResponseElement mre WITH (NOLOCK)
        INNER JOIN PS.MarketResponse mr WITH (NOLOCK)
            ON mr.DataSourceInstanceId = 50366
               AND mr.MarketResponseKey = CONCAT('MKTRES|', mre.MarketResponseId)

        INNER JOIN BP.ResponseManagementElement rme WITH (NOLOCK)
            ON rme.Id = mre.ResponseManagementElementId
    WHERE
        mre.IsDeleted = 0
        AND rme.IsDeleted = 0;

    /* Expiring Market Responses */
    INSERT INTO
        #MarketResponse
        (
            DataSourceInstanceId
          , MarketResponseId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , mr.MarketResponseId
      , rme.ElementId
      , mre.ElementBranchId
      , SourceUpdatedDate = GREATEST(mre.ETLUpdatedDate, rme.ETLUpdatedDate)
      , IsDeleted = (mre.IsDeleted | rme.IsDeleted)
    FROM
        BP.ExpiringResponseElement mre WITH (NOLOCK)
        INNER JOIN PS.MarketResponse mr WITH (NOLOCK)
            ON mr.MarketResponseKey = CONCAT('EXPRESP|', mre.ExpiringResponseGroupId, '|', mre.Id)
               AND mr.DataSourceInstanceId = 50366

        INNER JOIN BP.ResponseManagementElement rme WITH (NOLOCK)
            ON rme.Id = mre.ResponseManagementElementId
    WHERE
        mre.IsDeleted = 0
        AND rme.IsDeleted = 0;

    /* Adjustment Market Responses */
    INSERT INTO
        #MarketResponse
        (
            DataSourceInstanceId
          , MarketResponseId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , mr.MarketResponseId
      , rme.ElementId
      , are.ElementBranchId
      , SourceUpdatedDate = GREATEST(are.ETLUpdatedDate, rme.ETLUpdatedDate)
      , IsDeleted = (are.IsDeleted | rme.IsDeleted)
    FROM
        BP.AdjustmentResponseElement are WITH (NOLOCK)
        INNER JOIN PS.MarketResponse mr WITH (NOLOCK)
            ON mr.MarketResponseKey = CONCAT('ADJRESP|', are.AdjustmentResponseGroupId, '|', are.Id)
               AND mr.DataSourceInstanceId = 50366

        INNER JOIN BP.ResponseManagementElement rme WITH (NOLOCK)
            ON rme.Id = are.ResponseManagementElementId
    WHERE
        are.IsDeleted = 0
        AND rme.IsDeleted = 0;

    INSERT INTO
        #MarketResponseElement
        (
            DataSourceInstanceId
          , MarketResponseElementKey
          , ParentDataSourceInstanceId
          , ParentMarketResponseElementKey
          , MarketResponseId
          , TypeKeyPath
          , ElementLevel
          , ElementDisplayOrder
          , ElementId
          , ElementTypeId
          , ElementType
          , ElementTypeOverride
          , ParentElementId
          , ParentElementTypeId
          , ParentElementType
          , ParentElementTypeOverride
          , SourceUpdatedDate
          , IsDeleted
          , ElementBranchId
        )
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseElementKey = CONCAT(mr.MarketResponseId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , ParentDataSourceInstanceId = 50366
      , ParentMarketResponseElementKey = CASE WHEN ecparent.ElementId IS NOT NULL
                                                  THEN CONCAT(
                                                           mr.MarketResponseId
                                                         , N'|'
                                                         , ecparent.ElementId
                                                         , N'|'
                                                         , ecparent.ElementBranchId
                                                       ) END
      , mr.MarketResponseId
      , ec.TypeKeyPath
      , ElementLevel = ec.HierarchyId.GetLevel()
      , ElementDisplayOrder = ed.[Index]
      , ec.ElementId
      , ec.ElementTypeId
      , et.ElementType
      , ElementTypeOverride = ed.Label
      , ec.ParentElementId
      , ParentElementTypeId = ecparent.ElementTypeId
      , ParentElementType = etparent.ElementType
      , ParentElementTypeOverride = edparent.Label
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate)
      , mr.IsDeleted
      , ec.ElementBranchId
    FROM
        #MarketResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = mr.ElementId
               AND ec.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementDelta ed WITH (NOLOCK)
            ON ed.ElementDeltaId = ec.ElementDeltaId

        INNER JOIN ref.ElementType et WITH (NOLOCK)
            ON et.ElementTypeId = ec.ElementTypeId

        LEFT JOIN dbo.ElementCache ecparent WITH (NOLOCK)
            ON ecparent.ElementId = ec.ParentElementId
               AND ISNULL(ecparent.ElementBranchId, -1) = ISNULL(ec.ElementBranchId, -1)

        LEFT JOIN ref.ElementType etparent WITH (NOLOCK)
            ON etparent.ElementTypeId = ecparent.ElementTypeId

        LEFT JOIN dbo.ElementDelta edparent WITH (NOLOCK)
            ON edparent.ElementDeltaId = ecparent.ElementDeltaId
    WHERE
        mr.IsDeleted = 0
    ORDER BY
        MarketResponseElementKey;

    INSERT INTO
        #MarketResponseElement
        (
            DataSourceInstanceId
          , MarketResponseElementKey
          , ParentDataSourceInstanceId
          , ParentMarketResponseElementKey
          , MarketResponseId
          , TypeKeyPath
          , ElementLevel
          , ElementDisplayOrder
          , ElementId
          , ElementTypeId
          , ElementType
          , ElementTypeOverride
          , ParentElementId
          , ParentElementTypeId
          , ParentElementType
          , ParentElementTypeOverride
          , SourceUpdatedDate
          , IsDeleted
          , ElementBranchId
        )
    /* Market Element Composition Linked Element */
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseElementKey = CONCAT(
                                       mr.MarketResponseId
                                     , N'|'
                                     , ec.ElementId
                                     , N'|'
                                     , ec.ElementBranchId
                                     , N'|'
                                     , ec.ParentElementId
                                     , N'|'
                                     , ecle.AnchorElementId
                                     , N'|'
                                     , ecAnchor.ElementBranchId
                                   )
      , ParentDataSourceInstanceId = mr.DataSourceInstanceId
      , ParentMarketResponseAttributeKey = CASE WHEN ec.ParentElementId IS NOT NULL
                                                    THEN CONCAT(
                                                             mr.MarketResponseId
                                                           , N'|'
                                                           , ecparent.ElementId
                                                           , N'|'
                                                           , ecparent.ElementBranchId
                                                           , N'|'
                                                           , ecparent.ParentElementId
                                                           , N'|'
                                                           , ecle.AnchorElementId
                                                           , N'|'
                                                           , ecAnchor.ElementBranchId
                                                         )
                                                ELSE
                                                    CONCAT(
                                                        mr.MarketResponseId
                                                      , N'|'
                                                      , ecAnchor.ElementId
                                                      , N'|'
                                                      , ecAnchor.ElementBranchId
                                                    ) END
      , mr.MarketResponseId
      , TypeKeyPath = LEFT(ecAnchor.TypeKeyPath, NULLIF(LEN(ecAnchor.TypeKeyPath) - 1, -1)) + ec.TypeKeyPath
      , ElementLevel = ec.HierarchyId.GetLevel() + ecAnchor.HierarchyId.GetLevel()
      , ElementDisplayOrder = ed.[Index]
      , ec.ElementId
      , ec.ElementTypeId
      , et.ElementType
      , ElementTypeOverride = ed.Label
      , ParentElementId = ISNULL(ec.ParentElementId, ecAnchor.ElementId)
      , ParentElementTypeId = ISNULL(ecparent.ElementTypeId, ecAnchor.ElementTypeId)
      , ParentElementType = etparent.ElementType
      , ParentElementTypeOverride = edparent.Label
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate)
      , mr.IsDeleted
      , ec.ElementBranchId
    FROM
        #MarketResponse mr WITH (NOLOCK)
        INNER JOIN PS.ElementCompositionLinkedElement ecle WITH (NOLOCK)
            ON mr.ElementId = ecle.RootElementId
               AND ecle.IsDeleted = 0

        INNER JOIN PS.ElementCompositionLinkedElementBranch ecleb WITH (NOLOCK)
            ON ecleb.ElementCompositionLinkedElementId = ecle.ElementCompositionLinkedElementId
               AND mr.ElementBranchId = ecleb.RootElementBranchId
               AND ecleb.IsDeleted = 0

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = ecle.LinkedRootElementId
               AND ec.ElementBranchId = ecleb.LinkedElementBranchId

        INNER JOIN dbo.ElementDelta ed WITH (NOLOCK)
            ON ed.ElementDeltaId = ec.ElementDeltaId

        INNER JOIN dbo.ElementCache ecAnchor WITH (NOLOCK)
            ON ecAnchor.ElementId = ecle.AnchorElementId
               AND ecAnchor.ElementBranchId = ecleb.RootElementBranchId

        INNER JOIN ref.ElementType et WITH (NOLOCK)
            ON et.ElementTypeId = ec.ElementTypeId

        LEFT JOIN dbo.ElementCache ecparent WITH (NOLOCK)
            ON ecparent.ElementId = ISNULL(ec.ParentElementId, ecAnchor.ElementId)
               AND ISNULL(ecparent.ElementBranchId, -1) = ISNULL(ec.ElementBranchId, -1)

        LEFT JOIN ref.ElementType etparent WITH (NOLOCK)
            ON etparent.ElementTypeId = ISNULL(ecparent.ElementTypeId, ecAnchor.ElementTypeId)

        LEFT JOIN dbo.ElementDelta edparent WITH (NOLOCK)
            ON edparent.ElementDeltaId = ISNULL(ecparent.ElementDeltaId, ecAnchor.ElementDeltaId)
    WHERE
        mr.IsDeleted = 0
    ORDER BY
        MarketResponseElementKey;

    MERGE PS.MarketResponseElement WITH (HOLDLOCK) T
    USING (
        SELECT
            DataSourceInstanceId
          , MarketResponseElementKey
          , ParentDataSourceInstanceId
          , ParentMarketResponseElementKey
          , MarketResponseId
          , TypeKeyPath
          , ElementLevel
          , ElementDisplayOrder
          , ElementId
          , ElementTypeId
          , ElementType
          , ElementTypeOverride
          , ParentElementId
          , ParentElementTypeId
          , ParentElementType
          , ParentElementTypeOverride
          , SourceUpdatedDate
          , IsDeleted
          , ElementBranchId
        FROM
            #MarketResponseElement WITH (NOLOCK)
    ) S
    ON T.MarketResponseElementKey = S.MarketResponseElementKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , MarketResponseElementKey
               , ParentDataSourceInstanceId
               , ParentMarketResponseElementKey
               , MarketResponseId
               , TypeKeyPath
               , ElementLevel
               , ElementDisplayOrder
               , ElementId
               , ElementTypeId
               , ElementType
               , ElementTypeOverride
               , ParentElementId
               , ParentElementTypeId
               , ParentElementType
               , ParentElementTypeOverride
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
               , ElementBranchId
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.MarketResponseElementKey
                   , S.ParentDataSourceInstanceId
                   , S.ParentMarketResponseElementKey
                   , S.MarketResponseId
                   , S.TypeKeyPath
                   , S.ElementLevel
                   , S.ElementDisplayOrder
                   , S.ElementId
                   , S.ElementTypeId
                   , S.ElementType
                   , S.ElementTypeOverride
                   , S.ParentElementId
                   , S.ParentElementTypeId
                   , S.ParentElementType
                   , S.ParentElementTypeOverride
                   , @UpdateDate
                   , @UpdateDate
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.ElementBranchId
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.ParentDataSourceInstanceId
                               , S.ParentMarketResponseElementKey
                               , S.MarketResponseId
                               , S.TypeKeyPath
                               , S.ElementLevel
                               , S.ElementDisplayOrder
                               , S.ElementId
                               , S.ElementTypeId
                               , S.ElementType
                               , S.ElementTypeOverride
                               , S.ParentElementId
                               , S.ParentElementTypeId
                               , S.ParentElementType
                               , S.ParentElementTypeOverride
                               , S.IsDeleted
                               , S.ElementBranchId
                             INTERSECT
                             SELECT
                                 T.ParentDataSourceInstanceId
                               , T.ParentMarketResponseElementKey
                               , T.MarketResponseId
                               , T.TypeKeyPath
                               , T.ElementLevel
                               , T.ElementDisplayOrder
                               , T.ElementId
                               , T.ElementTypeId
                               , T.ElementType
                               , T.ElementTypeOverride
                               , T.ParentElementId
                               , T.ParentElementTypeId
                               , T.ParentElementType
                               , T.ParentElementTypeOverride
                               , T.IsDeleted
                               , T.ElementBranchId
                         )
        THEN UPDATE SET
                 T.ParentDataSourceInstanceId = S.ParentDataSourceInstanceId
               , T.ParentMarketResponseElementKey = S.ParentMarketResponseElementKey
               , T.MarketResponseId = S.MarketResponseId
               , T.TypeKeyPath = S.TypeKeyPath
               , T.ElementLevel = S.ElementLevel
               , T.ElementDisplayOrder = S.ElementDisplayOrder
               , T.ElementId = S.ElementId
               , T.ElementTypeId = S.ElementTypeId
               , T.ElementType = S.ElementType
               , T.ElementTypeOverride = S.ElementTypeOverride
               , T.ParentElementId = S.ParentElementId
               , T.ParentElementTypeId = S.ParentElementTypeId
               , T.ParentElementType = S.ParentElementType
               , T.ParentElementTypeOverride = S.ParentElementTypeOverride
               , T.ETLUpdatedDate = @UpdateDate
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.ElementBranchId = S.ElementBranchId
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = @UpdateDate
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
GO