CREATE TABLE ref.FinancialSegment (
    FinancialSegmentId          INT            IDENTITY(1, 1) PRIMARY KEY NOT NULL
  , DataSourceInstanceId        INT            NOT NULL
  , FinancialSegmentKey         NVARCHAR(100)  NOT NULL
  , FinancialSegment            NVARCHAR(2000) NULL
  , SourceQuery                 NVARCHAR(100)  NULL
  , SourceKey                   NVARCHAR(100)  NULL
  , GlobalFinancialSegmentId    INT            NULL
  , FinancialSegmentCode        NVARCHAR(500)  NULL
  , FinancialSegmentDescription NVARCHAR(2000) NULL
  , Level1                      NVARCHAR(500)  NULL
  , Level2                      NVARCHAR(500)  NULL
  , Level3                      NVARCHAR(500)  NULL
  , Level4                      NVARCHAR(500)  NULL
  , Level5                      NVARCHAR(500)  NULL
  , ETLCreatedDate              DATETIME2(7)   NOT NULL
        DEFAULT (GETUTCDATE())
  , ETLUpdatedDate              DATETIME2(7)   NOT NULL
        DEFAULT (GETUTCDATE())
  , IsDeprecated                BIT            NOT NULL
        DEFAULT (0)
  , PACTFinancialSegmentId      INT            NULL
  , PASFinancialSegmentId       INT            NULL
  , FinancialStructureId        INT            NULL
  , SourceUpdatedDate           DATETIME2(7)   NULL
  , CONSTRAINT FK_ref_FinancialSegment_Reference_DataSourceInstance
        FOREIGN KEY
        (
            DataSourceInstanceId
        )
        REFERENCES Reference.DataSourceInstance
        (
            DataSourceInstanceId
        )
);
GO

CREATE UNIQUE INDEX IXU_ref_FinancialSegment_PASFinancialSegmentId
ON ref.FinancialSegment
(
    PASFinancialSegmentId
);
