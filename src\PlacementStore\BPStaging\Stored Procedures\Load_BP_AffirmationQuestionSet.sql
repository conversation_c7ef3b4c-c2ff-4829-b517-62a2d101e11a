/*
Lineage
BP.AffirmationQuestionSet.Id=BPStaging.AffirmationQuestionSet.Id
BP.AffirmationQuestionSet.AutoFollowKey=BPStaging.AffirmationQuestionSet.AutoFollowKey
BP.AffirmationQuestionSet.LabelTranslationDescription=BPStaging.AffirmationQuestionSet.LabelTranslationDescription
BP.AffirmationQuestionSet.DescriptionTranslationText=BPStaging.AffirmationQuestionSet.DescriptionTranslationText
BP.AffirmationQuestionSet.SourceUpdatedDate=BPStaging.AffirmationQuestionSet.ValidTo
BP.AffirmationQuestionSet.SourceUpdatedDate=BPStaging.AffirmationQuestionSet.ValidFrom
BP.AffirmationQuestionSet.IsDeleted=BPStaging.AffirmationQuestionSet.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_AffirmationQuestionSet
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.AffirmationQuestionSet';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.AffirmationQuestionSet)
BEGIN
    BEGIN TRY
        MERGE BP.AffirmationQuestionSet t
        USING (
            SELECT
                inner_select.Id
              , inner_select.AutoFollowKey
              , inner_select.LabelTranslationDescription
              , inner_select.DescriptionTranslationText
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                     THEN 1
                                 ELSE 0 END
            FROM (
            SELECT
                Id
              , AutoFollowKey
              , LabelTranslationDescription
              , DescriptionTranslationText
              , ValidFrom
              , ValidTo
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.AffirmationQuestionSet
        ) inner_select
            WHERE
                inner_select.RowNo = 1
        ) s
        ON t.Id = s.Id
        WHEN NOT MATCHED
            THEN INSERT (
                     Id
                   , AutoFollowKey
                   , LabelTranslationDescription
                   , DescriptionTranslationText
                   , SourceUpdatedDate
                   , IsDeleted
                 )
                 VALUES
                     (
                         s.Id
                       , s.AutoFollowKey
                       , s.LabelTranslationDescription
                       , s.DescriptionTranslationText
                       , s.SourceUpdatedDate
                       , s.IsDeleted
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        t.AutoFollowKey
      , t.LabelTranslationDescription
      , t.DescriptionTranslationText
      , t.SourceUpdatedDate
      , t.IsDeleted
    INTERSECT
    SELECT
        s.AutoFollowKey
      , s.LabelTranslationDescription
      , s.DescriptionTranslationText
      , s.SourceUpdatedDate
      , s.IsDeleted
)
            THEN UPDATE SET
                     t.AutoFollowKey = s.AutoFollowKey
                   , t.LabelTranslationDescription = s.LabelTranslationDescription
                   , t.DescriptionTranslationText = s.DescriptionTranslationText
                   , t.SourceUpdatedDate = s.SourceUpdatedDate
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.IsDeleted = s.IsDeleted
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);