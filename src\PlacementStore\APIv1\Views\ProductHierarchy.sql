/*
Lineage
ProductID=rpt.ProductHierarchy.ProductId
DataSourceInstanceID=rpt.ProductHierarchy.DataSourceInstanceId
ProductName=rpt.ProductHierarchy.ProductName
ParentID=rpt.ProductHierarchy.ParentId
LevelNum=rpt.ProductHierarchy.LevelNum
LevelName=rpt.ProductHierarchy.LevelName
ParentLevelName=rpt.ProductHierarchy.ParentLevelName
ProductNode=rpt.ProductHierarchy.ProductNode
ProductKey=rpt.ProductHierarchy.ProductKey
CreatedDate=rpt.ProductHierarchy.ETLCreatedDate
UpdatedDate=rpt.ProductHierarchy.ETLUpdatedDate
IsDeprecated=rpt.ProductHierarchy.IsDeprecated
ReferenceProductID=rpt.ProductHierarchy.ReferenceProductId
PSProductId=rpt.ProductHierarchy.PSProductId
*/
CREATE VIEW APIv1.ProductHierarchy
AS
SELECT
    ProductID = ProductId
  , DataSourceInstanceID = DataSourceInstanceId
  , ProductName
  , ParentID = ParentId
  , LevelNum
  , LevelName
  , ParentLevelName
  , ProductNode
  , ProductKey
  , CreatedDate = ETLCreatedDate
  , UpdatedDate = ETLUpdatedDate
  , IsDeprecated
  , ReferenceProductID = ReferenceProductId
  , PSProductId
FROM
    rpt.ProductHierarchy;