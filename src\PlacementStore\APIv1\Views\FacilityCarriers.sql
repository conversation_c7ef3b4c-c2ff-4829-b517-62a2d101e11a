/*
Lineage
FacilityId=ref.Facility.PSFacilityPolicyId
CarrierId=ref.FacilitySectionCarrier.CarrierId
Lead=ref.FacilitySectionCarrier.IsLead
Follow=ref.FacilitySectionCarrier.IsFollow
Follow=ref.FacilitySectionCarrier.IsLead
IsDeleted=ref.FacilitySectionCarrier.IsDeprecated
LastUpdatedUTCDate=ref.FacilitySectionCarrier.ETLUpdatedDate
*/
/* Can't find external reference to this. */
CREATE VIEW APIv1.FacilityCarriers
AS
SELECT
    FacilityId = F.PSFacilityPolicyId
  , CarrierId = FSC.CarrierId
  , Lead = MAX(CAST(FSC.IsLead AS INT))
  , Follow = CASE WHEN MAX(CAST(FSC.IsLead AS INT)) = 1
                      THEN 0
                  ELSE MAX(CAST(FSC.IsFollow AS INT)) END
  , IsDeleted = MIN(CAST(FSC.IsDeprecated AS INT))
  , LastUpdatedUTCDate = ISNULL(MAX(FSC.ETLUpdatedDate), GETUTCDATE())
FROM
    ref.FacilitySectionCarrier FSC
    INNER JOIN ref.FacilitySection FS
        ON FS.FacilitySectionId = FSC.FacilitySectionId

    INNER JOIN ref.Facility F
        ON F.FacilityId = FS.FacilityId
GROUP BY
    F.PSFacilityPolicyId
  , FSC.CarrierId;