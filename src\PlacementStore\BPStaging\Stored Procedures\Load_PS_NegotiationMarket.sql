/*
Lineage
PS.NegotiationMarket.NegotiationMarketKey=BP.SubmissionContainerMarket.Id
PS.NegotiationMarket.NegotiationMarketKey=BP.ExpiringResponseGroup.Id
PS.NegotiationMarket.NegotiationMarketKey=BP.AdjustmentResponseGroup.Id
PS.NegotiationMarket.NegotiationMarketKey=BPStaging.MarketSelection.Id
PS.NegotiationMarket.NegotiationMarketKey=BPStaging.CarrierResponse.Id
PS.NegotiationMarket.NegotiationId=PS.Negotiation.NegotiationId
PS.NegotiationMarket.PlacementId=PS.Negotiation.PlacementId
PS.NegotiationMarket.PlacementId=dbo.Placement.PlacementId
PS.NegotiationMarket.ParentId=PS.NegotiationMarket.NegotiationMarketId
PS.NegotiationMarket.MarketKindId=BP.SubmissionContainerMarket.MarketKindId
PS.NegotiationMarket.MarketKindId=BPStaging.MarketSelection.MarketKindId
PS.NegotiationMarket.MarketKindId=BPStaging.CarrierResponse.MarketKindId
PS.NegotiationMarket.IsIncumbent=BP.SubmissionContainerMarket.IsIncumbent
PS.NegotiationMarket.IsIncumbent=BPStaging.MarketSelection.Incumbent
PS.NegotiationMarket.IsLead=BP.SubmissionContainerMarket.IsLead
PS.NegotiationMarket.Sent=BP.SubmissionContainerMarket.Sent
PS.NegotiationMarket.ParentMarketKindId=BP.SubmissionContainerMarket.MarketKindId
PS.NegotiationMarket.IsChildMarketKind=BPStaging.MarketKind.IsChildMarketKind
PS.NegotiationMarket.IsThirdParty=BPStaging.MarketKind.IsThirdParty
PS.NegotiationMarket.CanDistribute=BPStaging.MarketKind.CanDistribute
PS.NegotiationMarket.CarrierId=dbo.Carrier.CarrierId
PS.NegotiationMarket.FacilityId=ref.Facility.FacilityId
PS.NegotiationMarket.FacilitySectionId=ref.FacilitySection.FacilitySectionId
PS.NegotiationMarket.PanelId=ref.Panel.PanelSK
PS.NegotiationMarket.PanelId=ref.PanelMember.PanelSK
PS.NegotiationMarket.PanelMemberId=ref.PanelMember.PanelMemberSK
PS.NegotiationMarket.PanelMemberName=ref.PanelMember.PanelMemberName
PS.NegotiationMarket.ThirdPartyCarrierId=dbo.Carrier.CarrierId
PS.NegotiationMarket.ThirdPartyMarketName=BP.SubmissionContainerThirdPartyMarket.Name
PS.NegotiationMarket.MarketKey=ref.Facility.PSFacilityPolicyId
PS.NegotiationMarket.MarketKey=ref.FacilitySection.FacilitySectionId
PS.NegotiationMarket.MarketKey=dbo.Carrier.PSCarrierId
PS.NegotiationMarket.MarketKey=ref.Panel.PanelSK
PS.NegotiationMarket.MarketKey=ref.PanelMember.PanelMemberSK
PS.NegotiationMarket.MarketKey=ref.MarketKind.MarketKind
PS.NegotiationMarket.MarketKey=BP.SubmissionContainerThirdPartyMarket.Name
PS.NegotiationMarket.UnderwriterName=BP.SubmissionContainerMarket.UnderwriterName
PS.NegotiationMarket.UnderwriterEmail=BP.SubmissionContainerMarket.UnderwriterEmail
PS.NegotiationMarket.IsClientRequest=BP.SubmissionContainerMarket.IsClientRequest
PS.NegotiationMarket.IsInsurerRequest=BP.SubmissionContainerMarket.IsInsurerRequest
PS.NegotiationMarket.IsBrokerExperience=BP.SubmissionContainerMarket.IsClientRequest
PS.NegotiationMarket.IsBrokerExperience=BP.SubmissionContainerMarket.IsInsurerRequest
PS.NegotiationMarket.IsBrokerExperience=BP.SubmissionContainerMarket.IsIncumbent
PS.NegotiationMarket.IsDeleted=BP.SubmissionContainerMarket.IsDeleted
PS.NegotiationMarket.IsDeleted=BP.SubmissionContainerCarrier.IsDeleted
PS.NegotiationMarket.IsDeleted=BP.SubmissionContainerFacility.IsDeleted
PS.NegotiationMarket.IsDeleted=BP.SubmissionContainerPanel.IsDeleted
PS.NegotiationMarket.IsDeleted=BP.SubmissionContainerPanelMember.IsDeleted
PS.NegotiationMarket.IsDeleted=BP.SubmissionContainerThirdPartyMarket.IsDeleted
PS.NegotiationMarket.IsDeleted=BP.ExpiringResponseGroup.IsDeleted
PS.NegotiationMarket.IsDeleted=BP.AdjustmentResponseGroup.IsDeleted
PS.NegotiationMarket.IsDeleted=BPStaging.MarketSelection.IsDeleted
PS.NegotiationMarket.IsDeleted=BPStaging.CarrierResponse.IsDeleted
PS.NegotiationMarket.SourceUpdatedDate=BP.SubmissionContainerMarket.SourceUpdatedDate
PS.NegotiationMarket.SourceUpdatedDate=BP.ExpiringResponseGroup.ETLUpdatedDate
PS.NegotiationMarket.SourceUpdatedDate=PS.Negotiation.ETLUpdatedDate
PS.NegotiationMarket.SourceUpdatedDate=BP.AdjustmentResponseGroup.ETLUpdatedDate
PS.NegotiationMarket.SourceUpdatedDate=BPStaging.MarketSelection.ValidFrom
PS.NegotiationMarket.SourceUpdatedDate=BPStaging.CarrierResponse.ValidFrom
PS.NegotiationMarket.SourceCarrierId=BP.SubmissionContainerCarrier.CarrierId
PS.NegotiationMarket.SourceCarrierId=BPStaging.MarketSelection.CarrierId
PS.NegotiationMarket.SourceCarrierId=dbo.Carrier.CarrierId
PS.NegotiationMarket.IsPrimaryLeadIncumbent=BP.SubmissionContainerMarket.IsPrimaryLeadIncumbent
PS.NegotiationMarket.SubmissionDate=BP.SubmissionContainerMarket.SubmissionDate
*/
CREATE PROCEDURE BPStaging.Load_PS_NegotiationMarket
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.NegotiationMarket';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY

    /*---------- Staging Table ----------*/
    DROP TABLE IF EXISTS #SourceForMerge;

    CREATE TABLE #SourceForMerge (
        DataSourceInstanceId   INT           NOT NULL
      , MarketCategory         NVARCHAR(50)  NOT NULL
      , NegotiationMarketKey   NVARCHAR(50)  COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
      , NegotiationId          INT           NOT NULL
      , PlacementId            BIGINT        NULL
      , SourceParentId         INT           NULL
      , ParentId               INT           NULL
      , MarketKindId           INT           NULL
      , IsIncumbent            BIT           NULL
      , IsLead                 BIT           NULL
      , Sent                   BIT           NULL
      , ParentMarketKindId     INT           NULL
      , IsChildMarketKind      BIT           NULL
      , IsThirdParty           BIT           NULL
      , CanDistribute          BIT           NULL
      , CarrierId              INT           NULL
      , FacilityId             BIGINT        NULL
      , FacilitySectionId      BIGINT        NULL
      , PanelId                INT           NULL
      , PanelMemberId          INT           NULL
      , PanelMemberName        NVARCHAR(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
      , ThirdPartyCarrierId    INT           NULL
      , ThirdPartyMarketName   NVARCHAR(355) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
      , MarketKey              NVARCHAR(255) NULL
      , UnderwriterName        NVARCHAR(200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
      , UnderwriterEmail       NVARCHAR(320) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
      , IsClientRequest        BIT           NULL
      , IsInsurerRequest       BIT           NULL
      , IsBrokerExperience     BIT           NULL
      , SourceCarrierId        INT           NULL
      , SourceUpdatedDate      DATETIME2(7)  NULL
      , IsDeleted              INT           NOT NULL
      , IsPrimaryLeadIncumbent BIT           NULL
      , SubmissionDate         DATETIME2(2)  NULL
    );

    /*----------- Submission Container Market Records -----------*/
    DROP TABLE IF EXISTS #scm_staged;

    SELECT
        scm.Id
      , scm.SubmissionContainerId
      , scm.ParentId
      , scm.MarketKindId
      , scm.IsIncumbent
      , scm.IsLead
      , scm.Sent
      , ParentMarketKindId = par.MarketKindId
      , mk.IsChildMarketKind
      , mk.IsThirdParty
      , mk.CanDistribute
      , scc.CarrierId
      , scf.FacilityId
      , FacilitySectionId = scf.SectionId
      , scp.PanelId
      , scpm.PanelMemberId
      , ThirdPartyCarrierId = sctp.CarrierId
      , ThirdPartyMarketName = sctp.Name
      , scm.UnderwriterName
      , scm.UnderwriterEmail
      , scm.IsClientRequest
      , scm.IsInsurerRequest
      , scm.SourceUpdatedDate
      , scm.IsPrimaryLeadIncumbent
      , scm.SubmissionDate
      , IsDeleted = GREATEST(
                        ISNULL(scm.IsDeleted, 0)
                      , ISNULL(scc.IsDeleted, 0)
                      , ISNULL(scf.IsDeleted, 0)
                      , ISNULL(scp.IsDeleted, 0)
                      , ISNULL(scpm.IsDeleted, 0)
                      , ISNULL(sctp.IsDeleted, 0)
                    )
    INTO #scm_staged
    FROM
        BP.SubmissionContainerMarket scm
        INNER JOIN BPStaging.MarketKind mk
            ON scm.MarketKindId = mk.Id

        LEFT JOIN BP.SubmissionContainerMarket par
            ON scm.ParentId = par.Id

        LEFT JOIN BP.SubmissionContainerCarrier scc
            ON scm.Id = scc.SubmissionContainerMarketId

        LEFT JOIN BP.SubmissionContainerFacility scf
            ON scm.Id = scf.SubmissionContainerMarketId

        LEFT JOIN BP.SubmissionContainerPanel scp
            ON scm.Id = scp.SubmissionContainerMarketId

        LEFT JOIN BP.SubmissionContainerPanelMember scpm
            ON scm.Id = scpm.SubmissionContainerMarketId

        LEFT JOIN BP.SubmissionContainerThirdPartyMarket sctp
            ON scm.Id = sctp.SubmissionContainerMarketId;

    /* Staged Records */
    INSERT INTO
        #SourceForMerge
        (
            DataSourceInstanceId
          , MarketCategory
          , NegotiationMarketKey
          , NegotiationId
          , PlacementId
          , SourceParentId
          , ParentId
          , MarketKindId
          , IsIncumbent
          , IsLead
          , Sent
          , ParentMarketKindId
          , IsChildMarketKind
          , IsThirdParty
          , CanDistribute
          , CarrierId
          , FacilityId
          , FacilitySectionId
          , PanelId
          , PanelMemberId
          , PanelMemberName
          , ThirdPartyCarrierId
          , ThirdPartyMarketName
          , MarketKey
          , UnderwriterName
          , UnderwriterEmail
          , IsClientRequest
          , IsInsurerRequest
          , IsBrokerExperience
          , SourceCarrierId
          , SourceUpdatedDate
          , IsDeleted
          , IsPrimaryLeadIncumbent
          , SubmissionDate
        )
    SELECT
        DataSourceInstanceId = 50366
      , MarketCategory = 'SubmissionMarket'
      , NegotiationMarketKey = CONCAT('SUBCONMKT|', scm.Id)
      , N.NegotiationId
      , N.PlacementId
      , SourceParentId = scm.ParentId
      , ParentId = nm.NegotiationMarketId
      , scm.MarketKindId
      , scm.IsIncumbent
      , scm.IsLead
      , scm.Sent
      , scm.ParentMarketKindId
      , scm.IsChildMarketKind
      , scm.IsThirdParty
      , scm.CanDistribute
      , CarrierId = c.CarrierId
      , fa.FacilityId
      , fs.FacilitySectionId
      , PanelId = ISNULL(p.PanelSK, pm.PanelSK)
      , PanelMemberId = pm.PanelMemberSK
      , PanelMemberName = pm.PanelMemberName
      , ThirdPartyCarrierId = tpc.CarrierId
      , scm.ThirdPartyMarketName
      , MarketKey = CASE WHEN mk.MarketKind = 'Facility'
                             THEN 'Facility-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId)
                         WHEN mk.MarketKind = 'Facility Member'
                             THEN 'FacilityMember-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId) + '-'
                                  + CONVERT(VARCHAR(20), fs.FacilitySectionId) + '-'
                                  + CONVERT(VARCHAR(20), c.PSCarrierId)
                         WHEN mk.MarketKind = 'Panel'
                             THEN 'Panel-' + CONVERT(VARCHAR(20), p.PanelSK)
                         WHEN mk.MarketKind = 'Panel Member'
                              AND fa.PSFacilityPolicyId IS NULL
                             THEN 'PanelMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK)
                         WHEN mk.MarketKind = 'Panel Member'
                              AND fa.PSFacilityPolicyId IS NOT NULL
                             THEN 'PanelMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK) + '-'
                                   + CONVERT(NVARCHAR(9), fa.PSFacilityPolicyId)
                         WHEN mk.MarketKind = 'Auto-Follow'
                             THEN 'AutoFollow-' + CONVERT(VARCHAR(20), p.PanelSK)
                         WHEN mk.MarketKind = 'Auto-Follow Member'
                              AND fa.PSFacilityPolicyId IS NULL
                             THEN 'AutoFollowMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK)
                         WHEN mk.MarketKind = 'Auto-Follow Member'
                              AND fa.PSFacilityPolicyId IS NOT NULL
                             THEN 'AutoFollowMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK) + '-'
                                   + CONVERT(NVARCHAR(9), fa.PSFacilityPolicyId)
                         WHEN mk.MarketKindId > 5
                              AND (
                                  scm.IsThirdParty = 1
                                  OR scm.IsChildMarketKind = 1
                              )
                             THEN REPLACE(mk.MarketKind, ' ', '') + '-'
                                  + COALESCE(
                                        CONVERT(NVARCHAR(20), tpc.PSCarrierId)
                                      , SUBSTRING(scm.ThirdPartyMarketName, 1, 30)
                                    )
                         ELSE 'Carrier-' + CONVERT(VARCHAR(20), c.PSCarrierId) --add catch-all
                    END
      , scm.UnderwriterName
      , scm.UnderwriterEmail
      , scm.IsClientRequest
      , scm.IsInsurerRequest
      , IsBrokerExperience = CASE WHEN scm.IsClientRequest = 1
                                       OR scm.IsInsurerRequest = 1
                                       OR scm.IsIncumbent = 1
                                      THEN 0
                                  ELSE 1 END
      , SourceCarrierId = scm.CarrierId
      , SourceUpdatedDate = scm.SourceUpdatedDate
      , IsDeleted = scm.IsDeleted
      , scm.IsPrimaryLeadIncumbent
      , scm.SubmissionDate
    FROM
        #scm_staged scm
        LEFT JOIN dbo.Carrier c /* To Map from BP CarrierId to PS CarrierId */
            ON c.PSCarrierId = scm.CarrierId
               AND c.CarrierTypeId <> 4 /* IM Carrier causes duplicates but isn't sent to BP */

        LEFT JOIN ref.Facility fa
            ON fa.PSFacilityPolicyId = scm.FacilityId

        LEFT JOIN ref.FacilitySection fs
            ON fs.PSFacilityPolicySectionId = scm.FacilitySectionId

        LEFT JOIN ref.PanelMember pm
            ON pm.PanelMemberKey = scm.PanelMemberId
               AND pm.DataSourceInstanceId = 50463

        LEFT JOIN ref.Panel p
            ON p.PanelKey = scm.PanelId
               AND p.DataSourceInstanceId = 50463

        LEFT JOIN dbo.Carrier tpc /* To Map from BP CarrierId to PS CarrierId */
            ON tpc.PSCarrierId = scm.ThirdPartyCarrierId
               AND tpc.CarrierTypeId <> 4 /* IM Carrier causes duplicates but isn't sent to BP */

        LEFT JOIN PS.NegotiationMarket nm
            ON CONCAT('SUBCONMKT|', scm.ParentId) = nm.NegotiationMarketKey

        LEFT JOIN ref.MarketKind mk
            ON mk.MarketKindId = scm.MarketKindId

        INNER JOIN PS.Negotiation N
            ON N.NegotiationKey = CONCAT('SUBC|', scm.SubmissionContainerId)
    UNION ALL
    SELECT
        DataSourceInstanceId = 50366
      , MarketCategory = 'ExpiringMarket'
      , NegotiationMarketKey = CONCAT('EXPRESPMKT|', erg.Id)
      , N.NegotiationId
      , N.PlacementId
      , SourceParentId = NULL
      , ParentId = NULL
      , MarketKindId = NULL
      , IsIncumbent = NULL
      , IsLead = NULL
      , Sent = NULL
      , ParentMarketKindId = NULL
      , IsChildMarketKind = NULL
      , IsThirdParty = NULL
      , CanDistribute = NULL
      , CarrierId = c.CarrierId
      , fa.FacilityId
      , FacilitySectionId = NULL
      , PanelId = pm.PanelSK
      , PanelMemberId = pm.PanelMemberSK
      , pm.PanelMemberName
      , ThirdPartyCarrierId = NULL
      , ThirdPartyMarketName = NULL
      , MarketKey = CASE WHEN erg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NOT NULL
                              AND fa.FacilityId IS NULL
                             THEN 'AutoFollowMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK)
                         WHEN erg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NOT NULL
                              AND fa.FacilityId IS NOT NULL
                             THEN 'AutoFollowMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK) + '-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId)
                         WHEN erg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NULL
                              AND fa.FacilityId IS NULL
                             THEN 'PanelMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK)
                         WHEN erg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NULL
                              AND fa.FacilityId IS NOT NULL
                             THEN 'PanelMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK) + '-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId)
                         WHEN fa.FacilityId IS NOT NULL
                             THEN 'Facility-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId)
                         ELSE 'Carrier-' + CONVERT(VARCHAR(20), c.PSCarrierId) --add catch-all
                    END
      , UnderwriterName = NULL
      , UnderwriterEmail = NULL
      , IsClientRequest = NULL
      , IsInsurerRequest = NULL
      , IsBrokerExperience = NULL
      , SourceCarrierId = NULL
      , SourceUpdatedDate = (
            SELECT MAX(value.v) FROM (VALUES (erg.ETLUpdatedDate), (N.ETLUpdatedDate)) value (v)
        )
      , IsDeleted = erg.IsDeleted
      , IsPrimaryLeadIncumbent = NULL
      , SubmissionDate = NULL
    FROM
        BP.ExpiringResponseGroup erg
        INNER JOIN PS.Negotiation N
            ON N.NegotiationKey = CONCAT('EXPRESP|', erg.Id)

        LEFT JOIN ref.Facility fa
            ON fa.PSFacilityPolicyId = erg.FacilityId

        LEFT JOIN dbo.Carrier c /* To Map from BP CarrierId to PS CarrierId */
            ON c.PSCarrierId = erg.CarrierId
               AND c.CarrierTypeId <> 4 /* IM Carrier causes duplicates but isn't sent to BP */

        LEFT JOIN ref.PanelMember pm
            ON pm.PanelMemberKey = erg.PanelMemberId
               AND pm.DataSourceInstanceId = 50463

        LEFT JOIN ref.Panel p
            ON p.PanelSK = pm.PanelSK
               AND p.IsDeprecated = 0
    UNION ALL
    SELECT
        DataSourceInstanceId = 50366
      , MarketCategory = 'MTA'
      , NegotiationMarketKey = CONCAT('ADJRESPMKT|', arg.Id)
      , N.NegotiationId
      , N.PlacementId
      , SourceParentId = NULL
      , ParentId = NULL
      , MarketKindId = NULL
      , IsIncumbent = NULL
      , IsLead = NULL
      , Sent = NULL
      , ParentMarketKindId = NULL
      , IsChildMarketKind = NULL
      , IsThirdParty = NULL
      , CanDistribute = NULL
      , CarrierId = c.CarrierId
      , fa.FacilityId
      , FacilitySectionId = NULL
      , PanelId = pm.PanelSK
      , PanelMemberId = pm.PanelMemberSK
      , pm.PanelMemberName
      , ThirdPartyCarrierId = NULL
      , ThirdPartyMarketName = NULL
      , MarketKey = CASE WHEN arg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NOT NULL
                              AND fa.FacilityId IS NULL
                             THEN 'AutoFollowMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK)
                         WHEN arg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NOT NULL
                              AND fa.FacilityId IS NOT NULL
                             THEN 'AutoFollowMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK) + '-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId)
                         WHEN arg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NULL
                              AND fa.FacilityId IS NULL
                             THEN 'PanelMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK)
                         WHEN arg.PanelMemberId IS NOT NULL
                              AND p.AutoFollowKey IS NULL
                              AND fa.FacilityId IS NOT NULL
                             THEN 'PanelMember-' + CONVERT(VARCHAR(20), pm.PanelMemberSK) + '-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId)
                         WHEN fa.FacilityId IS NOT NULL
                             THEN 'Facility-' + CONVERT(VARCHAR(20), fa.PSFacilityPolicyId)
                         ELSE 'Carrier-' + CONVERT(VARCHAR(20), c.PSCarrierId) --add catch-all
                    END
      , UnderwriterName = NULL
      , UnderwriterEmail = NULL
      , IsClientRequest = NULL
      , IsInsurerRequest = NULL
      , IsBrokerExperience = NULL
      , SourceCarrierId = NULL
      , SourceUpdatedDate = (
            SELECT MAX(value.v) FROM (VALUES (arg.ETLUpdatedDate), (N.ETLUpdatedDate)) value (v)
        )
      , IsDeleted = arg.IsDeleted
      , IsPrimaryLeadIncumbent = NULL
      , SubmissionDate = NULL
    FROM
        BP.AdjustmentResponseGroup arg
        INNER JOIN PS.Negotiation N
            ON N.NegotiationKey = CONCAT('ADJRESP|', arg.Id)

        LEFT JOIN ref.Facility fa
            ON fa.PSFacilityPolicyId = arg.FacilityId

        LEFT JOIN dbo.Carrier c /* To Map from BP CarrierId to PS CarrierId */
            ON c.PSCarrierId = arg.CarrierId
               AND c.CarrierTypeId <> 4 /* IM Carrier causes duplicates but isn't sent to BP */

        LEFT JOIN ref.PanelMember pm
            ON pm.PanelMemberKey = arg.PanelMemberId
               AND pm.DataSourceInstanceId = 50463

        LEFT JOIN ref.Panel p
            ON p.PanelSK = pm.PanelSK
               AND p.IsDeprecated = 0;

    /*----------- Market Selection Records -----------*/
    DROP TABLE IF EXISTS #RankedMarketSelection;

    SELECT
        rms.Id
      , rms.PlacementId
      , rms.MarketKindId
      , rms.CarrierId
      , rms.PSCarrierId
      , rms.FacilityId
      , rms.PSFacilityPolicyId
      , rms.IsIncumbent
      , rms.IsDeleted
    INTO #RankedMarketSelection
    FROM (
        SELECT
            ms.Id
          , pl.PlacementId
          , ms.MarketKindId
          , CarrierId = C.CarrierId
          , C.PSCarrierId
          , fa.FacilityId
          , fa.PSFacilityPolicyId
          , IsIncumbent = ms.Incumbent
          , ms.IsDeleted
          , ROW_NO = ROW_NUMBER() OVER (PARTITION BY pl.PlacementId, ms.MarketKindId, C.CarrierId, ms.FacilityId ORDER BY ms.IsDeleted ASC, ms.Id DESC)
        FROM
            BPStaging.MarketSelection ms
            INNER JOIN dbo.Placement pl
                ON pl.PlacementSystemId = ms.PlacementId
                   AND pl.DataSourceInstanceId = 50366

            LEFT JOIN ref.Facility fa
                ON fa.PSFacilityPolicyId = ms.FacilityId

            LEFT JOIN dbo.Carrier C /* To Map from BP CarrierId to PS CarrierId */
                ON C.PSCarrierId = ms.CarrierId
                   AND C.CarrierTypeId <> 4 /* IM Carrier causes duplicates but isn't sent to BP */
    ) rms
    WHERE
        rms.ROW_NO = 1;

    CREATE INDEX IX_#RankedMarketSelection
    ON #RankedMarketSelection
    (
        Id
      , PlacementId
      , MarketKindId
      , CarrierId
      , FacilityId
    );

    CREATE INDEX IX_#RankedMarketSelection2
    ON #RankedMarketSelection
    (
        PlacementId
      , MarketKindId
      , CarrierId
      , FacilityId
    );

    DROP TABLE IF EXISTS #RankedCarrierResponse;

    SELECT
        rcr.Id
      , rcr.PlacementId
      , rcr.MarketKindId
      , rcr.CarrierId
      , rcr.PSCarrierId
      , rcr.FacilityId
      , rcr.PSFacilityPolicyId
      , rcr.IsDeleted
    INTO #RankedCarrierResponse
    FROM (
        SELECT
            cr.Id
          , pl.PlacementId
          , cr.MarketKindId
          , CarrierId = c.CarrierId
          , c.PSCarrierId
          , fa.FacilityId
          , fa.PSFacilityPolicyId
          , cr.IsDeleted
          , ROW_NO = ROW_NUMBER() OVER (PARTITION BY pl.PlacementId, cr.MarketKindId, c.CarrierId, cr.FacilityId ORDER BY cr.IsDeleted ASC, cr.Id DESC)
        FROM
            BPStaging.CarrierResponse cr
            INNER JOIN dbo.Placement pl
                ON pl.PlacementSystemId = cr.PlacementId
                   AND pl.DataSourceInstanceId = 50366

            LEFT JOIN ref.Facility fa
                ON fa.PSFacilityPolicyId = cr.FacilityId

            LEFT JOIN dbo.Carrier c /* To Map from BP CarrierId to PS CarrierId */
                ON c.PSCarrierId = cr.CarrierId
                   AND c.CarrierTypeId <> 4 /* IM Carrier causes duplicates but isn't sent to BP */
    ) rcr
    WHERE
        rcr.ROW_NO = 1;

    CREATE INDEX IX_#RankedCarrierResponse
    ON #RankedCarrierResponse
    (
        Id
      , PlacementId
      , MarketKindId
      , CarrierId
      , FacilityId
    );

    CREATE INDEX IX_#RankedCarrierResponse2
    ON #RankedCarrierResponse
    (
        PlacementId
      , MarketKindId
      , CarrierId
      , FacilityId
    );

    /* Market Selection Staged Records */
    INSERT INTO
        #SourceForMerge
        (
            DataSourceInstanceId
          , MarketCategory
          , NegotiationMarketKey
          , NegotiationId
          , PlacementId
          , SourceParentId
          , ParentId
          , MarketKindId
          , IsIncumbent
          , IsLead
          , Sent
          , ParentMarketKindId
          , IsChildMarketKind
          , IsThirdParty
          , CanDistribute
          , CarrierId
          , FacilityId
          , FacilitySectionId
          , PanelId
          , PanelMemberId
          , PanelMemberName
          , ThirdPartyCarrierId
          , ThirdPartyMarketName
          , MarketKey
          , UnderwriterName
          , UnderwriterEmail
          , IsClientRequest
          , IsInsurerRequest
          , IsBrokerExperience
          , SourceCarrierId
          , SourceUpdatedDate
          , IsDeleted
          , IsPrimaryLeadIncumbent
          , SubmissionDate
        )
    SELECT
        DataSourceInstanceId = 50366
      , MarketCategory = 'MarketSelection'
      , NegotiationMarketKey = CONCAT('MKTSEL|', rms.Id)
      , n.NegotiationId
      , rms.PlacementId
      , SourceParentId = NULL
      , ParentId = NULL
      , rms.MarketKindId
      , IsIncumbent = ms.Incumbent
      , IsLead = NULL
      , Sent = NULL
      , ParentMarketKindId = NULL
      , IsChildMarketKind = NULL
      , IsThirdParty = NULL
      , CanDistribute = NULL
      , rms.CarrierId
      , rms.FacilityId
      , FacilitySectionId = NULL
      , PanelId = NULL
      , PanelMemberId = NULL
      , PanelMemberName = CAST(NULL AS NVARCHAR(200))
      , ThirdPartyCarrierId = NULL
      , ThirdPartyMarketName = CAST(NULL AS NVARCHAR(355))
      , MarketKey = CASE WHEN rms.FacilityId IS NOT NULL
                             THEN 'Facility-' + CONVERT(VARCHAR(20), rms.PSFacilityPolicyId)
                         ELSE 'Carrier-' + CONVERT(VARCHAR(20), rms.PSCarrierId) --add catch-all
                    END
      , UnderwriterName = CAST(NULL AS NVARCHAR(200))
      , UnderwriterEmail = CAST(NULL AS NVARCHAR(320))
      , IsClientRequest = NULL
      , IsInsurerRequest = NULL
      , IsBrokerExperience = NULL
      , SourceCarrierId = ms.CarrierId
      , SourceUpdatedDate = ms.ValidFrom
      , IsDeleted = CASE WHEN ms.IsDeleted = 1
                              AND ISNULL(rcr.IsDeleted, 1) = 1 /* Deleted and No active Response */
                             THEN 1
                         ELSE 0 END
      , IsPrimaryLeadIncumbent = NULL
      , SubmissionDate = NULL
    FROM
        #RankedMarketSelection rms
        INNER JOIN BPStaging.MarketSelection ms
            ON ms.Id = rms.Id

        INNER JOIN PS.Negotiation n
            ON n.PlacementId = rms.PlacementId
               AND n.NegotiationKey LIKE 'PLAC|%'

        LEFT JOIN #RankedCarrierResponse rcr
            ON rcr.PlacementId = rms.PlacementId
               AND rcr.MarketKindId = rms.MarketKindId
               AND ISNULL(rcr.CarrierId, 0) = ISNULL(rms.CarrierId, 0)
               AND ISNULL(rcr.FacilityId, 0) = ISNULL(rms.FacilityId, 0)
    WHERE
        NOT EXISTS (
        SELECT 1
        FROM
            #SourceForMerge sfm
        WHERE
            sfm.PlacementId = rms.PlacementId
            AND sfm.MarketKindId = rms.MarketKindId
            AND ISNULL(sfm.CarrierId, 0) = ISNULL(rms.CarrierId, 0)
            AND ISNULL(sfm.FacilityId, 0) = ISNULL(rms.FacilityId, 0)
    );

    DROP TABLE IF EXISTS #RankedMarketSelection;

    /*---------- CarrierResponse records ----------*/
    DROP TABLE IF EXISTS #WantedCarrierResponse;

    SELECT
        rcr.Id
      , rcr.PlacementId
      , rcr.MarketKindId
      , rcr.CarrierId
      , rcr.PSCarrierId
      , rcr.FacilityId
      , rcr.PSFacilityPolicyId
    INTO #WantedCarrierResponse
    FROM
        #RankedCarrierResponse rcr
    WHERE
        NOT EXISTS (
        SELECT 1
        FROM
            #SourceForMerge sfm
        WHERE
            sfm.PlacementId = rcr.PlacementId
            AND sfm.MarketKindId = rcr.MarketKindId
            AND ISNULL(sfm.CarrierId, 0) = ISNULL(rcr.CarrierId, 0)
            AND ISNULL(sfm.FacilityId, 0) = ISNULL(rcr.FacilityId, 0)
    );

    DROP TABLE IF EXISTS #RankedCarrierResponse;

    INSERT INTO
        #SourceForMerge
        (
            DataSourceInstanceId
          , MarketCategory
          , NegotiationMarketKey
          , NegotiationId
          , PlacementId
          , SourceParentId
          , ParentId
          , MarketKindId
          , IsIncumbent
          , IsLead
          , Sent
          , ParentMarketKindId
          , IsChildMarketKind
          , IsThirdParty
          , CanDistribute
          , CarrierId
          , FacilityId
          , FacilitySectionId
          , PanelId
          , PanelMemberId
          , PanelMemberName
          , ThirdPartyCarrierId
          , ThirdPartyMarketName
          , MarketKey
          , UnderwriterName
          , UnderwriterEmail
          , IsClientRequest
          , IsInsurerRequest
          , IsBrokerExperience
          , SourceCarrierId
          , SourceUpdatedDate
          , IsDeleted
          , IsPrimaryLeadIncumbent
          , SubmissionDate
        )
    SELECT
        DataSourceInstanceId = 50366
      , MarketCategory = 'CarrierResponse'
      , NegotiationMarketKey = CONCAT('CARRES|', rcr.Id)
      , n.NegotiationId
      , rcr.PlacementId
      , SourceParentId = NULL
      , ParentId = NULL
      , rcr.MarketKindId
      , IsIncumbent = NULL
      , IsLead = NULL
      , Sent = NULL
      , ParentMarketKindId = NULL
      , IsChildMarketKind = NULL
      , IsThirdParty = NULL
      , CanDistribute = NULL
      , rcr.CarrierId
      , rcr.FacilityId
      , FacilitySectionId = NULL
      , PanelId = NULL
      , PanelMemberId = NULL
      , PanelMemberName = CAST(NULL AS NVARCHAR(200))
      , ThirdPartyCarrierId = NULL
      , ThirdPartyMarketName = CAST(NULL AS NVARCHAR(200))
      , MarketKey = CASE WHEN rcr.FacilityId IS NOT NULL
                             THEN 'Facility-' + CONVERT(VARCHAR(20), rcr.PSFacilityPolicyId)
                         ELSE 'Carrier-' + CONVERT(VARCHAR(20), rcr.PSCarrierId) --add catch-all
                    END
      , UnderwriterName = CAST(NULL AS NVARCHAR(200))
      , UnderwriterEmail = CAST(NULL AS NVARCHAR(640))
      , IsClientRequest = NULL
      , IsInsurerRequest = NULL
      , IsBrokerExperience = NULL
      , SourceCarrierId = rcr.CarrierId
      , CR.ValidFrom
      , CR.IsDeleted
      , IsPrimaryLeadIncumbent = NULL
      , SubmissionDate = NULL
    FROM
        #WantedCarrierResponse rcr
        INNER JOIN BPStaging.CarrierResponse CR
            ON CR.Id = rcr.Id

        INNER JOIN PS.Negotiation n
            ON n.PlacementId = rcr.PlacementId
               AND n.NegotiationKey LIKE 'PLAC|%';

    DROP TABLE IF EXISTS #WantedCarrierResponse;

    CREATE UNIQUE INDEX IXU_#SourceForMerge
    ON #SourceForMerge
    (
        DataSourceInstanceId
      , NegotiationMarketKey
    );

    /* Do the merge */
    MERGE PS.NegotiationMarket t
    USING #SourceForMerge s
    ON t.DataSourceInstanceId = s.DataSourceInstanceId
       AND t.NegotiationMarketKey = s.NegotiationMarketKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , MarketCategory
               , NegotiationMarketKey
               , NegotiationId
               , PlacementId
               , ParentId
               , MarketKindId
               , IsIncumbent
               , IsLead
               , Sent
               , ParentMarketKindId
               , IsChildMarketKind
               , IsThirdParty
               , CanDistribute
               , CarrierId
               , FacilityId
               , FacilitySectionId
               , PanelId
               , PanelMemberId
               , PanelMemberName
               , ThirdPartyCarrierId
               , ThirdPartyMarketName
               , MarketKey
               , UnderwriterName
               , UnderwriterEmail
               , IsClientRequest
               , IsInsurerRequest
               , IsBrokerExperience
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
               , SourceUpdatedDate
               , SourceCarrierId
               , IsPrimaryLeadIncumbent
               , SubmissionDate
             )
             VALUES
                 (
                     s.DataSourceInstanceId
                   , s.MarketCategory
                   , s.NegotiationMarketKey
                   , s.NegotiationId
                   , s.PlacementId
                   , s.ParentId
                   , s.MarketKindId
                   , s.IsIncumbent
                   , s.IsLead
                   , s.Sent
                   , s.ParentMarketKindId
                   , s.IsChildMarketKind
                   , s.IsThirdParty
                   , s.CanDistribute
                   , s.CarrierId
                   , s.FacilityId
                   , s.FacilitySectionId
                   , s.PanelId
                   , s.PanelMemberId
                   , s.PanelMemberName
                   , s.ThirdPartyCarrierId
                   , s.ThirdPartyMarketName
                   , s.MarketKey
                   , s.UnderwriterName
                   , s.UnderwriterEmail
                   , s.IsClientRequest
                   , s.IsInsurerRequest
                   , s.IsBrokerExperience
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , s.IsDeleted
                   , s.SourceUpdatedDate
                   , s.SourceCarrierId
                   , ISNULL(s.IsPrimaryLeadIncumbent, 0)
                   , s.SubmissionDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.MarketCategory
                               , S.NegotiationId
                               , S.PlacementId
                               , S.ParentId
                               , S.MarketKindId
                               , S.IsIncumbent
                               , S.IsLead
                               , S.Sent
                               , S.ParentMarketKindId
                               , S.IsChildMarketKind
                               , S.IsThirdParty
                               , S.CanDistribute
                               , S.CarrierId
                               , S.FacilityId
                               , S.FacilitySectionId
                               , S.PanelId
                               , S.PanelMemberId
                               , S.PanelMemberName
                               , S.ThirdPartyCarrierId
                               , S.ThirdPartyMarketName
                               , S.MarketKey
                               , S.UnderwriterName
                               , S.UnderwriterEmail
                               , S.IsClientRequest
                               , S.IsInsurerRequest
                               , S.IsBrokerExperience
                               , S.IsDeleted
                               , S.SourceCarrierId
                               , ISNULL(S.IsPrimaryLeadIncumbent, 0)
                               , S.SubmissionDate
                             INTERSECT
                             SELECT
                                 T.MarketCategory
                               , T.NegotiationId
                               , T.PlacementId
                               , T.ParentId
                               , T.MarketKindId
                               , T.IsIncumbent
                               , T.IsLead
                               , T.Sent
                               , T.ParentMarketKindId
                               , T.IsChildMarketKind
                               , T.IsThirdParty
                               , T.CanDistribute
                               , T.CarrierId
                               , T.FacilityId
                               , T.FacilitySectionId
                               , T.PanelId
                               , T.PanelMemberId
                               , T.PanelMemberName
                               , T.ThirdPartyCarrierId
                               , T.ThirdPartyMarketName
                               , T.MarketKey
                               , T.UnderwriterName
                               , T.UnderwriterEmail
                               , T.IsClientRequest
                               , T.IsInsurerRequest
                               , T.IsBrokerExperience
                               , T.IsDeleted
                               , T.SourceCarrierId
                               , T.IsPrimaryLeadIncumbent
                               , T.SubmissionDate
                         )
        THEN UPDATE SET
                 t.MarketCategory = s.MarketCategory
               , t.NegotiationId = s.NegotiationId
               , t.PlacementId = s.PlacementId
               , t.ParentId = s.ParentId
               , t.MarketKindId = s.MarketKindId
               , t.IsIncumbent = s.IsIncumbent
               , t.IsLead = s.IsLead
               , t.Sent = s.Sent
               , t.ParentMarketKindId = s.ParentMarketKindId
               , t.IsChildMarketKind = s.IsChildMarketKind
               , t.IsThirdParty = s.IsThirdParty
               , t.CanDistribute = s.CanDistribute
               , t.CarrierId = s.CarrierId
               , t.FacilityId = s.FacilityId
               , t.FacilitySectionId = s.FacilitySectionId
               , t.PanelId = s.PanelId
               , t.PanelMemberId = s.PanelMemberId
               , t.PanelMemberName = s.PanelMemberName
               , t.ThirdPartyCarrierId = s.ThirdPartyCarrierId
               , t.ThirdPartyMarketName = s.ThirdPartyMarketName
               , t.MarketKey = s.MarketKey
               , t.UnderwriterName = s.UnderwriterName
               , t.UnderwriterEmail = s.UnderwriterEmail
               , t.IsClientRequest = s.IsClientRequest
               , t.IsInsurerRequest = s.IsInsurerRequest
               , t.IsBrokerExperience = s.IsBrokerExperience
               , t.SourceCarrierId = s.SourceCarrierId
               , t.IsDeleted = s.IsDeleted
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.IsPrimaryLeadIncumbent = ISNULL(s.IsPrimaryLeadIncumbent, 0)
               , t.SubmissionDate = s.SubmissionDate
    WHEN NOT MATCHED BY SOURCE AND t.IsDeleted = 0
        THEN UPDATE SET
                 t.IsDeleted = 1
               , t.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    -- Update the parent IDs once negotiation market has been created.
    UPDATE T
    SET
        T.ParentId = S.NegotiationMarketId
      , T.ETLUpdatedDate = GETUTCDATE()
    FROM
        PS.NegotiationMarket T
        INNER JOIN #SourceForMerge link
            ON link.NegotiationMarketKey = T.NegotiationMarketKey
               AND link.IsDeleted = 0

        LEFT JOIN PS.NegotiationMarket S
            ON S.NegotiationMarketKey = CONCAT('SUBCONMKT|', link.SourceParentId)
               AND S.DataSourceInstanceId = 50366
    WHERE
        T.DataSourceInstanceId = 50366
        AND NOT EXISTS (
        SELECT T.ParentId INTERSECT SELECT S.NegotiationMarketId
    );

    SELECT @UpdatedCount = @UpdatedCount + @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

DROP TABLE IF EXISTS #SourceForMerge;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;