/*
Lineage
dbo.PlacementTeams.PlacementId=dbo.Placement.PlacementId
dbo.PlacementTeams.TeamId=ref.Team.TeamId
dbo.PlacementTeams.DataSourceInstanceId=dbo.Placement.ServicingPlatformId
dbo.PlacementTeams.IsDeleted=BPStaging.PlacementTeams.IsDeleted
*/
CREATE PROCEDURE BPStaging.LoadPlacementTeams
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementTeams';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.PlacementTeams
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE dbo.PlacementTeams T
    USING (
        SELECT
            P.PlacementId
          , T.TeamId
          , DataSourceInstanceId = P.ServicingPlatformId
          , PT.IsDeleted
        FROM
            BPStaging.PlacementTeams PT
            INNER JOIN dbo.Placement P
                ON P.PlacementSystemId = PT.PlacementId
                   AND P.DataSourceInstanceId = 50366

            INNER JOIN ref.Team T
                ON T.TeamId = PT.OrganisationId
    ) S
    ON T.PlacementId = S.PlacementId
       AND T.TeamId = S.TeamId
    WHEN NOT MATCHED
        THEN INSERT (
                 PlacementId
               , TeamId
               , DataSourceInstanceId
               , IsDeleted
             )
             VALUES
                 (
                     S.PlacementId
                   , S.TeamId
                   , S.DataSourceInstanceId
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.PlacementId
      , T.TeamId
      , T.DataSourceInstanceId
      , T.IsDeleted
    INTERSECT
    SELECT
        S.PlacementId
      , S.TeamId
      , S.DataSourceInstanceId
      , S.IsDeleted
)
        THEN UPDATE SET
                 T.PlacementId = S.PlacementId
               , T.TeamId = S.TeamId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.UpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.UpdatedDate = GETUTCDATE()
               , T.IsDeleted = 1
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);