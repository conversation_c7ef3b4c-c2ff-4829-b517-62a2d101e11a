/*
Lineage
ref.MTAType.MTATypeKey=BPStaging.MTAType.Id
ref.MTAType.TranslationKey=BPStaging.MTAType.LabelTranslationKey
ref.MTAType.MTAType=BPStaging.MTAType.Text
ref.MTAType.IsDeprecated=BPStaging.MTAType.IsDeprecated
ref.MTAType.SourceUpdatedDate=BPStaging.MTAType.ValidTo
ref.MTAType.SourceUpdatedDate=BPStaging.MTAType.ValidFrom
ref.MTAType.IsDeleted=BPStaging.MTAType.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_ref_MTAType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.MTAType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.MTAType t
    USING (
        SELECT
            Id
          , DataSourceInstanceId = 50366
          , TranslationKey
          , MTAType
          , IsDeprecated
          , SourceUpdatedDate = IIF(YEAR(ValidTo) < 9999, ValidTo, ValidFrom)
          , IsDeleted = IIF(YEAR(ValidTo) < 9999, 1, 0)
        FROM (
        SELECT
            Id
          , TranslationKey = LabelTranslationKey
          , MTAType = Text
          , IsDeprecated
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.MTAType
    ) s
        WHERE
            s.RowNo = 1
    ) s
    ON s.Id = t.MTATypeKey
       AND s.DataSourceInstanceId = t.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 MTATypeKey
               , TranslationKey
               , MTAType
               , IsDeprecated
               , DataSourceInstanceId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.Id
                   , s.TranslationKey
                   , s.MTAType
                   , s.IsDeprecated
                   , s.DataSourceInstanceId
                   , s.SourceUpdatedDate
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.TranslationKey
                               , s.MTAType
                               , s.IsDeprecated
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.TranslationKey
                               , t.MTAType
                               , t.IsDeprecated
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.TranslationKey = s.TranslationKey
               , t.MTAType = s.MTAType
               , t.IsDeprecated = s.IsDeprecated
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.IsDeleted = s.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;