/*
Lineage
BPStaging.PS_Staging_RiskStructurePolicy.ContractId=PS.Contract.ContractId
BPStaging.PS_Staging_RiskStructurePolicy.PlacementId=dbo.Placement.PlacementId
BPStaging.PS_Staging_RiskStructurePolicy.BrokerCode=ref.LegalEntity.BrokerCode
BPStaging.PS_Staging_RiskStructurePolicy.PolicyReference=dbo.Policy.PolicyReference
BPStaging.PS_Staging_RiskStructurePolicy.PolicyReference=PS.Contract.Reference
BPStaging.PS_Staging_RiskStructurePolicy.PolicyId=dbo.Policy.PolicyId
BPStaging.PS_Staging_RiskStructurePolicy.RiskProfileId=PS.ContractRiskProfile.RiskProfileId
BPStaging.PS_Staging_RiskStructurePolicy.LayerTypeId=PS.Contract.LayerTypeId
BPStaging.PS_Staging_RiskStructurePolicy.LimitCurrencyId=PS.Contract.LimitCurrencyTypeId
BPStaging.PS_Staging_RiskStructurePolicy.Limit=PS.Contract.Limit
BPStaging.PS_Staging_RiskStructurePolicy.AttachmentPointCurrencyId=PS.Contract.AttachmentPointCurrencyTypeId
BPStaging.PS_Staging_RiskStructurePolicy.AttachmentPoint=PS.Contract.AttachmentPoint
BPStaging.PS_Staging_RiskStructurePolicy.DeductibleCurrencyId=PS.Contract.DeductibleCurrencyTypeId
BPStaging.PS_Staging_RiskStructurePolicy.Deductible=PS.Contract.Deductible
BPStaging.PS_Staging_RiskStructurePolicy.ValidFrom=PS.Contract.SourceUpdatedDate
BPStaging.PS_Staging_RiskStructurePolicy.ValidFrom=PS.ContractRiskProfile.SourceUpdatedDate
BPStaging.PS_Staging_RiskStructurePolicy.IsDeleted=PS.Contract.IsDeleted
BPStaging.PS_Staging_RiskStructurePolicy.IsDeleted=PS.ContractRiskProfile.IsDeleted
BPStaging.PS_Staging_RiskStructurePolicy.ProgramStructureTypeId=PS.Contract.ProgramStructureTypeId
*/
CREATE PROCEDURE BPStaging.Load_PS_Staging_RiskStructurePolicy
WITH RECOMPILE
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BPStaging.PS_Staging_ContractRiskProfile';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    TRUNCATE TABLE BPStaging.PS_Staging_RiskStructurePolicy;

    DROP TABLE IF EXISTS #ContractPolicyDetails;

    SELECT
        P.ContractId
      , P.PlacementId
      , P.PolicyId
      , P.CPPolicyId
      , P.PolicyReference
      , P.LegalEntityId
      , RO = ROW_NUMBER() OVER (PARTITION BY P.ContractId ORDER BY CASE WHEN P.PolicyId = P.CPPolicyId THEN 1 ELSE 0 END DESC)
    INTO #ContractPolicyDetails
    FROM (
        SELECT
            CO.ContractId
          , PP.PlacementId
          , PolicyId = PO.PolicyId
          , CPPolicyId = NULL
          , PO.PolicyReference
          , LegalEntityId = LE.LegalEntityKey
        FROM
            dbo.Policy PO
            INNER JOIN dbo.PlacementPolicy PP
                ON PP.PolicyId = PO.PolicyId
                   AND PP.IsDeleted = 0
                   AND PP.PlacementPolicyRelationshipTypeId = 1 --Current

            INNER JOIN dbo.PolicyOrganisation PORG
                ON PORG.PolicyId = PO.PolicyId
                   AND PORG.IsDeleted = 0

            INNER JOIN APIv1.OrganisationHierarchyTable OHT
                ON OHT.OrganisationId = PORG.OrganisationId

            INNER JOIN ref.OrganisationLegalEntity OLE
                ON OLE.OrganisationId = OHT.Level1OrgId
                   AND OLE.IsDeprecated = 0

            INNER JOIN ref.LegalEntity LE
                ON LE.LegalEntityKey = CAST(OLE.LegalEntityId AS NVARCHAR(100))
                   AND LE.DataSourceInstanceId = 50366

            INNER JOIN PS.Contract CO
                ON CAST(CO.LegalEntityId AS NVARCHAR(100)) = LE.LegalEntityKey
                   AND CO.Reference = PO.PolicyReference
                   AND CO.IsDeleted = 0
        WHERE
            PO.IsDeleted = 0
        UNION ALL -- Changed to UNION ALL because CPPolicyId = NULL (above) and ON CP.PSPolicyId = PO.PolicyId (below) mean these 2 unioned sets will never have duplicates
        SELECT
            CO.ContractId
          , PP.PlacementId
          , PolicyId = PO.PolicyId
          , CP.PolicyId
          , PO.PolicyReference
          , CO.LegalEntityId
        FROM
            dbo.Policy PO
            INNER JOIN dbo.PlacementPolicy PP
                ON PP.PolicyId = PO.PolicyId
                   AND PP.IsDeleted = 0
                   AND PP.PlacementPolicyRelationshipTypeId = 1 --Current

            INNER JOIN dbo.ContractPolicy CP
                ON CP.PolicyId = PO.PolicyId
                   AND CP.IsDeleted = 0

            INNER JOIN PS.Contract CO
                ON CO.ContractId = CP.ContractId
                   AND CO.IsDeleted = 0
        WHERE
            PO.IsDeleted = 0
    ) P;

    INSERT INTO
        BPStaging.PS_Staging_RiskStructurePolicy
        (
            DataSourceInstanceId
          , ContractId
          , PlacementId
          , BrokerCode
          , PolicyReference
          , PolicyId
          , RiskProfileId
          , LayerTypeId
          , LimitCurrencyId
          , Limit
          , AttachmentPointCurrencyId
          , AttachmentPoint
          , DeductibleCurrencyId
          , Deductible
          , ValidFrom
          , IsDeleted
          , ProgramStructureTypeId
        )
    SELECT
        DataSourceInstanceId = 50366
      , C.ContractId
      , PL.PlacementId
      , LE.BrokerCode
      , PolicyReference = ISNULL(CPR.PolicyReference, C.Reference)
      , CPR.PolicyId
      , CRP.RiskProfileId
      , C.LayerTypeId
      , LimitCurrencyId = C.LimitCurrencyTypeId
      , C.Limit
      , AttachmentPointCurrencyId = C.AttachmentPointCurrencyTypeId
      , C.AttachmentPoint
      , DeductibleCurrencyId = C.DeductibleCurrencyTypeId
      , C.Deductible
      , ValidFrom = COALESCE(C.SourceUpdatedDate, CRP.SourceUpdatedDate)
      , IsDeleted = C.IsDeleted | CRP.IsDeleted
      , C.ProgramStructureTypeId
    FROM
        PS.Contract C
        INNER JOIN dbo.Placement PL
            ON PL.PlacementId = C.PlacementId
               AND PL.DataSourceInstanceId = 50366

        LEFT JOIN #ContractPolicyDetails CPR
            ON C.ContractId = CPR.ContractId
               AND CPR.RO = 1

        INNER JOIN PS.ContractRiskProfile CRP
            ON C.ContractId = CRP.ContractId
               AND CRP.IsDeleted = 0

        LEFT JOIN ref.LegalEntity LE
            ON LE.LegalEntityKey = CAST(C.LegalEntityId AS NVARCHAR(100))
               AND LE.DataSourceInstanceId = 50366;

    SELECT @InsertedCount = @@ROWCOUNT;

    DROP TABLE IF EXISTS #ContractPolicyDetails;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Insert ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

RETURN 0;