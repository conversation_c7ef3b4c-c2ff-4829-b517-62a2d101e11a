CREATE PROCEDURE APIv1.GetRenewals (
    @placementDataSourceInstanceId INT
  , @dateFrom                      DATETIME
  , @dateTo                        DATETIME
)
AS
SELECT
    PlacementId
  , InceptionDate
  , ExpiryDate
  , DataSourceInstanceId
  , DataSourceInstanceName
  , ClientKey = PartyKey
  , ClientCode = ISNULL(ClientCode, LookupCode)
  , GlobalClientId = GlobalPartyId
FROM (
    SELECT
        PlacementId = pl.PlacementListenerId
      , p.InceptionDate
      , p.ExpiryDate
      , py.DataSourceInstanceId
      , dsi.DataSourceInstanceName
      , py.PartyKey
      , py.GlobalPartyId
      , ClientCode = ppa.EpicClientCode
      , LookupCode = ppa.EpicLookupCode
      , ClientOrder = ROW_NUMBER() OVER (PARTITION BY p.PlacementId ORDER BY ppr.IsPrimaryParty DESC)
    FROM
        dbo.PlacementListener pl WITH (NOLOCK)
        INNER JOIN dbo.Placement p WITH (NOLOCK)
            ON p.PlacementId = pl.PlacementId
               AND p.IsDeleted = 0

        INNER JOIN dbo.PlacementPartyRole ppr WITH (NOLOCK)
            ON ppr.PlacementId = p.PlacementId
               AND ppr.IsDeleted = 0

        INNER JOIN ref.PartyRole prl WITH (NOLOCK)
            ON prl.PartyRoleId = ppr.PartyRoleId
               AND prl.IsDeprecated = 0
               AND prl.GlobalPartyRoleId = 100 -- Client

        INNER JOIN dbo.Party py WITH (NOLOCK)
            ON py.PartyId = ppr.PartyId
               AND py.IsDeleted = 0

        INNER JOIN Reference.DataSourceInstance dsi WITH (NOLOCK)
            ON dsi.DataSourceInstanceId = py.DataSourceInstanceId
               AND dsi.IsDeleted = 0

        LEFT JOIN PAS.Party pasp WITH(NOLOCK)
            ON pasp.PASPartyId = py.PACTPartyId
		        AND pasp.DataSourceInstanceId = py.DataSourceInstanceId

	    LEFT JOIN PAS.PartyAttribute ppa WITH(NOLOCK)
            ON ppa.PartyKey = pasp.PartyKey
		        AND ppa.DataSourceInstanceId = pasp.DataSourceInstanceId

    WHERE
        pl.PlacementDataSourceInstanceId = @placementDataSourceInstanceId
        AND pl.IsDeleted = 0
        AND pl.IsReadyToSend = 1
        AND pl.RenewalProcessStartDate >= DATEADD(DAY, -14, @dateFrom)
        AND pl.RenewalProcessStartDate <= @dateTo -- Give a 14 day leeway window
) D
WHERE
    ClientOrder = 1;