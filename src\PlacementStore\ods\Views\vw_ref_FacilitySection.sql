/*
Lineage
FacilitySectionId=ref.FacilitySection.FacilitySectionId
DataSourceInstanceId=ref.FacilitySection.DataSourceInstanceId
FacilityId=ref.Facility.FacilityId
FacilityName=ref.Facility.FacilityName
PolicyReference=ref.Facility.FacilityReference
LineSlipRef=ref.FacilitySection.LineSlipRef
SectionCode=ref.FacilitySection.SectionCode
InceptionDate=ref.Facility.InceptionDate
ExpiryDate=ref.Facility.ExpiryDate
RenewedFromPolicyId=ref.Facility.RenewedFromPolicyId
FacilityType=ref.Facility.FacilityType
RefPolicyStatusId=ref.Facility.RefPolicyStatusId
PolicySectionStatusKey=ref.FacilitySection.PolicySectionStatusKey
RefPolicyStatus=PAS.RefPolicyStatus.RefPolicyStatus
PolicySectionStatus=PAS.PolicySectionStatus.PolicySectionStatus
PolicySectionStatus=PAS.PolicyStatus.PolicyStatus
ETLCreatedDate=ref.FacilitySection.ETLCreatedDate
ETLUpdatedDate=ref.FacilitySection.ETLUpdatedDate
SourceUpdatedDate=ref.FacilitySection.SourceUpdatedDate
IsDeprecated=ref.FacilitySection.IsDeprecated
FacilitySectionKey=ref.FacilitySection.FacilitySectionKey
EffectiveFromDate=ref.FacilitySection.EffectiveFromDate
EffectiveToDate=ref.FacilitySection.EffectiveToDate
*/

CREATE VIEW ods.vw_ref_FacilitySection
AS
SELECT
    fs.FacilitySectionId
  , fs.DataSourceInstanceId
  , f.FacilityId
  , f.FacilityName
  , PolicyReference = f.FacilityReference
  , fs.LineSlipRef
  , fs.SectionCode
  , f.InceptionDate
  , f.ExpiryDate
  , f.RenewedFromPolicyId
  , f.FacilityType
  , f.RefPolicyStatusId
  , PolicySectionStatusId = -1
  , fs.PolicySectionStatusKey
  , rps.RefPolicyStatus
  , PolicySectionStatus = COALESCE(pss.PolicySectionStatus, ps.PolicyStatus, 'N/A')
  , fs.ETLCreatedDate
  , fs.ETLUpdatedDate
  , fs.SourceUpdatedDate
  , fs.IsDeprecated
  , fs.FacilitySectionKey
  , fs.EffectiveFromDate
  , fs.EffectiveToDate
FROM
    ref.FacilitySection fs
    INNER JOIN ref.Facility f
        ON f.FacilityId = fs.FacilityId

    LEFT JOIN PAS.PolicySectionStatus pss
        ON pss.PolicySectionStatusKey = fs.PolicySectionStatusKey
            AND pss.DataSourceInstanceId = fs.DataSourceInstanceId

    LEFT JOIN PAS.PolicyStatus ps
        ON f.PolicyStatusKey = ps.PolicyStatusKey
            AND f.DataSourceInstanceId = ps.DataSourceInstanceId

    LEFT JOIN PAS.RefPolicyStatus rps
        ON rps.RefPolicyStatusId = f.RefPolicyStatusId;
GO

-- Add View Level Detail
EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The reference view in Placement Store which contains details of the facility section within a facility'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection';
GO

-- Add Column Level Detail
EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id used to define a Facility Section in Placement Store '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'FacilitySectionId';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id from ERD which describes the source of this data.'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'DataSourceInstanceId';
GO

-- Add Column Level Detail
EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Key used to define a Facility Section in Placement Store to be used with the DataSourceInstanceId'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'FacilitySectionKey';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id used to identify a Facility in Placement Store '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'FacilityId';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The name given to a Facility in Placement Store '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'FacilityName';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique reference associated with a policy '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyReference';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The reference associated with a Line Slip '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'LineSlipRef';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The code used to define a section on a facility '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'SectionCode';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The date at which the insurance policy coverage begins/ becomes effective '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'InceptionDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The date at which the insurance policy coverage ends'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'ExpiryDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id of the policy from which the current policy has renewed'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'RenewedFromPolicyId';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'A description of the facility type '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'FacilityType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The ERD Id which defines the Policy status at a point in time '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'RefPolicyStatusId';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Id which defines the status of the Policy section at a point in time'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicySectionStatusId';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Key which defines the status of the Policy section at a point in time'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicySectionStatusKey';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The text that describes the status of the Policy '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'RefPolicyStatus';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The text that describes the status of the Policy section '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicySectionStatus';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The UTC date when the record was created in the Placement Store.'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'ETLCreatedDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The UTC date when the record was last updated in the Placement Store.'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'ETLUpdatedDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The date when the record was last updated in the source system.'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'SourceUpdatedDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'An indicator to show if the record has been deprecated.'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'IsDeprecated';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The date at which the insurance policy coverage Start'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'EffectiveFromDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The date at which the insurance policy coverage ends'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_FacilitySection'
  , @level2type = N'COLUMN'
  , @level2name = N'EffectiveToDate';
GO