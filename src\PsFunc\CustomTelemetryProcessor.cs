using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;

namespace PsFunc;
public class CustomTelemetryProcessor : ITelemetryProcessor
{
    private ITelemetryProcessor Next { get; set; }

    // Link processors to each other in the chain.
    public CustomTelemetryProcessor(ITelemetryProcessor next)
    {
        this.Next = next;
    }
    public void Process(ITelemetry item)
    {
        if(item is DependencyTelemetry)
        {
            // Do not send any dependency telemetry.
            return;
        }
        // Call the next processor in the chain.
        this.Next.Process(item);
    }
}
