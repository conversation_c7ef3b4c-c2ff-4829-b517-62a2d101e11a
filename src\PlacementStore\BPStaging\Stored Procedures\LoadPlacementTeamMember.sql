/*
Lineage
dbo.PlacementTeamMember.PlacementTeamMemberId=BPStaging.PlacementTeamMember.Id
dbo.PlacementTeamMember.PlacementId=dbo.Placement.PlacementId
dbo.PlacementTeamMember.UserId=BPStaging.PlacementTeamMember.UserId
dbo.PlacementTeamMember.RoleId=BPStaging.PlacementTeamMember.RoleId
dbo.PlacementTeamMember.SourceUpdatedDate=BPStaging.PlacementTeamMember.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadPlacementTeamMember
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementTeamMember';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.PlacementTeamMember
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE dbo.PlacementTeamMember T
    USING (
        SELECT
            PlacementTeamMemberId = ptm.Id
          , PlacementId = pl.PlacementId
          , DataSourceInstanceId = 50366
          , UserId = ptm.UserId
          , RoleId = ptm.RoleId
          , SourceUpdatedDate = ptm.ValidFrom
          , IsDeleted = 0
        FROM
            BPStaging.PlacementTeamMember ptm
            INNER JOIN dbo.Placement pl
                ON pl.PlacementSystemId = ptm.PlacementId
                   AND pl.DataSourceInstanceId = 50366
    ) S
    ON S.PlacementTeamMemberId = T.PlacementTeamMemberId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 PlacementTeamMemberId
               , PlacementId
               , DataSourceInstanceId
               , UserId
               , RoleId
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.PlacementTeamMemberId
                   , S.PlacementId
                   , S.DataSourceInstanceId
                   , S.UserId
                   , S.RoleId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.PlacementId
      , T.DataSourceInstanceId
      , T.UserId
      , T.RoleId
      , T.IsDeleted
    INTERSECT
    SELECT
        S.PlacementId
      , S.DataSourceInstanceId
      , S.UserId
      , S.RoleId
      , S.IsDeleted
)
        THEN UPDATE SET
                 T.PlacementTeamMemberId = S.PlacementTeamMemberId
               , T.PlacementId = S.PlacementId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.UserId = S.UserId
               , T.RoleId = S.RoleId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;