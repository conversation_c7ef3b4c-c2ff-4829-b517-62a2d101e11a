/*
Lineage
dbo.Placement.ServicingPlatformId=ref.Team.DataSourceInstanceId
dbo.Placement.ServicingPlatformId=dbo.DataSourceInstanceIdMapping.DataSourceInstanceId
*/
CREATE PROCEDURE BPStaging.UpdatePlacement
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @DataSourceInstanceUpdatedCount INT;
DECLARE @CompletionStatusColUpdatedCount INT;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    UPDATE pl
    SET pl.ServicingPlatformId = COALESCE(t.DataSourceInstanceId, region.DataSourceInstanceId, 50366)
    FROM
        dbo.Placement pl
        LEFT JOIN dbo.PlacementTeams pt
            ON pl.PlacementId = pt.PlacementId
               AND pt.IsDeleted = 0

        LEFT JOIN ref.Team t
            ON t.TeamId = pt.TeamId

        LEFT JOIN dbo.DataSourceInstanceIdMapping region
            ON pl.BrokingRegionId = region.BrokingRegionId
               AND ISNULL(pl.BrokingSegmentId, -999999) = region.BrokingSegmentId
               AND ISNULL(pl.BrokingSubSegmentId, -999999) = region.BrokingSubSegmentId
    WHERE
        pl.ServicingPlatformId = 50366
        AND COALESCE(t.DataSourceInstanceId, region.DataSourceInstanceId, 50366) <> 50366;

    SELECT @DataSourceInstanceUpdatedCount = @@ROWCOUNT;

    UPDATE PL
    SET PL.PlacementCompletionStatusId = 1
    FROM
        dbo.Placement PL
    WHERE
        PL.ServicingPlatformId = 50003 /* Col (Brazil) */
        AND PL.PlacementSystemId IS NOT NULL
        AND PL.PlacementCompletionStatusId IS NULL
        AND PL.DataSourceInstanceId = 50366;

    SELECT @CompletionStatusColUpdatedCount = @@ROWCOUNT;

    SELECT @UpdatedCount = @DataSourceInstanceUpdatedCount + @CompletionStatusColUpdatedCount;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

-- Updates are only carried out if the values are different.
SET @Action =
    N'DataSourceInstanceId Updated Record Count - ' + CONVERT(NVARCHAR(10), ISNULL(@DataSourceInstanceUpdatedCount, 0))
    + N', PlacementCompletionStatusId for Col (Brazil) Updated Record Count - '
    + CONVERT(NVARCHAR(10), ISNULL(@CompletionStatusColUpdatedCount, 0)) + N'.';

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);