/*
Lineage
PS.SpecificationElement.DataSourceInstanceId=PS.Specification.DataSourceInstanceId
PS.SpecificationElement.SpecificationElementKey=PS.Specification.SpecificationId
PS.SpecificationElement.SpecificationElementKey=dbo.ElementCache.ElementId
PS.SpecificationElement.SpecificationElementKey=dbo.ElementCache.ElementBranchId
PS.SpecificationElement.ParentDataSourceInstanceId=PS.Specification.DataSourceInstanceId
PS.SpecificationElement.ParentSpecificationElementKey=PS.Specification.SpecificationId
PS.SpecificationElement.ParentSpecificationElementKey=dbo.ElementCache.ElementId
PS.SpecificationElement.ParentSpecificationElementKey=dbo.ElementCache.ElementBranchId
PS.SpecificationElement.SpecificationId=PS.Specification.SpecificationId
PS.SpecificationElement.TypeKeyPath=dbo.ElementCache.TypeKeyPath
PS.SpecificationElement.ElementLevel=dbo.ElementCache.HierarchyId
PS.SpecificationElement.ElementDisplayOrder=dbo.ElementDelta.Index
PS.SpecificationElement.ElementId=dbo.ElementCache.ElementId
PS.SpecificationElement.ElementTypeId=dbo.ElementCache.ElementTypeId
PS.SpecificationElement.ElementType=ref.ElementType.ElementType
PS.SpecificationElement.ElementTypeOverride=dbo.ElementDelta.Label
PS.SpecificationElement.ParentElementId=dbo.ElementCache.ParentElementId
PS.SpecificationElement.ParentElementTypeId=dbo.ElementCache.ElementTypeId
PS.SpecificationElement.ParentElementType=ref.ElementType.ElementType
PS.SpecificationElement.ParentElementTypeOverride=dbo.ElementDelta.Label
PS.SpecificationElement.SourceUpdatedDate=PS.Specification.ETLUpdatedDate
PS.SpecificationElement.SourceUpdatedDate=BP.RequestedCoverageElement.ETLUpdatedDate
PS.SpecificationElement.SourceUpdatedDate=BP.ResponseManagementElement.ETLUpdatedDate
PS.SpecificationElement.SourceUpdatedDate=dbo.ElementCache.LastUpdatedUTCDate
PS.SpecificationElement.SourceUpdatedDate=ref.ElementType.SourceUpdatedDate
PS.SpecificationElement.SourceUpdatedDate=dbo.ElementDelta.SourceLastUpdateDate
PS.SpecificationElement.IsDeleted=PS.Specification.IsDeleted
PS.SpecificationElement.IsDeleted=BP.RequestedCoverageElement.IsDeleted
PS.SpecificationElement.IsDeleted=BP.ResponseManagementElement.IsDeleted
PS.SpecificationElement.ElementBranchId=dbo.ElementCache.ElementBranchId
*/

CREATE PROCEDURE BPStaging.Load_PS_SpecificationElement
    @LastUpdatedDate DATETIME2(7)
AS
DECLARE @MaxLastUpdatedUTCDate DATETIME2(7);
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.SpecificationElement';

DECLARE @Actions TABLE (
    CHANGE VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @UpdateDate DATETIME2(7) = GETUTCDATE();

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #MetaTag;
    DROP TABLE IF EXISTS #Specification;
    DROP TABLE IF EXISTS #SpecificationElement;

    CREATE TABLE #MetaTag (
        ElementTypeId  INT          NOT NULL
      , MetaTagId      INT          NOT NULL
      , IsDeprecated   BIT          NOT NULL
      , ETLUpdatedDate DATETIME2(7) NOT NULL
      , RowNo          INT          NOT NULL
    );

    CREATE TABLE #Specification (
        DataSourceInstanceId INT          NOT NULL
      , SpecificationId      INT          NOT NULL
      , ElementId            INT          NOT NULL
      , ElementBranchId      INT          NOT NULL
      , SourceUpdatedDate    DATETIME2(7) NULL
      , IsDeleted            BIT          NOT NULL
    );

    CREATE TABLE #SpecificationElement (
        DataSourceInstanceId          INT           NOT NULL
      , SpecificationElementKey       NVARCHAR(50)  NOT NULL
      , ParentDataSourceInstanceId    INT           NULL
      , ParentSpecificationElementKey NVARCHAR(50)  NULL
      , SpecificationId               INT           NOT NULL
      , TypeKeyPath                   NVARCHAR(500) NULL
      , ElementLevel                  INT           NOT NULL
      , ElementDisplayOrder           INT           NULL
      , ElementId                     INT           NOT NULL
      , ElementTypeId                 INT           NOT NULL
      , ElementType                   NVARCHAR(500) NOT NULL
      , ElementTypeOverride           NVARCHAR(500) NULL
      , ParentElementId               INT           NULL
      , ParentElementTypeId           INT           NULL
      , ParentElementType             NVARCHAR(500) NULL
      , ParentElementTypeOverride     NVARCHAR(500) NULL
      , SourceUpdatedDate             DATETIME2(7)  NULL
      , LastChangedDate               DATETIME2(7)  NULL
      , IsDeleted                     BIT           NOT NULL
      , IsInScope                     BIT           NOT NULL
      , ElementBranchId               INT           NULL
    );

    /* Meta Tag*/
    INSERT INTO
        #MetaTag
        (
            ElementTypeId
          , MetaTagId
          , IsDeprecated
          , ETLUpdatedDate
          , RowNo
        )
    SELECT
        etmt.ElementTypeId
      , etmt.MetaTagId
      , etmt.IsDeprecated
      , ETLUpdatedDate = GREATEST(etmt.ETLUpdatedDate, mt.ETLUpdatedDate)
      , RowNo = ROW_NUMBER() OVER (PARTITION BY etmt.ElementTypeId ORDER BY etmt.ElementTypeMetaTagId DESC)
    FROM
        ref.ElementTypeMetaTag etmt WITH (NOLOCK)
        INNER JOIN ref.MetaTag mt WITH (NOLOCK)
            ON mt.MetaTagId = etmt.MetaTagId
               AND mt.MetaTagGroupKey = 'metricContext'
               AND mt.TranslationKey = 'requested';

    CREATE CLUSTERED INDEX PK_#MetaTag
    ON #MetaTag
    (
        ElementTypeId
    );

    /* Specification */
    INSERT INTO
        #Specification
        (
            DataSourceInstanceId
          , SpecificationId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        s.DataSourceInstanceId
      , s.SpecificationId
      , rme.ElementId
      , rce.ElementBranchId
      , SourceUpdatedDate = GREATEST(s.ETLUpdatedDate, rce.ETLUpdatedDate, rme.ETLUpdatedDate)
      , IsDeleted = (s.IsDeleted | rce.IsDeleted | rme.IsDeleted)
    FROM
        PS.Specification s WITH (NOLOCK)
        INNER JOIN BP.RequestedCoverageElement rce WITH (NOLOCK)
            ON s.SpecificationKey = CONCAT(N'RCE|', rce.Id)

        INNER JOIN BP.ResponseManagementElement rme WITH (NOLOCK)
            ON rce.ResponseManagementElementId = rme.Id;

    CREATE CLUSTERED INDEX PK_#Specification
    ON #Specification
    (
        SpecificationId
    );

    /* Specification Element */
    INSERT INTO
        #SpecificationElement
        (
            SpecificationElementKey
          , DataSourceInstanceId
          , ParentDataSourceInstanceId
          , ParentSpecificationElementKey
          , SpecificationId
          , TypeKeyPath
          , ElementLevel
          , ElementDisplayOrder
          , ElementId
          , ElementTypeId
          , ElementType
          , ElementTypeOverride
          , ParentElementId
          , ParentElementTypeId
          , ParentElementType
          , ParentElementTypeOverride
          , SourceUpdatedDate
          , LastChangedDate
          , IsDeleted
          , IsInScope
          , ElementBranchId
        )
    SELECT
        SpecificationElementKey = CONCAT(s.SpecificationId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , s.DataSourceInstanceId
      , ParentDataSourceInstanceId = s.DataSourceInstanceId
      , ParentSpecificationElementKey = CASE WHEN ecparent.ElementId IS NOT NULL
                                                 THEN CONCAT(
                                                          s.SpecificationId
                                                        , N'|'
                                                        , ecparent.ElementId
                                                        , N'|'
                                                        , ecparent.ElementBranchId
                                                      ) END
      , s.SpecificationId
      , ec.TypeKeyPath
      , ElementLevel = ec.HierarchyId.GetLevel()
      , ElementDisplayOrder = ed.[Index]
      , ec.ElementId
      , ec.ElementTypeId
      , et.ElementType
      , ElementTypeOverride = ed.Label
      , ec.ParentElementId
      , ParentElementTypeId = ecparent.ElementTypeId
      , ParentElementType = etparent.ElementType
      , ParentElementTypeOverride = edparent.Label
      , SourceUpdatedDate = GREATEST(
                                s.SourceUpdatedDate
                              , ec.LastUpdatedUTCDate
                              , et.SourceUpdatedDate
                              , ed.SourceLastUpdateDate
                            )
      , LastChangedDate = GREATEST(
                              s.SourceUpdatedDate
                            , ec.LastUpdatedUTCDate
                            , et.ETLUpdatedDate
                            , ed.ETLUpdatedDate
                            , etm.ETLUpdatedDate
                            , ecparent.LastUpdatedUTCDate
                            , etparent.ETLUpdatedDate
                            , edparent.ETLUpdatedDate
                          )
      , s.IsDeleted
      , IsInScope = CAST(CASE WHEN etm.MetaTagId IS NOT NULL
                                  THEN 1
                              ELSE 0 END AS BIT)
      , ec.ElementBranchId
    FROM
        #Specification s WITH (NOLOCK)
        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = s.ElementId
               AND ec.ElementBranchId = s.ElementBranchId

        INNER JOIN ref.ElementType et WITH (NOLOCK)
            ON et.ElementTypeId = ec.ElementTypeId

        INNER JOIN dbo.ElementDelta ed WITH (NOLOCK)
            ON ed.ElementDeltaId = ec.ElementDeltaId

        LEFT JOIN #MetaTag etm WITH (NOLOCK)
            ON etm.ElementTypeId = ec.ElementTypeId
               AND etm.RowNo = 1

        LEFT JOIN dbo.ElementCache ecparent WITH (NOLOCK)
            ON ecparent.ElementId = ec.ParentElementId
               AND ecparent.ElementBranchId = ec.ElementBranchId

        LEFT JOIN ref.ElementType etparent WITH (NOLOCK)
            ON etparent.ElementTypeId = ecparent.ElementTypeId

        LEFT JOIN dbo.ElementDelta edparent WITH (NOLOCK)
            ON edparent.ElementDeltaId = ecparent.ElementDeltaId;

    CREATE CLUSTERED INDEX PK_#SpecificationElement
    ON #SpecificationElement
    (
        SpecificationElementKey ASC
      , DataSourceInstanceId ASC
    );

    CREATE NONCLUSTERED INDEX IDX_#SpecifcationElement_IsInScope
    ON #SpecificationElement
    (
        IsInScope
      , LastChangedDate
    )
    INCLUDE
    (
        SpecificationElementKey
      , DataSourceInstanceId
    );

    CREATE TABLE #SpecificationElementInScope (
        SpecificationElementKey NVARCHAR(50) NOT NULL
      , DataSourceInstanceId    INT          NOT NULL
    );

    /* CTE to only include InScope Elements and its parent structure */
    WITH CTE_SpecificationElement AS (
        SELECT
            SE.DataSourceInstanceId
          , SE.SpecificationElementKey
          , SE.ParentDataSourceInstanceId
          , SE.ParentSpecificationElementKey
          , SE.IsInScope
        FROM
            #SpecificationElement SE
        WHERE
            SE.IsInScope = 1
            AND SE.LastChangedDate >= @LastUpdatedDate
        UNION ALL
        SELECT
            PSE.DataSourceInstanceId
          , PSE.SpecificationElementKey
          , PSE.ParentDataSourceInstanceId
          , PSE.ParentSpecificationElementKey
          , PSE.IsInScope
        FROM
            CTE_SpecificationElement C
            INNER JOIN #SpecificationElement PSE
                ON PSE.SpecificationElementKey = C.ParentSpecificationElementKey
                   AND PSE.DataSourceInstanceId = C.ParentDataSourceInstanceId
    )
    INSERT INTO
        #SpecificationElementInScope
        (
            SpecificationElementKey
          , DataSourceInstanceId
        )
    SELECT DISTINCT
           CTE_SpecificationElement.SpecificationElementKey
         , CTE_SpecificationElement.DataSourceInstanceId
    FROM
        CTE_SpecificationElement;

    MERGE PS.SpecificationElement WITH (HOLDLOCK) T
    USING (
        SELECT
            se.DataSourceInstanceId
          , se.SpecificationElementKey
          , se.ParentDataSourceInstanceId
          , se.ParentSpecificationElementKey
          , se.SpecificationId
          , se.TypeKeyPath
          , se.ElementLevel
          , se.ElementDisplayOrder
          , se.ElementId
          , se.ElementTypeId
          , se.ElementType
          , se.ElementTypeOverride
          , se.ParentElementId
          , se.ParentElementTypeId
          , se.ParentElementType
          , se.ParentElementTypeOverride
          , se.SourceUpdatedDate
          , se.IsDeleted
          , se.ElementBranchId
        FROM
            #SpecificationElement se WITH (NOLOCK)
            INNER JOIN #SpecificationElementInScope seis WITH (NOLOCK)
                ON seis.SpecificationElementKey = se.SpecificationElementKey
                   AND seis.DataSourceInstanceId = se.DataSourceInstanceId
    ) S
    ON T.SpecificationElementKey = S.SpecificationElementKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , SpecificationElementKey
               , ParentDataSourceInstanceId
               , ParentSpecificationElementKey
               , SpecificationId
               , TypeKeyPath
               , ElementLevel
               , ElementDisplayOrder
               , ElementId
               , ElementTypeId
               , ElementType
               , ElementTypeOverride
               , ParentElementId
               , ParentElementTypeId
               , ParentElementType
               , ParentElementTypeOverride
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
               , ElementBranchId
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.SpecificationElementKey
                   , S.ParentDataSourceInstanceId
                   , S.ParentSpecificationElementKey
                   , S.SpecificationId
                   , S.TypeKeyPath
                   , S.ElementLevel
                   , S.ElementDisplayOrder
                   , S.ElementId
                   , S.ElementTypeId
                   , S.ElementType
                   , S.ElementTypeOverride
                   , S.ParentElementId
                   , S.ParentElementTypeId
                   , S.ParentElementType
                   , S.ParentElementTypeOverride
                   , @UpdateDate
                   , @UpdateDate
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.ElementBranchId
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.ParentDataSourceInstanceId
                               , S.ParentSpecificationElementKey
                               , S.SpecificationId
                               , S.TypeKeyPath
                               , S.ElementLevel
                               , S.ElementDisplayOrder
                               , S.ElementId
                               , S.ElementTypeId
                               , S.ElementType
                               , S.ElementTypeOverride
                               , S.ParentElementId
                               , S.ParentElementTypeId
                               , S.ParentElementType
                               , S.ParentElementTypeOverride
                               , S.IsDeleted
                               , S.ElementBranchId
                             INTERSECT
                             SELECT
                                 T.ParentDataSourceInstanceId
                               , T.ParentSpecificationElementKey
                               , T.SpecificationId
                               , T.TypeKeyPath
                               , T.ElementLevel
                               , T.ElementDisplayOrder
                               , T.ElementId
                               , T.ElementTypeId
                               , T.ElementType
                               , T.ElementTypeOverride
                               , T.ParentElementId
                               , T.ParentElementTypeId
                               , T.ParentElementType
                               , T.ParentElementTypeOverride
                               , T.IsDeleted
                               , T.ElementBranchId
                         )
        THEN UPDATE SET
                 T.ParentDataSourceInstanceId = S.ParentDataSourceInstanceId
               , T.ParentSpecificationElementKey = S.ParentSpecificationElementKey
               , T.SpecificationId = S.SpecificationId
               , T.TypeKeyPath = S.TypeKeyPath
               , T.ElementLevel = S.ElementLevel
               , T.ElementDisplayOrder = S.ElementDisplayOrder
               , T.ElementId = S.ElementId
               , T.ElementTypeId = S.ElementTypeId
               , T.ElementType = S.ElementType
               , T.ElementTypeOverride = S.ElementTypeOverride
               , T.ParentElementId = S.ParentElementId
               , T.ParentElementTypeId = S.ParentElementTypeId
               , T.ParentElementType = S.ParentElementType
               , T.ParentElementTypeOverride = S.ParentElementTypeOverride
               , T.ETLUpdatedDate = @UpdateDate
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.ElementBranchId = S.ElementBranchId
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN CHANGE = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN CHANGE = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN CHANGE = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    SELECT @MaxLastUpdatedUTCDate = ISNULL(MAX(ETLUpdatedDate), @LastUpdatedDate)
    FROM
        PS.SpecificationElement;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @MaxLastUpdatedUTCDate = NULL;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0)
  , MaxLastUpdatedUTCDate = ISNULL(@MaxLastUpdatedUTCDate, @LastUpdatedDate);

RETURN 0;
GO