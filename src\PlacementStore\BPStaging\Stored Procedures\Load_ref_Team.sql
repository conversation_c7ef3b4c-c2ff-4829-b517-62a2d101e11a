/*
Lineage
ref.Team.TeamKey=BPStaging.UserGroup.Id
ref.Team.TeamId=BPStaging.UserGroup.PsId
ref.Team.OrgNode=BPStaging.UserGroup.OrgNode
ref.Team.OrgLevel=BPStaging.UserGroup.OrgLevel
ref.Team.TeamName=BPStaging.UserGroup.Name
ref.Team.DataSourceInstanceId=BPStaging.UserGroup.ServicingPlatformId
ref.Team.OrganisationRole=BPStaging.UserGroup.OrganisationRole
ref.Team.FullPath=BPStaging.UserGroup.FullPath
ref.Team.SourceUpdatedDate=BPStaging.UserGroup.ValidTo
ref.Team.SourceUpdatedDate=BPStaging.UserGroup.ValidFrom
ref.Team.IsDeprecated=BPStaging.UserGroup.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_ref_Team
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.Team';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.Team T
    USING (
        SELECT
            TeamId = R.PsId
          , TeamKey = CAST(R.Id AS NVARCHAR(255))
          , R.OrgNode
          , R.OrgLevel
          , TeamName = R.Name
          , DataSourceInstanceId = R.ServicingPlatformId
          , R.OrganisationRole
          , FullPath
          , SourceUpdatedDate = IIF(YEAR(R.ValidTo) < 9999, R.ValidTo, R.ValidFrom)
          , IsDeprecated = IIF(YEAR(R.ValidTo) < 9999, 1, 0)
        FROM (
        SELECT
            PsId
          , Id
          , OrgNode
          , OrgLevel
          , Name
          , ServicingPlatformId
          , OrganisationRole
          , FullPath
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY PsId ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.UserGroup
        WHERE
            PsId IS NOT NULL
            AND FullPath IS NOT NULL
    ) R
        WHERE
            RowNo = 1
    ) S
    ON T.TeamId = S.TeamId
    WHEN NOT MATCHED
        THEN INSERT (
                 TeamKey
               , TeamId
               , OrgNode
               , OrgLevel
               , TeamName
               , DataSourceInstanceId
               , OrganisationRole
               , FullPath
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.TeamKey
                   , S.TeamId
                   , S.OrgNode
                   , S.OrgLevel
                   , S.TeamName
                   , S.DatasourceInstanceId
                   , S.OrganisationRole
                   , S.FullPath
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.TeamId
                               , T.OrgNode
                               , T.OrgLevel
                               , T.TeamName
                               , T.OrganisationRole
                               , T.FullPath
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.TeamId
                               , S.OrgNode
                               , S.OrgLevel
                               , S.TeamName
                               , S.OrganisationRole
                               , S.FullPath
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.TeamId = S.TeamId
               , T.OrgNode = S.OrgNode
               , T.OrgLevel = S.OrgLevel
               , T.TeamName = S.TeamName
               , T.OrganisationRole = S.OrganisationRole
               , T.FullPath = S.FullPath
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);