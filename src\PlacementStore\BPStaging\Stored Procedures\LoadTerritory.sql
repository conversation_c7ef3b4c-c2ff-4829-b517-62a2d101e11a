/*
Lineage
ref.Territory.TerritoryId=BPStaging.Territory.Id
ref.Territory.TerritoryKey=BPStaging.Territory.LabelTranslationKey
ref.Territory.Territory=BPStaging.Territory.Text
ref.Territory.SourceUpdatedDate=BPStaging.Territory.ValidFrom
ref.Territory.IsDeprecated=BPStaging.Territory.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadTerritory
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.Territory';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.Territory T
    USING (
        SELECT
            TerritoryId = Id
          , DataSourceInstanceId = 50366
          , TerritoryKey = LabelTranslationKey
          , Territory = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.Territory
    ) S
    ON T.TerritoryId = S.TerritoryId
    WHEN NOT MATCHED
        THEN INSERT (
                 TerritoryId
               , DataSourceInstanceId
               , TerritoryKey
               , Territory
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.TerritoryId
                   , S.DataSourceInstanceId
                   , S.TerritoryKey
                   , S.Territory
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.TerritoryId
                               , T.DataSourceInstanceId
                               , T.TerritoryKey
                               , T.Territory
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.TerritoryId
                               , S.DataSourceInstanceId
                               , S.TerritoryKey
                               , S.Territory
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.TerritoryId = S.TerritoryId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.TerritoryKey = S.TerritoryKey
               , T.Territory = S.Territory
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
