/*
Lineage
BP.ExpiringResponseElement.Id=BPStaging.ExpiringResponseElement.Id
BP.ExpiringResponseElement.ExpiringResponseGroupId=BPStaging.ExpiringResponseElement.ExpiringResponseGroupId
BP.ExpiringResponseElement.ResponseManagementElementId=BPStaging.ExpiringResponseElement.ResponseManagementElementId
BP.ExpiringResponseElement.ElementBranchId=BPStaging.ExpiringResponseElement.ElementBranchId
BP.ExpiringResponseElement.FollowTypeId=ref.FollowType.FollowTypeId
BP.ExpiringResponseElement.QuotedToLead=BPStaging.ExpiringResponseElement.QuotedToLead
BP.ExpiringResponseElement.QuotedToLead=ref.FollowType.FollowType
BP.ExpiringResponseElement.IsInvalid=BPStaging.ExpiringResponseElement.IsInvalid
BP.ExpiringResponseElement.IsDeleted=BPStaging.ExpiringResponseElement.ValidTo
BP.ExpiringResponseElement.SourceUpdatedDate=BPStaging.ExpiringResponseElement.ValidTo
BP.ExpiringResponseElement.SourceUpdatedDate=BPStaging.ExpiringResponseElement.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_BP_ExpiringResponseElement
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.ExpiringResponseElement';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE BP.ExpiringResponseElement T
    USING (
        SELECT
            Id
          , ExpiringResponseGroupId
          , ResponseManagementElementId
          , ElementBranchId
          , FollowTypeId
          , IsInvalid
          , QuotedToLead = CASE WHEN FollowType = 'Co-Lead'
                                    THEN 1
                                ELSE QuotedToLead END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            ere.Id
          , ere.ExpiringResponseGroupId
          , ere.ResponseManagementElementId
          , ere.ElementBranchId
          , f.FollowTypeId
          , f.FollowType
          , ere.IsInvalid
          , ere.QuotedToLead
          , ere.ValidTo
          , ere.ValidFrom
          , RowNo = ROW_NUMBER() OVER (PARTITION BY ere.Id ORDER BY ere.ValidFrom DESC, ere.ValidTo ASC)
        FROM
            BPStaging.ExpiringResponseElement ere
            LEFT JOIN ref.FollowType f
                ON ere.FollowTypeId = f.FollowTypeId
                   AND f.IsDeprecated = 0
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , ExpiringResponseGroupId
               , ResponseManagementElementId
               , ElementBranchId
               , FollowTypeId
               , QuotedToLead
               , IsInvalid
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.Id
                   , S.ExpiringResponseGroupId
                   , S.ResponseManagementElementId
                   , S.ElementBranchId
                   , S.FollowTypeId
                   , S.QuotedToLead
                   , S.IsInvalid
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeleted
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.ExpiringResponseGroupId
                               , S.ResponseManagementElementId
                               , S.ElementBranchId
                               , S.FollowTypeId
                               , S.QuotedToLead
                               , S.IsInvalid
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.ExpiringResponseGroupId
                               , T.ResponseManagementElementId
                               , T.ElementBranchId
                               , T.FollowTypeId
                               , T.QuotedToLead
                               , T.IsInvalid
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.ExpiringResponseGroupId = S.ExpiringResponseGroupId
               , T.ResponseManagementElementId = S.ResponseManagementElementId
               , T.ElementBranchId = S.ElementBranchId
               , T.FollowTypeId = S.FollowTypeId
               , T.QuotedToLead = S.QuotedToLead
               , T.IsInvalid = S.IsInvalid
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate

    /* Supporting incremental so no delete of any kind */
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = CONCAT(N'Merge ', @TargetTable);

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;