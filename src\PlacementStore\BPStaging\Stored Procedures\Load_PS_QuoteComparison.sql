/*
Lineage
PS.QuoteComparison.Id=BPStaging.QuoteComparison.Id
PS.QuoteComparison.PlacementId=BPStaging.QuoteComparison.PlacementId
PS.QuoteComparison.DisplayIndex=BPStaging.QuoteComparison.DisplayIndex
PS.QuoteComparison.Name=BPStaging.QuoteComparison.Name
PS.QuoteComparison.ProductId=BPStaging.QuoteComparison.ProductId
PS.QuoteComparison.IncludeExpiring=BPStaging.QuoteComparison.IncludeExpiring
PS.QuoteComparison.IncludeRequested=BPStaging.QuoteComparison.IncludeRequested
PS.QuoteComparison.SourceUpdatedDate=BPStaging.QuoteComparison.ValidTo
PS.QuoteComparison.SourceUpdatedDate=BPStaging.QuoteComparison.ValidFrom
PS.QuoteComparison.IsDeleted=BPStaging.QuoteComparison.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_QuoteComparison
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.QuoteComparison';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.QuoteComparison)
BEGIN TRY
    MERGE PS.QuoteComparison T
    USING (
        SELECT
            Id
          , PlacementId
          , DisplayIndex
          , Name
          , ProductId
          , IncludeExpiring
          , IncludeRequested
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , PlacementId
          , DisplayIndex
          , Name
          , ProductId
          , IncludeExpiring
          , IncludeRequested
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.QuoteComparison cp
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , PlacementId
               , DisplayIndex
               , Name
               , ProductId
               , IncludeExpiring
               , IncludeRequested
               , SourceUpdatedDate
               , IsDeleted
               , DataSourceInstanceId
             )
             VALUES
                 (
                     S.Id
                   , S.PlacementId
                   , S.DisplayIndex
                   , S.Name
                   , S.ProductId
                   , S.IncludeExpiring
                   , S.IncludeRequested
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.PlacementId
      , T.DisplayIndex
      , T.Name
      , T.ProductId
      , T.IncludeExpiring
      , T.IncludeRequested
      , T.SourceUpdatedDate
      , T.IsDeleted
      , T.DataSourceInstanceId
    INTERSECT
    SELECT
        S.PlacementId
      , S.DisplayIndex
      , S.Name
      , S.ProductId
      , S.IncludeExpiring
      , S.IncludeRequested
      , S.SourceUpdatedDate
      , S.IsDeleted
      , S.DataSourceInstanceId
)
        THEN UPDATE SET
                 T.PlacementId = S.PlacementId
               , T.DisplayIndex = S.DisplayIndex
               , T.Name = S.Name
               , T.ProductId = S.ProductId
               , T.IncludeExpiring = S.IncludeExpiring
               , T.IncludeRequested = S.IncludeRequested
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);