/*
Lineage
UserKey=dbo.PlacementSystemAudit.DataSourceInstanceId
UserKey=dbo.PlacementSystemAudit.UserId
ModifiedBy=dbo.PlacementSystemAudit.ModifiedBy
TableId=dbo.PlacementSystemAudit.TableId
TableKey=dbo.PlacementSystemAudit.TableKey
Action=dbo.PlacementSystemAudit.Action
ModifiedDateKey=dbo.PlacementSystemAudit.ModifiedDate
ModifiedDate=dbo.PlacementSystemAudit.ModifiedDate
PlacementId=dbo.PlacementSystemAudit.PlacementId
Description=dbo.PlacementSystemAudit.Description
Detail=dbo.PlacementSystemAudit.Detail
GroupingSection=dbo.PlacementSystemAudit.GroupingSection
GroupingSectionType=dbo.PlacementSystemAudit.GroupingSectionType
UserId=dbo.PlacementSystemAudit.UserId
*/
CREATE VIEW APIv1.PlacementSystemAudit
AS
SELECT
    UserKey = CAST(a.DataSourceInstanceId AS NVARCHAR(6)) + '|' + CAST(a.UserId AS NVARCHAR(8))
  , a.<PERSON>y
  , a.TableId
  , a.TableKey
  , a.Action
  , ModifiedDateKey = CONVERT(INT, CONVERT(CHAR(8), a.ModifiedDate, 112))
  , a.ModifiedDate
  , a.PlacementId
  , a.Description
  , a.Detail
  , a.GroupingSection
  , a.GroupingSectionType
  , IsUserEvent = CASE WHEN a.UserId = (
                           SELECT UserId FROM dbo.PlacementSystemUser WHERE UserPrincipalName = 'system'
                       )
                           THEN CAST(0 AS BIT)
                       WHEN a.UserId IS NULL --Unknown / FunctionApp
                           THEN CAST(0 AS BIT)
                       ELSE CAST(1 AS BIT) END
  , a.UserId
FROM
    dbo.PlacementSystemAudit a;