/*
Lineage
PS.LibAdditionalDataItem.LibAdditionalDataItemId=BPStaging.LibAdditionalDataItem.Id
PS.LibAdditionalDataItem.ExternalCode=BPStaging.LibAdditionalDataItem.ExternalCode
PS.LibAdditionalDataItem.Key=BPStaging.LibAdditionalDataItem.Key
PS.LibAdditionalDataItem.ExternalCodeDescription=BPStaging.LibAdditionalDataItem.ExternalCodeDescription
PS.LibAdditionalDataItem.ValueTypeId=BPStaging.LibAdditionalDataItem.ValueTypeId
PS.LibAdditionalDataItem.SupportedLanguageId=BPStaging.LibAdditionalDataItem.SupportedLanguageId
PS.LibAdditionalDataItem.IsoCode=BPStaging.LibAdditionalDataItem.IsoCode
PS.LibAdditionalDataItem.SourceUpdatedDate=BPStaging.LibAdditionalDataItem.ValidTo
PS.LibAdditionalDataItem.SourceUpdatedDate=BPStaging.LibAdditionalDataItem.ValidFrom
PS.LibAdditionalDataItem.IsDeleted=BPStaging.LibAdditionalDataItem.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_LibAdditionalDataItem
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.LibAdditionalDataItem';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.LibAdditionalDataItem t
    USING (
        SELECT
            Id
          , DataSourceInstanceId = 50366
          , ExternalCode
          , [Key]
          , ExternalCodeDescription
          , ValueTypeId
          , SupportedLanguageId
          , IsoCode
          , SourceUpdatedDate = IIF(YEAR(ValidTo) < 9999, ValidTo, ValidFrom)
          , IsDeleted = IIF(YEAR(ValidTo) < 9999, 1, 0)
        FROM
            BPStaging.LibAdditionalDataItem
    ) s
    ON s.Id = t.LibAdditionalDataItemId
       AND s.DataSourceInstanceId = t.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 LibAdditionalDataItemId
               , DataSourceInstanceId
               , ExternalCode
               , [Key]
               , ExternalCodeDescription
               , ValueTypeId
               , SupportedLanguageId
               , IsoCode
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.Id
                   , s.DataSourceInstanceId
                   , s.ExternalCode
                   , s.[Key]
                   , s.ExternalCodeDescription
                   , s.ValueTypeId
                   , s.SupportedLanguageId
                   , s.IsoCode
                   , s.SourceUpdatedDate
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.ExternalCode
                               , s.DataSourceInstanceId
                               , s.[Key]
                               , s.ExternalCodeDescription
                               , s.ValueTypeId
                               , s.SupportedLanguageId
                               , s.IsoCode
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.ExternalCode
                               , t.DataSourceInstanceId
                               , t.[Key]
                               , t.ExternalCodeDescription
                               , t.ValueTypeId
                               , t.SupportedLanguageId
                               , t.IsoCode
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.ExternalCode = s.ExternalCode
               , t.DataSourceInstanceId = s.DataSourceInstanceId
               , t.[Key] = s.[Key]
               , t.ExternalCodeDescription = s.ExternalCodeDescription
               , t.ValueTypeId = s.ValueTypeId
               , t.SupportedLanguageId = s.SupportedLanguageId
               , t.IsoCode = s.IsoCode
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.IsDeleted = s.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND t.IsDeleted = 0
        THEN UPDATE SET
                 t.IsDeleted = 1
               , t.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;