/*
Lineage
dbo.SelectedReason.SelectedReasonId=BPStaging.SelectedReason.Id
dbo.SelectedReason.SelectedReason=BPStaging.SelectedReason.Text
dbo.SelectedReason.IsDeleted=BPStaging.SelectedReason.IsDeprecated
dbo.SelectedReason.SourceUpdatedDate=BPStaging.SelectedReason.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadSelectedReason
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.SelectedReason';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.SelectedReason T
    USING (
        SELECT
            SelectedReasonId = Id
          , SelectedReason = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeleted = IsDeprecated
        FROM
            BPStaging.SelectedReason
    ) S
    ON T.SelectedReasonId = S.SelectedReasonId
    WHEN NOT MATCHED
        THEN INSERT (
                 SelectedReasonId
               , SelectedReason
               , IsDeleted
               , CreatedUTCDate
               , CreatedUser
               , LastUpdatedUTCDate
               , LastUpdatedUser
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.SelectedReasonId
                   , S.SelectedReason
                   , S.IsDeleted
                   , GETUTCDATE()
                   , 'FMAImport'
                   , GETUTCDATE()
                   , 'FMAImport'
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT T.SelectedReason, T.IsDeleted INTERSECT SELECT S.SelectedReason, S.IsDeleted
                         )
        THEN UPDATE SET
                 T.SelectedReason = S.SelectedReason
               , T.IsDeleted = S.IsDeleted
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.LastUpdatedUser = 'FMAImport'
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.LastUpdatedUser = 'FMAImport'
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
