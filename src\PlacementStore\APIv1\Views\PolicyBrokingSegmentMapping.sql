/*
Lineage
PolicyId=dbo.Policy.PolicyId
PolicyKey=dbo.Policy.PolicyKey
DataSourceInstanceId=dbo.Policy.DataSourceInstanceId
*/

CREATE VIEW APIv1.PolicyBrokingSegmentMapping
AS
SELECT
    b.PolicyId
  , b.<PERSON>
  , b.DataSourceInstanceId
  , b.BrokingSegmentId
  , b.BrokingSegment
FROM (
    SELECT
        ROW_NO = ROW_NUMBER() OVER (PARTITION BY a.PolicyId ORDER BY a.BrokingSegmentId DESC)
      , a.PolicyId
      , a.<PERSON>
      , a.DataSourceInstanceId
      , a.BrokingSegmentId
      , a.BrokingSegment
    FROM (
        SELECT DISTINCT
               pol.PolicyId
             , pol.PolicyKey
             , pol.DataSourceInstanceId
             , BrokingSegmentId = CASE WHEN LTRIM(RTRIM(ecs.value)) IN (
                                           'Large & Complex Casualty', 'Large & Complex Property', 'Large & Complex FINEX'
                                       )
                                           THEN 3
                                       WHEN LTRIM(RTRIM(ecs.value)) IN (
                                           'Middle Market', 'Middle Market Property', 'Middle Market Casualty'
                                         , 'Middle Market FINEX'
                                       )
                                           THEN 2
                                       WHEN LTRIM(RTRIM(ecs.value)) IN (
                                           'Select', 'Select FINEX', 'Commercial', 'Commercial FINEX'
                                       )
                                           THEN 1 END
             , BrokingSegment = CASE WHEN LTRIM(RTRIM(ecs.value)) IN (
                                         'Large & Complex Casualty', 'Large & Complex Property', 'Large & Complex FINEX'
                                     )
                                         THEN 'Large & Complex'
                                     WHEN LTRIM(RTRIM(ecs.value)) IN (
                                         'Middle Market', 'Middle Market Property', 'Middle Market Casualty'
                                       , 'Middle Market FINEX'
                                     )
                                         THEN 'Middle Market'
                                     WHEN LTRIM(RTRIM(ecs.value)) IN (
                                         'Select', 'Select FINEX', 'Commercial', 'Commercial FINEX'
                                     )
                                         THEN 'Select' END
        FROM
            dbo.Policy pol
            INNER JOIN dbo.PolicySection ps WITH (NOLOCK)
                ON ps.PolicyId = pol.PolicyId
                   AND ps.IsDeleted = 0

            INNER JOIN PAS.PolicySection pasps WITH (NOLOCK)
                ON pasps.PASPolicySectionId = ps.PACTPolicySectionId

            INNER JOIN PAS.PolicySectionAttribute paspsa WITH (NOLOCK)
                ON paspsa.PolicySectionKey = pasps.PolicySectionKey
                   AND paspsa.DataSourceInstanceId = pasps.DataSourceInstanceId
                   AND paspsa.IsDeleted = 0
            CROSS APPLY STRING_SPLIT(paspsa.EpicCapacitySegment, ',') ecs
        WHERE
            pol.DataSourceInstanceId = 50001
            AND EXISTS (
            SELECT 1 FROM dbo.PolicyOrganisation po WITH (NOLOCK) WHERE po.PolicyId = pol.PolicyId AND po.IsDeleted = 0
        )
    ) a
    WHERE
        a.BrokingSegmentId IS NOT NULL
) b
WHERE
    b.ROW_NO = 1;
