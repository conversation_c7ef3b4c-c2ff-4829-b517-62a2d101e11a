/*
Lineage
dbo.PlacementProduct.LastUpdatedUTCDate=BPStaging.RiskStructure.ValidFrom
dbo.PlacementProduct.ProductId=BPStaging.RiskStructure.ProductId
dbo.PlacementProduct.PlacementStructureId=dbo.PlacementStructure.PlacementStructureId
*/
CREATE PROCEDURE BPStaging.LoadPlacementProduct
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementProduct';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.RiskStructure
        );

IF @CheckCount <> 0
BEGIN TRY
    INSERT INTO
        dbo.PlacementProduct
        (
            CreatedUser
          , CreatedUTCDate
          , LastUpdatedUTCDate
          , ProductId
          , PlacementStructureId
        )
    SELECT
        CreatedUser = 'FMA'
      , CreatedUTCDate = GETUTCDATE()
      , ValidFrom = MAX(PP.ValidFrom)
      , PP.ProductId
      , PS.PlacementStructureId
    FROM
        BPStaging.RiskStructure PP
        INNER JOIN rpt.ProductHierarchy PH
            ON PP.ProductId = PH.ProductId

        INNER JOIN (
            SELECT
                p.PlacementId
              , p.PlacementSystemId
              , p.DataSourceInstanceId
              , p.ServicingPlatformId
            FROM
                dbo.Placement p
        /*inner join dbo.PlacementListener PL on  PL.PlacementId = P.PlacementId*/
        ) P
            ON PP.PlacementId = P.PlacementSystemId
               AND P.DataSourceInstanceId = 50366

        INNER JOIN (
            SELECT
                PlacementStructureId
              , PlacementId
              , PlacementSystemStructureId
              , ProductId
            FROM
                dbo.PlacementStructure
            WHERE
                IsDeleted = 0
        ) PS
            ON PP.Id = PS.PlacementSystemStructureId
               AND P.PlacementId = PS.PlacementId
               AND PP.ProductId = PS.ProductId
    --LEFT JOIN dbo.PlacementProduct PP1 ON PP.ProductId = PP1.ProductId AND PS.PlacementStructureId = PP1.PlacementStructureId
    WHERE
        NOT EXISTS (
        SELECT 1
        FROM
            dbo.PlacementProduct NC
        WHERE
            PP.ProductId = NC.ProductId
            AND PS.PlacementStructureId = NC.PlacementStructureId
    )
    GROUP BY
        PP.ProductId
      , PS.PlacementStructureId;

    SELECT @InsertedCount = @@ROWCOUNT;

    SELECT @UpdatedCount = 0;

    --delete incorrect placement products referrring to invalid structures
    UPDATE pp
    SET pp.IsDeleted = 1
    FROM
        dbo.PlacementProduct pp
        INNER JOIN dbo.PlacementStructure st
            ON pp.PlacementStructureId = st.PlacementStructureId
               AND st.IsDeleted = 0
    WHERE
        NOT EXISTS (
        SELECT 1
        FROM
            BPStaging.RiskStructure source
        WHERE
            st.PlacementSystemStructureId = source.Id
            AND pp.ProductId = source.ProductId
    )
        AND pp.CreatedUser = 'FMA'; --only delete BP items

    SELECT @UpdatedCount = @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
GO