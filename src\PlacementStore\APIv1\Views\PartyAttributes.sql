/*
Lineage
PartyId=dbo.PartyAttribute.PartyId
PartyId=dbo.Party.PartyId
IndustryID=dbo.PartyAttribute.LookupId
IndustryID=dbo.LookupGroup.LocalCode
OwnerID=dbo.PartyAttribute.LookupId
OwnerID=dbo.LookupGroup.LocalCode
ProgramTypeID=dbo.PartyAttribute.LookupId
ProgramTypeID=dbo.LookupGroup.LocalCode
TurnoverID=dbo.PartyAttribute.LookupId
TurnoverID=dbo.LookupGroup.LocalCode
SegmentationID=dbo.PartyAttribute.LookupId
SegmentationID=dbo.LookupGroup.LocalCode
BranchID=dbo.Lookup.Id
*/
CREATE VIEW APIv1.PartyAttributes
AS
SELECT
    PartyId
  , IndustryID = MAX(IndustryId)
  , OwnerID = MAX(OwnerId)
  , ProgramTypeID = MAX(ProgramTypeId)
  , TurnoverID = MAX(TurnoverId)
  , SegmentationID = MAX(SegmentationId)
  , BranchID = MAX(BranchId)
FROM (
    SELECT
        PA.PartyId
      , IndustryId = CASE WHEN LG.LocalCode = '57'
                              THEN PA.LookupId
                          ELSE NULL END
      , OwnerId = CASE WHEN LG.LocalCode = '59'
                           THEN PA.LookupId
                       ELSE NULL END
      , ProgramTypeId = CASE WHEN LG.LocalCode = '60'
                                 THEN PA.LookupId
                             ELSE NULL END
      , TurnoverId = CASE WHEN LG.LocalCode = '88'
                              THEN PA.LookupId
                          ELSE NULL END
      , SegmentationId = CASE WHEN LG.LocalCode = '90'
                                  THEN PA.LookupId
                              ELSE NULL END
      , BranchId = NULL
    FROM
        dbo.PartyAttribute PA
        JOIN dbo.LookupGroup LG
            ON LG.Id = PA.LookupGroupId

        JOIN dbo.Lookup L
            ON L.Id = PA.LookupId
    WHERE
        PA.IsDeleted = 0
        AND LG.IsDeleted = 0
        AND LG.IsActive = 1
        AND L.IsDeleted = 0
        AND L.IsActive = 1
    UNION
    SELECT
        P.PartyId
      , NULL
      , NULL
      , NULL
      , NULL
      , NULL
      , BranchId = L.Id
    FROM
        dbo.Party P
        JOIN dbo.Lookup L
            ON L.LocalCode = CAST(P.BranchId AS NVARCHAR(50))

        JOIN dbo.LookupGroup LG
            ON LG.Id = L.LookupGroupId
               AND LG.LocalCode = 'Tabela_GruposHierarq'
               AND LG.DataSourceInstanceId = 50003
) PA
GROUP BY
    PartyId;
