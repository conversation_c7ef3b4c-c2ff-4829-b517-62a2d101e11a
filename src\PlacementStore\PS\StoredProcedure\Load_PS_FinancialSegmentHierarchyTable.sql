/*
Lineage
PS.FinancialSegmentHierarchyTable.DataSourceInstanceId=ref.FinancialSegment.DataSourceInstanceId
PS.FinancialSegmentHierarchyTable.FinancialSegmentId=ref.FinancialSegment.FinancialSegmentId
PS.FinancialSegmentHierarchyTable.ParentId=ref.FinancialSegment.FinancialSegmentId
PS.FinancialSegmentHierarchyTable.PASFinancialSegmentId=ref.FinancialSegment.PASFinancialSegmentId
PS.FinancialSegmentHierarchyTable.FinancialSegment=ref.FinancialSegment.FinancialSegment
PS.FinancialSegmentHierarchyTable.FinancialSegment=ref.FinancialSegment.Level1
PS.FinancialSegmentHierarchyTable.FinancialSegment=ref.FinancialSegment.Level2
PS.FinancialSegmentHierarchyTable.FinancialSegment=ref.FinancialSegment.Level3
PS.FinancialSegmentHierarchyTable.FinancialSegment=ref.FinancialSegment.Level4
PS.FinancialSegmentHierarchyTable.SegLevel1=ref.FinancialSegment.FinancialSegment
PS.FinancialSegmentHierarchyTable.SegLevel1=ref.FinancialSegment.Level1
PS.FinancialSegmentHierarchyTable.SegLevel2=ref.FinancialSegment.Level2
PS.FinancialSegmentHierarchyTable.SegLevel3=ref.FinancialSegment.Level3
PS.FinancialSegmentHierarchyTable.SegLevel4=ref.FinancialSegment.Level4
PS.FinancialSegmentHierarchyTable.SegLevel5=ref.FinancialSegment.Level5
PS.FinancialSegmentHierarchyTable.SegRole=ref.FinancialStructureAttribute.EpicUniqBranch
PS.FinancialSegmentHierarchyTable.SegRole=ref.FinancialStructureAttribute.EpicUniqDepartment
PS.FinancialSegmentHierarchyTable.SegRole=ref.FinancialStructureAttribute.EpicUniqProfitCenter
PS.FinancialSegmentHierarchyTable.SegNode=ref.FinancialSegment.FinancialSegmentId
PS.FinancialSegmentHierarchyTable.Level1SegId=ref.FinancialSegment.FinancialSegmentId
PS.FinancialSegmentHierarchyTable.Level2SegId=ref.FinancialSegment.FinancialSegmentId
PS.FinancialSegmentHierarchyTable.Level3SegId=ref.FinancialSegment.FinancialSegmentId
PS.FinancialSegmentHierarchyTable.Level4SegId=ref.FinancialSegment.FinancialSegmentId
PS.FinancialSegmentHierarchyTable.Level5SegId=ref.FinancialSegment.FinancialSegmentId
*/

CREATE PROCEDURE PS.Load_PS_FinancialSegmentHierarchyTable
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.FinancialSegmentHierarchyTable';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY;

    /* Merge results into target */
    WITH cte_Tree AS (
        SELECT
            DataSourceInstanceId = fs.DataSourceInstanceId
          , fs.FinancialSegmentId
          , ParentId = -1
          , fs.PASFinancialSegmentId
          , fs.FinancialSegment
          , SegLevel1 = fs.FinancialSegment
          , SegLevel2 = CAST(NULL AS NVARCHAR(500))
          , SegLevel3 = CAST(NULL AS NVARCHAR(500))
          , SegLevel4 = CAST(NULL AS NVARCHAR(500))
          , SegLevel5 = CAST(NULL AS NVARCHAR(500))
          , LevelNum = 1
          , SegRole = CASE WHEN fs.DataSourceInstanceId IN (
                               50001, 50354
                           )
                               THEN CASE WHEN COALESCE(
                                                  fsa.EpicUniqBranch
                                                , fsa.EpicUniqDepartment
                                                , fsa.EpicUniqProfitCenter
                                                , -1
                                              ) < 0
                                             THEN 'AGENCY'
                                         WHEN COALESCE(fsa.EpicUniqDepartment, fsa.EpicUniqProfitCenter, -1) < 0
                                             THEN 'Branch'
                                         WHEN COALESCE(fsa.EpicUniqProfitCenter, -1) < 0
                                             THEN 'Department'
                                         WHEN COALESCE(fsa.EpicUniqProfitCenter, -1) > 0
                                             THEN 'Profit Center'
                                         ELSE 'Collection' END END
          , SegNode = CAST('/' + CAST(fs.FinancialSegmentId AS VARCHAR(30)) + '/' AS HIERARCHYID)
          , Level1SegId = fs.FinancialSegmentId
          , Level2SegId = CAST(NULL AS INT)
          , Level3SegId = CAST(NULL AS INT)
          , Level4SegId = CAST(NULL AS INT)
          , Level5SegId = CAST(NULL AS INT)
        FROM
            ref.FinancialSegment fs
            LEFT JOIN ref.FinancialStructureAttribute fsa
                ON fs.FinancialStructureId = fsa.FinancialStructureId
        WHERE
            fs.DataSourceInstanceId <> 50000
        UNION ALL
        SELECT
            DataSourceInstanceId
          , FinancialSegmentId = 1000000 + FinancialSegmentId --Legal Entity levle1
          , ParentId = -1
          , PASFinancialSegmentId
          , FinancialSegment = Level1
          , SegLevel1 = Level1
          , SegLevel2 = CAST(NULL AS NVARCHAR(500))
          , SegLevel3 = CAST(NULL AS NVARCHAR(500))
          , SegLevel4 = CAST(NULL AS NVARCHAR(500))
          , SegLevel5 = CAST(NULL AS NVARCHAR(500))
          , LevelNum = 1
          , SegRole = 'Legal Entity'
          , SegNode = CAST('/' + CAST(1000000 + FinancialSegmentId AS VARCHAR(30)) + '/' AS HIERARCHYID)
          , Level1SegId = 1000000 + FinancialSegmentId
          , Level2SegId = CAST(NULL AS INT)
          , Level3SegId = CAST(NULL AS INT)
          , Level4SegId = CAST(NULL AS INT)
          , Level5SegId = CAST(NULL AS INT)
        FROM
            ref.FinancialSegment fs
        WHERE
            fs.DataSourceInstanceId = 50000
            AND Level1 IS NOT NULL
        UNION ALL
        SELECT
            DataSourceInstanceId
          , FinancialSegmentId = 2000000 + FinancialSegmentId --Business Unit level2
          , ParentId = 1000000 + FinancialSegmentId
          , PASFinancialSegmentId
          , FinancialSegment = Level2
          , SegLevel1 = Level1
          , SegLevel2 = Level2
          , SegLevel3 = CAST(NULL AS NVARCHAR(500))
          , SegLevel4 = CAST(NULL AS NVARCHAR(500))
          , SegLevel5 = CAST(NULL AS NVARCHAR(500))
          , LevelNum = 2
          , SegRole = 'Business Unit'
          , SegNode = CAST('/' + CAST(1000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(2000000 + FinancialSegmentId AS VARCHAR(30)) + '/' AS HIERARCHYID)
          , Level1SegId = 1000000 + FinancialSegmentId
          , Level2SegId = 2000000 + FinancialSegmentId
          , Level3SegId = CAST(NULL AS INT)
          , Level4SegId = CAST(NULL AS INT)
          , Level5SegId = CAST(NULL AS INT)
        FROM
            ref.FinancialSegment fs
        WHERE
            fs.DataSourceInstanceId = 50000
            AND Level2 IS NOT NULL
        UNION ALL
        SELECT
            DataSourceInstanceId
          , FinancialSegmentId = 3000000 + FinancialSegmentId --Function level3
          , ParentId = 2000000 + FinancialSegmentId
          , PASFinancialSegmentId
          , FinancialSegment = Level3
          , SegLevel1 = Level1
          , SegLevel2 = Level2
          , SegLevel3 = Level3
          , SegLevel4 = CAST(NULL AS NVARCHAR(500))
          , SegLevel5 = CAST(NULL AS NVARCHAR(500))
          , LevelNum = 3
          , SegRole = 'Function'
          , SegNode = CAST('/' + CAST(1000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(2000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(3000000 + FinancialSegmentId AS VARCHAR(30)) + '/' AS HIERARCHYID)
          , Level1SegId = 1000000 + FinancialSegmentId
          , Level2SegId = 2000000 + FinancialSegmentId
          , Level3SegId = 3000000 + FinancialSegmentId
          , Level4SegId = CAST(NULL AS INT)
          , Level5SegId = CAST(NULL AS INT)
        FROM
            ref.FinancialSegment fs
        WHERE
            fs.DataSourceInstanceId = 50000
            AND Level3 IS NOT NULL
        UNION ALL
        SELECT
            DataSourceInstanceId
          , FinancialSegmentId = 4000000 + FinancialSegmentId --Department level4
          , ParentId = 3000000 + FinancialSegmentId
          , PASFinancialSegmentId
          , FinancialSegment = Level4
          , SegLevel1 = Level1
          , SegLevel2 = Level2
          , SegLevel3 = Level3
          , SegLevel4 = Level4
          , SegLevel5 = CAST(NULL AS NVARCHAR(500))
          , LevelNum = 4
          , SegRole = 'Department'
          , SegNode = CAST('/' + CAST(1000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(2000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(3000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(4000000 + FinancialSegmentId AS VARCHAR(30)) + '/' AS HIERARCHYID)
          , Level1SegId = 1000000 + FinancialSegmentId
          , Level2SegId = 2000000 + FinancialSegmentId
          , Level3SegId = 3000000 + FinancialSegmentId
          , Level4SegId = 4000000 + FinancialSegmentId
          , Level5SegId = CAST(NULL AS INT)
        FROM
            ref.FinancialSegment fs
        WHERE
            fs.DataSourceInstanceId = 50000
            AND Level4 IS NOT NULL
        UNION ALL
        SELECT
            DataSourceInstanceId
          , FinancialSegmentId = FinancialSegmentId --Team Level
          , ParentId = 4000000 + FinancialSegmentId
          , PASFinancialSegmentId
          , FinancialSegment = FinancialSegment
          , SegLevel1 = Level1
          , SegLevel2 = Level2
          , SegLevel3 = Level3
          , SegLevel4 = Level4
          , SegLevel5 = Level5
          , LevelNum = 5
          , SegRole = 'Team'
          , SegNode = CAST('/' + CAST(1000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(2000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(3000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(4000000 + FinancialSegmentId AS VARCHAR(30)) + '/'
                           + CAST(FinancialSegmentId AS VARCHAR(30)) + '/' AS HIERARCHYID)
          , Level1SegId = 1000000 + FinancialSegmentId
          , Level2SegId = 2000000 + FinancialSegmentId
          , Level3SegId = 3000000 + FinancialSegmentId
          , Level4SegId = 4000000 + FinancialSegmentId
          , Level5SegId = FinancialSegmentId
        FROM
            ref.FinancialSegment fs
        WHERE
            fs.DataSourceInstanceId = 50000
            AND Level5 IS NOT NULL
    )
    MERGE PS.FinancialSegmentHierarchyTable t
    USING (
        SELECT
            DataSourceInstanceId
          , FinancialSegmentId
          , ParentId
          , PASFinancialSegmentId
          , FinancialSegment
          , SegLevel1
          , SegLevel2
          , SegLevel3
          , SegLevel4
          , SegLevel5
          , LevelNum
          , SegRole
          , SegNode
          , Level1SegId
          , Level2SegId
          , Level3SegId
          , Level4SegId
          , Level5SegId
        FROM
            cte_Tree
    ) s
    ON t.FinancialSegmentId = s.FinancialSegmentId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , FinancialSegmentId
               , ParentId
               , PASFinancialSegmentId
               , FinancialSegment
               , SegLevel1
               , SegLevel2
               , SegLevel3
               , SegLevel4
               , SegLevel5
               , LevelNum
               , SegRole
               , SegNode
               , ETLCreatedDate
               , ETLUpdatedDate
               , Level1SegId
               , Level2SegId
               , Level3SegId
               , Level4SegId
               , Level5SegId
             )
             VALUES
                 (
                     s.DataSourceInstanceId
                   , s.FinancialSegmentId
                   , s.ParentId
                   , s.PASFinancialSegmentId
                   , s.FinancialSegment
                   , s.SegLevel1
                   , s.SegLevel2
                   , s.SegLevel3
                   , s.SegLevel4
                   , s.SegLevel5
                   , s.LevelNum
                   , s.SegRole
                   , s.SegNode
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , s.Level1SegId
                   , s.Level2SegId
                   , s.Level3SegId
                   , s.Level4SegId
                   , s.Level5SegId
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 t.DataSourceInstanceId
                               , t.FinancialSegmentId
                               , t.ParentId
                               , t.PASFinancialSegmentId
                               , t.FinancialSegment
                               , t.SegLevel1
                               , t.SegLevel2
                               , t.SegLevel3
                               , t.SegLevel4
                               , t.SegLevel5
                               , t.LevelNum
                               , t.SegRole
                               , t.SegNode
                               , t.Level1SegId
                               , t.Level2SegId
                               , t.Level3SegId
                               , t.Level4SegId
                               , t.Level5SegId
                             INTERSECT
                             SELECT
                                 s.DataSourceInstanceId
                               , s.FinancialSegmentId
                               , s.ParentId
                               , s.PASFinancialSegmentId
                               , s.FinancialSegment
                               , s.SegLevel1
                               , s.SegLevel2
                               , s.SegLevel3
                               , s.SegLevel4
                               , s.SegLevel5
                               , s.LevelNum
                               , s.SegRole
                               , s.SegNode
                               , s.Level1SegId
                               , s.Level2SegId
                               , s.Level3SegId
                               , s.Level4SegId
                               , s.Level5SegId
                         )
        THEN UPDATE SET
                 t.DataSourceInstanceId = s.DataSourceInstanceId
               , t.FinancialSegmentId = s.FinancialSegmentId
               , t.ParentId = s.ParentId
               , t.PASFinancialSegmentId = s.PASFinancialSegmentId
               , t.FinancialSegment = s.FinancialSegment
               , t.SegLevel1 = s.SegLevel1
               , t.SegLevel2 = s.SegLevel2
               , t.SegLevel3 = s.SegLevel3
               , t.SegLevel4 = s.SegLevel4
               , t.SegLevel5 = s.SegLevel5
               , t.LevelNum = s.LevelNum
               , t.SegRole = s.SegRole
               , t.SegNode = s.SegNode
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.Level1SegId = s.Level1SegId
               , t.Level2SegId = s.Level2SegId
               , t.Level3SegId = s.Level3SegId
               , t.Level4SegId = s.Level4SegId
               , t.Level5SegId = s.Level5SegId
    WHEN NOT MATCHED BY SOURCE AND t.IsDeprecated = 0
        THEN UPDATE SET
                 t.IsDeprecated = 1
               , t.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
GO