/*
Lineage
dbo.Scope.ScopeId=BPStaging.Scope.Id
dbo.Scope.ScopeItemId=BPStaging.Scope.ScopeItemId
dbo.Scope.TeamId=BPStaging.Scope.TeamId
dbo.Scope.BrokingSegmentId=BPStaging.Scope.BrokingSegmentId
dbo.Scope.BrokingRegionId=BPStaging.Scope.RegionId
dbo.Scope.BrokingSubSegmentId=BPStaging.Scope.ProductId
dbo.Scope.IndustryId=BPStaging.Scope.IndustryId
dbo.Scope.PlacementStatusId=ref.PlacementStatus.PlacementStatusId
dbo.Scope.SourceUpdatedDate=BPStaging.Scope.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadScope
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.Scope';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.Scope
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE dbo.Scope T
    USING (
        SELECT
            ScopeId = s.Id
          , s.ScopeItemId
          , s.TeamId
          , s.BrokingSegmentId
          , BrokingRegionId = s.RegionId
          , BrokingSubSegmentId = s.ProductId
          , s.IndustryId
          , ps.PlacementStatusId
          , SourceUpdatedDate = s.ValidFrom
          , DataSourceInstanceId = 50366
          , IsDeleted = 0
        FROM
            BPStaging.Scope s
            LEFT JOIN ref.PlacementStatus ps
                ON ps.PlacementStatusKey = s.PlacementStatusId
                   AND ps.DataSourceInstanceId = 50366
    ) S
    ON S.ScopeId = T.ScopeId
       AND S.ScopeItemId = T.ScopeItemId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 ScopeId
               , ScopeItemId
               , DataSourceInstanceId
               , TeamId
               , BrokingSegmentId
               , BrokingRegionId
               , BrokingSubSegmentId
               , IndustryId
               , PlacementStatusId
               , ETLCreatedDate
               , LastUpdatedUTCDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.ScopeId
                   , S.ScopeItemId
                   , S.DataSourceInstanceId
                   , S.TeamId
                   , S.BrokingSegmentId
                   , S.BrokingRegionId
                   , S.BrokingSubSegmentId
                   , S.IndustryId
                   , S.PlacementStatusId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.DataSourceInstanceId
      , T.TeamId
      , T.BrokingSegmentId
      , T.BrokingRegionId
      , T.BrokingSubSegmentId
      , T.IndustryId
      , T.PlacementStatusId
      , T.SourceUpdatedDate
      , T.IsDeleted
    INTERSECT
    SELECT
        S.DataSourceInstanceId
      , S.TeamId
      , S.BrokingSegmentId
      , S.BrokingRegionId
      , S.BrokingSubSegmentId
      , S.IndustryId
      , S.PlacementStatusId
      , S.SourceUpdatedDate
      , S.IsDeleted
)
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.TeamId = S.TeamId
               , T.BrokingSegmentId = S.BrokingSegmentId
               , T.BrokingRegionId = S.BrokingRegionId
               , T.BrokingSubSegmentId = S.BrokingSubSegmentId
               , T.IndustryId = S.IndustryId
               , T.PlacementStatusId = S.PlacementStatusId
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;