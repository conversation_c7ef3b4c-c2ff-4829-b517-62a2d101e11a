<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects Condition="'$(MSBuildVersion)' == '' Or '$(MSBuildVersion)' &lt; '16.0'">$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
    <HasSharedItems>true</HasSharedItems>
    <SharedGUID>3609cc6a-5ff3-4b39-ab4a-897f017d23f5</SharedGUID>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <Import_RootNamespace>build</Import_RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <None Include="$(MSBuildThisFileDirectory)Build\build_adf.yml" />
    <None Include="$(MSBuildThisFileDirectory)Build\build_apps.yml" />
    <None Include="$(MSBuildThisFileDirectory)Build\build_db.yml" />
    <None Include="$(MSBuildThisFileDirectory)Build\dast_scan.yml" />
    <None Include="$(MSBuildThisFileDirectory)ci-pipeline-config.yml" />
    <None Include="$(MSBuildThisFileDirectory)ci-pipeline-reporting.yml" />
    <None Include="$(MSBuildThisFileDirectory)ci-pipeline.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\branch_prod.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\clean_up.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\deploy_reporting_models.yml" />
	<None Include="$(MSBuildThisFileDirectory)Deploy\deploy_reporting_scale_sync.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\deploy_adf.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\dev.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\iat.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\scale_down.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\scale_up.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\uat.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\qa.yml" />
    <None Include="$(MSBuildThisFileDirectory)Build\code_analysis.yml" />
    <None Include="$(MSBuildThisFileDirectory)Config\start.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\branch_qa.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\branch_uat.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\prod.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\ci.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\cleanup.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\deploy_infra.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\deploy_apps.yml" />
    <None Include="$(MSBuildThisFileDirectory)Deploy\ci.yml" />
    <None Include="$(MSBuildThisFileDirectory)nightly-pipeline.yml" />
    <None Include="$(MSBuildThisFileDirectory)Test\api_tests.yml" />
    <None Include="$(MSBuildThisFileDirectory)Test\int_tests.yml" />
    <None Include="$(MSBuildThisFileDirectory)Test\sql_tests.yml" />
  </ItemGroup>
</Project>