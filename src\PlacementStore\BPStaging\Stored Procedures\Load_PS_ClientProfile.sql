/*
Lineage
PS.ClientProfile.ClientProfileId=BPStaging.ClientProfile.Id
PS.ClientProfile.ClientProfileKey=BPStaging.ClientProfile.Id
PS.ClientProfile.PlacementId=dbo.Placement.PlacementId
PS.ClientProfile.PartyId=BPStaging.ClientProfile.PartyId
PS.ClientProfile.PartyId=BPStaging.PlacementPartyRelationship.PartyId
PS.ClientProfile.GlobalPartyId=dbo.Party.GlobalPartyId
PS.ClientProfile.DescriptionOfOperations=BPStaging.ClientProfile.DescriptionOfOperations
PS.ClientProfile.WebsiteAddress=BPStaging.ClientProfile.WebsiteAddress
PS.ClientProfile.RegistrationNumber=BPStaging.ClientProfile.RegistrationNumber
PS.ClientProfile.IndustryId=BPStaging.ClientProfile.IndustryId
PS.ClientProfile.FinancialsAsAtDate=BPStaging.ClientProfile.FinancialsAsAtDate
PS.ClientProfile.CurrencyId=BPStaging.ClientProfile.CurrencyId
PS.ClientProfile.GlobalAnnualRevenue=BPStaging.ClientProfile.GlobalAnnualRevenue
PS.ClientProfile.EBITDA=BPStaging.ClientProfile.EBITDA
PS.ClientProfile.GlobalEmployeeCount=BPStaging.ClientProfile.GlobalEmployeeCount
PS.ClientProfile.SPCreditRating=BPStaging.ClientProfile.SPCreditRating
PS.ClientProfile.DebtRatio=BPStaging.ClientProfile.DebtRatio
PS.ClientProfile.ProfitMargin=BPStaging.ClientProfile.ProfitMargin
PS.ClientProfile.OperationalCashFlow=BPStaging.ClientProfile.OperationalCashFlow
PS.ClientProfile.SourceUpdatedDate=BPStaging.ClientProfile.ValidFrom
PS.ClientProfile.IsDeleted=BPStaging.ClientProfile.ValidTo
PS.ClientProfile.AdditionalClientInfo=BPStaging.ClientProfile.AdditionalClientInfo
*/
CREATE PROCEDURE BPStaging.Load_PS_ClientProfile
    @Success BIT OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ClientProfile';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ClientProfile)
BEGIN TRY
    DROP TABLE IF EXISTS #ClientProfile;

    -- Load all records from the Client Profile staging table and identify the records to load into PS
    SELECT
        cp.Id
      , pl.PlacementId
      , PartyId = ISNULL(cp.PartyId, ppr.PartyId)
      , p.GlobalPartyId
      , RowNo = ROW_NUMBER() OVER (PARTITION BY ppr.PlacementId ORDER BY ISNULL(cp.PartyId, ppr.PartyId) DESC, ppr.Id ASC)
      , cp.DescriptionOfOperations
      , cp.WebsiteAddress
      , cp.RegistrationNumber
      , cp.IndustryId
      , cp.FinancialsAsAtDate
      , cp.CurrencyId
      , cp.GlobalAnnualRevenue
      , cp.EBITDA
      , cp.GlobalEmployeeCount
      , cp.SPCreditRating
      , cp.DebtRatio
      , cp.ProfitMargin
      , cp.OperationalCashFlow
      , cp.AdditionalClientInfo
      , cp.ValidFrom
      , cp.ValidTo
    INTO #ClientProfile
    FROM
        /* Shouldn't be joining 2 staging tables. But currently full loads so works. */
        BPStaging.ClientProfile cp
        LEFT JOIN BPStaging.PlacementPartyRelationship ppr
            ON ppr.PlacementId = cp.PlacementId
               AND ppr.IsDeleted = 0

        LEFT JOIN dbo.Party p
            ON p.PartyId = ppr.PartyId

        /* INNER JOIN to prevent error trying to insert NULL into PlacementId */

        INNER JOIN dbo.Placement pl
            ON pl.PlacementSystemId = cp.PlacementId
               AND pl.DataSourceInstanceId = 50366
    WHERE
        ppr.Type = 3;

    -- Merge the first Client Profile record for the Placement which has a PartyId using the RowNo field
    MERGE PS.ClientProfile T
    USING (
        SELECT
            Id
          , DataSourceInstanceId = 50366
          , ClientProfileKey = CONCAT('CLPRO|', Id)
          , PlacementId
          , PartyId
          , GlobalPartyId
          , DescriptionOfOperations
          , WebsiteAddress
          , RegistrationNumber
          , IndustryId
          , FinancialsAsAtDate
          , CurrencyId
          , GlobalAnnualRevenue
          , EBITDA
          , GlobalEmployeeCount
          , SPCreditRating
          , DebtRatio
          , ProfitMargin
          , OperationalCashFlow
          , AdditionalClientInfo
          , SourceUpdatedDate = ValidFrom
          , IsDeleted = CASE WHEN YEAR(ValidTo) = 9999
                                 THEN 0
                             ELSE 1 END
        FROM
            #ClientProfile
        WHERE
            RowNo = 1
    ) S
    ON S.Id = T.ClientProfileId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 ClientProfileId
               , DataSourceInstanceId
               , ClientProfileKey
               , PlacementId
               , PartyId
               , GlobalPartyId
               , DescriptionOfOperations
               , WebsiteAddress
               , RegistrationNumber
               , IndustryId
               , FinancialsAsAtDate
               , CurrencyId
               , GlobalAnnualRevenue
               , EBITDA
               , GlobalEmployeeCount
               , SPCreditRating
               , DebtRatio
               , ProfitMargin
               , OperationalCashFlow
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
               , AdditionalClientInfo
             )
             VALUES
                 (
                     S.Id
                   , S.DataSourceInstanceId
                   , S.ClientProfileKey
                   , S.PlacementId
                   , S.PartyId
                   , S.GlobalPartyId
                   , S.DescriptionOfOperations
                   , S.WebsiteAddress
                   , S.RegistrationNumber
                   , S.IndustryId
                   , S.FinancialsAsAtDate
                   , S.CurrencyId
                   , S.GlobalAnnualRevenue
                   , S.EBITDA
                   , S.GlobalEmployeeCount
                   , S.SPCreditRating
                   , S.DebtRatio
                   , S.ProfitMargin
                   , S.OperationalCashFlow
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.AdditionalClientInfo
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.PlacementId
      , S.DataSourceInstanceId
      , S.ClientProfileKey
      , S.PartyId
      , S.GlobalPartyId
      , S.DescriptionOfOperations
      , S.WebsiteAddress
      , S.RegistrationNumber
      , S.IndustryId
      , S.FinancialsAsAtDate
      , S.CurrencyId
      , S.GlobalAnnualRevenue
      , S.EBITDA
      , S.GlobalEmployeeCount
      , S.SPCreditRating
      , S.DebtRatio
      , S.ProfitMargin
      , S.OperationalCashFlow
      , S.SourceUpdatedDate
      , S.IsDeleted
      , S.AdditionalClientInfo
    INTERSECT
    SELECT
        T.PlacementId
      , T.DataSourceInstanceId
      , T.ClientProfileKey
      , T.PartyId
      , T.GlobalPartyId
      , T.DescriptionOfOperations
      , T.WebsiteAddress
      , T.RegistrationNumber
      , T.IndustryId
      , T.FinancialsAsAtDate
      , T.CurrencyId
      , T.GlobalAnnualRevenue
      , T.EBITDA
      , T.GlobalEmployeeCount
      , T.SPCreditRating
      , T.DebtRatio
      , T.ProfitMargin
      , T.OperationalCashFlow
      , T.SourceUpdatedDate
      , T.IsDeleted
      , T.AdditionalClientInfo
)
        THEN UPDATE SET
                 T.PlacementId = S.PlacementId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ClientProfileKey = S.ClientProfileKey
               , T.PartyId = S.PartyId
               , T.GlobalPartyId = S.GlobalPartyId
               , T.DescriptionOfOperations = S.DescriptionOfOperations
               , T.WebsiteAddress = S.WebsiteAddress
               , T.RegistrationNumber = S.RegistrationNumber
               , T.IndustryId = S.IndustryId
               , T.FinancialsAsAtDate = S.FinancialsAsAtDate
               , T.CurrencyId = S.CurrencyId
               , T.GlobalAnnualRevenue = S.GlobalAnnualRevenue
               , T.EBITDA = S.EBITDA
               , T.GlobalEmployeeCount = S.GlobalEmployeeCount
               , T.SPCreditRating = S.SPCreditRating
               , T.DebtRatio = S.DebtRatio
               , T.ProfitMargin = S.ProfitMargin
               , T.OperationalCashFlow = S.OperationalCashFlow
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.AdditionalClientInfo = S.AdditionalClientInfo
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);