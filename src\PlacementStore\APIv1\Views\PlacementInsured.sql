/*
Lineage
PlacementId=dbo.Placement.PlacementId
LastUpdatedUTCDate=dbo.PlacementPartyRole.UpdatedDate
LastUpdatedUTCDate=dbo.Placement.LastUpdatedUTCDate
LastUpdatedUTCDate=dbo.Placement.CreatedUTCDate
LastUpdatedUTCDate=dbo.PlacementListener.ValidFrom
RenewalProcessStartDate=dbo.PlacementListener.RenewalProcessStartDate
PartyID=dbo.PlacementPartyRole.PartyId
PartyRoleDescription=Reference.PartyRole.PartyRoleDescription
PlacementDataSourceInstanceID=dbo.PlacementListener.PlacementDataSourceInstanceId
ExpiryDate=dbo.Placement.ExpiryDate
Incumbent=dbo.PlacementPartyRole.Incumbent
InsuredTypeID=ref.PartyRole.PartyRoleKey
InsuredTypeID=ref.PartyRole.DataSourceInstanceId
IsPrimaryParty=dbo.PlacementPartyRole.IsPrimaryParty
CountryID=Reference.Country.CountryId
*/

CREATE VIEW APIv1.PlacementInsured
AS

SELECT
    pl.PlacementId
  , LastUpdatedUTCDate = (GREATEST(ppr.UpdatedDate, pl.LastUpdatedUTCDate, pl.CreatedUTCDate, pli.ValidFrom))
  , pli.RenewalProcessStartDate
  , PartyID = ppr.PartyId
  , gpr.PartyRoleDescription
  , PlacementDataSourceInstanceID = pli.PlacementDataSourceInstanceId
  , pl.ExpiryDate
  , ppr.Incumbent
  , InsuredTypeID = CASE WHEN pr.DataSourceInstanceId = 50366
                             THEN CAST(pr.PartyRoleKey AS INT)
                         --WHEN pr.PartyRoleKey IS NOT NULL THEN pr.PartyRoleKey
                         --WHEN pr.PartyRole = 'Third Party Broker' THEN 7
                         ELSE 1 END
  , ppr.IsPrimaryParty
  , CountryID = ctry.CountryId
FROM
    dbo.Placement pl WITH (NOLOCK)
    INNER JOIN dbo.PlacementListener pli WITH (NOLOCK)
        ON pli.PlacementId = pl.PlacementId
           AND pli.IsDeleted = 0

    INNER JOIN dbo.PlacementPartyRole ppr WITH (NOLOCK)
        ON pl.PlacementId = ppr.PlacementId
           AND ppr.IsDeleted = 0
           AND ppr.DataSourceInstanceId = pl.ServicingPlatformId -- PBI118084 - Stop duplicates where the DataSourceInstanceIDs don't match.

    INNER JOIN ref.PartyRole pr WITH (NOLOCK)
        ON ppr.PartyRoleId = pr.PartyRoleId
           AND pr.IsDeprecated = 0

    INNER JOIN Reference.PartyRole gpr WITH (NOLOCK)
        ON pr.GlobalPartyRoleId = gpr.PartyRoleId
           AND gpr.IsDeleted = 0

    INNER JOIN dbo.Party p WITH (NOLOCK)
        ON p.PartyId = ppr.PartyId
           AND p.IsDeleted = 0

    LEFT JOIN PAS.Geography geo WITH (NOLOCK)
        ON geo.GeographyKey = p.GeographyKey
           AND geo.DataSourceInstanceId = p.DataSourceInstanceId
           AND geo.IsDeleted = 0

    LEFT JOIN Reference.Country ctry WITH (NOLOCK)
        ON ctry.CountryId = geo.GlobalCountryId /* AND ctry.IsDeleted = 0 if we have the ID it doesn't matter if it's now deleted */
WHERE
    pli.RenewalProcessStartDate IS NOT NULL
    AND (
        pr.GlobalPartyRoleId IN (
            '100', '101', '106'
        ) --client, insured, reinsured
        OR (
            pr.GlobalPartyRoleId IN (
                117, 121
            )
            AND pr.DataSourceInstanceId IN (
                    SELECT DataSourceInstanceId FROM Reference.DataSourceInstance WHERE DataSourceId = 50001
                )
        ) --- for eGlobal INTERMEDIARY AND SubAgent
    )
    AND pl.IsDeleted = 0
    AND ppr.Incumbent = 1
    AND (
        ppr.OnPolicy = 1
        OR ppr.OnPlacement = 1
    );
