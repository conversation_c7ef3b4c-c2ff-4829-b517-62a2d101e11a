/*
Lineage
dbo.ElementTagCache.IsDeleted=dbo.ElementTagDelta.DeltaType
dbo.ElementTagCache.IsDeleted=PS.Element.IsDeleted
dbo.ElementTagCache.IsDeleted=PS.ElementTag.IsDeleted
dbo.ElementTagCache.IsDeleted=dbo.ElementChangeSet.IsDeleted
dbo.ElementTagCache.ElementTagDeltaId=dbo.ElementTagDelta.ElementTagDeltaId
dbo.ElementTagCache.ElementTagTypeId=PS.ElementTag.ElementTagTypeId
dbo.ElementTagCache.SourceUpdatedDate=PS.Element.ETLUpdatedDate
dbo.ElementTagCache.SourceUpdatedDate=PS.ElementTag.ETLUpdatedDate
dbo.ElementTagCache.SourceUpdatedDate=dbo.ElementTagDelta.ETLUpdatedDate
dbo.ElementTagCache.SourceUpdatedDate=dbo.ElementChangeSet.ETLUpdatedDate
dbo.ElementTagCache.SourceUpdatedDate=dbo.ElementBranch.ETLUpdatedDate
dbo.ElementTagCache.RootElementId=PS.Element.RootElementId
dbo.ElementTagCache.RootElementId=PS.Element.ElementId
dbo.ElementTagCache.ElementBranchId=dbo.ElementBranch.ElementBranchId
dbo.ElementTagCache.ElementId=PS.Element.ElementId
dbo.ElementTagCache.ElementTagId=PS.ElementTag.ElementTagId
*/
CREATE PROCEDURE BPStaging.Load_dbo_ElementTagCache (
    @LastUpdatedDate DATETIME2(7)
)
AS
DECLARE @MaxLastUpdatedUTCDate DATETIME2(7);
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @LogicalDeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.ElementTagCache';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY

    /*--------------------------------------------- 
    #RootElementIds 
    -----------------------------------------------*/
    DROP TABLE IF EXISTS #RootElementIds;

    CREATE TABLE #RootElementIds (
        RootElementId INT NOT NULL PRIMARY KEY
    );

    INSERT INTO
        #RootElementIds
        (
            RootElementId
        )
    SELECT DISTINCT RootElementId = ISNULL(Element.RootElementId, Element.ElementId)
    FROM
        PS.ElementTag ElementTag
        INNER JOIN PS.Element Element
            ON Element.ElementId = ElementTag.ElementId;

    /*--------------------------------------------- 
    #Branches 
    -----------------------------------------------*/
    DROP TABLE IF EXISTS #Branches;

    CREATE TABLE #Branches (
        RootElementId     INT          NOT NULL
      , ElementBranchId   INT          NOT NULL
      , ChangeSetBranchId INT          NOT NULL
      , ETLUpdatedDate    DATETIME2(7) NULL
    );

    INSERT INTO
        #Branches
        (
            RootElementId
          , ElementBranchId
          , ChangeSetBranchId
          , ETLUpdatedDate
        )
    SELECT
        RootElementId
      , ElementBranchId = -1
      , ChangeSetBranchId = -1
      , ETLUpdatedDate = CAST(NULL AS DATETIME2(7))
    FROM
        #RootElementIds
    UNION ALL
    SELECT
        RootElementId = rootElement.RootElementId
      , ElementBranchId = branch.ElementBranchId
      , ChangeSetBranchId = -1
      , ETLUpdatedDate = branch.ETLUpdatedDate
    FROM
        #RootElementIds rootElement
        INNER JOIN dbo.ElementBranch branch
            ON branch.RootElementId = rootElement.RootElementId
    UNION ALL
    SELECT
        RootElementId = rootElement.RootElementId
      , ElementBranchId = descendantBranch.ElementBranchId
      , ChangeSetBranchId = ancestorBranch.ElementBranchId
      , ETLUpdatedDate = GREATEST(ancestorBranch.ETLUpdatedDate, descendantBranch.ETLUpdatedDate)
    FROM
        #RootElementIds rootElement
        INNER JOIN dbo.ElementBranch ancestorBranch
            ON ancestorBranch.RootElementId = rootElement.RootElementId

        INNER JOIN dbo.ElementBranch descendantBranch
            ON descendantBranch.HierarchyId.IsDescendantOf(ancestorBranch.HierarchyId) = 1;

    DROP TABLE IF EXISTS #Stage_ElementTagCache;

    CREATE TABLE #Stage_ElementTagCache (
        RootElementId     INT          NOT NULL
      , ElementBranchId   INT          NOT NULL
      , ElementId         INT          NOT NULL
      , ElementTagId      INT          NOT NULL
      , ElementTagDeltaId INT          NOT NULL
      , ElementTagTypeId  INT          NOT NULL
      , SourceUpdatedDate DATETIME2(7) NOT NULL
      , IsDeleted         BIT          NOT NULL
      , CONSTRAINT PK_#Stage_ElementTagCache
            PRIMARY KEY
            (
                ElementId ASC
              , ElementBranchId ASC
              , ElementTagId ASC
              , RootElementId ASC
            ),
    );

    INSERT INTO
        #Stage_ElementTagCache
        (
            RootElementId
          , ElementBranchId
          , ElementId
          , ElementTagId
          , ElementTagDeltaId
          , ElementTagTypeId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        tagWithDelta.RootElementId
      , tagWithDelta.ElementBranchId
      , tagWithDelta.ElementId
      , tagWithDelta.ElementTagId
      , tagWithDelta.ElementTagDeltaId
      , tagWithDelta.ElementTagTypeId
      , tagWithDelta.SourceUpdatedDate
      , tagWithDelta.IsDeleted
    FROM (
        SELECT
            RootElementId = rootElement.RootElementId
          , ElementBranchId = branch.ElementBranchId
          , ElementTagId = elementTag.ElementTagId
          , ElementId = element.ElementId
          , ElementTagTypeId = elementTag.ElementTagTypeId
          , ElementTagDeltaId = elementTagDelta.ElementTagDeltaId
          , RowNumber = ROW_NUMBER() OVER (PARTITION BY
                                               elementTag.ElementTagId
                                             , branch.ElementBranchId
                                           ORDER BY
                                               elementTagDelta.DeltaType DESC -- This is so deletes in child branches take precedence over subsequent updates in ancestors
                                             , changeSet.CreatedDate DESC
                                             , elementTagDelta.ElementTagDeltaId DESC
                                     )
          , SourceUpdatedDate = GREATEST(
                                    element.ETLUpdatedDate
                                  , elementTag.ETLUpdatedDate
                                  , elementTagDelta.ETLUpdatedDate
                                  , changeSet.ETLUpdatedDate
                                  , branch.ETLUpdatedDate
                                )
          , IsDeleted = CASE WHEN elementTagDelta.DeltaType = '3' /* Delete */
                                  OR element.IsDeleted = 1
                                  OR elementTag.IsDeleted = 1
                                  -- OR elementTagDelta.IsDeprecated = 1
                                  OR changeSet.IsDeleted = 1
                                 THEN 1
                             ELSE 0 END
        FROM
            #RootElementIds rootElement
            INNER JOIN PS.Element element
                ON element.RootElementId = rootElement.RootElementId
                   OR element.ElementId = rootElement.RootElementId

            INNER JOIN PS.ElementTag elementTag
                ON elementTag.ElementId = element.ElementId

            INNER JOIN dbo.ElementTagDelta elementTagDelta
                ON elementTagDelta.ElementTagId = elementTag.ElementTagId

            INNER JOIN (
                SELECT
                    ecs.ElementChangeSetId
                  , ElementBranchId = ISNULL(ecs.ElementBranchId, -1)/* Replace NULL with -1 for easier joining. */
                  , ecs.CreatedDate
                  , ecs.ETLUpdatedDate
                  , ecs.IsDeleted
                FROM
                    dbo.ElementChangeSet ecs
            ) changeSet
                ON changeSet.ElementChangeSetId = elementTagDelta.ElementChangeSetId

            LEFT JOIN #Branches branch
                ON branch.RootElementId = rootElement.RootElementId
                   AND branch.ChangeSetBranchId = changeSet.ElementBranchId
    ) tagWithDelta
    WHERE
        tagWithDelta.RowNumber = 1;

    /* As we have stage all the records anything we have that isn't in the staged table must be deleted (if not already) */
    UPDATE etc
    SET
        etc.IsDeleted = 1
      , etc.ETLUpdatedDate = GETUTCDATE()
    FROM
        dbo.ElementTagCache etc
    WHERE
        NOT EXISTS (
        SELECT *
        FROM
            #Stage_ElementTagCache setc
        WHERE
            setc.ElementId = etc.ElementId
            AND setc.ElementBranchId = etc.ElementBranchId
            AND setc.ElementTagId = etc.ElementTagId
            AND setc.RootElementId = etc.RootElementId
    )
        AND IsDeleted = 0;

    SELECT @LogicalDeletedCount = @@ROWCOUNT;

    UPDATE etc
    SET
        etc.IsDeleted = setc.IsDeleted
      , etc.ElementTagDeltaId = setc.ElementTagDeltaId
      , etc.ElementTagTypeId = setc.ElementTagTypeId
      , etc.SourceUpdatedDate = setc.SourceUpdatedDate
      , etc.ETLUpdatedDate = GETUTCDATE()
    FROM
        dbo.ElementTagCache etc
        INNER JOIN #Stage_ElementTagCache setc
            ON setc.ElementId = etc.ElementId
               AND setc.ElementBranchId = etc.ElementBranchId
               AND setc.ElementTagId = etc.ElementTagId
               AND setc.RootElementId = etc.RootElementId
    WHERE
        NOT EXISTS (
        SELECT
            etc.ElementTagDeltaId
          , etc.ElementTagTypeId
          , etc.IsDeleted
          , etc.SourceUpdatedDate
        INTERSECT
        SELECT
            setc.ElementTagDeltaId
          , setc.ElementTagTypeId
          , setc.IsDeleted
          , setc.SourceUpdatedDate
    );

    SELECT @UpdatedCount = @@ROWCOUNT;

    INSERT INTO
        dbo.ElementTagCache
        (
            RootElementId
          , ElementBranchId
          , ElementId
          , ElementTagId
          , ElementTagDeltaId
          , ElementTagTypeId
          , ETLCreatedDate
          , ETLUpdatedDate
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        setc.RootElementId
      , setc.ElementBranchId
      , setc.ElementId
      , setc.ElementTagId
      , setc.ElementTagDeltaId
      , setc.ElementTagTypeId
      , ETLCreatedDate = GETUTCDATE()
      , ETLUpdatedDate = GETUTCDATE()
      , setc.SourceUpdatedDate
      , setc.IsDeleted
    FROM
        #Stage_ElementTagCache setc
    WHERE
        NOT EXISTS (
        SELECT *
        FROM
            dbo.ElementTagCache etc
        WHERE
            setc.ElementId = etc.ElementId
            AND setc.ElementBranchId = etc.ElementBranchId
            AND setc.ElementTagId = etc.ElementTagId
            AND setc.RootElementId = etc.RootElementId
    )
        AND setc.IsDeleted = 0; /* Don't bother to insert deleted records. May as well save some space to start with! */

    SELECT @InsertedCount = @@ROWCOUNT;

    /* Look for the maximum LastUpdatedUTCDate in the temp table */
    /* Although I don't think this can safely be incremental     */
    /* But by loading it now if we do turn it on it will have    */
    /* the dates ready to go.                                    */
    SELECT @MaxLastUpdatedUTCDate = MAX(SourceUpdatedDate)
    FROM
        #Stage_ElementTagCache;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

-- We can get slightly better stats than the log as we can get the logical deletion count. 
SET @Action =
    CONCAT(
        N'Load '
      , @TargetTable
      , N'. Logically Deleted: '
      , @LogicalDeletedCount
      , N'. Updated: '
      , @UpdatedCount
      , N'. Inserted: '
      , @InsertedCount
      , N'. Max Updated Date: '
      , @MaxLastUpdatedUTCDate
      , N'.'
    );
SET @UpdatedCount = ISNULL(@UpdatedCount, 0) + ISNULL(@LogicalDeletedCount, 0);

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , 0
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = @UpdatedCount
  , DeletedCount = 0
  , RejectedCount = ISNULL(@RejectedCount, 0)
  , MaxLastUpdatedUTCDate = ISNULL(@MaxLastUpdatedUTCDate, @LastUpdatedDate);