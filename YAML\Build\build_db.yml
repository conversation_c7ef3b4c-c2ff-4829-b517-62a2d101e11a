parameters:
  compareBranch: trunk
  configOnly: false
  reporting: false
  dotnetSDKVersion: 8.0.x

stages:
- stage: build_db
  dependsOn: start
  jobs:

  # Build the SQL Databases
  - ${{ if ne(parameters.reporting,'true') }}:
    - job: BuildPlacementStoreDbJob
      displayName: 'PlacementStore Database'
      pool:
        name: 'Private-AKS-CRB-Linux'
      steps:
      - checkout: self
        fetchDepth: 1    # shallow
        fetchTags: false # dont fetch tags
      - template: Build/build-dnc.yml@templates
        parameters:
          buildConfiguration: $(buildConfiguration)
          buildVersion: $(Build.BuildNumber)
          retryCountOnTaskFailure: 2
          dotnetSDKVersion: 8.0.x

          restoreDNC:
          - displayName: 'Nuget Restore'
            projects: '$(Build.SourcesDirectory)/src/PlacementStoreDb.Build/PlacementStoreDb.Build.csproj'
            vstsFeed: $(vstsFeed)

          # dotnet framework build
          buildDNC:
          - displayName: 'Build Placement Store Database'
            projects: '$(Build.SourcesDirectory)/src/PlacementStoreDb.Build/PlacementStoreDb.Build.csproj'

          # publish as pipeline artefact for consumption in subsequent stage
          publishBuildArtifacts:
          - pathToPublish: '$(Build.SourcesDirectory)/src/PlacementStoreDb.Build/bin/Release/netstandard2.0'
            artifactName: PlacementStore.Database

  - ${{ if eq(parameters.configOnly,'true') }}:
    # Extract Post-Deployment Script for Config-Only pipeline
    - job: ConfigOnlyJob
      displayName: 'Config-Only Script'
      dependsOn: BuildPlacementStoreDbJob
      pool:
        name: Private-AKS-CRB-Linux
      steps:
      - download: current
        artifact: PlacementStore.Database
        displayName: 'Download: PlacementStore.Database'
      - task: UseDotNet@2
        displayName: 'Use .NET Core SDK ${{parameters.dotnetSDKVersion}}'
        inputs:
          packageType: sdk
          version: ${{parameters.dotnetSDKVersion}}
          installationPath: $(Agent.ToolsDirectory)/dotnet
      - pwsh: |
          $sourcesDirectory = "$(Build.SourcesDirectory)"
          $pipelineWorkspace = "$(Pipeline.Workspace)"
          $artifactStagingDirectory = "$(Build.ArtifactStagingDirectory)"
          $nugetPackages = '~/.nuget/packages'

          $databaseName = 'PlacementStore'
          $sqlProjectPath = "$sourcesDirectory/src/PlacementStore"
          $sqlOptionsPath = "$sourcesDirectory/src/PlacementStore/sqlDeployOptions.json"

          # Load DacFx Types
          $csproj = @"
          <Project Sdk=`"Microsoft.NET.Sdk`">
            <PropertyGroup>
              <TargetFramework>net8.0</TargetFramework>
            </PropertyGroup>
            <ItemGroup>
              <PackageReference Include=`"Microsoft.Data.SqlClient`" Version=`"3.0.1`" />
              <PackageReference Include=`"Microsoft.SqlServer.DacFx`" Version=`"160.5400.1`" />
              <PackageReference Include=`"System.IO.Packaging`" Version=`"6.0.0`" />
              <PackageReference Include=`"System.Resources.Extensions`" Version=`"4.7.1`" />
            </ItemGroup>
          </Project>
          "@

          if (!(Test-Path "DatabaseProj")) {
            New-Item -Type Directory "DatabaseProj" | Out-Null
          }
          $dbProj =  "DatabaseProj/Database.csproj"
          Set-Content -Path $dbProj -Value $csproj

          Write-Host "Restoring nuget packages.."
          dotnet restore $dbProj
          Remove-Item $dbProj -Recurse -Force | Out-Null

          # Load DacPac dependencies
          Add-Type -Path "$nugetPackages/microsoft.data.sqlclient/3.0.1/lib/netstandard2.0/Microsoft.Data.SqlClient.dll"
          Add-Type -Path "$nugetPackages/microsoft.sqlserver.dacfx/160.5400.1/lib/netstandard2.1/Microsoft.SqlServer.Dac.dll"
          Add-Type -Path "$nugetPackages/microsoft.sqlserver.dacfx/160.5400.1/lib/netstandard2.1/Microsoft.SqlServer.Dac.Extensions.dll"
          Add-Type -Path "$nugetPackages/system.io.packaging/6.0.0/lib/netstandard2.0/System.IO.Packaging.dll"
          Add-Type -Path "$nugetPackages/system.resources.extensions/4.7.1/lib/netstandard2.0/System.Resources.Extensions.dll"

          $sourceDacPac = "$pipelineWorkspace/PlacementStore.Database/PlacementStoreDb.Build.dacpac"
          $sourceDacPacSchemaOnly = "$pipelineWorkspace/PlacementStore.Database/PlacementStore-SchemaOnly.dacpac"
          $postDeployPath = "$artifactStagingDirectory/PostDeployScript"
          $postDeployScriptPath = "$artifactStagingDirectory/PostDeployScript/postdeploy.sql"
          if (!(Test-Path $postDeployPath)) {
            New-Item -ItemType Directory $postDeployPath | Out-Null
          }

          $serialiser = New-Object Newtonsoft.Json.JsonSerializer
          $sr = New-Object System.IO.StreamReader($sqlOptionsPath)
          $reader = New-Object Newtonsoft.Json.JsonTextReader($sr)
          [Microsoft.SqlServer.Dac.DacDeployOptions]$options = $serialiser.Deserialize($reader, [Microsoft.SqlServer.Dac.DacDeployOptions])

          Write-Host "Make a schema only copy of the dacpac"
          Copy-Item $sourceDacPac $sourceDacPacSchemaOnly -Force

          Write-Host "Open DacPac and locate post deployment script"
          $pkg = [System.IO.Packaging.ZipPackage]::Open($sourceDacPacSchemaOnly)
          $postDeploy = $pkg.GetParts() | Where-Object { $_.Uri -eq '/postdeploy.sql' }

          Write-Host "Extract postdeploy script"
          $postdeployStream = $postdeploy.GetStream()
          $buff = [System.Byte[]]::new($postdeployStream.Length)
          $postdeployStream.Read($buff, 0, $buff.Length) | Out-Null
          [System.IO.File]::WriteAllBytes($postDeployScriptPath, $buff)
          $postdeployStream.Close()
        displayName: 'Extract: Post-Deployment Script'
        retryCountOnTaskFailure: 2
      - publish: '$(Build.ArtifactStagingDirectory)/PostDeployScript'
        artifact: PostDeployScript
        displayName: 'Publish: Post-Deployment Script'

  - ${{ elseif eq(parameters.reporting,'true') }}:
    - job: BuildReportingJob
      displayName: 'Reporting'
      pool:
        name: Private-VM-CRB-VS2022
      steps:
      - checkout: self
        fetchDepth: 1    # shallow
        fetchTags: false # dont fetch tags
      - template: Build/build-dnf.yml@templates
        parameters:
          buildConfiguration: $(buildConfiguration)
          buildVersion: $(Build.BuildNumber)

          # dotnet framework build
          buildDNF:
          - displayName: 'Build General Analytics Reporting Layer SSAS Project'
            solution: '$(Build.SourcesDirectory)/src/ReportingLayer/GeneralAnalytics_AS_TAB.smproj'

          - displayName: 'Build Broking Platform Usage Reporting Layer SSAS Project'
            solution: '$(Build.SourcesDirectory)/src/ReportingLayer/BrokingPlatformUsage.smproj'

          - displayName: 'Build Broking Platform Carrier Reporting Layer SSAS Project'
            solution: '$(Build.SourcesDirectory)/src/ReportingLayer/CarrierAnalytics_AS_TAB.smproj'

          copyFiles:
          - displayName: 'Copy Build Files to Artifacts staging'
            sourceFolder: $(Build.SourcesDirectory)/src/ReportingLayer/bin/$(buildConfiguration)
            contents: |
               *.asdatabase
               *.deploymentoptions
               *.deploymenttargets
            targetFolder: $(build.artifactstagingdirectory)/ReportingLayer

          - displayName: 'Copy Bim, PowerShell and XML publish profiles files to Artifacts staging'
            sourceFolder: src\ReportingLayer
            contents: |
               *_AS_TAB*.publish.xml
               *.PS1
               *.bim*
            targetFolder: $(build.artifactstagingdirectory)/ReportingLayer

          - displayName: 'Copy Deployment Options to Artifacts staging'
            sourceFolder: src\ReportingLayer\deploymentoptions
            contents: |
               *.*
            targetFolder: $(build.artifactstagingdirectory)/ReportingLayer/deploymentoptions

          - displayName: 'Copy xmla scripts Options to Artifacts staging'
            sourceFolder: src\ReportingLayer\xmla
            contents: |
               *.*
            targetFolder: $(build.artifactstagingdirectory)/ReportingLayer/xmla

          # publish as pipeline artefact for consumption in subsequent stage
          publishBuildArtifacts:
          - pathToPublish: '$(Build.artifactstagingdirectory)/ReportingLayer'
            artifactName: PlacementStore.Reporting

  - ${{ else }}:
    # Build the SQL Tests
    - job: BuildSQLTestsJob
      displayName: 'SQL Tests'
      pool:
        name: 'Private-AKS-CRB-Linux'
      steps:
      - checkout: self
        fetchDepth: 1    # shallow
        fetchTags: false # dont fetch tags
      - template: Build/build-dnc.yml@templates           # Build Application
        parameters:
          buildConfiguration: $(buildConfiguration)
          buildVersion: $(Build.BuildNumber)
          retryCountOnTaskFailure: 2
          dotnetSDKVersion: 8.0.x

          # dotnet core restore
          restoreDNC:
          - displayName: 'Nuget Restore'
            projects: '$(Build.SourcesDirectory)/test/PsDb.Tests/PsDb.Tests.csproj'
            vstsFeed: $(vstsFeed)

          # dotnet core build
          buildDNC:
          - displayName: 'Build DB Tests'
            projects: '$(Build.SourcesDirectory)/test/PsDb.Tests/PsDb.Tests.csproj'

          # merge artefact files together
          copyFiles:
          - displayName: 'Copy: SQL Tests'
            sourceFolder: $(Build.SourcesDirectory)/test/PsDb.Tests/bin/$(buildConfiguration)/net8.0
            targetFolder: $(Build.ArtifactStagingDirectory)/TestSQL

          # publish as pipeline artefact for consumption in subsequent stage
          publishBuildArtifacts:
          - pathToPublish: '$(Build.ArtifactStagingDirectory)/TestSQL'
            artifactName: TestSQL