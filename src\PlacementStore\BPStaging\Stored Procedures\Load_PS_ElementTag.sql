/*
Lineage
PS.ElementTag.ElementTagId=BPStaging.ElementTag.Id
PS.ElementTag.ElementId=BPStaging.ElementTag.ElementId
PS.ElementTag.ElementTagTypeId=BPStaging.ElementTag.ElementTagTypeId
PS.ElementTag.Index=BPStaging.ElementTag.Index
PS.ElementTag.SourceUpdatedDate=BPStaging.ElementTag.ValidTo
PS.ElementTag.SourceUpdatedDate=BPStaging.ElementTag.ValidFrom
PS.ElementTag.IsDeleted=BPStaging.ElementTag.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_ElementTag
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ElementTag';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.ElementTag
        );

IF EXISTS (SELECT * FROM BPStaging.ElementTag)
BEGIN TRY
    MERGE PS.ElementTag T
    USING (
        SELECT
            Id
          , ElementId
          , ElementTagTypeId
          , [Index]
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , ElementId
          , ElementTagTypeId
          , [Index]
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ElementTag
    ) Inner_select
        WHERE
            Inner_select.RowNo = 1
    ) S
    ON T.ElementTagId = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 ElementTagId
               , ElementId
               , ElementTagTypeId
               , [Index]
               , DataSourceInstanceId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.Id
                   , S.ElementId
                   , S.ElementTagTypeId
                   , S.[Index]
                   , S.DataSourceInstanceId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.ElementId
      , T.ElementTagTypeId
      , T.[Index]
      , T.SourceUpdatedDate
      , T.DataSourceInstanceId
      , T.IsDeleted
    INTERSECT
    SELECT
        S.ElementId
      , S.ElementTagTypeId
      , S.[Index]
      , S.SourceUpdatedDate
      , S.DataSourceInstanceId
      , S.IsDeleted
)
        THEN UPDATE SET
                 T.ElementId = S.ElementId
               , T.ElementTagTypeId = S.ElementTagTypeId
               , T.[Index] = S.[Index]
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN;