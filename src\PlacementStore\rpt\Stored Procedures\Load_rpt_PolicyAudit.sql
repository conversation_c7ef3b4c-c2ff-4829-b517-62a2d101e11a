/*
Lineage
rpt.PolicyAudit.DataSourceInstanceId=dbo.Policy.DataSourceInstanceId
rpt.PolicyAudit.PolicyId=dbo.Policy.PolicyId
rpt.PolicyAudit.PolicyReference=dbo.Policy.PolicyReference
rpt.PolicyAudit.PolicyDescription=dbo.Policy.PolicyDescription
rpt.PolicyAudit.PolicyType=PAS.PolicyType.PolicyType
rpt.PolicyAudit.PolicyKey=dbo.Policy.PolicyKey
rpt.PolicyAudit.PolicyStatus=PAS.PolicyStatus.PolicyStatus
rpt.PolicyAudit.InceptionDate=dbo.Policy.InceptionDate
rpt.PolicyAudit.InceptionDateKey=dbo.Policy.InceptionDate
rpt.PolicyAudit.OpportunityType=ref.OpportunityType.OpportunityType
rpt.PolicyAudit.ExpiryDate=dbo.Policy.ExpiryDate
rpt.PolicyAudit.InsuranceType=ref.RefInsuranceType.RefInsuranceType
rpt.PolicyAudit.IsDeleted=dbo.Policy.IsDeleted
rpt.PolicyAudit.CreatedUTCDate=dbo.Policy.ETLCreatedDate
rpt.PolicyAudit.CreationDateKey=dbo.Policy.ETLCreatedDate
rpt.PolicyAudit.LastUpdatedUTCDate=dbo.Policy.ETLUpdatedDate
rpt.PolicyAudit.IsFacility=dbo.Policy.IsFacility
rpt.PolicyAudit.SumInsuredCurrency=Reference.Currency.CurrencyAlphaCode
rpt.PolicyAudit.SumInsured=dbo.Policy.SumInsured
rpt.PolicyAudit.SumInsuredUSD=dbo.Policy.SumInsured
rpt.PolicyAudit.SumInsuredUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.PolicyAudit.OrganisationId=dbo.PolicyOrganisation.OrganisationId
rpt.PolicyAudit.ExpiringPolicyId=dbo.Policy.PolicyId
rpt.PolicyAudit.ExpiringPolicyReference=dbo.Policy.PolicyReference
rpt.PolicyAudit.NewOrRenewal=dbo.Policy.RenewedFromPolicyId
rpt.PolicyAudit.ExpiringPolicyStatus=PAS.PolicyStatus.PolicyStatus
rpt.PolicyAudit.ExpiringPolicyKey=dbo.Policy.PolicyKey
rpt.PolicyAudit.RenewedToPolicyReference=dbo.Policy.PolicyReference
rpt.PolicyAudit.SentToBP=dbo.Placement.RenewedToPlacementId
rpt.PolicyAudit.RuleId=dbo.Policy.RuleId
rpt.PolicyAudit.InScope=dbo.Policy.RuleId
rpt.PolicyAudit.InScope=dbo.Placement.PlacementId
rpt.PolicyAudit.RuleName=PactConfig.Rule.RuleName
rpt.PolicyAudit.RuleDescription=PactConfig.Rule.RuleDescription
rpt.PolicyAudit.LinkedToPlacement=dbo.Placement.PlacementId
rpt.PolicyAudit.RefPolicyStatus=PAS.RefPolicyStatus.RefPolicyStatus
rpt.PolicyAudit.HasPlacement=dbo.Placement.PlacementId
rpt.PolicyAudit.CurrentPSPlacementId=dbo.Placement.PlacementId
rpt.PolicyAudit.CurrentBPPlacementId=dbo.Placement.PlacementSystemId
rpt.PolicyAudit.ExpiringPSPlacementId=dbo.Placement.PlacementId
rpt.PolicyAudit.ExpiringBPPlacementId=dbo.Placement.PlacementSystemId
rpt.PolicyAudit.BrokingSegmentName=ref.BrokingSegment.BrokingSegment
rpt.PolicyAudit.BrokingRegionName=ref.BrokingRegion.BrokingRegion
rpt.PolicyAudit.BrokingSubSegmentName=ref.BrokingSubSegment.BrokingSubSegment
rpt.PolicyAudit.RenewalsEnabled=PactConfig.Rule.RuleId
rpt.PolicyAudit.RenewalPlacement=dbo.Placement.RenewedFromPlacementId
rpt.PolicyAudit.RenewalSent=dbo.Placement.RenewedToPlacementId
rpt.PolicyAudit.NewBusinessOnly=PactConfig.Rule.Enabled
rpt.PolicyAudit.NewBusinessOnly=PactConfig.Rule.RuleId
rpt.PolicyAudit.OutOfScope=PactConfig.Rule.Enabled
rpt.PolicyAudit.OutOfScope=PactConfig.Rule.RuleId
rpt.PolicyAudit.OutOfScope=dbo.Placement.PlacementId
rpt.PolicyAudit.OutOfScope=dbo.Policy.RuleId
rpt.PolicyAudit.IncomingGlobalBusiness=dbo.Policy.IncomingGlobalBusiness
rpt.PolicyAudit.Backloaded=dbo.Policy.Backloaded
rpt.PolicyAudit.ProductCode=PS.ProductAttribute.COLProductCode
rpt.PolicyAudit.Proposta=COLStaging.Tabela_Documentos.Proposta
rpt.PolicyAudit.IsMainPolicy=dbo.Policy.IsMainPolicy
rpt.PolicyAudit.COLRenewalDate=dbo.Policy.ExpiryDate
rpt.PolicyAudit.COLPolicyStatus=dbo.Placement.PlacementSystemId
rpt.PolicyAudit.COLPolicyStatus=dbo.Policy.ExpiryDate
rpt.PolicyAudit.COLPolicyStatus=ref.Team.TeamName
rpt.PolicyAudit.TeamName=ref.Team.TeamName
rpt.PolicyAudit.COLCurrentPolicyStatus=dbo.Policy.PlacementId
rpt.PolicyAudit.COLCurrentPolicyStatus=dbo.Policy.RenewedFromPolicyId
rpt.PolicyAudit.COLCurrentPolicyStatus=dbo.Policy.InceptionDate
rpt.PolicyAudit.COLCurrentPolicyStatus=PAS.RefPolicyStatus.RefPolicyStatus
rpt.PolicyAudit.COLCurrentPolicyStatus=dbo.Policy.RuleId
rpt.PolicyAudit.COLCurrentPolicyStatus=dbo.Policy.RunId
rpt.PolicyAudit.COLPlacementSystemId=dbo.Placement.PlacementSystemId
rpt.PolicyAudit.HasContract=PS.Contract.ContractId
rpt.PolicyAudit.HasContractWording=dbo.ContractDocumentElement.ContractDocumentElementId
rpt.PolicyAudit.IsPrimaryParty=dbo.PolicyPartyRelationship.IsPrimaryParty
rpt.PolicyAudit.GlobalPartyRole=Reference.PartyRole.PartyRoleDescription
rpt.PolicyAudit.GlobalPartyRoleId=ref.PartyRole.GlobalPartyRoleId
rpt.PolicyAudit.PartyName=dbo.Party.PartyName
rpt.PolicyAudit.BusinessKey=dbo.Party.BusinessKey
rpt.PolicyAudit.GlobalPartyId=dbo.Party.GlobalPartyId
*/

CREATE PROCEDURE rpt.Load_rpt_PolicyAudit
WITH RECOMPILE
AS
/*

WARNING - SQL Prompt will likely change the case of a RuleID/RuleId in here causing compile errors.
    Currently 13 occurrences.
*/
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'rpt.PolicyAudit';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    DROP TABLE IF EXISTS #fx;

    CREATE TABLE #fx (
        FromCurrencyId INT            NOT NULL
      , EffectiveDate  DATETIME2(7)   NOT NULL
      , ToDate         DATETIME2(7)   NOT NULL
      , ExchangeRate   DECIMAL(18, 8) NOT NULL
    );

    INSERT INTO
        #fx
        (
            FromCurrencyId
          , EffectiveDate
          , ToDate
          , ExchangeRate
        )
    SELECT
        FromCurrencyId
      , EffectiveFromDate
      , EffectiveToDate
      , ExchangeRate
    FROM
        PS.vwToAverageExchangeRate WITH (NOLOCK)
    WHERE
        ToCurrencyAlphaCode = 'USD'
    ORDER BY
        FromCurrencyId ASC
      , EffectiveFromDate ASC;

    CREATE CLUSTERED INDEX PK_#fx
    ON #fx
    (
        FromCurrencyId ASC
      , EffectiveDate ASC
    );
    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    /* Get Contract Policy Details using current and legacy method */
    DROP TABLE IF EXISTS #PolicyContractDetailsStaging;

    CREATE TABLE #PolicyContractDetailsStaging (
        PolicyId                  BIGINT NOT NULL
      , ContractId                INT    NOT NULL
      , ContractDocumentElementID INT    NULL
      , HasContract               BIT    NOT NULL
      , HasContractWording        BIT    NOT NULL
    );

    INSERT INTO
        #PolicyContractDetailsStaging
        (
            PolicyId
          , ContractId
          , ContractDocumentElementID
          , HasContract
          , HasContractWording
        )
    SELECT
        PolicyId = PO.PolicyId
      , CO.ContractId
      , CDE.ContractDocumentElementId
      , HasContract = CASE WHEN CO.ContractId IS NULL
                               THEN 0
                           ELSE 1 END
      , HasContractWording = CASE WHEN CDE.ContractDocumentElementId IS NULL
                                      THEN 0
                                  ELSE 1 END
    FROM
        dbo.Policy PO WITH (NOLOCK)
        INNER JOIN dbo.PolicyOrganisation PORG WITH (NOLOCK)
            ON PORG.PolicyId = PO.PolicyId

        INNER JOIN APIv1.OrganisationHierarchyTable OHT WITH (NOLOCK)
            ON OHT.OrganisationId = PORG.OrganisationId

        INNER JOIN ref.OrganisationLegalEntity OLE WITH (NOLOCK)
            ON OLE.OrganisationId = OHT.Level1OrgId

        INNER JOIN ref.LegalEntity LE WITH (NOLOCK)
            ON LE.LegalEntityKey = CAST(OLE.LegalEntityId AS NVARCHAR(100))

        INNER JOIN PS.Contract CO WITH (NOLOCK)
            ON CAST(CO.LegalEntityId AS NVARCHAR(100)) = LE.LegalEntityKey
               AND CO.Reference = PO.PolicyReference

        LEFT JOIN dbo.ContractDocumentElement CDE WITH (NOLOCK)
            ON CDE.ContractId = CO.ContractId
               AND CDE.IsDeleted = 0
    WHERE
        PO.IsDeleted = 0
        AND PORG.IsDeleted = 0
        AND OLE.IsDeprecated = 0
        AND LE.DataSourceInstanceId = 50366
        AND CO.IsExpiring = 0
        AND CO.IsDeleted = 0
    UNION
    SELECT
        PolicyId = PO.PolicyId
      , CO.ContractId
      , CDE.ContractDocumentElementId
      , HasContract = CASE WHEN CO.ContractId IS NULL
                               THEN 0
                           ELSE 1 END
      , HasContractWording = CASE WHEN CDE.ContractDocumentElementId IS NULL
                                      THEN 0
                                  ELSE 1 END
    FROM
        dbo.Policy PO WITH (NOLOCK)
        INNER JOIN dbo.ContractPolicy CP WITH (NOLOCK)
            ON CP.PolicyId = PO.PolicyId

        INNER JOIN PS.Contract CO WITH (NOLOCK)
            ON CO.ContractId = CP.ContractId

        LEFT JOIN dbo.ContractDocumentElement CDE WITH (NOLOCK)
            ON CDE.ContractId = CO.ContractId
               AND CDE.IsDeleted = 0
    WHERE
        PO.IsDeleted = 0
        AND CP.IsDeleted = 0
        AND CO.IsExpiring = 0
        AND CO.IsDeleted = 0
    ORDER BY
        PolicyId ASC
      , CDE.ContractDocumentElementId DESC
      , ContractId DESC;

    CREATE CLUSTERED INDEX PK_#PolicyContractDetailsStaging
    ON #PolicyContractDetailsStaging
    (
        PolicyId ASC
      , ContractDocumentElementID DESC
      , ContractId DESC
    );

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    /* Merge Contract Policy Details and remove duplicates */
    DROP TABLE IF EXISTS #PolicyContractDetails;

    CREATE TABLE #PolicyContractDetails (
        PolicyId           BIGINT NOT NULL
      , HasContract        BIT    NOT NULL
      , HasContractWording BIT    NOT NULL
    );

    INSERT INTO
        #PolicyContractDetails
        (
            PolicyId
          , HasContract
          , HasContractWording
        )
    SELECT
        main.PolicyId
      , main.HasContract
      , main.HasContractWording
    FROM (
        SELECT
            PolicyId
          , HasContract
          , HasContractWording
          , RNum = ROW_NUMBER() OVER (PARTITION BY PolicyId ORDER BY ContractDocumentElementID DESC, ContractId DESC)
        FROM
            #PolicyContractDetailsStaging
    ) main
    WHERE
        main.RNum = 1;

    CREATE CLUSTERED INDEX PK_#PolicyContractDetails
    ON #PolicyContractDetails
    (
        PolicyId
    );

    --No longer needed, drop table of approx 50K rows
    DROP TABLE IF EXISTS #PolicyContractDetailsStaging;

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    /* List of "Product Codes" for COL */
    DROP TABLE IF EXISTS #ProductCode;

    CREATE TABLE #ProductCode (
        PolicyId    BIGINT         NOT NULL
      , ProductCode NVARCHAR(4000) NOT NULL
    );

    INSERT INTO
        #ProductCode
        (
            PolicyId
          , ProductCode
        )
    SELECT
        x.PolicyId
      , ProductCode = STRING_AGG(x.COLProductCode, ',') WITHIN GROUP(ORDER BY
                                                                         x.COLProductCode ASC)
    FROM (
        SELECT DISTINCT
               ps.PolicyId
             , pa.COLProductCode
        FROM
            dbo.PolicySection ps WITH (NOLOCK)
            INNER JOIN dbo.PolicySectionProduct psp
                ON psp.PolicySectionId = ps.PolicySectionId
                   AND psp.IsDeleted = 0

            INNER JOIN dbo.Product prod WITH (NOLOCK)
                ON prod.ProductId = psp.ProductId

            INNER JOIN PS.ProductAttribute pa WITH (NOLOCK)
                ON pa.ProductId = prod.ProductId
                   AND pa.IsDeleted = 0
        WHERE
            ps.DataSourceInstanceId = 50003
            AND ps.IsDeleted = 0
    ) x
    GROUP BY
        x.PolicyID
    ORDER BY
        x.PolicyID;

    CREATE CLUSTERED INDEX PK_#ProductCode
    ON #ProductCode
    (
        PolicyId
    );

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    /* Proposta for COL */
    DROP TABLE IF EXISTS #Proposta;

    CREATE TABLE #Proposta (
        PolicyId BIGINT        NOT NULL
      , Proposta NVARCHAR(MAX) NULL
    );

    INSERT INTO
        #Proposta
        (
            PolicyId
          , Proposta
        )
    SELECT
        pol.PolicyId
      , Proposta = STRING_AGG(CAST(STR(u.Proposta, 15, 0) AS NVARCHAR(MAX)), ',') WITHIN GROUP(ORDER BY
                                                                                                   STR(
                                                                                                       u.Proposta
                                                                                                     , 15
                                                                                                     , 0
                                                                                                   ) ASC)
    FROM
        dbo.Policy pol WITH (NOLOCK)
        INNER JOIN COLStaging.Tabela_Documentos u WITH (NOLOCK)
            ON u.Documento = CAST(pol.PolicyKey AS FLOAT) /* Would normally cast Documento to a NVARCHAR but floats don't convert easily */
    WHERE
        pol.DataSourceInstanceId = 50003 --> Only for COL
    GROUP BY
        pol.PolicyId
    ORDER BY
        pol.PolicyId ASC;

    CREATE CLUSTERED INDEX PK_#Proposta
    ON #Proposta
    (
        PolicyId
    );

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    DROP TABLE IF EXISTS #tmpPlacementPolicyStaging;

    CREATE TABLE #tmpPlacementPolicyStaging (
        PolicyId                          BIGINT NOT NULL
      , PlacementPolicyRelationshipTypeId INT    NOT NULL
      , PlacementId                       BIGINT NOT NULL
      , PlacementSystemId                 INT    NULL
      , MAXPlacementSystemId              INT    NULL
      , RenewalPlacement                  BIT    NOT NULL
      , RenewedToPlacementId              INT    NULL
      , IsDeleted                         BIT    NOT NULL
    );

    INSERT INTO
        #tmpPlacementPolicyStaging
        (
            PolicyId
          , PlacementPolicyRelationshipTypeId
          , PlacementId
          , PlacementSystemId
          , MAXPlacementSystemId
          , RenewalPlacement
          , RenewedToPlacementId
          , IsDeleted
        )
    SELECT
        main.PolicyId
      , main.PlacementPolicyRelationshipTypeId
      , main.PlacementId
      , main.PlacementSystemId
      , main.MAXPlacementSystemId
      , main.RenewalPlacement
      , main.RenewedToPlacementId
      , main.IsDeleted
    FROM (
        SELECT
            pp.PolicyId
          , pp.PlacementPolicyRelationshipTypeId
          , rpl.PlacementId
          , rpl.PlacementSystemId
          , MAXPlacementSystemId = MAX(rpl.PlacementSystemId) OVER (PARTITION BY pp.PolicyId, pp.PlacementPolicyRelationshipTypeId)
          , RenewalPlacement = CASE WHEN rpl.RenewedFromPlacementId IS NOT NULL
                                        THEN 1
                                    ELSE 0 END
          , rpl.RenewedToPlacementId
          , rpl.IsDeleted
          , rnum = ROW_NUMBER() OVER (PARTITION BY pp.PolicyId, pp.PlacementPolicyRelationshipTypeId ORDER BY pp.PlacementId DESC)
        FROM
            dbo.PlacementPolicy pp WITH (NOLOCK)
            INNER JOIN dbo.Placement rpl WITH (NOLOCK)
                ON rpl.PlacementId = pp.PlacementId
                   AND rpl.DataSourceInstanceId = 50366
                   AND ISNULL(rpl.CancellationReasonId, 8) <> 6 --Undo Merge so not active / replaced by another placement
                   AND rpl.IsDeleted = 0

            INNER JOIN ref.PlacementStatus ps WITH (NOLOCK)
                ON ps.PlacementStatusId = rpl.PlacementStatusId
                   AND ISNULL(ps.PlacementStatusKey, '1') <> '4' --Merged so not active / replaced by another placement
        WHERE
            pp.DataSourceInstanceId = 50366 /* Broking Platform */
            AND pp.PlacementPolicyRelationshipTypeId IN (
                    1, 2
                ) --Current and Expiring
            AND pp.IsDeleted = 0
    ) main
    WHERE
        main.rnum = 1
    ORDER BY
        main.PolicyId ASC
      , main.PlacementPolicyRelationshipTypeId ASC;

    CREATE CLUSTERED INDEX PK_#tmpPlacementPolicyStaging
    ON #tmpPlacementPolicyStaging
    (
        PolicyId ASC
      , PlacementPolicyRelationshipTypeId ASC
    );

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    DROP TABLE IF EXISTS #renfrom;

    CREATE TABLE #renfrom (
        RenewedFromPolicyId BIGINT       NOT NULL
      , PolicyReference     NVARCHAR(50) NULL
      , Cnt                 INT          NULL
    );

    INSERT INTO
        #renfrom
        (
            RenewedFromPolicyId
          , PolicyReference
          , Cnt
        )
    SELECT
        main.RenewedFromPolicyId
      , main.PolicyReference
      , main.Cnt
    FROM (
        SELECT
            p.RenewedFromPolicyId
          , p.PolicyReference
          , Cnt = COUNT(*) OVER (PARTITION BY p.RenewedFromPolicyId)
          , rnum = ROW_NUMBER() OVER (PARTITION BY p.RenewedFromPolicyId ORDER BY p.PolicyId DESC)
        FROM
            dbo.Policy p WITH (NOLOCK)
        WHERE
            p.RenewedFromPolicyId IS NOT NULL
    --AND IsDeleted = 0; -- To match the original version
    ) main
    WHERE
        main.rnum = 1
    ORDER BY
        main.RenewedFromPolicyId ASC;

    CREATE CLUSTERED INDEX PK_#renfrom
    ON #renfrom
    (
        RenewedFromPolicyId
    );

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    DROP TABLE IF EXISTS #prenew;

    CREATE TABLE #prenew (
        PolicyId                 BIGINT        NOT NULL
      , ExpiringPolicyId         BIGINT        NULL
      , ExpiringPolicyReference  NVARCHAR(50)  NULL
      , ExpiringPolicyKey        NVARCHAR(101) NULL
      , RenewedToPolicyReference NVARCHAR(50)  NULL
      , RenewalPolicies          INT           NULL
      , PolicyStatus             NVARCHAR(200) NULL
      , ExpiringPolicyStatus     NVARCHAR(200) NULL
      , RefPolicyStatus          NVARCHAR(200) NULL
      , OpportunityType          NVARCHAR(200) NULL
      , InsuranceType            NVARCHAR(200) NULL
      , PolicyType               NVARCHAR(200) NULL
      , COLCurrentPolicyStatus   NVARCHAR(200) NULL
    );

    INSERT INTO
        #prenew
        (
            PolicyId
          , ExpiringPolicyId
          , ExpiringPolicyReference
          , ExpiringPolicyKey
          , RenewedToPolicyReference
          , RenewalPolicies
          , PolicyStatus
          , ExpiringPolicyStatus
          , RefPolicyStatus
          , OpportunityType
          , InsuranceType
          , PolicyType
          , COLCurrentPolicyStatus
        )
    SELECT
        p.PolicyId
      , ExpiringPolicyId = px.PolicyId
      , ExpiringPolicyReference = px.PolicyReference
      , ExpiringPolicyKey = px.PolicyKey
      , RenewedToPolicyReference = rfrom.PolicyReference
      , RenewalPolicies = rfrom.Cnt
      , PolicyStatus = ISNULL(ps.PolicyStatus, 'Unknown')
      , ExpiringPolicyStatus = ISNULL(pxs.PolicyStatus, 'Unknown')
      , rps.RefPolicyStatus
      , OpportunityType = ISNULL(ot.OpportunityType, 'Unknown')
      , InsuranceType = ISNULL(rit.RefInsuranceType, 'Unknown')
      , PolicyType = ISNULL(pt.PolicyType, 'Unknown')
      , COLCurrentPolicyStatus = CASE WHEN rps.RefPolicyStatus NOT IN (
                                          'Placement', 'Not Taken Up (NTU)'
                                      )
                                           AND p.PlacementId IS NULL
                                           AND p.RenewedFromPolicyId IS NULL
                                           AND p.InceptionDate > DATEADD(yy, DATEDIFF(yy, 0, GETDATE()), 0)
                                          THEN 'New Policy'
                                      WHEN rps.RefPolicyStatus = 'LIVE'
                                           AND p.PlacementId IS NOT NULL
                                           AND p.RuleId IS NOT NULL
                                           AND p.RunId IS NOT NULL
                                          THEN 'Renewed Policy'
                                      WHEN rps.RefPolicyStatus NOT IN (
                                          'LIVE'
                                      )
                                           AND p.PlacementId IS NOT NULL
                                           AND p.RuleId IS NOT NULL
                                           AND p.RunId IS NOT NULL
                                          THEN 'Placement/Policy Cancelled/Lapsed/NA'
                                      WHEN (
                                          p.PlacementId IS NULL
                                          OR p.RuleId IS NULL
                                          OR p.RunId IS NULL
                                      )
                                          THEN 'UnRenewed Policy' END
    FROM
        dbo.Policy p WITH (NOLOCK)
        INNER JOIN PactConfig.SourceSystem ss WITH (NOLOCK)
            ON p.DataSourceInstanceId = ss.DataSourceInstanceId

        LEFT JOIN dbo.Policy px WITH (NOLOCK)
            ON px.PolicyId = p.RenewedFromPolicyId
               AND px.IsDeleted = 0

        LEFT JOIN #renfrom rfrom WITH (NOLOCK)
            ON rfrom.RenewedFromPolicyId = p.PolicyId

        LEFT JOIN PAS.PolicyStatus ps WITH (NOLOCK)
            ON ps.PolicyStatusKey = p.PolicyStatusKey
               AND ps.DataSourceInstanceId = p.DataSourceInstanceId

        LEFT JOIN PAS.RefPolicyStatus rps WITH (NOLOCK)
            ON p.RefPolicyStatusId = rps.RefPolicyStatusId

        LEFT JOIN PAS.PolicyStatus pxs WITH (NOLOCK)
            ON pxs.PolicyStatusKey = px.PolicyStatusKey
               AND pxs.DataSourceInstanceId = p.DataSourceInstanceId

        LEFT JOIN PAS.PolicyType pt WITH (NOLOCK)
            ON pt.PolicyTypeKey = p.PolicyTypeKey
               AND pt.DataSourceInstanceId = p.DataSourceInstanceId

        LEFT JOIN ref.OpportunityType ot WITH (NOLOCK)
            ON ot.OpportunityTypeId = p.OpportunityTypeId
               AND ot.IsDeprecated = 0

        LEFT JOIN ref.RefInsuranceType rit WITH (NOLOCK)
            ON rit.RefInsuranceTypeId = p.InsuranceTypeId
               AND rit.IsDeprecated = 0
    WHERE
        p.IsDeleted = 0
        AND p.InceptionDate BETWEEN ss.ExpiringFrom AND DATEADD(MONTH, 6, GETDATE())
        AND ss.Enabled = 1
    ORDER BY
        p.PolicyId ASC;

    CREATE CLUSTERED INDEX PK_#prenew
    ON #prenew
    (
        PolicyId
    );

    --No longer need, table of 4.4M rows
    DROP TABLE IF EXISTS #renfrom;

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    /* Get the Policy Organisation links */
    DROP TABLE IF EXISTS #porg;

    CREATE TABLE #porg (
        PolicyId       BIGINT NOT NULL
      , OrganisationId INT    NOT NULL
    );

    INSERT INTO
        #porg
        (
            PolicyId
          , OrganisationId
        )
    SELECT
        main.PolicyId
      , main.OrganisationId
    FROM (
        SELECT
            po.PolicyId
          , po.OrganisationId
          , rnum = ROW_NUMBER() OVER (PARTITION BY po.PolicyId
                                      ORDER BY
                                          ISNULL(po.IsDeleted, 0) ASC
                                        , po.OrganisationRoleId DESC
                                        , po.OrganisationId DESC
                                        , po.ETLUpdatedDate DESC
                                )
        FROM
            dbo.PolicyOrganisation po WITH (NOLOCK)
            INNER JOIN #prenew pre WITH (NOLOCK)
                ON po.PolicyId = pre.PolicyId
    ) main
    WHERE
        main.rnum = 1
    ORDER BY
        main.PolicyId ASC;

    CREATE CLUSTERED INDEX PK_#porg
    ON #porg
    (
        PolicyId
    );

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    DROP TABLE IF EXISTS #tmpPolicyAuditStaging;

    CREATE TABLE #tmpPolicyAuditStaging (
        DataSourceInstanceId     INT            NULL
      , PolicyId                 BIGINT         NOT NULL
      , PolicyReference          NVARCHAR(50)   NULL
      , PolicyDescription        NVARCHAR(500)  NULL
      , PolicyKey                NVARCHAR(101)  NULL
      , InceptionDate            DATETIME2(7)   NULL
      , InceptionDateKey         INT            NULL
      , ExpiryDate               DATETIME2(7)   NULL
      , IsDeleted                BIT            NOT NULL
      , CreatedUTCDate           DATETIME2(7)   NULL
      , CreationDateKey          INT            NOT NULL
      , LastUpdatedUTCDate       DATETIME2(7)   NULL
      , IsFacility               BIT            NOT NULL
      , SumInsured               DECIMAL(18, 4) NULL
      , SumInsuredUSD            DECIMAL(18, 4) NULL
      , OrganisationId           INT            NULL
      , ExpiringPolicyId         BIGINT         NULL
      , ExpiringPolicyReference  NVARCHAR(50)   NULL
      , NewOrRenewal             NVARCHAR(10)   NOT NULL
      , ExpiringPolicyKey        NVARCHAR(101)  NULL
      , RenewedToPolicyReference NVARCHAR(50)   NULL
      , RenewalPolicies          INT            NULL
      , SentToBP                 BIT            NOT NULL
      , RuleId                   INT            NULL
      , InScope                  BIT            NOT NULL
      , LinkedToPlacement        BIT            NOT NULL
      , HasPlacement             BIT            NOT NULL
      , PlacementId              BIGINT         NULL
      , CurrentBPPlacementID     NVARCHAR(100)  NULL
      , RenewalPlacement         BIT            NOT NULL
      , RenewalSent              NVARCHAR(15)   NOT NULL
      , IncomingGlobalBusiness   BIT            NULL
      , Backloaded               NVARCHAR(500)  NULL
      , IsMainPolicy             INT            NULL
      , COLRenewalDate           DATETIME2(7)   NULL
      , COLPlacementSystemId     INT            NULL
      , PolicyStatus             NVARCHAR(200)  NULL
      , ExpiringPolicyStatus     NVARCHAR(200)  NULL
      , RefPolicyStatus          NVARCHAR(200)  NULL
      , OpportunityType          NVARCHAR(200)  NULL
      , InsuranceType            NVARCHAR(200)  NULL
      , PolicyType               NVARCHAR(200)  NULL
      , COLCurrentPolicyStatus   NVARCHAR(200)  NULL
      , SumInsuredCurrency       NVARCHAR(50)   NULL
      , BrokingSegmentName       NVARCHAR(200)  NULL
      , BrokingRegionName        NVARCHAR(200)  NULL
      , BrokingSubSegmentName    NVARCHAR(200)  NULL
      , RenewedFromPolicyId      INT            NULL
      , RunId                    INT            NULL
    );

    INSERT INTO
        #tmpPolicyAuditStaging
        (
            DataSourceInstanceId
          , PolicyId
          , PolicyReference
          , PolicyDescription
          , PolicyKey
          , InceptionDate
          , InceptionDateKey
          , ExpiryDate
          , IsDeleted
          , CreatedUTCDate
          , CreationDateKey
          , LastUpdatedUTCDate
          , IsFacility
          , SumInsured
          , SumInsuredUSD
          , OrganisationId
          , ExpiringPolicyId
          , ExpiringPolicyReference
          , NewOrRenewal
          , ExpiringPolicyKey
          , RenewedToPolicyReference
          , RenewalPolicies
          , SentToBP
          , RuleId
          , InScope
          , LinkedToPlacement
          , HasPlacement
          , PlacementId
          , CurrentBPPlacementID
          , RenewalPlacement
          , RenewalSent
          , IncomingGlobalBusiness
          , Backloaded
          , IsMainPolicy
          , COLRenewalDate
          , COLPlacementSystemId
          , PolicyStatus
          , ExpiringPolicyStatus
          , RefPolicyStatus
          , OpportunityType
          , InsuranceType
          , PolicyType
          , COLCurrentPolicyStatus
          , SumInsuredCurrency
          , BrokingSegmentName
          , BrokingRegionName
          , BrokingSubSegmentName
          , RenewedFromPolicyId
          , RunId
        )
    SELECT
        p.DataSourceInstanceId
      , p.PolicyId
      , PolicyReference = CASE WHEN COALESCE(p.PolicyReference, '') = ''
                                   THEN NULL
                               ELSE p.PolicyReference END
      , PolicyDescription = CASE WHEN COALESCE(p.PolicyDescription, '') = ''
                                     THEN NULL
                                 ELSE p.PolicyDescription END
      , p.PolicyKey
      , InceptionDate = CASE WHEN p.InceptionDate >= '9999-01-01'
                                 THEN NULL
                             ELSE p.InceptionDate END
      , InceptionDateKey = CASE WHEN p.InceptionDate >= '9999-01-01'
                                    THEN NULL
                                ELSE CAST(REPLACE(CONVERT(DATE, p.InceptionDate, 121), '-', '') AS INT) END
      , p.ExpiryDate
      , p.IsDeleted
      , CreatedUTCDate = CAST(p.ETLCreatedDate AS DATE)
      , CreationDateKey = CAST(REPLACE(CONVERT(DATE, p.ETLCreatedDate, 121), '-', '') AS INT)
      , LastUpdatedUTCDate = p.ETLUpdatedDate
      , p.IsFacility
      , p.SumInsured
      , SumInsuredUSD = p.SumInsured * ISNULL(p_si_rate.ExchangeRate, 1)
      , porg.OrganisationId
      , pren.ExpiringPolicyId
      , ExpiringPolicyReference = CASE WHEN ISNULL(pren.ExpiringPolicyReference, '') = ''
                                           THEN NULL
                                       ELSE pren.ExpiringPolicyReference END
      , NewOrRenewal = CASE WHEN p.RenewedFromPolicyId IS NULL
                                THEN N'New'
                            ELSE N'Renewal' END
      , pren.ExpiringPolicyKey
      , RenewedToPolicyReference = pren.RenewedToPolicyReference
      , RenewalPolicies = ISNULL(pren.RenewalPolicies, 0)
      , SentToBP = CASE WHEN rpl.RenewedToPlacementId IS NOT NULL
                            THEN 1
                        ELSE 0 END
      , p.RuleId
      , InScope = CASE WHEN ISNULL(p.RuleId, 0) < 1
                            AND rpl.PlacementId IS NULL
                           THEN 0
                       ELSE 1 END
      , LinkedToPlacement = CASE WHEN rpl.PlacementId IS NOT NULL
                                     THEN 1
                                 ELSE 0 END
      , HasPlacement = CASE WHEN rpl.PlacementId IS NULL
                                THEN 0
                            ELSE 1 END
      , rpl.PlacementId
      , CurrentBPPlacementID = CAST(rpl.PlacementSystemId AS NVARCHAR(100))
      , RenewalPlacement = ISNULL(rpl.RenewalPlacement, 0)
      , RenewalSent = CASE WHEN rpl.RenewedToPlacementId IS NOT NULL
                               THEN 'Renewed'
                           ELSE 'Not Renewed' END
      , p.IncomingGlobalBusiness
      , p.Backloaded
      , p.IsMainPolicy
      , COLRenewalDate = DATEADD(DAY, -110, p.ExpiryDate)
      , COLPlacementSystemId = rpl.MAXPlacementSystemId
      , pren.PolicyStatus
      , pren.ExpiringPolicyStatus
      , pren.RefPolicyStatus
      , pren.OpportunityType
      , pren.InsuranceType
      , pren.PolicyType
      , pren.COLCurrentPolicyStatus
      , SumInsuredCurrency = pc_si.CurrencyAlphaCode
      , BrokingSegmentName = ISNULL(bs.BrokingSegment, 'Unspecified')
      , BrokingRegionName = ISNULL(br.BrokingRegion, 'Unspecified')
      , BrokingSubSegmentName = ISNULL(pc.BrokingSubSegment, 'Unspecified')
      , p.RenewedFromPolicyId
      , p.RunId
    FROM
        #prenew pren
        INNER JOIN dbo.Policy p WITH (NOLOCK)
            ON p.PolicyId = pren.PolicyId

        LEFT JOIN #tmpPlacementPolicyStaging rpl WITH (NOLOCK)
            ON rpl.PolicyId = p.PolicyId
               AND rpl.PlacementPolicyRelationshipTypeId = 1

        LEFT JOIN #porg porg WITH (NOLOCK)
            ON porg.PolicyId = p.PolicyId

        LEFT JOIN ref.BrokingSegment bs WITH (NOLOCK)
            ON p.BrokingSegmentId = bs.BrokingSegmentId

        LEFT JOIN ref.BrokingRegion br WITH (NOLOCK)
            ON p.BrokingRegionId = br.BrokingRegionId

        LEFT JOIN ref.BrokingSubSegment pc WITH (NOLOCK)
            ON p.BrokingSubSegmentId = pc.BrokingSubSegmentId

        LEFT JOIN Reference.Currency pc_si WITH (NOLOCK)
            ON pc_si.CurrencyId = p.SumInsuredCurrencyId

        LEFT JOIN #fx p_si_rate
            ON p_si_rate.FromCurrencyId = pc_si.CurrencyId
               AND p_si_rate.EffectiveDate <= CAST(p.ETLCreatedDate AS DATE)
               AND CAST(p.ETLCreatedDate AS DATE) < p_si_rate.ToDate
    ORDER BY
        pren.PolicyId ASC;

    CREATE CLUSTERED INDEX PK_#tmpPolicyAuditStaging
    ON #tmpPolicyAuditStaging
    (
        PolicyId
    );

    --These temporary table no longer required
    DROP TABLE IF EXISTS #porg;
    DROP TABLE IF EXISTS #prenew;

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    DROP TABLE IF EXISTS #PartyTable;

    CREATE TABLE #PartyTable (
        PolicyId             BIGINT        NOT NULL
      , IsPrimaryParty       BIT           NOT NULL
      , GlobalPartyRoleId    INT           NULL
      , PartyName            NVARCHAR(500) NULL
      , BusinessKey          NVARCHAR(50)  NULL
      , PartyRoleDescription NVARCHAR(250) NULL
      , GlobalPartyId        INT           NULL
    );

    INSERT INTO
        #PartyTable
        (
            PolicyId
          , IsPrimaryParty
          , GlobalPartyRoleId
          , PartyName
          , BusinessKey
          , PartyRoleDescription
          , GlobalPartyId
        )
    SELECT
        main.PolicyId
      , main.IsPrimaryParty
      , main.GlobalPartyRoleId
      , main.PartyName
      , main.BusinessKey
      , main.PartyRoleDescription
      , main.GlobalPartyId
    FROM (
        SELECT
            ppr.PolicyId
          , ppr.IsPrimaryParty
          , pr.GlobalPartyRoleId
          , p.PartyName
          , p.BusinessKey
          , rpr.PartyRoleDescription
          , p.GlobalPartyId
          --Note: part of PID 314344, original order by clause was non-deterministic, changed ETLUpdateDate source table and added ParttId to force consistent solution
          , rnum = ROW_NUMBER() OVER (PARTITION BY ppr.PolicyId
                                      ORDER BY
                                          ppr.IsPrimaryParty DESC
                                        , pr.GlobalPartyRoleId ASC
                                        , ppr.ETLUpdatedDate DESC
                                        , p.PartyId DESC
                                )
        FROM
            dbo.PolicyPartyRelationship ppr WITH (NOLOCK)
            LEFT JOIN dbo.Party p
                ON ppr.PartyId = p.PartyId

            INNER JOIN ref.PartyRole pr WITH (NOLOCK)
                ON ppr.PartyRoleId = pr.PartyRoleId --only include client (100), insured (101) and reinsured (106)

            LEFT JOIN Reference.PartyRole rpr WITH (NOLOCK)
                ON rpr.PartyRoleId = pr.GlobalPartyRoleId
        WHERE
            ppr.IsDeleted = 0
            AND pr.GlobalPartyRoleId IN (
                    100, 101, 106
                )
    ) main
    WHERE
        main.rnum = 1
    ORDER BY
        main.PolicyId;

    CREATE CLUSTERED INDEX PK_#PartyTable
    ON #PartyTable
    (
        PolicyId
    );

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    -- Truncate right at the last moment just before the insert.
    TRUNCATE TABLE rpt.PolicyAudit;

    INSERT INTO
        rpt.PolicyAudit WITH (TABLOCK)
        (
            DataSourceInstanceId
          , PolicyId
          , PolicyReference
          , PolicyDescription
          , PolicyType
          , PolicyKey
          , PolicyStatus
          , InceptionDate
          , InceptionDateKey
          , OpportunityType
          , ExpiryDate
          , InsuranceType
          , IsDeleted
          , CreatedUTCDate
          , CreationDateKey
          , LastUpdatedUTCDate
          , IsFacility
          , SumInsuredCurrency
          , SumInsured
          , SumInsuredUSD
          , OrganisationId
          , ExpiringPolicyId
          , ExpiringPolicyReference
          , NewOrRenewal
          , ExpiringPolicyStatus
          , ExpiringPolicyKey
          , RenewedToPolicyReference
          , RenewalPolicies
          , SentToBP
          , RuleId
          , InScope
          , RuleName
          , RuleDescription
          , LinkedToPlacement
          , RefPolicyStatus
          , HasPlacement
          , CurrentPSPlacementId
          , CurrentBPPlacementId
          , ExpiringPSPlacementId
          , ExpiringBPPlacementId
          , BrokingSegmentName
          , BrokingRegionName
          , BrokingSubSegmentName
          , RenewalsEnabled
          , RenewalPlacement
          , RenewalSent
          , NewBusinessOnly
          , OutOfScope
          , IncomingGlobalBusiness
          , Backloaded
          , ProductCode
          , Proposta
          , IsMainPolicy
          , COLRenewalDate
          , COLPolicyStatus
          , TeamName
          , COLCurrentPolicyStatus
          , COLPlacementSystemId
          , HasContract
          , HasContractWording
          , IsPrimaryParty
          , GlobalPartyRole
          , GlobalPartyRoleId
          , PartyName
          , BusinessKey
          , GlobalPartyId
        )
    SELECT
        p.DataSourceInstanceId
      , p.PolicyId
      , p.PolicyReference
      , p.PolicyDescription
      , p.PolicyType
      , p.PolicyKey
      , p.PolicyStatus
      , p.InceptionDate
      , p.InceptionDateKey
      , p.OpportunityType
      , p.ExpiryDate
      , p.InsuranceType
      , p.IsDeleted
      , p.CreatedUTCDate
      , p.CreationDateKey
      , p.LastUpdatedUTCDate
      , p.IsFacility
      , p.SumInsuredCurrency
      , p.SumInsured
      , p.SumInsuredUSD
      , p.OrganisationId
      , p.ExpiringPolicyId
      , p.ExpiringPolicyReference
      , p.NewOrRenewal
      , p.ExpiringPolicyStatus
      , p.ExpiringPolicyKey
      , p.RenewedToPolicyReference
      , p.RenewalPolicies
      , p.SentToBP
      , p.RuleId
      , p.InScope
      , rules.RuleName
      , rules.RuleDescription
      , p.LinkedToPlacement
      , p.RefPolicyStatus
      , HasPlacement = p.HasPlacement
      , CurrentPSPlacementID = p.PlacementId
      , CurrentBPPlacementID = p.CurrentBPPlacementID
      , ExpiringPSPlacementID = RPL.PlacementId
      , ExpiringBPPlacementID = RPL.MAXPlacementSystemId
      , p.BrokingSegmentName
      , p.BrokingRegionName
      , p.BrokingSubSegmentName
      , RenewalsEnabled = CASE WHEN ISNULL(lr.RuleId, 0) > 0
                                   THEN 1
                               ELSE 0 END
      , p.RenewalPlacement
      , p.RenewalSent
      , NewBusinessOnly = CASE WHEN rules.Enabled = 1
                                    AND nbonly.RuleId IS NOT NULL
                                   THEN 1
                               ELSE 0 END
      , OutOfScope = CASE WHEN rules.Enabled = 1
                               AND nbonly.RuleId IS NULL
                               AND (
                                   p.PlacementId IS NOT NULL
                                   OR ISNULL(p.RuleId, 0) > 0
                               )
                               AND ISNULL(lr.RuleId, 0) = 0
                              THEN 1
                          ELSE 0 END
      , p.IncomingGlobalBusiness
      , p.Backloaded
      --> Added below selection for COL Exception Report
      , ProductCode.ProductCode
      , pro.Proposta
      , p.IsMainPolicy
      , p.COLRenewalDate
      , COLPolicyStatus = CASE WHEN RPL.PlacementSystemId IS NOT NULL
                                   THEN 'Send to Broking Platform'
                               WHEN p.ExpiryDate > GETUTCDATE()
                                   THEN 'Future Renewal'
                               WHEN ISNULL(T.TeamName, 'Un-Assigned Team') = 'Un-Assigned Team'
                                   THEN 'Out of scope of Renewal'
                               ELSE NULL END
      , TeamName = ISNULL(T.TeamName, 'Un-Assigned Team')
      , p.COLCurrentPolicyStatus
      , p.COLPlacementSystemId
      , HasContract = ISNULL(pcd.HasContract, 0)
      , HasContractWording = ISNULL(pcd.HasContractWording, 0)
      , IsPrimaryParty = ISNULL(TPT.IsPrimaryParty, -1)
      , GlobalPartyRole = TPT.PartyRoleDescription
      , GlobalPartyRoleId = TPT.GlobalPartyRoleId
      , PartyName = TPT.PartyName
      , BusinessKey = TPT.BusinessKey
      , GlobalPartyId = TPT.GlobalPartyId
    FROM
        #tmpPolicyAuditStaging p
        LEFT JOIN #PolicyContractDetails pcd
            ON pcd.PolicyId = p.PolicyId

        LEFT JOIN PactConfig.[Rule] rules WITH (NOLOCK)
            ON p.RuleId = rules.RuleId
               AND rules.DataSourceInstanceId IS NOT NULL
               AND rules.IsDeleted = 0

        LEFT JOIN (
            SELECT
                RuleId = MAX(RuleId)
              , ParentRuleId
            FROM
                PactConfig.[Rule] WITH (NOLOCK)
            WHERE
                ParentRuleId IS NOT NULL
                AND Enabled = 1
                AND IsDeleted = 0
            GROUP BY
                ParentRuleId
        ) lr
            ON lr.ParentRuleId = rules.RuleId

        LEFT JOIN (
            SELECT RuleId = par.RuleId
            FROM
                PactConfig.[Rule] par WITH (NOLOCK)
            WHERE
                par.RuleTypeId = 1
                AND NOT EXISTS (
                SELECT * FROM PactConfig.[Rule] ch WITH (NOLOCK) WHERE ch.ParentRuleId = par.RuleId AND ch.IsDeleted = 0
            )
                AND par.IsDeleted = 0
        ) nbonly
            ON nbonly.RuleId = rules.RuleId

        LEFT JOIN #tmpPlacementPolicyStaging RPL
            ON RPL.PolicyId = p.PolicyId
               AND RPL.PlacementPolicyRelationshipTypeId = 2

        ----> Added Joins for COL Exception Reporting data

        LEFT JOIN #ProductCode ProductCode
            ON ProductCode.PolicyId = p.PolicyId --> Only for COL

        LEFT JOIN #Proposta pro
            ON pro.PolicyId = p.PolicyId

        LEFT JOIN ref.Team T WITH (NOLOCK)
            ON T.TeamId = rules.TeamId

        LEFT JOIN #PartyTable TPT
            ON p.PolicyId = TPT.PolicyId
    OPTION (MAXDOP 1);

    --This forces serialisation and in doing so compels the query optimiser to serailise the joins in the listed order
    SELECT @InsertedCount = @@ROWCOUNT;

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    DROP TABLE #Proposta;
    DROP TABLE #ProductCode;
    DROP TABLE #tmpPlacementPolicyStaging;
    DROP TABLE #tmpPolicyAuditStaging;
    DROP TABLE #PolicyContractDetails;
    DROP TABLE #PartyTable;
    DROP TABLE #fx;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
GO