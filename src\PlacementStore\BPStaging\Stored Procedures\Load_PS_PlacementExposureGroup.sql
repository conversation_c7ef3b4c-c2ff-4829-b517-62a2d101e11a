/*
Lineage
PS.PlacementExposureGroup.PlacementExposureGroupKey=BP.PlacementExposureSummary.Id
PS.PlacementExposureGroup.PlacementId=dbo.Placement.PlacementId
PS.PlacementExposureGroup.ExposurePeriodId=BP.PlacementExposureSummaryGroup.ExposurePeriodGroupId
PS.PlacementExposureGroup.ExposureTypeId=BP.ExposureSummaryElement.ExposureTypeId
PS.PlacementExposureGroup.IsDeleted=BP.PlacementExposureSummaryGroup.IsDeleted
PS.PlacementExposureGroup.IsDeleted=BP.PlacementExposureSummary.IsDeleted
PS.PlacementExposureGroup.IsDeleted=BP.ExposureSummaryElement.IsDeleted
PS.PlacementExposureGroup.SourceUpdatedDate=BP.PlacementExposureSummaryGroup.ETLUpdatedDate
PS.PlacementExposureGroup.SourceUpdatedDate=BP.PlacementExposureSummary.ETLUpdatedDate
PS.PlacementExposureGroup.SourceUpdatedDate=BP.ExposureSummaryElement.ETLUpdatedDate
*/
CREATE PROCEDURE BPStaging.Load_PS_PlacementExposureGroup
    @Success BIT OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.PlacementExposureGroup';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.PlacementExposureGroup T
    USING (
        SELECT
            DataSourceInstanceId = 50366
          , PlacementExposureGroupKey = CONCAT(N'PLEXPSUM|', pes.Id)
          , pl.PlacementId
          , ExposurePeriodId = pesg.ExposurePeriodGroupId
          , ese.ExposureTypeId
          , IsDeleted = (pesg.IsDeleted | pes.IsDeleted | ese.IsDeleted)
          , SourceUpdatedDate = (
                SELECT MAX(v) FROM (VALUES (pesg.ETLUpdatedDate), (pes.ETLUpdatedDate), (ese.ETLUpdatedDate)) value (v)
            )
        FROM
            BP.PlacementExposureSummaryGroup pesg
            INNER JOIN BP.PlacementExposureSummary pes
                ON pes.PlacementExposureSummaryGroupId = pesg.Id

            INNER JOIN BP.ExposureSummaryElement ese
                ON ese.Id = pes.ExposureSummaryElementId

            INNER JOIN dbo.Placement pl
                ON pl.PlacementSystemId = pesg.PlacementId
                   AND pl.DataSourceInstanceId = 50366
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.PlacementExposureGroupKey = S.PlacementExposureGroupKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , PlacementExposureGroupKey
               , PlacementId
               , ExposurePeriodId
               , ExposureTypeId
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.PlacementExposureGroupKey
                   , S.PlacementId
                   , S.ExposurePeriodId
                   , S.ExposureTypeId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeleted
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.PlacementId
                               , S.ExposurePeriodId
                               , S.ExposureTypeId
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.PlacementId
                               , T.ExposurePeriodId
                               , T.ExposureTypeId
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.PlacementId = S.PlacementId
               , T.ExposurePeriodId = S.ExposurePeriodId
               , T.ExposureTypeId = S.ExposureTypeId
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = CONCAT(N'Merge ', @TargetTable);

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;