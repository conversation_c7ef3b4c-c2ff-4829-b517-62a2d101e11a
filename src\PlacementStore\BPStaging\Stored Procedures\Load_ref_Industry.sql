/*
Lineage
ref.Industry.DataSourceInstanceId=dbo.Industry.DataSourceInstanceId
ref.Industry.IndustryKey=Reference.Industry.IndustryId
ref.Industry.IndustryKey=Reference.IndustrySector.IndustrySectorId
ref.Industry.IndustryKey=Reference.IndustrySubSector.IndustrySubSectorId
ref.Industry.IndustryKey=dbo.Industry.SourceIndustryId
ref.Industry.IndustryCode=dbo.Industry.IndustryCode
ref.Industry.IndustryName=Reference.Industry.IndustryName
ref.Industry.IndustryName=Reference.IndustrySector.IndustrySectorName
ref.Industry.IndustryName=Reference.IndustrySubSector.IndustrySubsectorName
ref.Industry.IndustryName=ref.ElementTagType.ElementTagType
ref.Industry.LevelNum=dbo.Industry.LevelNumber
ref.Industry.ElementTagTypeKey=ref.ElementTagType.ElementTagTypeKey
ref.Industry.SourceUpdatedDate=Reference.Industry.LastUpdateTime
ref.Industry.SourceUpdatedDate=Reference.IndustrySector.LastUpdateTime
ref.Industry.SourceUpdatedDate=Reference.IndustrySubSector.LastUpdateTime
ref.Industry.SourceUpdatedDate=ref.ElementTagType.ETLUpdatedDate
ref.Industry.SourceUpdatedDate=ref.ElementTagGroup.ETLUpdatedDate
ref.Industry.SourceUpdatedDate=dbo.Industry.UpdatedDate
ref.Industry.IsDeprecated=Reference.Industry.IsDeleted
ref.Industry.IsDeprecated=Reference.IndustrySector.IsDeleted
ref.Industry.IsDeprecated=Reference.IndustrySubSector.IsDeleted
ref.Industry.IsDeprecated=ref.ElementTagType.IsDeprecated
ref.Industry.IsDeprecated=ref.ElementTagGroup.IsDeprecated
ref.Industry.IsDeprecated=dbo.Industry.IsDeleted
ref.Industry.ParentId=ref.Industry.IndustryId
*/
CREATE PROCEDURE BPStaging.Load_ref_Industry
    @Success BIT OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY

    /* Need to be able to set the right parent IDs */
    DROP TABLE IF EXISTS #Industry;

    SELECT
        DataSourceInstanceId = 50355
      , IndustryKey = CAST(ri.IndustryId AS NVARCHAR(50))
      , IndustryCode = CAST(NULL AS NVARCHAR(20))
      , IndustryName = CAST(ri.IndustryName AS NVARCHAR(250))
      , ParentKey = CAST(NULL AS NVARCHAR(50))
      , LevelNum = 1
      , ETT.ElementTagTypeKey
      , SourceUpdatedDate = ri.LastUpdateTime
      , IsDeprecated = ri.IsDeleted
    INTO #Industry
    FROM
        Reference.Industry ri
        LEFT JOIN (
            SELECT
                ETT.ElementTagTypeKey
              , ETT.ElementTagType
            FROM
                ref.ElementTagType ETT
                INNER JOIN ref.ElementTagGroup ETG
                    ON ETG.ElementTagGroupId = ETT.ElementTagGroupId
            WHERE
                ETG.ElementTagGroupKey = 'riskIndustryLevelOne'
        ) ETT
            ON ETT.ElementTagType = ri.IndustryName
    UNION ALL
    SELECT
        DataSourceInstanceId = 50355
      , IndustryKey = CAST(ris.IndustrySectorId AS NVARCHAR(50))
      , IndustryCode = CAST(NULL AS NVARCHAR(20))
      , IndustryName = CAST(ris.IndustrySectorName AS NVARCHAR(250))
      , ParentKey = CAST(ris.IndustryId AS NVARCHAR(50))
      , LevelNum = 2
      , ETT.ElementTagTypeKey
      , SourceUpdatedDate = ris.LastUpdateTime
      , IsDeprecated = ris.IsDeleted
    FROM
        Reference.IndustrySector ris
        LEFT JOIN (
            SELECT
                ETT.ElementTagTypeKey
              , ETT.ElementTagType
            FROM
                ref.ElementTagType ETT
                INNER JOIN ref.ElementTagGroup ETG
                    ON ETG.ElementTagGroupId = ETT.ElementTagGroupId
            WHERE
                ETG.ElementTagGroupKey = 'riskIndustryLevelTwo'
        ) ETT
            ON ETT.ElementTagType = ris.IndustrySectorName
    UNION ALL
    SELECT
        DataSourceInstanceId = 50355
      , IndustryKey = CAST(riss.IndustrySubSectorId AS NVARCHAR(50))
      , IndustryCode = CAST(NULL AS NVARCHAR(20))
      , IndustryName = CAST(riss.IndustrySubsectorName AS NVARCHAR(250))
      , ParentKey = CAST(riss.IndustrySectorId AS NVARCHAR(50))
      , LevelNum = 3
      , ETT.ElementTagTypeKey
      , SourceUpdatedDate = riss.LastUpdateTime
      , IsDeprecated = riss.IsDeleted
    FROM
        Reference.IndustrySubSector riss
        LEFT JOIN (
            SELECT
                ETT.ElementTagTypeKey
              , ETT.ElementTagType
            FROM
                ref.ElementTagType ETT
                INNER JOIN ref.ElementTagGroup ETG
                    ON ETG.ElementTagGroupId = ETT.ElementTagGroupId
            WHERE
                ETG.ElementTagGroupKey = 'riskIndustryLevelThree'
        ) ETT
            ON ETT.ElementTagType = riss.IndustrySubsectorName
    UNION ALL

    /* Finmar Industry Group */
    SELECT
        DataSourceInstanceId = ind.DataSourceInstanceId
      , IndustryKey = CAST(CONCAT(N'FINMARGRP|', ind.SourceIndustryId) AS NVARCHAR(50))
      , IndustryCode = ind.IndustryCode
      , IndustryName = ETT.ElementTagType
      , ParentKey = CAST(NULL AS NVARCHAR(50))
      , LevelNum = 1
      , ETT.ElementTagTypeKey
      , SourceUpdatedDate = CASE WHEN ETT.ETLUpdatedDate > ind.UpdatedDate
                                     THEN ETT.ETLUpdatedDate
                                 ELSE ind.UpdatedDate END
      , IsDeprecated = CASE WHEN ETT.IsDeprecated = 1
                                 OR ind.IsDeleted = 1
                                THEN 1
                            ELSE 0 END
    FROM
        dbo.Industry ind
        LEFT JOIN (
            SELECT
                ETT.ElementTagTypeKey
              , ETT.ElementTagType
              , ETLUpdatedDate = CASE WHEN ETT.ETLUpdatedDate > ETG.ETLUpdatedDate
                                          THEN ETT.ETLUpdatedDate
                                      ELSE ETG.ETLUpdatedDate END
              , IsDeprecated = CASE WHEN ETT.IsDeprecated = 1
                                         OR ETG.IsDeprecated = 1
                                        THEN 1
                                    ELSE 0 END
            FROM
                ref.ElementTagType ETT
                INNER JOIN ref.ElementTagGroup ETG
                    ON ETG.ElementTagGroupId = ETT.ElementTagGroupId
            WHERE
                ETG.ElementTagGroupKey = 'finmarIndustryGroup'
        ) ETT
            ON TRY_CAST(SUBSTRING(ETT.ElementTagTypeKey, CHARINDEX(N'_', ETT.ElementTagTypeKey) + 1, 100) AS INT) = ind.SourceIndustryId
    WHERE
        ind.LevelNumber = 1
        AND ind.DataSourceInstanceId = 50351
    UNION ALL

    /* Finmar industry  */
    SELECT
        DataSourceInstanceId = ind.DataSourceInstanceId
      , IndustryKey = CAST(CONCAT(N'FINMARIND|', ind.SourceIndustryId) AS NVARCHAR(50))
      , IndustryCode = ind.IndustryCode
      , IndustryName = ETT.ElementTagType
      , ParentKey = CAST(CAST(CONCAT(N'FINMARGRP|', pind.SourceIndustryId) AS NVARCHAR(50)) AS NVARCHAR(50))
      , LevelNum = ind.LevelNumber
      , ETT.ElementTagTypeKey
      , SourceUpdatedDate = CASE WHEN ETT.ETLUpdatedDate > ind.UpdatedDate
                                     THEN ETT.ETLUpdatedDate
                                 ELSE ind.UpdatedDate END
      , IsDeprecated = CASE WHEN ETT.IsDeprecated = 1
                                 OR ind.IsDeleted = 1
                                THEN 1
                            ELSE 0 END
    FROM
        dbo.Industry ind
        INNER JOIN dbo.Industry pind
            ON pind.IndustryId = ind.ParentId
               AND pind.LevelNumber = 1
               AND pind.DataSourceInstanceId = ind.DataSourceInstanceId

        LEFT JOIN (
            SELECT
                ETT.ElementTagTypeKey
              , ETT.ElementTagType
              , ETLUpdatedDate = CASE WHEN ETT.ETLUpdatedDate > ETG.ETLUpdatedDate
                                          THEN ETT.ETLUpdatedDate
                                      ELSE ETG.ETLUpdatedDate END
              , IsDeprecated = CASE WHEN ETT.IsDeprecated = 1
                                         OR ETG.IsDeprecated = 1
                                        THEN 1
                                    ELSE 0 END
            FROM
                ref.ElementTagType ETT
                INNER JOIN ref.ElementTagGroup ETG
                    ON ETG.ElementTagGroupId = ETT.ElementTagGroupId
            WHERE
                ETG.ElementTagGroupKey = 'finmarIndustry'
        ) ETT
            ON TRY_CAST(SUBSTRING(ETT.ElementTagTypeKey, CHARINDEX(N'_', ETT.ElementTagTypeKey) + 1, 100) AS INT) = ind.SourceIndustryId
    WHERE
        ind.LevelNumber = 2
        AND ind.DataSourceInstanceId = 50351;

    CREATE UNIQUE INDEX IX_#Industry
    ON #Industry
    (
        DataSourceInstanceId
      , IndustryKey
    );

    MERGE ref.Industry T
    USING (
        SELECT
            DataSourceInstanceId
          , IndustryKey
          , IndustryCode
          , IndustryName
          , LevelNum
          , ElementTagTypeKey
          , SourceUpdatedDate
          , IsDeprecated
        FROM
            #Industry
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.IndustryKey = S.IndustryKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , IndustryKey
               , IndustryCode
               , IndustryName
               , LevelNum
               , ElementTagTypeKey
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.IndustryKey
                   , S.IndustryCode
                   , S.IndustryName
                   , S.LevelNum
                   , S.ElementTagTypeKey
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.IndustryCode
                               , S.IndustryName
                               , S.LevelNum
                               , S.ElementTagTypeKey
                               , S.IsDeprecated
                             INTERSECT
                             SELECT
                                 T.IndustryCode
                               , T.IndustryName
                               , T.LevelNum
                               , T.ElementTagTypeKey
                               , T.IsDeprecated
                         )
        THEN UPDATE SET
                 T.IndustryCode = S.IndustryCode
               , T.IndustryName = S.IndustryName
               , T.LevelNum = S.LevelNum
               , T.ElementTagTypeKey = S.ElementTagTypeKey
               , T.IsDeprecated = S.IsDeprecated
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    /* Now update the ParentId if required                               */
    /* We need to wait because we need to know the Ids from ref.Industry */
    UPDATE ind
    SET
        ind.ETLUpdatedDate = GETUTCDATE()
      , ind.ParentId = pind.IndustryId
    FROM
        ref.Industry ind
        INNER JOIN #Industry tind
            ON tind.DataSourceInstanceId = ind.DataSourceInstanceId
               AND tind.IndustryKey = ind.IndustryKey

        LEFT JOIN ref.Industry pind
            ON pind.DataSourceInstanceId = tind.DataSourceInstanceId
               AND pind.IndustryKey = tind.ParentKey
    WHERE
        ISNULL(ind.ParentId, 0) <> ISNULL(pind.IndustryId, 0);

    DROP TABLE #Industry;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    DROP TABLE IF EXISTS #Industry;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = N'Merge ref.Industry';

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

RETURN 0;