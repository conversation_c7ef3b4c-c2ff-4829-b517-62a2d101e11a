/*
Lineage
placementid=dbo.Placement.PlacementId
NumericValue=dbo.Policy.VNAB_Number
BooleanValue=dbo.Policy.VNAB_Number
BooleanValue=dbo.Policy.RenewableFlag
BooleanValue=dbo.Policy.AutoInvoiceFlag
RenewalProcessStartDate=dbo.PlacementListener.RenewalProcessStartDate
LastUpdatedUTCDate=dbo.Policy.ETLUpdatedDate
*/
CREATE VIEW APIv1.AdditionalDataItem
AS

SELECT DISTINCT
       placementid = pl.PlacementId
     , LibraryAdditionalDataItemId = 1 --> From lib.AdditionalDataItem 
     , ValueTypeId = 2
     , CurrencyTypeId = CAST(NULL AS INT)
     , NumericValue = CAST(MAX(Pol.VNAB_Number) AS NUMERIC(18, 4))/* Deliberately moved the CAST to outside. Seen the case where it will do the cast before applying the WHERE clause and raise an error */
     , TextValue = CAST(NULL AS NVARCHAR(MAX))
     , ValueTypeLookupValueId = CAST(NULL AS INT)
     , DateValue = CAST(NULL AS DATETIME2(7))
     , ComplexValue = CAST(NULL AS NVARCHAR(MAX))
     , BooleanValue = CAST(NULL AS BIT)
     , RenewalProcessStartDate = MAX(PLI.RenewalProcessStartDate)
     , LastUpdatedUTCDate = MAX(Pol.ETLUpdatedDate)
FROM
    dbo.Policy Pol
    INNER JOIN dbo.PlacementPolicy ppcur
        ON ppcur.PolicyId = Pol.PolicyId
           AND ppcur.IsDeleted = 0
           AND ppcur.PlacementPolicyRelationshipTypeId = 1
           AND ppcur.DataSourceInstanceId = 50366

    INNER JOIN dbo.Placement pl
        ON pl.PlacementId = ppcur.PlacementId

    LEFT JOIN dbo.PlacementListener PLI
        ON PLI.PlacementId = pl.PlacementId
           AND PLI.IsDeleted = 0
WHERE
    Pol.DataSourceInstanceId IN (
        SELECT DataSourceInstanceId FROM Reference.DataSourceInstance WHERE DataSourceId = 50001
    ) -- Apply for all the eGlobal countries 
    AND ISNUMERIC(Pol.VNAB_Number) = 1
GROUP BY
    pl.PlacementId
UNION ALL

--> IsVNAB 
SELECT DISTINCT
       placementid = pl.PlacementId
     , LibraryAdditionalDataItemId = 3 --> From lib.AdditionalDataItem 
     , ValueTypeId = 24
     , CurrencyTypeId = CAST(NULL AS INT)
     , NumericValue = CAST(NULL AS NUMERIC(18, 4))
     , TextValue = CAST(NULL AS NVARCHAR(MAX))
     , ValueTypeLookupValueId = CAST(NULL AS INT)
     , DateValue = CAST(NULL AS DATETIME2(7))
     , ComplexValue = CAST(NULL AS NVARCHAR(MAX))
     , BooleanValue = CAST(MAX(CASE WHEN Pol.VNAB_Number IS NOT NULL
                                        THEN 1
                                    ELSE 0 END
                           ) AS BIT)
     , RenewalProcessStartDate = MAX(PLI.RenewalProcessStartDate)
     , LastUpdatedUTCDate = MAX(Pol.ETLUpdatedDate)
FROM
    dbo.Policy Pol
    INNER JOIN dbo.PlacementPolicy ppcur
        ON ppcur.PolicyId = Pol.PolicyId
           AND ppcur.IsDeleted = 0
           AND ppcur.PlacementPolicyRelationshipTypeId = 1
           AND ppcur.DataSourceInstanceId = 50366

    INNER JOIN dbo.Placement pl
        ON pl.PlacementId = ppcur.PlacementId

    LEFT JOIN dbo.PlacementListener PLI
        ON PLI.PlacementId = pl.PlacementId
           AND PLI.IsDeleted = 0
WHERE
    Pol.DataSourceInstanceId IN (
        SELECT DataSourceInstanceId FROM Reference.DataSourceInstance WHERE DataSourceId = 50001
    ) -- Apply for all the eGlobal countries  
GROUP BY
    pl.PlacementId
UNION ALL

-->IsRenewable’ 
SELECT DISTINCT
       placementid = pl.PlacementId
     , LibraryAdditionalDataItemId = 5 --> From lib.AdditionalDataItem 
     , ValueTypeId = 24
     , CurrencyTypeId = CAST(NULL AS INT)
     , NumericValue = CAST(NULL AS NUMERIC(18, 4))
     , TextValue = CAST(NULL AS NVARCHAR(MAX))
     , ValueTypeLookupValueId = CAST(NULL AS INT)
     , DateValue = CAST(NULL AS DATETIME2(7))
     , ComplexValue = CAST(NULL AS NVARCHAR(MAX))
     , BooleanValue = CAST(MAX(CAST(Pol.RenewableFlag AS INT)) AS BIT)
     , RenewalProcessStartDate = MAX(PLI.RenewalProcessStartDate)
     , LastUpdatedUTCDate = MAX(Pol.ETLUpdatedDate)
FROM
    dbo.Policy Pol
    INNER JOIN dbo.PlacementPolicy ppcur
        ON ppcur.PolicyId = Pol.PolicyId
           AND ppcur.IsDeleted = 0
           AND ppcur.PlacementPolicyRelationshipTypeId = 1
           AND ppcur.DataSourceInstanceId = 50366

    INNER JOIN dbo.Placement pl
        ON pl.PlacementId = ppcur.PlacementId

    LEFT JOIN dbo.PlacementListener PLI
        ON PLI.PlacementId = pl.PlacementId
           AND PLI.IsDeleted = 0
WHERE
    Pol.DataSourceInstanceId IN (
        SELECT DataSourceInstanceId FROM Reference.DataSourceInstance WHERE DataSourceId = 50001
    ) -- Apply for all the eGlobal countries 
GROUP BY
    pl.PlacementId
UNION ALL

-->Auto Invoice
SELECT DISTINCT
       placementid = pl.PlacementId
     , LibraryAdditionalDataItemId = 11 --> From lib.AdditionalDataItem 
     , ValueTypeId = 24
     , CurrencyTypeId = CAST(NULL AS INT)
     , NumericValue = CAST(NULL AS NUMERIC(18, 4))
     , TextValue = CAST(NULL AS NVARCHAR(MAX))
     , ValueTypeLookupValueId = CAST(NULL AS INT)
     , DateValue = CAST(NULL AS DATETIME2(7))
     , ComplexValue = CAST(NULL AS NVARCHAR(MAX))
     , BooleanValue = CAST(MAX(CAST(Pol.AutoInvoiceFlag AS INT)) AS BIT)
     , RenewalProcessStartDate = MAX(PLI.RenewalProcessStartDate)
     , LastUpdatedUTCDate = MAX(Pol.ETLUpdatedDate)
FROM
    dbo.Policy Pol
    INNER JOIN dbo.PlacementPolicy ppcur
        ON ppcur.PolicyId = Pol.PolicyId
           AND ppcur.IsDeleted = 0
           AND ppcur.PlacementPolicyRelationshipTypeId = 1
           AND ppcur.DataSourceInstanceId = 50366

    INNER JOIN dbo.Placement pl
        ON pl.PlacementId = ppcur.PlacementId

    LEFT JOIN dbo.PlacementListener PLI
        ON PLI.PlacementId = pl.PlacementId
           AND PLI.IsDeleted = 0
WHERE
    Pol.DataSourceInstanceId IN (
        SELECT DataSourceInstanceId FROM Reference.DataSourceInstance WHERE DataSourceId = 50001
    ) -- Apply for all the eGlobal countries 
GROUP BY
    pl.PlacementId;