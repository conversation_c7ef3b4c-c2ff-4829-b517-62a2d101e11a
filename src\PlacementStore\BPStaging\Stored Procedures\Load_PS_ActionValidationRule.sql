/*
Lineage
PS.ActionValidationRule.ActionValidationRuleKey=BPStaging.ActionValidationRule.Id
PS.ActionValidationRule.ActionId=BPStaging.ActionValidationRule.ActionId
PS.ActionValidationRule.ValidationRule=BPStaging.ActionValidationRule.ValidationRule
*/
CREATE PROCEDURE BPStaging.Load_PS_ActionValidationRule
    @Success BIT = 1 OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ActionValidationRule';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.ActionValidationRule T
    USING (
        SELECT
            ActionValidationRuleKey = CONCAT('AVR|', Id)
          , ActionId
          , ValidationRule
          , SourceUpdatedDate = ValidFrom
          , DataSourceInstanceId = 50366
          , IsDeleted = 0
        FROM
            BPStaging.ActionValidationRule
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.ActionValidationRuleKey = S.ActionValidationRuleKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , ActionValidationRuleKey
               , ActionId
               , ValidationRule
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.ActionValidationRuleKey
                   , S.ActionId
                   , S.ValidationRule
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT S.ActionId, S.ValidationRule INTERSECT SELECT T.ActionId, T.ValidationRule
                         )
        THEN UPDATE SET
                 T.ActionId = S.ActionId
               , T.ValidationRule = S.ValidationRule
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;