/*
Lineage
dbo.Specification.SpecificationId=BPStaging.Specification.Id
dbo.Specification.PlacementSystemId=BPStaging.Specification.PlacementId
dbo.Specification.PlacementId=dbo.Placement.PlacementId
dbo.Specification.ProductId=BPStaging.Specification.ProductId
dbo.Specification.Label=BPStaging.Specification.Label
dbo.Specification.SubProductId=BPStaging.Specification.SubProductId
dbo.Specification.RiskDefinitionItemElementId=dbo.ElementCache.ElementId
*/
CREATE PROCEDURE BPStaging.LoadSpecification
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.Specification';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.Specification
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE INTO dbo.Specification T
    USING (
        SELECT
            spec.Id
          , PlacementSystemId = spec.PlacementId
          , pl.PlacementId
          , spec.ProductId
          , spec.Label
          , spec.SubProductId
          , RiskDefinitionItemElementId = elem.ElementId
        FROM
            BPStaging.Specification spec
            LEFT JOIN dbo.Placement pl
                ON pl.PlacementSystemId = spec.PlacementId
                   AND pl.DataSourceInstanceId = 50366

            LEFT JOIN dbo.ElementCache elem
                ON elem.ElementId = spec.RiskDefinitionItemElementId
    ) S
    ON S.Id = T.SpecificationId
    WHEN NOT MATCHED
        THEN INSERT (
                 SpecificationId
               , PlacementSystemId
               , PlacementId
               , ProductId
               , Label
               , IsDeleted
               , SubProductId
               , RiskDefinitionItemElementId
               , LastUpdatedUTCDate
             )
             VALUES
                 (
                     S.Id
                   , S.PlacementSystemId
                   , S.PlacementId
                   , S.ProductId
                   , S.Label
                   , 0
                   , S.SubProductId
                   , S.RiskDefinitionItemElementId
                   , GETUTCDATE()
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.PlacementSystemId
      , T.PlacementId
      , T.ProductId
      , T.Label
      , T.IsDeleted
      , T.SubProductId
      , T.RiskDefinitionItemElementId
    INTERSECT
    SELECT
        S.PlacementSystemId
      , S.PlacementId
      , S.ProductId
      , S.Label
      , 0
      , S.SubProductId
      , S.RiskDefinitionItemElementId
)
        THEN UPDATE SET
                 T.PlacementSystemId = S.PlacementSystemId
               , T.PlacementId = S.PlacementId
               , T.ProductId = S.ProductId
               , T.Label = S.Label
               , T.IsDeleted = 0
               , T.SubProductId = S.SubProductId
               , T.RiskDefinitionItemElementId = S.RiskDefinitionItemElementId
               , T.LastUpdatedUTCDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
