/*
Lineage
PS.ContractStatusHistory.ContractId=PS.Contract.ContractId
PS.ContractStatusHistory.SourceUpdatedDate=BPStaging.Contract.ValidFrom
PS.ContractStatusHistory.ContractStatusId=ref.ContractStatus.ContractStatusId
*/
CREATE PROCEDURE BPStaging.Load_PS_ContractStatusHistory
AS
BEGIN
    DECLARE @InsertedCount INT = 0;
    DECLARE @UpdatedCount INT = 0;
    DECLARE @DeletedCount INT = 0;
    DECLARE @RejectedCount INT = 0;
    DECLARE @TargetTable VARCHAR(50) = 'PS.ContractStatusHistory';
    DECLARE @SprocName VARCHAR(255);
    DECLARE @Action NVARCHAR(255);

    SET NOCOUNT ON;

    SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

    EXEC ADF.StoredProcStartLog @SprocName;

    BEGIN TRY
        INSERT INTO
            PS.ContractStatusHistory
            (
                ContractId
              , ContractStatusId
              , SourceUpdatedDate
              , DataSourceInstanceId
              , ETLCreatedDate
              , ETLUpdatedDate
            )
        SELECT
            c.ContractId
          , c.ContractStatusId
          , c.SourceUpdatedDate
          , DataSourceInstanceId = 50366
          , ETLCreatedDate = GETUTCDATE()
          , ETLUpdatedDate = GETUTCDATE()
        FROM (
            SELECT
                b.ContractId
              , b.SourceUpdatedDate
              , b.ContractStatusId
              , b.FromCurrent
            FROM (
                SELECT
                    a.ContractId
                  , a.SourceUpdatedDate
                  , a.ContractStatusId
                  , a.FromCurrent /* The order on the PARTITION to make these win over staged versions as current don't need inserting. */
                  , PrevContractStatusId = ISNULL(
                                               LEAD(a.ContractStatusId) OVER (PARTITION BY a.ContractId ORDER BY a.SourceUpdatedDate DESC, a.FromCurrent ASC)
                                             , -1
                                           )
                FROM (
                    SELECT
                        ContractId = con.ContractId
                      , SourceUpdatedDate = sc.ValidFrom
                      , cs.ContractStatusId
                      , FromCurrent = 0
                    FROM
                        BPStaging.Contract sc
                        INNER JOIN PS.Contract con
                            ON con.ContractId = sc.Id
                               AND con.DataSourceInstanceId = 50366

                        LEFT JOIN ref.ContractStatus cs
                            ON cs.DataSourceInstanceId = 50366
                               AND cs.ContractStatusKey = CAST(sc.ContractStatusId AS NVARCHAR(50))
                    UNION ALL
                    SELECT
                        ContractId
                      , SourceUpdatedDate
                      , ContractStatusId
                      , FromCurrent = 1
                    FROM
                        PS.ContractStatusHistory
                ) a
            ) b
            WHERE
                b.PrevContractStatusId <> b.ContractStatusId /* To get rid of adjacent statuses. */
        ) c
        WHERE
            c.FromCurrent = 0 /* If there is a current then it is there and can't be stored again. */
        ORDER BY
            c.SourceUpdatedDate ASC;

        SELECT @InsertedCount = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;

    SET @Action = CONCAT(N'Load ', @TargetTable, N'.');

    EXEC ADF.StoredProcSetSqlLog
        @SprocName
      , @InsertedCount
      , @UpdatedCount
      , @DeletedCount
      , @RejectedCount
      , @Action
      , NULL;

    EXEC ADF.StoredProcEndLog @SprocName;

    SELECT
        InsertedCount = ISNULL(@InsertedCount, 0)
      , UpdatedCount = ISNULL(@UpdatedCount, 0)
      , DeletedCount = ISNULL(@DeletedCount, 0)
      , RejectedCount = ISNULL(@RejectedCount, 0);

    RETURN 0;
END;