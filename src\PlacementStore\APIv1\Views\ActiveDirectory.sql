/*
Lineage
ActiveDirectoryId=Reference.ActiveDirectory.ActiveDirectoryId
AccountName=Reference.ActiveDirectory.AccountName
ObjectGUID=Reference.ActiveDirectory.ObjectGUID
UserPrincipalName=Reference.ActiveDirectory.UserPrincipalName
ObjectSID=Reference.ActiveDirectory.ObjectSID
AzureSynchronizedFlag=Reference.ActiveDirectory.AzureSynchronizedFlag
CanonicalName=Reference.ActiveDirectory.CanonicalName
DistinguishedName=Reference.ActiveDirectory.DistinguishedName
CreatedDate=Reference.ActiveDirectory.CreatedDate
ModifiedDate=Reference.ActiveDirectory.ModifiedDate
EmailAddress=Reference.ActiveDirectory.EmailAddress
LastName=Reference.ActiveDirectory.LastName
GivenName=Reference.ActiveDirectory.GivenName
Domain=Reference.ActiveDirectory.Domain
Initials=Reference.ActiveDirectory.Initials
DisplayName=Reference.ActiveDirectory.DisplayName
OfficePhone=Reference.ActiveDirectory.OfficePhone
CommonName=Reference.ActiveDirectory.CommonName
LastLogonDate=Reference.ActiveDirectory.LastLogonDate
AccountExpirationDate=Reference.ActiveDirectory.AccountExpirationDate
Enabled=Reference.ActiveDirectory.Enabled
IsDeleted=Reference.ActiveDirectory.IsDeleted
LastUpdateTime=Reference.ActiveDirectory.LastUpdateTime
LastUpdateBy=Reference.ActiveDirectory.LastUpdateBy
ETLUpdatedDate=Reference.ActiveDirectory.ETLUpdatedDate
*/
CREATE VIEW APIv1.ActiveDirectory
AS
SELECT
    ActiveDirectoryId
  , AccountName
  , ObjectGUID
  , UserPrincipalName
  , ObjectSID
  , AzureSynchronizedFlag
  , CanonicalName
  , DistinguishedName
  , CreatedDate
  , ModifiedDate
  , EmailAddress
  , LastName
  , GivenName
  , Domain
  , Initials
  , DisplayName
  , OfficePhone
  , CommonName
  , LastLogonDate
  , AccountExpirationDate
  , Enabled
  , IsDeleted
  , LastUpdateTime
  , LastUpdateBy
  , ETLUpdatedDate
FROM (
    SELECT
        RowNumber = ROW_NUMBER() OVER (PARTITION BY UserPrincipalName
                                       ORDER BY
                                           IsDeleted ASC --Proritise Active Records
                                         , Enabled DESC --Proritise Enabled Records
                                         , AzureSynchronizedFlag DESC --Proritise Azure Synchronized Records
                                 )
      , ActiveDirectoryId
      , AccountName
      , ObjectGUID
      , UserPrincipalName
      , ObjectSID
      , AzureSynchronizedFlag
      , CanonicalName
      , DistinguishedName
      , CreatedDate
      , ModifiedDate
      , EmailAddress
      , LastName
      , GivenName
      , Domain
      , Initials
      , DisplayName
      , OfficePhone
      , CommonName
      , LastLogonDate
      , AccountExpirationDate
      , Enabled
      , IsDeleted
      , LastUpdateTime
      , LastUpdateBy
      , ETLUpdatedDate
    FROM
        Reference.ActiveDirectory
    WHERE
        (
        DistinguishedName LIKE '%OU=ACCOUNTS%'
        OR DistinguishedName LIKE '%OU=USER%'
    )
        AND (
            DistinguishedName NOT LIKE '%CN=%room%'
            OR EmailAddress IS NOT NULL
        )
        AND (
            DistinguishedName NOT LIKE '%PRINTER%'
            OR EmailAddress IS NOT NULL
        )
        AND UserPrincipalName NOT LIKE 'SVC%'
        AND UserPrincipalName NOT LIKE 'DUMMY%'
) A
WHERE
    RowNumber = 1;
