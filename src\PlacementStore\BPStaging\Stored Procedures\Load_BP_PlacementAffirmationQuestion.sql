/*
Lineage
BP.PlacementAffirmationQuestion.Id=BPStaging.PlacementAffirmationQuestion.Id
BP.PlacementAffirmationQuestion.PlacementId=BPStaging.PlacementAffirmationQuestion.PlacementId
BP.PlacementAffirmationQuestion.AffirmationQuestionId=BPStaging.PlacementAffirmationQuestion.AffirmationQuestionId
BP.PlacementAffirmationQuestion.Answer=BPStaging.PlacementAffirmationQuestion.Answer
BP.PlacementAffirmationQuestion.AnsweredByUserId=BPStaging.PlacementAffirmationQuestion.AnsweredByUserId
BP.PlacementAffirmationQuestion.SourceUpdatedDate=BPStaging.PlacementAffirmationQuestion.ValidTo
BP.PlacementAffirmationQuestion.SourceUpdatedDate=BPStaging.PlacementAffirmationQuestion.ValidFrom
BP.PlacementAffirmationQuestion.IsDeleted=BPStaging.PlacementAffirmationQuestion.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_PlacementAffirmationQuestion
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.PlacementAffirmationQuestion';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.PlacementAffirmationQuestion)
BEGIN
    BEGIN TRY
        MERGE BP.PlacementAffirmationQuestion t
        USING (
            SELECT
                inner_select.Id
              , inner_select.PlacementId
              , inner_select.AffirmationQuestionId
              , inner_select.Answer
              , inner_select.AnsweredByUserId
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                     THEN 1
                                 ELSE 0 END
            FROM (
            SELECT
                Id
              , PlacementId
              , AffirmationQuestionId
              , Answer
              , AnsweredByUserId
              , ValidFrom
              , ValidTo
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.PlacementAffirmationQuestion
        ) inner_select
            WHERE
                inner_select.RowNo = 1
        ) s
        ON t.Id = s.Id
        WHEN NOT MATCHED
            THEN INSERT (
                     Id
                   , PlacementId
                   , AffirmationQuestionId
                   , Answer
                   , AnsweredByUserId
                   , SourceUpdatedDate
                   , IsDeleted
                 )
                 VALUES
                     (
                         s.Id
                       , s.PlacementId
                       , s.AffirmationQuestionId
                       , s.Answer
                       , s.AnsweredByUserId
                       , s.SourceUpdatedDate
                       , s.IsDeleted
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        t.Id
      , t.PlacementId
      , t.AffirmationQuestionId
      , t.Answer
      , t.AnsweredByUserId
      , t.SourceUpdatedDate
      , t.IsDeleted
    INTERSECT
    SELECT
        s.Id
      , s.PlacementId
      , s.AffirmationQuestionId
      , s.Answer
      , s.AnsweredByUserId
      , s.SourceUpdatedDate
      , s.IsDeleted
)
            THEN UPDATE SET
                     t.PlacementId = s.PlacementId
                   , t.AffirmationQuestionId = s.AffirmationQuestionId
                   , t.Answer = s.Answer
                   , t.AnsweredByUserId = s.AnsweredByUserId
                   , t.SourceUpdatedDate = s.SourceUpdatedDate
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.IsDeleted = s.IsDeleted
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);