/*
Lineage
PlacementID=dbo.MergedPlacements.MergedPlacementId
PlacementID=APIv1.Placements.PlacementId
PolicyId=dbo.Policy.PolicyId
LastUpdatedUTCDate=dbo.Policy.ETLUpdatedDate
LastUpdatedUTCDate=APIv1.Placements.LastUpdatedUTCDate
LastUpdatedUTCDate=APIv1.Placements.CreatedUTCDate
RenewalProcessStartDate=APIv1.Placements.RenewalProcessStartDate
PlacementDataSourceInstanceID=APIv1.Placements.PlacementDataSourceInstanceID
ExpiryDate=dbo.Policy.ExpiryDate
*/

CREATE VIEW APIv1.PlacementPolicies
AS

SELECT DISTINCT
       PlacementID = ISNULL(mp.MergedPlacementId, pp.PlacementId)
     , pp.PolicyId
     , pp.LastUpdatedUTCDate
     , pp.RenewalProcessStartDate
     , pp.Association
     , PlacementDataSourceInstanceID = pp.PlacementDataSourceInstanceId
     , pp.ExpiryDate
FROM (
    /*PlacementPolicies*/
    SELECT
        plac.PlacementId
      , Pol.PolicyId
      , Pol.ExpiryDate
      , plac.PlacementDataSourceInstanceID
      , plac.RenewalProcessStartDate
      , LastUpdatedUTCDate = (
            SELECT MAX(value.v)
            FROM (
                VALUES (
                    Pol.ETLUpdatedDate
                )
                     , (
                    plac.LastUpdatedUTCDate
                ) --This considers the PlacementListener LastUpdatedUTCDate
                     , (
                    plac.CreatedUTCDate
                )
            ) value (v)
        )
      , Pol.Association
    FROM
        APIv1.Placements plac
        INNER JOIN (

            /*Policies*/
            SELECT
                ppcur.PlacementId
              , p.PolicyId
              , p.ExpiryDate
              , p.ETLUpdatedDate
              , p.DataSourceInstanceId
              , Association = CAST('Expiring' AS NVARCHAR(10))
            FROM
                dbo.Policy p
                INNER JOIN dbo.PlacementPolicy ppcur /* All Placements with records in here get renewed to BP so no DataSourceInstance check required */
                    ON ppcur.PolicyId = p.PolicyId
                       AND ppcur.IsDeleted = 0
                       AND ppcur.PlacementPolicyRelationshipTypeId = 1

                INNER JOIN PAS.RefPolicyStatus rst
                    ON p.RefPolicyStatusId = rst.RefPolicyStatusId
            WHERE
                p.IsDeleted = 0
                AND p.RuleId > 0
                AND p.SuppressPlacementLink = 0
                AND rst.RefPolicyStatus IN (
                        'Placement', 'Live', 'Lapsed', 'Cancelled'
                    )
            UNION ALL

            /*RenewedToPolicies*/
            SELECT
                ppcur.PlacementId
              , rtp.PolicyId
              , rtp.ExpiryDate
              , rtp.ETLUpdatedDate
              , rtp.DataSourceInstanceId
              , Association = CAST('Current' AS NVARCHAR(10))
            FROM
                dbo.Policy rtp
                INNER JOIN dbo.PlacementPolicy PPRTP /* All Placements with records in here get renewed to BP so no DataSourceInstance check required */
                    ON PPRTP.PolicyId = rtp.PolicyId
                       AND PPRTP.IsDeleted = 0
                       AND PPRTP.PlacementPolicyRelationshipTypeId = 1
                       AND PPRTP.DataSourceInstanceId <> '50351' --excluding CMS

                INNER JOIN dbo.Policy p
                    ON p.PolicyId = rtp.RenewedFromPolicyId
                       AND p.IsDeleted = 0

                INNER JOIN PAS.RefPolicyStatus rst
                    ON rtp.RefPolicyStatusId = rst.RefPolicyStatusId
                       AND rst.RefPolicyStatus IN (
                               'Placement', 'Live', 'Not Taken Up (NTU)', 'Lapsed', 'Cancelled'
                           )
                       AND rst.IsDeleted = 0

                INNER JOIN dbo.PlacementPolicy ppcur /* All Placements with records in here get renewed to BP so no DataSourceInstance check required */
                    ON ppcur.PolicyId = p.PolicyId
                       AND ppcur.IsDeleted = 0
                       AND ppcur.PlacementPolicyRelationshipTypeId = 1
                --join to rules and teams for old and new policy

                INNER JOIN PactConfig.[Rule] r1
                    ON p.RuleId = r1.RuleId
                       AND r1.IsDeleted = 0

                INNER JOIN PactConfig.[Rule] r2
                    ON rtp.RuleId = r2.RuleId
                       AND r2.IsDeleted = 0
            WHERE
                rtp.IsDeleted = 0
                AND rtp.RuleId > 0
                AND (
                    rtp.RuleId = p.RuleId
                    OR r1.DataSourceInstanceId = r2.DataSourceInstanceId
                ) --same rule or new rule same country
                AND PPRTP.PlacementId <> ppcur.PlacementId --exclude where parent and child mapped to same placement
                AND rtp.SuppressPlacementLink = 0
            UNION ALL --added this section for non-renewal LINKED policies

            /*RenewedToPlacements*/
            SELECT
                pl.RenewedFromPlacementId
              , rtp.PolicyId
              , rtp.ExpiryDate
              , rtp.ETLUpdatedDate
              , rtp.DataSourceInstanceId
              , Association = CAST('Current' AS NVARCHAR(10))
            FROM
                dbo.Policy rtp
                INNER JOIN dbo.PlacementPolicy pprtp /* All Placements with records in here get renewed to BP so no DataSourceInstance check required */
                    ON pprtp.PolicyId = rtp.PolicyId
                       AND pprtp.IsDeleted = 0
                       AND pprtp.PlacementPolicyRelationshipTypeId = 1

                INNER JOIN dbo.Placement pl
                    ON pprtp.PlacementId = pl.PlacementId
                       AND pl.IsDeleted = 0

                INNER JOIN PAS.RefPolicyStatus rst
                    ON rtp.RefPolicyStatusId = rst.RefPolicyStatusId
                       AND rst.RefPolicyStatus IN (
                               'Placement', 'Live', 'Not Taken Up (NTU)', 'Lapsed', 'Cancelled'
                           )
                       AND rst.IsDeleted = 0
            WHERE
                rtp.IsDeleted = 0
                AND rtp.RuleId > 0
                AND rtp.RenewedFromPolicyId IS NULL
                AND rtp.SuppressPlacementLink = 0
                AND pl.RenewedFromPlacementId IS NOT NULL
        ) Pol
            ON Pol.PlacementId = plac.PlacementId
    WHERE
        plac.SuppressPolicyLink = 0
) pp
     LEFT JOIN ref.PlacementStatus ps
         ON ps.PlacementStatusKey = '6'
            AND ps.DataSourceInstanceId = 50366

     LEFT JOIN dbo.MergedPlacements mp
         ON pp.PlacementId = mp.OriginalPlacementId
            AND (
                ISNULL(mp.NewPlacementStatusId, 0) <> ps.PlacementStatusId
                OR ISNULL(mp.NewPlacementCancellationReasonId, 0) <> 6
            ); --> Don't consider unmerged cases
