/*
Lineage
PS.MarketResponse.MarketResponseKey=BPStaging.MarketResponse.Id
PS.MarketResponse.MarketResponseKey=BPStaging.CarrierResponse.Id
PS.MarketResponse.MarketResponseKey=BP.ExpiringResponseGroup.Id
PS.MarketResponse.MarketResponseKey=BP.ExpiringResponseElement.Id
PS.MarketResponse.MarketResponseKey=BP.AdjustmentResponseGroup.Id
PS.MarketResponse.MarketResponseKey=BP.AdjustmentResponseElement.Id
PS.MarketResponse.NegotiationMarketId=PS.NegotiationMarket.NegotiationMarketId
PS.MarketResponse.ParentId=BPStaging.MarketResponse.ParentId
PS.MarketResponse.ParentKey=BPStaging.MarketResponse.ParentId
PS.MarketResponse.ResponseDate=BPStaging.MarketResponse.ResponseDate
PS.MarketResponse.ResponseDate=BPStaging.CarrierResponse.ResponseDate
PS.MarketResponse.UnderwriterName=BPStaging.MarketResponse.Underwriter
PS.MarketResponse.UnderwriterName=BPStaging.CarrierResponse.Underwriter
PS.MarketResponse.UnderwriterEmail=BPStaging.MarketResponse.UnderwriterEmail
PS.MarketResponse.ResponseTypeId=BPStaging.MarketResponse.ResponseTypeId
PS.MarketResponse.ResponseTypeId=BPStaging.CarrierResponse.ResponseTypeId
PS.MarketResponse.ResponseTypeId=dbo.ElementAttributeCache.Value
PS.MarketResponse.DeclinationReasonId=BPStaging.MarketResponse.DeclinationReasonId
PS.MarketResponse.DeclinationReasonId=BPStaging.CarrierResponse.DeclinationReasonId
PS.MarketResponse.LayerTypeId=BPStaging.MarketResponse.LayerTypeId
PS.MarketResponse.LayerTypeId=BPStaging.CarrierResponse.LayerTypeId
PS.MarketResponse.LayerTypeId=dbo.ElementAttributeCache.Value
PS.MarketResponse.ProgramStructureType=BPStaging.MarketResponse.ProgramStructureType
PS.MarketResponse.ProgramStructureType=ref.ProgramStructureType.ProgramStructureType
PS.MarketResponse.PremiumCurrency=Reference.Currency.CurrencyAlphaCode
PS.MarketResponse.CommissionCurrency=Reference.Currency.CurrencyAlphaCode
PS.MarketResponse.LimitCurrencyId=BPStaging.MarketResponse.LimitCurrencyTypeId
PS.MarketResponse.LimitCurrencyId=BPStaging.CarrierResponse.LimitCurrencyTypeId
PS.MarketResponse.LimitCurrency=Reference.Currency.CurrencyAlphaCode
PS.MarketResponse.Limit=BPStaging.MarketResponse.Limit
PS.MarketResponse.Limit=BPStaging.CarrierResponse.Limit
PS.MarketResponse.AttachmentPointCurrencyId=BPStaging.MarketResponse.AttachmentPointCurrencyTypeId
PS.MarketResponse.AttachmentPointCurrencyId=BPStaging.CarrierResponse.AttachmentPointCurrencyTypeId
PS.MarketResponse.AttachmentPointCurrencyId=dbo.ElementAttributeCache.Value
PS.MarketResponse.AttachmentPointCurrency=Reference.Currency.CurrencyAlphaCode
PS.MarketResponse.AttachmentPoint=BPStaging.MarketResponse.AttachmentPoint
PS.MarketResponse.AttachmentPoint=BPStaging.CarrierResponse.AttachmentPoint
PS.MarketResponse.AttachmentPoint=dbo.ElementAttributeCache.Value
PS.MarketResponse.Comments=BPStaging.MarketResponse.Comments
PS.MarketResponse.Comments=BPStaging.CarrierResponse.Comments
PS.MarketResponse.Comments=dbo.ElementAttributeCache.Value
PS.MarketResponse.PendingActionReasonId=BPStaging.MarketResponse.PendingActionReasonId
PS.MarketResponse.PendingActionReasonId=BPStaging.CarrierResponse.PendingActionReasonId
PS.MarketResponse.AdditionalPolicyCostCurrency=Reference.Currency.CurrencyAlphaCode
PS.MarketResponse.DeductibleCurrency=Reference.Currency.CurrencyAlphaCode
PS.MarketResponse.SourceUpdatedDate=BPStaging.CarrierResponse.ValidFrom
PS.MarketResponse.SourceUpdatedDate=BP.ExpiringResponseGroup.ETLUpdatedDate
PS.MarketResponse.SourceUpdatedDate=BP.AdjustmentResponseGroup.ETLUpdatedDate
PS.MarketResponse.IsDeleted=BPStaging.CarrierResponse.IsDeleted
PS.MarketResponse.ResponseCreatedDate=BPStaging.MarketResponse.ResponseCreatedDate
PS.MarketResponse.ResponseCreatedDate=BPStaging.MarketResponse.ResponseDate
PS.MarketResponse.ResponseCreatedDate=BPStaging.CarrierResponse.ResponseDate
PS.MarketResponse.FacilitySectionId=ref.FacilitySection.FacilitySectionId
PS.MarketResponse.IsInvalid=BPStaging.MarketResponse.IsInvalid
PS.MarketResponse.IsInvalid=BP.ExpiringResponseElement.IsInvalid
PS.MarketResponse.IsInvalid=BP.AdjustmentResponseElement.IsInvalid
*/

CREATE PROCEDURE BPStaging.Load_PS_MarketResponse
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketResponse';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @BPDataSourceInstanceId INT = 50366;

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #DistinctMarketResponse;

    /* As MarketResponse also includes Basis component */
    SELECT DISTINCT
           Id
         , ParentId
         , ParentKey = 'MKTRES|' + CAST(ParentId AS VARCHAR(50))
         , SubmissionContainerMarketId = SubmissionContainerMarket
         , ResponseDate
         , Underwriter
         , UnderwriterEmail
         , ResponseTypeId
         , QuotedToLead
         , OutcomeStatusId
         , OutcomeReasonId
         , DeclinationReasonId
         , LayerTypeId
         , ProgramStructureType
         , PremiumCurrencyId
         , Premium
         , PremiumRate
         , NetPremium
         , OfferedLine
         , OfferedLineRate
         , CommissionCurrencyId
         , Commission
         , CommissionRate
         , LimitCurrencyTypeId
         , Limit
         , AttachmentPointCurrencyTypeId
         , AttachmentPoint
         , Comments
         , Subjectivity
         , SignedLine
         , SignedLineRate
         , IsPremiumRatePerMille
         , PendingActionReasonId
         , AdditionalPolicyCostCurrencyId
         , AdditionalPolicyCost
         , FrontingCarrierId
         , QuoteExpiryDate
         , BindRequestedDate
         , BoundDate
         , Deductible
         , DeductibleCurrencyId
         , IsInvalid
         , ResponseCreatedDate = COALESCE(ResponseCreatedDate, ResponseDate)
         , FacilitySectionId
    INTO #DistinctMarketResponse
    FROM
        BPStaging.MarketResponse;

    DROP TABLE IF EXISTS #RankedCarrierResponse;

    /* If there is more than 1 possible NegotiationMarket record we only want to link to the most appropriate */
    SELECT
        CR.Id
      , NM.NegotiationMarketKey
      , PL.PlacementId
      , CR.MarketKindId
      , CR.CarrierId
      , CR.FacilityId
      , CR.ResponseDate
      , UnderwriterName = CR.Underwriter
      , CR.ResponseTypeId
      , CR.QuotedToLead
      , CR.OutcomeStatusId
      , CR.OutcomeReasonId
      , CR.DeclinationReasonId
      , CR.LayerTypeId
      , CR.PremiumCurrencyId
      , CR.Premium
      , CR.PremiumRate
      , CR.OfferedLine
      , CR.OfferedLineRate
      , CR.CommissionRate
      , LimitCurrencyId = CR.LimitCurrencyTypeId
      , CR.Limit
      , AttachmentPointCurrencyId = CR.AttachmentPointCurrencyTypeId
      , CR.AttachmentPoint
      , CR.Comments
      , CR.Subjectivity
      , CR.PendingActionReasonId
      , SourceUpdatedDate = CR.ValidFrom
      , CR.IsDeleted
      , ResponseCreatedDate = CR.ResponseDate
      /* Prefer the MarketSelection intiated record where we have both this and a CarrierResponse based record */
      , ROW_NO = ROW_NUMBER() OVER (PARTITION BY CR.Id
                                    ORDER BY
                                        CASE WHEN NM.NegotiationMarketKey LIKE 'MKTSEL|%'
                                                 THEN 1
                                             WHEN NM.NegotiationMarketKey LIKE 'CARRES|%'
                                                 THEN 2
                                             ELSE 999 END ASC
                              )
    INTO #RankedCarrierResponse
    FROM
        BPStaging.CarrierResponse CR
        INNER JOIN dbo.Placement PL
            ON PL.PlacementSystemId = CR.PlacementId
               AND PL.DataSourceInstanceId = @BPDataSourceInstanceId

        INNER JOIN PS.NegotiationMarket NM
            ON NM.PlacementId = PL.PlacementId
               AND NM.MarketKindId = CR.MarketKindId
               AND ISNULL(NM.SourceCarrierId, 0) = ISNULL(CR.CarrierId, 0)
               AND ISNULL(NM.FacilityId, 0) = ISNULL(CR.FacilityId, 0)
               AND (
                   NM.NegotiationMarketKey LIKE 'MKTSEL|%'
                   OR NM.NegotiationMarketKey LIKE 'CARRES|%'
               )

        LEFT JOIN Reference.Currency PREMC
            ON PREMC.CurrencyId = CR.PremiumCurrencyId

        LEFT JOIN Reference.Currency LIMC
            ON LIMC.CurrencyId = CR.LimitCurrencyTypeId

        LEFT JOIN Reference.Currency APTC
            ON APTC.CurrencyId = CR.AttachmentPointCurrencyTypeId;

    /* Expiring Response attributes */
    DROP TABLE IF EXISTS #ExpiringResponseAttribute;

    SELECT
        P.ExpiringResponseGroupId
      , P.ExpiringResponseElementId
      , QuoteExpiryDate = P.[Quote Expiry Date]
      , ProgramStructureType = pst.ProgramStructureType
      , LayerTypeId = P.[Layer Type]
      , ResponseTypeId = 1 -- Has to have been offered
      , P.IsInvalid
      , PremiumCurrencyId = P.[100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) currency]
      , Premium = P.[100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) amount]
      , OfferedLineRate = P.[Offered Line % percentage]
      , CommissionCurrencyId = P.[Commission currency]
      , Commission = P.[Commission amount]
      , CommissionRate = P.[Commission percentage]
      , AdditionalPolicyCost = P.[Additional Policy Cost amount]
      , AdditionalPolicyCostCurrencyId = P.[Additional Policy Cost currency]
      , Comments = P.Comments
      , AttachmentPoint = P.[Layer Attachment Point(s) amount]
      , AttachmentPointCurrencyId = P.[Layer Attachment Point(s) currency]
    INTO #ExpiringResponseAttribute
    FROM (
        SELECT
            ExpiringResponseGroupId = erg.Id
          , ExpiringResponseElementId = ere.Id
          , ere.IsInvalid
          , ElementType = CASE WHEN LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', '')) IN (
                                   'currency', 'percentage', 'amount'
                               )
                                   THEN et.ElementType + ' '
                                        + LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', ''))
                               ELSE et.ElementType END
          , eattr.Value
        FROM
            BP.ExpiringResponseGroup erg
            INNER JOIN BP.ExpiringResponseElement ere
                ON erg.Id = ere.ExpiringResponseGroupId

            INNER JOIN dbo.ElementBranch eb
                ON eb.ElementBranchId = ere.ElementBranchId
            --      AND eb.ClonedFromElementBranchId IS NULL --> Include only they haven't been cloned from last years placement.

            INNER JOIN BP.ResponseManagementElement rme
                ON rme.Id = ere.ResponseManagementElementId

            INNER JOIN dbo.ElementCache ec
                ON ec.RootElementId = rme.ElementId
                   AND ec.ElementBranchId = ere.ElementBranchId

            INNER JOIN ref.ElementType et
                ON et.ElementTypeId = ec.ElementTypeId

            INNER JOIN dbo.ElementDelta ed
                ON ed.ElementDeltaId = ec.ElementDeltaId

            LEFT JOIN dbo.ElementCache ecparent
                ON ecparent.ElementId = ec.ParentElementId
                   AND ecparent.ElementBranchId = ec.ElementBranchId

            LEFT JOIN dbo.ElementAttributeCache eattr
                ON eattr.ElementId = ec.ElementId
                   AND eattr.ElementBranchId = ec.ElementBranchId

            LEFT JOIN dbo.ElementAttributeType eatt
                ON eatt.ElementAttributeTypeId = eattr.ElementAttributeTypeId
        WHERE
            eatt.ElementAttributeType <> 'includeinhighlevelcapture'
            AND et.ElementType IN (
                    'Response Type', 'Layer Type', 'Structure', 'Quote Expiry Date'
                  , '100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments)'
                  , 'Offered Line %', 'Commission', 'Additional Policy Cost', 'Comments', 'Layer Attachment Point(s)'
                )
    ) n
    PIVOT (
        MAX(Value)
        FOR ElementType IN (
            [Layer Type], Structure, [Quote Expiry Date]
          , [100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) amount]
          , [100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) currency]
          , [Offered Line % percentage], [Commission amount], [Commission currency], [Commission percentage]
          , [Additional Policy Cost amount], [Additional Policy Cost currency], Comments
          , [Layer Attachment Point(s) amount], [Layer Attachment Point(s) currency]
        )
    ) P
         LEFT JOIN ref.ProgramStructureType pst
             ON pst.DataSourceInstanceId = @BPDataSourceInstanceId
                AND pst.ProgramStructureTypeKey = P.Structure;

    /* Adjustment Response attributes */
    DROP TABLE IF EXISTS #AdjustmentResponseAttribute;

    SELECT
        P.AdjustmentResponseGroupId
      , P.AdjustmentResponseElementId
      , QuoteExpiryDate = P.[Quote Expiry Date]
      , ProgramStructureType = pst.ProgramStructureType
      , LayerTypeId = P.[Layer Type]
      , ResponseTypeId = COALESCE(TRY_CAST(P.[Response Type] AS INT), 1) -- If there is an adjustment record assume that it has been offered if there is no response type
      , P.IsInvalid
      , PremiumCurrencyId = P.[100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) currency]
      , Premium = P.[100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) amount]
      , OfferedLineRate = P.[Offered Line % percentage]
      , CommissionCurrencyId = P.[Commission currency]
      , Commission = P.[Commission amount]
      , CommissionRate = P.[Commission percentage]
      , AdditionalPolicyCost = P.[Additional Policy Cost amount]
      , AdditionalPolicyCostCurrencyId = P.[Additional Policy Cost currency]
      , Comments = P.Comments
      , AttachmentPoint = P.[Layer Attachment Point(s) amount]
      , AttachmentPointCurrencyId = P.[Layer Attachment Point(s) currency]
    INTO #AdjustmentResponseAttribute
    FROM (
        SELECT
            AdjustmentResponseGroupId = arg.Id
          , AdjustmentResponseElementId = are.Id
          , are.IsInvalid
          , ElementType = CASE WHEN LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', '')) IN (
                                   'currency', 'percentage', 'amount'
                               )
                                   THEN et.ElementType + ' '
                                        + LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', ''))
                               ELSE et.ElementType END
          , eattr.Value
        FROM
            BP.AdjustmentResponseGroup arg
            INNER JOIN BP.AdjustmentResponseElement are
                ON arg.Id = are.AdjustmentResponseGroupId

            INNER JOIN dbo.ElementBranch eb
                ON eb.ElementBranchId = are.ElementBranchId

            INNER JOIN BP.ResponseManagementElement rme
                ON rme.Id = are.ResponseManagementElementId

            INNER JOIN dbo.ElementCache ec
                ON ec.RootElementId = rme.ElementId
                   AND ec.ElementBranchId = are.ElementBranchId

            INNER JOIN ref.ElementType et
                ON et.ElementTypeId = ec.ElementTypeId

            INNER JOIN dbo.ElementDelta ed
                ON ed.ElementDeltaId = ec.ElementDeltaId

            LEFT JOIN dbo.ElementCache ecparent
                ON ecparent.ElementId = ec.ParentElementId
                   AND ecparent.ElementBranchId = ec.ElementBranchId

            LEFT JOIN dbo.ElementAttributeCache eattr
                ON eattr.ElementId = ec.ElementId
                   AND eattr.ElementBranchId = ec.ElementBranchId

            LEFT JOIN dbo.ElementAttributeType eatt
                ON eatt.ElementAttributeTypeId = eattr.ElementAttributeTypeId
        WHERE
            eatt.ElementAttributeType <> 'includeinhighlevelcapture'
            AND et.ElementType IN (
                    'Response Type', 'Layer Type', 'Structure', 'Quote Expiry Date'
                  , '100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments)'
                  , 'Offered Line %', 'Commission', 'Additional Policy Cost', 'Comments', 'Layer Attachment Point(s)'
                )
    ) n
    PIVOT (
        MAX(Value)
        FOR ElementType IN (
            [Response Type], [Layer Type], Structure, [Quote Expiry Date]
          , [100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) amount]
          , [100% Layer Premium (Excluding Surplus Lines Tax, and Taxes, Surcharges, & Assessments) currency]
          , [Offered Line % percentage], [Commission amount], [Commission currency], [Commission percentage]
          , [Additional Policy Cost amount], [Additional Policy Cost currency], Comments
          , [Layer Attachment Point(s) amount], [Layer Attachment Point(s) currency]
        )
    ) P
         LEFT JOIN ref.ProgramStructureType pst
             ON pst.DataSourceInstanceId = @BPDataSourceInstanceId
                AND pst.ProgramStructureTypeKey = P.Structure;

    MERGE PS.MarketResponse T
    USING (
        SELECT
            DataSourceInstanceId = @BPDataSourceInstanceId
          , MarketResponseKey = CONCAT('MKTRES|', MR.Id)
          , NM.NegotiationMarketId
          , MR.ParentId
          , MR.ParentKey
          , MR.ResponseDate
          , UnderwriterName = MR.Underwriter
          , MR.UnderwriterEmail
          , MR.ResponseTypeId
          , MR.DeclinationReasonId
          , MR.LayerTypeId
          , MR.ProgramStructureType
          , PremiumCurrency = PREMC.CurrencyAlphaCode
          , CommissionCurrency = COMMC.CurrencyAlphaCode
          , LimitCurrencyId = MR.LimitCurrencyTypeId
          , LimitCurrency = LIMC.CurrencyAlphaCode
          , MR.Limit
          , AttachmentPointCurrencyId = MR.AttachmentPointCurrencyTypeId
          , AttachmentPointCurrency = APTC.CurrencyAlphaCode
          , MR.AttachmentPoint
          , MR.Comments
          , MR.PendingActionReasonId
          , AdditionalPolicyCostCurrency = ADDCC.CurrencyAlphaCode
          , DeductibleCurrency = DEDC.CurrencyAlphaCode
          , SourceUpdatedDate = CAST(NULL AS DATETIME2(7))/* Doesn't have a source date */
          , IsDeleted = 0
          , MR.ResponseCreatedDate
          , FS.FacilitySectionId
          , MR.IsInvalid
        FROM
            #DistinctMarketResponse MR
            INNER JOIN PS.NegotiationMarket NM
                ON NM.NegotiationMarketKey = CONCAT('SUBCONMKT|', MR.SubmissionContainerMarketId)

            LEFT JOIN ref.FacilitySection FS
                ON FS.FacilitySectionKey = CAST(MR.FacilitySectionId AS NVARCHAR(100))

            LEFT JOIN Reference.Currency PREMC
                ON PREMC.CurrencyId = MR.PremiumCurrencyId

            LEFT JOIN Reference.Currency COMMC
                ON COMMC.CurrencyId = MR.CommissionCurrencyId

            LEFT JOIN Reference.Currency LIMC
                ON LIMC.CurrencyId = MR.LimitCurrencyTypeId

            LEFT JOIN Reference.Currency APTC
                ON APTC.CurrencyId = MR.AttachmentPointCurrencyTypeId

            LEFT JOIN Reference.Currency ADDCC
                ON ADDCC.CurrencyId = MR.AdditionalPolicyCostCurrencyId

            LEFT JOIN dbo.Carrier FC /* To Map from BP CarrierId to PS CarrierId */
                ON FC.PSCarrierId = MR.FrontingCarrierId
                   AND FC.CarrierTypeId <> 4 /* IM Carrier causes duplicates but isn't sent to BP */

            LEFT JOIN Reference.Currency DEDC
                ON DEDC.CurrencyId = MR.DeductibleCurrencyId
        UNION ALL
        SELECT
            DataSourceInstanceId = @BPDataSourceInstanceId
          , MarketResponseKey = CONCAT('CARRES|', CR.Id)
          , NM.NegotiationMarketId
          , ParentId = NULL
          , ParentKey = NULL
          , CR.ResponseDate
          , CR.UnderwriterName
          , UnderwriterEmail = NULL
          , CR.ResponseTypeId
          , CR.DeclinationReasonId
          , CR.LayerTypeId
          , ProgramStructureType = NULL
          , PremiumCurrency = PREMC.CurrencyAlphaCode
          , CommissionCurrency = NULL
          , CR.LimitCurrencyId
          , LimitCurrency = LIMC.CurrencyAlphaCode
          , CR.Limit
          , CR.AttachmentPointCurrencyId
          , AttachmentPointCurrency = APTC.CurrencyAlphaCode
          , CR.AttachmentPoint
          , CR.Comments
          , CR.PendingActionReasonId
          , AdditionalPolicyCostCurrency = NULL
          , DeductibleCurrency = NULL
          , CR.SourceUpdatedDate
          , CR.IsDeleted
          , CR.ResponseCreatedDate
          , FacilitySectionId = NULL
          , IsInvalid = CAST(0 AS BIT)
        FROM
            #RankedCarrierResponse CR
            INNER JOIN PS.NegotiationMarket NM
                ON NM.PlacementId = CR.PlacementId
                   AND NM.MarketKindId = CR.MarketKindId
                   AND ISNULL(NM.SourceCarrierId, 0) = ISNULL(CR.CarrierId, 0)
                   AND ISNULL(NM.FacilityId, 0) = ISNULL(CR.FacilityId, 0)
                   AND (
                       NM.NegotiationMarketKey LIKE 'MKTSEL|%'
                       OR NM.NegotiationMarketKey LIKE 'CARRES|%'
                   )
                   AND NM.IsDeleted = 0

            LEFT JOIN Reference.Currency PREMC
                ON PREMC.CurrencyId = CR.PremiumCurrencyId

            LEFT JOIN Reference.Currency LIMC
                ON LIMC.CurrencyId = CR.LimitCurrencyId

            LEFT JOIN Reference.Currency APTC
                ON APTC.CurrencyId = CR.AttachmentPointCurrencyId
        WHERE
            CR.ROW_NO = 1
        UNION ALL
        SELECT
            DataSourceInstanceId = @BPDataSourceInstanceId
          , MarketResponseKey = CONCAT('EXPRESP|', era.ExpiringResponseGroupId, '|', era.ExpiringResponseElementId)
          , NM.NegotiationMarketId
          , ParentId = NULL
          , ParentKey = NULL
          , ResponseDate = NULL
          , UnderwriterName = NULL
          , UnderwriterEmail = NULL
          , era.ResponseTypeId
          , DeclinationReasonId = NULL
          , era.LayerTypeId
          , era.ProgramStructureType
          , PremiumCurrency = PREMC.CurrencyAlphaCode
          , CommissionCurrency = COMMC.CurrencyAlphaCode
          , LimitCurrencyId = NULL
          , LimitCurrency = NULL
          , Limit = NULL
          , AttachmentPointCurrencyId = era.AttachmentPointCurrencyId
          , AttachmentPointCurrency = APTC.CurrencyAlphaCode
          , era.AttachmentPoint
          , era.Comments
          , PendingActionReasonId = NULL
          , AdditionalPolicyCostCurrency = ADDCC.CurrencyAlphaCode
          , DeductibleCurrency = NULL
          , SourceUpdatedDate = erg.ETLUpdatedDate
          , IsDeleted = 0
          , ResponseCreatedDate = NULL
          , FacilitySectionId = NULL
          , era.IsInvalid
        FROM
            #ExpiringResponseAttribute era
            INNER JOIN BP.ExpiringResponseGroup erg
                ON erg.Id = era.ExpiringResponseGroupId

            INNER JOIN PS.NegotiationMarket NM
                ON NM.NegotiationMarketKey = CONCAT('EXPRESPMKT|', era.ExpiringResponseGroupId)

            LEFT JOIN Reference.Currency PREMC
                ON PREMC.CurrencyId = era.PremiumCurrencyId

            LEFT JOIN Reference.Currency COMMC
                ON COMMC.CurrencyId = era.CommissionCurrencyId

            LEFT JOIN Reference.Currency ADDCC
                ON ADDCC.CurrencyId = era.AdditionalPolicyCostCurrencyId

            LEFT JOIN Reference.Currency APTC
                ON APTC.CurrencyId = era.AttachmentPointCurrencyId
        UNION ALL
        SELECT
            DataSourceInstanceId = @BPDataSourceInstanceId
          , MarketResponseKey = CONCAT('ADJRESP|', ara.AdjustmentResponseGroupId, '|', ara.AdjustmentResponseElementId)
          , NM.NegotiationMarketId
          , ParentId = NULL
          , ParentKey = NULL
          , ResponseDate = NULL
          , UnderwriterName = NULL
          , UnderwriterEmail = NULL
          , ara.ResponseTypeId
          , DeclinationReasonId = NULL
          , ara.LayerTypeId
          , ara.ProgramStructureType
          , PremiumCurrency = PREMC.CurrencyAlphaCode
          , CommissionCurrency = COMMC.CurrencyAlphaCode
          , LimitCurrencyId = NULL
          , LimitCurrency = NULL
          , Limit = NULL
          , AttachmentPointCurrencyId = ara.AttachmentPointCurrencyId
          , AttachmentPointCurrency = APTC.CurrencyAlphaCode
          , ara.AttachmentPoint
          , ara.Comments
          , PendingActionReasonId = NULL
          , AdditionalPolicyCostCurrency = ADDCC.CurrencyAlphaCode
          , DeductibleCurrency = NULL
          , SourceUpdatedDate = arg.ETLUpdatedDate
          , IsDeleted = 0
          , ResponseCreatedDate = NULL
          , FacilitySectionId = NULL
          , ara.IsInvalid
        FROM
            #AdjustmentResponseAttribute ara
            INNER JOIN BP.AdjustmentResponseGroup arg
                ON arg.Id = ara.AdjustmentResponseGroupId

            INNER JOIN PS.NegotiationMarket NM
                ON NM.NegotiationMarketKey = CONCAT('ADJRESPMKT|', ara.AdjustmentResponseGroupId)

            LEFT JOIN Reference.Currency PREMC
                ON PREMC.CurrencyId = ara.PremiumCurrencyId

            LEFT JOIN Reference.Currency COMMC
                ON COMMC.CurrencyId = ara.CommissionCurrencyId

            LEFT JOIN Reference.Currency ADDCC
                ON ADDCC.CurrencyId = ara.AdditionalPolicyCostCurrencyId

            LEFT JOIN Reference.Currency APTC
                ON APTC.CurrencyId = ara.AttachmentPointCurrencyId
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.MarketResponseKey = S.MarketResponseKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , MarketResponseKey
               , NegotiationMarketId
               , ParentId
               , ParentKey
               , ResponseDate
               , UnderwriterName
               , UnderwriterEmail
               , ResponseTypeId
               , DeclinationReasonId
               , LayerTypeId
               , ProgramStructureType
               , PremiumCurrency
               , CommissionCurrency
               , LimitCurrencyId
               , LimitCurrency
               , Limit
               , AttachmentPointCurrencyId
               , AttachmentPointCurrency
               , AttachmentPoint
               , Comments
               , PendingActionReasonId
               , AdditionalPolicyCostCurrency
               , DeductibleCurrency
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
               , ResponseCreatedDate
               , FacilitySectionId
               , IsInvalid
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.MarketResponseKey
                   , S.NegotiationMarketId
                   , S.ParentId
                   , S.ParentKey
                   , S.ResponseDate
                   , S.UnderwriterName
                   , S.UnderwriterEmail
                   , S.ResponseTypeId
                   , S.DeclinationReasonId
                   , S.LayerTypeId
                   , S.ProgramStructureType
                   , S.PremiumCurrency
                   , S.CommissionCurrency
                   , S.LimitCurrencyId
                   , S.LimitCurrency
                   , S.Limit
                   , S.AttachmentPointCurrencyId
                   , S.AttachmentPointCurrency
                   , S.AttachmentPoint
                   , S.Comments
                   , S.PendingActionReasonId
                   , S.AdditionalPolicyCostCurrency
                   , S.DeductibleCurrency
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.ResponseCreatedDate
                   , S.FacilitySectionId
                   , S.IsInvalid
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.NegotiationMarketId
                               , S.ParentId
                               , S.ParentKey
                               , S.ResponseDate
                               , S.UnderwriterName
                               , S.UnderwriterEmail
                               , S.ResponseTypeId
                               , S.DeclinationReasonId
                               , S.LayerTypeId
                               , S.ProgramStructureType
                               , S.PremiumCurrency
                               , S.CommissionCurrency
                               , S.LimitCurrencyId
                               , S.LimitCurrency
                               , S.Limit
                               , S.AttachmentPointCurrencyId
                               , S.AttachmentPointCurrency
                               , S.AttachmentPoint
                               , S.Comments
                               , S.PendingActionReasonId
                               , S.AdditionalPolicyCostCurrency
                               , S.DeductibleCurrency
                               , S.IsDeleted
                               , S.ResponseCreatedDate
                               , S.FacilitySectionId
                               , S.IsInvalid
                             INTERSECT
                             SELECT
                                 T.NegotiationMarketId
                               , T.ParentId
                               , T.ParentKey
                               , T.ResponseDate
                               , T.UnderwriterName
                               , T.UnderwriterEmail
                               , T.ResponseTypeId
                               , T.DeclinationReasonId
                               , T.LayerTypeId
                               , T.ProgramStructureType
                               , T.PremiumCurrency
                               , T.CommissionCurrency
                               , T.LimitCurrencyId
                               , T.LimitCurrency
                               , T.Limit
                               , T.AttachmentPointCurrencyId
                               , T.AttachmentPointCurrency
                               , T.AttachmentPoint
                               , T.Comments
                               , T.PendingActionReasonId
                               , T.AdditionalPolicyCostCurrency
                               , T.DeductibleCurrency
                               , T.IsDeleted
                               , T.ResponseCreatedDate
                               , T.FacilitySectionId
                               , T.IsInvalid
                         )
        THEN UPDATE SET
                 T.NegotiationMarketId = S.NegotiationMarketId
               , T.ParentId = S.ParentId
               , T.ParentKey = S.ParentKey
               , T.ResponseDate = S.ResponseDate
               , T.UnderwriterName = S.UnderwriterName
               , T.UnderwriterEmail = S.UnderwriterEmail
               , T.ResponseTypeId = S.ResponseTypeId
               , T.DeclinationReasonId = S.DeclinationReasonId
               , T.LayerTypeId = S.LayerTypeId
               , T.ProgramStructureType = S.ProgramStructureType
               , T.PremiumCurrency = S.PremiumCurrency
               , T.CommissionCurrency = S.CommissionCurrency
               , T.LimitCurrencyId = S.LimitCurrencyId
               , T.LimitCurrency = S.LimitCurrency
               , T.Limit = S.Limit
               , T.AttachmentPointCurrencyId = S.AttachmentPointCurrencyId
               , T.AttachmentPointCurrency = S.AttachmentPointCurrency
               , T.AttachmentPoint = S.AttachmentPoint
               , T.Comments = S.Comments
               , T.PendingActionReasonId = S.PendingActionReasonId
               , T.AdditionalPolicyCostCurrency = S.AdditionalPolicyCostCurrency
               , T.DeductibleCurrency = S.DeductibleCurrency
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ResponseCreatedDate = S.ResponseCreatedDate
               , T.FacilitySectionId = S.FacilitySectionId
               , T.IsInvalid = S.IsInvalid
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

RETURN 0;