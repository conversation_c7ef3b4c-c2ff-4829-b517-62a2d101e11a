﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns:ns0="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build" ToolsVersion="4.0">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>PlacementStore</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{0ab36745-130a-4a45-a2cf-390a6f2484c1}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath />
    <RootNamespace>PlacementStore</RootNamespace>
    <AssemblyName>PlacementStore</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
    <ServiceBrokerOption>DisableBroker</ServiceBrokerOption>
    <TargetFrameworkProfile />
    <CompatibilityMode>160</CompatibilityMode>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatTSqlWarningsAsErrors>True</TreatTSqlWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatTSqlWarningsAsErrors>True</TreatTSqlWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(NetCoreBuild)' != 'true' AND '$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(NetCoreBuild)' != 'true' AND '$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="Reference" />
    <Folder Include="dbo" />
    <Folder Include="Security" />
    <Folder Include="Reference\Tables" />
    <Folder Include="dbo\Tables" />
    <Folder Include="Scripts" />
    <Folder Include="Security\Roles" />
    <Folder Include="Security\Schemas" />
    <Folder Include="PactConfig" />
    <Folder Include="PactConfig\Functions" />
    <Folder Include="PactConfig\Stored Procedures" />
    <Folder Include="PactConfig\Tables" />
    <Folder Include="PactConfig\Views" />
    <Folder Include="Rules" />
    <Folder Include="Rules\Stored Procedures" />
    <Folder Include="Rules\Tables" />
    <Folder Include="Reference\StoredProcedures" />
    <Folder Include="RefConfig" />
    <Folder Include="RefConfig\Tables" />
    <Folder Include="RefConfig\StoredProcedures" />
    <Folder Include="dbo\Views" />
    <Folder Include="Rules\Views" />
    <Folder Include="APIv1" />
    <Folder Include="APIv1\Views" />
    <Folder Include="APIv1\Stored Procedures" />
    <Folder Include="FMATemp" />
    <Folder Include="FMATemp\Tables" />
    <Folder Include="APIv1\Tables" />
    <Folder Include="COLStaging" />
    <Folder Include="COLStaging\Tables" />
    <Folder Include="COLStaging\Stored Procedures" />
    <Folder Include="Rules\Function" />
    <Folder Include="PSConfig" />
    <Folder Include="PSConfig\Tables" />
    <Folder Include="BPStaging" />
    <Folder Include="BPStaging\Tables" />
    <Folder Include="BPStaging\Stored Procedures" />
    <Folder Include="dbo\Functions" />
    <Folder Include="BPStaging\Views" />
    <Folder Include="APIv1\Functions" />
    <Folder Include="CMSStaging" />
    <Folder Include="CMSStaging\Tables" />
    <Folder Include="CMSStaging\Stored Procedures" />
    <Folder Include="rpt" />
    <Folder Include="rpt\views" />
    <Folder Include="MarineMar" />
    <Folder Include="MarineMar\Tables" />
    <Folder Include="Support" />
    <Folder Include="Support\StoredProcedures" />
    <Folder Include="Support\Tables" />
    <Folder Include="PS" />
    <Folder Include="PS\StoredProcedure" />
    <Folder Include="PS\Tables" />
    <Folder Include="MarineMar\Views" />
    <Folder Include="MarineMar\Stored Procedures" />
    <Folder Include="EPICStaging" />
    <Folder Include="EPICStaging\StoredProcedures" />
    <Folder Include="EPICStaging\Tables" />
    <Folder Include="devops" />
    <Folder Include="devops\Functions" />
    <Folder Include="PACTStaging" />
    <Folder Include="PACTStaging\Tables" />
    <Folder Include="PACTStaging\Stored Procedures" />
    <Folder Include="EclipseStaging" />
    <Folder Include="SignetStaging" />
    <Folder Include="SignetStaging\Stored Procedures" />
    <Folder Include="SignetStaging\Tables" />
    <Folder Include="PACTStaging\Views" />
    <Folder Include="PS\Views" />
    <Folder Include="ReferenceStaging" />
    <Folder Include="ReferenceStaging\Stored Procedures" />
    <Folder Include="ReferenceStaging\Tables" />
    <Folder Include="SignetStaging\Views" />
    <Folder Include="ref" />
    <Folder Include="ref\Tables" />
    <Folder Include="Task" />
    <Folder Include="Task\StoredProcedures" />
    <Folder Include="Task\Tables" />
    <Folder Include="Scripts\reference" />
    <Folder Include="Scripts\reference\ref" />
    <Folder Include="FMATemp\StoredProcedures" />
    <Folder Include="Scripts\rules" />
    <Folder Include="Scripts\reference\PactConfig" />
    <Folder Include="Scripts\reference\dbo" />
    <Folder Include="ods" />
    <Folder Include="ods\Views" />
    <Folder Include="psapi" />
    <Folder Include="psapi\Stored Procedures" />
    <Folder Include="psapi\Tables" />
    <Folder Include="Compare" />
    <Folder Include="Compare\container" />
    <Folder Include="Compare\dev" />
    <Folder Include="Compare\local" />
    <Folder Include="Compare\prod" />
    <Folder Include="Compare\qa" />
    <Folder Include="Compare\uat" />
    <Folder Include="Compare\iat" />
    <Folder Include="devops\Stored Procedures" />
    <Folder Include="rpt\Tables" />
    <Folder Include="rpt\Stored Procedures" />
    <Folder Include="Scripts\reference\MarineMar" />
    <Folder Include="CAMStaging" />
    <Folder Include="CAMStaging\Stored Procedures" />
    <Folder Include="CAMStaging\Tables" />
    <Folder Include="ods\Stored Procedures" />
    <Folder Include="Scripts\reference\reference" />
    <Folder Include="ADF\" />
    <Folder Include="ADF\Tables\" />
    <Folder Include="ADF\Stored Procedures\" />
    <Folder Include="ADF\Function\" />
    <Folder Include="Scripts\reference\ADF\" />
    <Folder Include="ADF\Views" />
    <Folder Include="Storage" />
    <Folder Include="CMAStaging" />
    <Folder Include="CMAStaging\Stored Procedures" />
    <Folder Include="CMAStaging\Tables" />
    <Folder Include="Scripts\reference\psapi" />
    <Folder Include="Scripts\reference\RefConfig" />
    <Folder Include="Scripts\reference\SignetStaging" />
    <Folder Include="Scripts\reference\rpt" />
    <Folder Include="Support\Types" />
    <Folder Include="Eclipse" />
    <Folder Include="Eclipse\Tables" />
    <Folder Include="EclipseStaging\Stored Procedures" />
    <Folder Include="EclipseStaging\Tables" />
    <Folder Include="PASStaging" />
    <Folder Include="PASStaging\Stored Procedures" />
    <Folder Include="PASStaging\Tables" />
    <Folder Include="PAS" />
    <Folder Include="PAS\Tables" />
    <Folder Include="BP" />
    <Folder Include="BP\Tables" />
    <Folder Include="devops\Tables" />
    <Folder Include="PAS\Stored Procedures" />
    <Folder Include="WIBSStaging" />
    <Folder Include="WIBSStaging\Tables" />
    <Folder Include="WIBSStaging\Stored Procedures" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="Reference\Tables\ContactType.sql" />
    <Build Include="Reference\Tables\Country.sql" />
    <Build Include="Reference\Tables\Currency.sql" />
    <Build Include="Reference\Tables\DataSource.sql" />
    <Build Include="Reference\Tables\DataSourceInstance.sql" />
    <Build Include="Reference\Tables\ExchangeRate.sql" />
    <Build Include="Reference\Tables\GeographyGroup.sql" />
    <Build Include="Reference\Tables\GeographyGroupingScheme.sql" />
    <Build Include="Reference\Tables\Industry.sql" />
    <Build Include="Reference\Tables\IndustrySector.sql" />
    <Build Include="Reference\Tables\IndustrySubSector.sql" />
    <Build Include="Reference\Tables\LegalEntity.sql" />
    <Build Include="Reference\Tables\Office.sql" />
    <Build Include="Reference\Tables\Party.sql" />
    <Build Include="Reference\Tables\PartyRole.sql" />
    <Build Include="Reference\Tables\PartyRoleRelationship.sql" />
    <Build Include="Reference\Tables\PolicyType.sql" />
    <Build Include="Reference\Tables\ProductClass.sql" />
    <Build Include="Reference\Tables\ProductLine.sql" />
    <Build Include="Reference\Tables\Worker.sql" />
    <Build Include="Reference\Tables\WorkerAccount.sql" />
    <Build Include="Reference\Tables\WorkerContact.sql" />
    <Build Include="dbo\Tables\BusinessContext.sql" />
    <Build Include="dbo\Tables\Layer.sql" />
    <Build Include="dbo\Tables\MarketInteraction.sql" />
    <Build Include="dbo\Tables\MarketSelection.sql" />
    <Build Include="dbo\Tables\Party.sql" />
    <Build Include="dbo\Tables\Placement.sql" />
    <Build Include="dbo\Tables\PlacementProduct.sql" />
    <Build Include="dbo\Tables\PlacementStructure.sql" />
    <Build Include="dbo\Tables\PlacementWorker.sql" />
    <Build Include="dbo\Tables\Policy.sql" />
    <Build Include="dbo\Tables\PolicySection.sql" />
    <Build Include="dbo\Tables\PolicyWorker.sql" />
    <Build Include="dbo\Tables\Product.sql" />
    <Build Include="dbo\Tables\SelectedReason.sql" />
    <None Include="Scripts\Script.PreDeployment.EnableClr.sql" />
    <Build Include="Security\Schemas\APIv1.sql" />
    <Build Include="Security\Schemas\Reference.sql" />
    <Build Include="Security\Roles\ProgrammeStoreConsumerRole.sql" />
    <Build Include="Security\Roles\ProgrammeStoreQueryRole.sql" />
    <Build Include="Security\Schemas\PactConfig.sql" />
    <Build Include="Security\Schemas\Rules.sql" />
    <Build Include="Security\Roles\ETL_Role.sql" />
    <Build Include="PactConfig\Stored Procedures\EndRun.sql" />
    <Build Include="PactConfig\Stored Procedures\EndSourceSystemRun.sql" />
    <Build Include="PactConfig\Stored Procedures\GetSourceSystems.sql" />
    <Build Include="PactConfig\Stored Procedures\IsRunning.sql" />
    <Build Include="PactConfig\Stored Procedures\SetControlValue_Bit.sql" />
    <Build Include="PactConfig\Stored Procedures\StartRun.sql" />
    <Build Include="PactConfig\Stored Procedures\StartSourceSystemRun.sql" />
    <Build Include="PactConfig\Tables\ETLControl.sql" />
    <Build Include="PactConfig\Tables\ETLRun.sql" />
    <Build Include="PactConfig\Tables\QueryType.sql" />
    <Build Include="PactConfig\Tables\Rule.sql" />
    <Build Include="PactConfig\Tables\RuleType.sql" />
    <Build Include="PactConfig\Tables\SourceSystem.sql" />
    <Build Include="PactConfig\Tables\SourceSystemETLRun.sql" />
    <Build Include="PactConfig\Views\SourceSystemRules.sql" />
    <Build Include="Rules\Stored Procedures\Run_IncludeForRenewal.sql" />
    <Build Include="Rules\Tables\PlacementPolicies.sql" />
    <Build Include="Reference\Tables\GeographyGroupMembership.sql" />
    <Build Include="Reference\Tables\City.sql" />
    <Build Include="dbo\Tables\PolicySectionProduct.sql" />
    <Build Include="Reference\StoredProcedures\DeleteReferenceData.sql" />
    <Build Include="PactConfig\Functions\GetControlValue_Bit.sql" />
    <Build Include="PactConfig\Stored Procedures\GetDataSourceDates.sql" />
    <Build Include="PactConfig\Functions\GetQueryTableName.sql" />
    <Build Include="dbo\Tables\PolicyPartyRelationship.sql" />
    <Build Include="dbo\Tables\PolicyOrganisation.sql" />
    <Build Include="dbo\Tables\PlacementOrganisation.sql" />
    <Build Include="RefConfig\Tables\IncludedColumns.sql" />
    <Build Include="RefConfig\Tables\IncludedLocales.sql" />
    <Build Include="RefConfig\Tables\IncludedTables.sql" />
    <Build Include="RefConfig\Tables\Translation.sql" />
    <Build Include="Security\Schemas\RefConfig.sql" />
    <Build Include="RefConfig\StoredProcedures\SourceQuery.sql" />
    <Build Include="Reference\Tables\Language.sql" />
    <Build Include="Rules\Stored Procedures\Run_IncludeForRenewalRule.sql" />
    <Build Include="Rules\Views\PolicyRenewalRuleResults.sql" />
    <Build Include="PactConfig\Tables\SprocExecutionLog.sql" />
    <Build Include="Reference\Tables\Product.sql" />
    <Build Include="Rules\Views\PoliciesAndClients.sql" />
    <Build Include="APIv1\Views\IncludedTranslations.sql" />
    <Build Include="APIv1\Views\PartyHierarchy.sql" />
    <Build Include="APIv1\Views\ProductHierarchy.sql" />
    <Build Include="APIv1\Views\IndustryHierarchy.sql" />
    <Build Include="RefConfig\Tables\CodeList.sql" />
    <Build Include="Rules\Stored Procedures\MergePlacementIntoProgrammeStore.sql" />
    <Build Include="Rules\Stored Procedures\Load_dbo_PlacementStructure.sql" />
    <Build Include="Rules\Stored Procedures\MergePlacementOrganisationIntoProgrammeStore.sql" />
    <Build Include="Rules\Stored Procedures\MergePlacementPartyRoleIntoProgrammeStore.sql" />
    <Build Include="Rules\Stored Procedures\MergePlacementWorkerIntoProgrammeStore.sql" />
    <Build Include="Reference\Tables\ISIC4Industry.sql" />
    <Build Include="Reference\Tables\SIC87Industry.sql" />
    <Build Include="APIv1\Views\Placements.sql" />
    <Build Include="APIv1\Views\PolicyRenewals.sql" />
    <Build Include="APIv1\Views\PlacementPolicies.sql" />
    <Build Include="APIv1\Views\PlacementCarriers.sql" />
    <Build Include="APIv1\Views\PlacementInsured.sql" />
    <Build Include="APIv1\Stored Procedures\GetRenewals.sql" />
    <Build Include="Rules\Tables\RenewalPlacementDataSourceInstance.sql" />
    <Build Include="Rules\Tables\RenewalRun.sql" />
    <Build Include="Rules\Stored Procedures\StartRenewalRun.sql" />
    <Build Include="Rules\Stored Procedures\EndRenewalRun.sql" />
    <Build Include="APIv1\Stored Procedures\EndRenewalRun.sql" />
    <Build Include="dbo\Tables\Log.sql" />
    <Build Include="Security\Schemas\FMATemp.sql" />
    <Build Include="FMATemp\Tables\PlacementCarrier.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Party.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Product.sql" />
    <Build Include="FMATemp\Tables\Policy.sql" />
    <Build Include="FMATemp\Tables\Placement.sql" />
    <Build Include="FMATemp\Tables\PlacementPolicy.sql" />
    <Build Include="FMATemp\Tables\PolicyProduct.sql" />
    <Build Include="FMATemp\Tables\Insured.sql" />
    <Build Include="dbo\Tables\PlacementSystemStatus.sql" />
    <Build Include="APIv1\Stored Procedures\UpdateSinglePlacementSystemStatus.sql" />
    <Build Include="APIv1\Stored Procedures\UpdatePlacementSystemStatus.sql" />
    <Build Include="APIv1\Views\PlacementProducts.sql" />
    <Build Include="dbo\Tables\JustificationReason.sql" />
    <Build Include="dbo\Tables\JustificationReasonType.sql" />
    <Build Include="FMATemp\Tables\PlacementOrganisation.sql" />
    <Build Include="dbo\Tables\Carrier.sql" />
    <Build Include="APIv1\Views\CarrierHierarchy.sql" />
    <Build Include="dbo\Tables\RefTranslation.sql" />
    <Build Include="dbo\Tables\PlacementListener.sql" />
    <Build Include="Rules\Stored Procedures\Run_CreateListener.sql" />
    <Build Include="dbo\Tables\PolicyCarrierDetails.sql" />
    <Build Include="APIv1\Views\FacilityCarriers.sql" />
    <Build Include="FMATemp\Tables\Facility.sql" />
    <Build Include="FMATemp\Tables\staging_vw_FacilityCarrier.sql" />
    <Build Include="dbo\Tables\MarketInteractionFacilities.sql" />
    <Build Include="dbo\Tables\PlacementCompletionStatus.sql" />
    <Build Include="APIv1\Stored Procedures\UpdatePlacementCompletionStatus.sql" />
    <Build Include="APIv1\Stored Procedures\GetPlacementsReadyToSendToServicingPlatform.sql" />
    <Build Include="dbo\Tables\PlacementTeams.sql" />
    <Build Include="Rules\Stored Procedures\MergePlacementTeamsIntoProgrammeStore.sql" />
    <Build Include="FMATemp\Tables\PlacementTeams.sql" />
    <Build Include="dbo\Tables\CarrierAlias.sql" />
    <Build Include="dbo\Tables\LookupGroup.sql" />
    <Build Include="dbo\Tables\Lookup.sql" />
    <Build Include="dbo\Tables\PartyAttribute.sql" />
    <Build Include="FMATemp\Tables\PartyAttribute.sql" />
    <Build Include="APIv1\Views\NewClientDetails.sql" />
    <Build Include="APIv1\Views\PartyAttributes.sql" />
    <Build Include="APIv1\Views\Lookups.sql" />
    <Build Include="dbo\Tables\ClientMapping.sql" />
    <Build Include="APIv1\Views\ClientMapping.sql" />
    <Build Include="APIv1\Stored Procedures\AddClientMapping.sql" />
    <Build Include="APIv1\Views\PolicyParties.sql" />
    <Build Include="APIv1\Views\PolicyOrganisationHierarchy.sql" />
    <Build Include="APIv1\Views\PolicyProductDetails.sql" />
    <Build Include="dbo\Tables\MergedPlacements.sql" />
    <Build Include="dbo\Tables\PlacementSystemUser.sql" />
    <Build Include="dbo\Tables\TeamMember.sql" />
    <Build Include="rpt\Tables\ContractTimeline.sql" />
    <Build Include="dbo\Tables\PlacementSystemTable.sql" />
    <Build Include="dbo\Tables\NewClient.sql" />
    <Build Include="dbo\Tables\CarrierAgencyRating.sql" />
    <Build Include="dbo\Tables\Agency.sql" />
    <Build Include="dbo\Tables\CarrierRestrictionDefinition.sql" />
    <Build Include="dbo\Tables\CarrierRestriction.sql" />
    <Build Include="dbo\Tables\CarrierRating.sql" />
    <Build Include="APIv1\Views\CarrierAgencyRating.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Agency.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierAgencyRating.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierRating.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierRestriction.sql" />
    <Build Include="dbo\Tables\CarrierStatus.sql" />
    <Build Include="dbo\Tables\CarrierRatingOutlook.sql" />
    <Build Include="COLStaging\Tables\Tabela_Documentos.sql" />
    <Build Include="Rules\Function\GetRenewalRuleSQL.sql" />
    <Build Include="Rules\Stored Procedures\Run_MergeRuleIDIntoPolicy.sql" />
    <Build Include="Reference\Tables\EmploymentStatus.sql" />
    <Build Include="APIv1\Views\PlacementUpdates.sql" />
    <Build Include="FMATemp\Tables\PlacementUpdates.sql" />
    <Build Include="APIv1\Views\PlacementSystemAudit.sql" />
    <Build Include="APIv1\Views\PlacementSystemRole.sql" />
    <Build Include="Reference\Tables\ActiveDirectory.sql" />
    <Build Include="APIv1\Views\ActiveDirectory.sql" />
    <Build Include="FMATemp\Tables\staging_vw_PartyAddress.sql" />
    <Build Include="APIv1\Stored Procedures\GetPlacementDetail.sql" />
    <Build Include="Rules\Function\GetRenewalRuleSQL_woRuleId.sql" />
    <Build Include="Rules\Tables\PlacementPoliciesNonBP.sql" />
    <Build Include="APIv1\Views\PolicyBrokingSegmentMapping.sql" />
    <Build Include="APIv1\Views\PlacementWorkers.sql" />
    <Build Include="dbo\Tables\PlacementSystemRoleMapping.sql" />
    <Build Include="FMATemp\Tables\PlacementWorkers.sql" />
    <Build Include="PactConfig\Tables\rptAttribute.sql" />
    <Build Include="Reference\Tables\FinancialStructureMapping.sql" />
    <Build Include="dbo\Tables\Coverage.sql" />
    <Build Include="dbo\Tables\CoverageGroup.sql" />
    <Build Include="dbo\Tables\CoverageGroupSection.sql" />
    <Build Include="dbo\Tables\CoverageType.sql" />
    <Build Include="dbo\Tables\SubmissionCoverageGroup.sql" />
    <Build Include="dbo\Tables\Specification.sql" />
    <Build Include="dbo\Tables\Value.sql" />
    <Build Include="dbo\Tables\ValueType.sql" />
    <Build Include="dbo\Tables\ValueTypeLookupValue.sql" />
    <Build Include="PSConfig\Tables\SharedKeyAccess.sql" />
    <Build Include="APIv1\Views\WillisOffice.sql" />
    <Build Include="dbo\Tables\Scope.sql" />
    <Build Include="dbo\Tables\UserScope.sql" />
    <Build Include="dbo\Tables\PlacementTeamMember.sql" />
    <Build Include="APIv1\Stored Procedures\GetSharedAccessKey.sql" />
    <Build Include="APIv1\Views\PlacementSimple.sql" />
    <Build Include="APIv1\Views\PlacementParties.sql" />
    <Build Include="APIv1\Stored Procedures\InvalidateNewClient.sql" />
    <Build Include="BPStaging\Tables\Coverage.sql" />
    <Build Include="BPStaging\Tables\CoverageGroup.sql" />
    <Build Include="BPStaging\Tables\CoverageGroupSection.sql" />
    <Build Include="BPStaging\Tables\CoverageType.sql" />
    <Build Include="BPStaging\Tables\Specification.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainer.sql" />
    <Build Include="BPStaging\Tables\SubmissionCoverageGroup.sql" />
    <Build Include="BPStaging\Tables\Value.sql" />
    <Build Include="BPStaging\Tables\ValueType.sql" />
    <Build Include="BPStaging\Tables\ValueTypeLookupValue.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadCoverage.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadCoverageGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadCoverageGroupSection.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadCoverageType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadSpecification.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadSubmissionCoverageGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadValue.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadValueType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadValueTypeLookupValue.sql" />
    <Build Include="dbo\Tables\TerritoryCountry.sql" />
    <Build Include="APIv1\Stored Procedures\GetPlacementsServiceBus.sql" />
    <Build Include="Rules\Tables\PlacementServiceBus.sql" />
    <Build Include="dbo\Tables\PlacementExtension.sql" />
    <Build Include="BPStaging\Tables\PolicyType.sql" />
    <Build Include="dbo\Functions\RemoveInvalidCharacters.sql" />
    <Build Include="BPStaging\Tables\Scope.sql" />
    <Build Include="BPStaging\Tables\UserScope.sql" />
    <Build Include="BPStaging\Tables\PlacementTeamMember.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementTeamMember.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadScope.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadUserScope.sql" />
    <Build Include="dbo\Tables\Submission.sql" />
    <Build Include="dbo\Tables\SubmissionDistribution.sql" />
    <Build Include="dbo\Tables\SubmissionRecipient.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_dbo_Submission.sql" />
    <Build Include="APIv1\Views\PolicyCarriers.sql" />
    <Build Include="FMATemp\Tables\PolicyCarrier.sql" />
    <Build Include="BPStaging\Tables\Placement.sql" />
    <Build Include="BPStaging\Tables\PlacementTeams.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacement.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementTeams.sql" />
    <Build Include="BPStaging\Tables\MarketResponse.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementPolicyRelationshipType.sql" />
    <Build Include="APIv1\Views\PlacementMarketInteractions.sql" />
    <Build Include="dbo\Tables\PlacementPremium.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementPremium.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadCarrierResponse.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadLayer.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadMarketInteractionFacility.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadMarketSelection.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadMergedPlacements.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadNewClient.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementPolicies.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementProduct.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementStructure.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementSystemTable.sql" />
    <Build Include="BPStaging\Tables\CarrierResponse.sql" />
    <Build Include="BPStaging\Tables\Layer.sql" />
    <Build Include="BPStaging\Tables\MarketInteraction.sql" />
    <Build Include="BPStaging\Tables\MarketInteractionFacility.sql" />
    <Build Include="BPStaging\Tables\MarketSelection.sql" />
    <Build Include="BPStaging\Tables\MergedPlacements.sql" />
    <Build Include="BPStaging\Tables\NewClient.sql" />
    <Build Include="BPStaging\Tables\PlacementPartyRelationship.sql" />
    <Build Include="BPStaging\Tables\PlacementPolicy.sql" />
    <Build Include="BPStaging\Tables\PlacementSystemTable.sql" />
    <Build Include="FMATemp\Tables\PlacementProducts.sql" />
    <Build Include="BPStaging\Views\InsertCarrierResponse.sql" />
    <Build Include="BPStaging\Views\InsertMarketInteractionFacility.sql" />
    <Build Include="BPStaging\Views\InsertMarketSelection.sql" />
    <Build Include="BPStaging\Views\InsertMergedPlacements.sql" />
    <Build Include="APIv1\Views\BrazilCarrierStatusOverride.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierStatusOverride.sql" />
    <Build Include="PactConfig\Functions\GetControlValue_DateTime.sql" />
    <Build Include="PactConfig\Stored Procedures\ErrorRun.sql" />
    <Build Include="PactConfig\Stored Procedures\ErrorSourceSystemRun.sql" />
    <Build Include="PactConfig\Stored Procedures\SetControlValue_DateTime.sql" />
    <Build Include="PactConfig\Tables\ETLRunType.sql" />
    <Build Include="dbo\Tables\ExtendedReportingPeriod.sql" />
    <Build Include="dbo\Tables\PaymentPeriod.sql" />
    <Build Include="Security\Schemas\SIGNET.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_SubmissionPortalSummary.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_dbo_SubmissionMarketDocuments.sql" />
    <Build Include="dbo\Tables\SubmissionMarketDocuments.sql" />
    <Build Include="Security\Schemas\CarrierMaintenance.sql" />
    <Build Include="Rules\Stored Procedures\Run_MergeBrokingRegionAndSegment.sql" />
    <Build Include="dbo\Tables\DataSourceInstanceIdMapping.sql" />
    <Build Include="Reference\Tables\CountrySubdivision.sql" />
    <Build Include="CMSStaging\Stored Procedures\Load_ref_Product.sql" />
    <Build Include="dbo\Tables\CarrierType.sql" />
    <Build Include="BPStaging\Stored Procedures\UpdatePlacement.sql" />
    <Build Include="dbo\Tables\CarrierMapping.sql" />
    <Build Include="dbo\Tables\CarrierMappingType.sql" />
    <None Include="Scripts\Script.PostDeployment.PopulateSharedKeyAccess.sql" />
    <Build Include="APIv1\Views\CarrierHierarchyExtended.sql" />
    <Build Include="dbo\Tables\ElementAttributeType.sql" />
    <Build Include="BPStaging\Tables\ElementTag.sql" />
    <Build Include="dbo\Tables\PlacementRiskDefinitionElement.sql" />
    <Build Include="BPStaging\Tables\DocumentElement.sql" />
    <Build Include="dbo\Tables\DocumentElement.sql" />
    <Build Include="BPStaging\Stored Procedures\UpdatePlacementRiskDefinitionElement.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadDocumentElement.sql" />
    <Build Include="APIv1\Views\OrganisationAddress.sql" />
    <Build Include="Rules\Stored Procedures\ClearSegmentationRules.sql" />
    <Build Include="APIv1\Functions\GetLatestRenewedFacility.sql" />
    <Build Include="APIv1\Views\CarrierRelationship.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierExtended.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierRelationship.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Facility.sql" />
    <Build Include="FMATemp\Tables\staging_vw_FacilitySection.sql" />
    <Build Include="FMATemp\Tables\staging_vw_FacilitySectionCarrier.sql" />
    <Build Include="BPStaging\Tables\MarketResponseSecurity.sql" />
    <Build Include="BPStaging\Tables\PlacementMarketValidation.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadMarketResponseSecurity.sql" />
    <Build Include="dbo\Tables\MarketResponseSecurity.sql" />
    <Build Include="dbo\Tables\PlacementMarketValidation.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_dbo_PlacementMarketValidation.sql" />
    <Build Include="FMATemp\Tables\AdditionalDataItem.sql" />
    <Build Include="APIv1\Views\AdditionalDataItem.sql" />
    <Build Include="dbo\Tables\IndexationType.sql" />
    <Build Include="Security\Schemas\rpt.sql" />
    <Build Include="Reference\Tables\WillisCoreCountry.sql" />
    <Build Include="Reference\Tables\WillisCoreCountrySub.sql" />
    <Build Include="BPStaging\Tables\SubmissionPortalDetails.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadSubmissionPortalDetails.sql" />
    <Build Include="dbo\Tables\SubmissionPortalDetails.sql" />
    <Build Include="Rules\Stored Procedures\Run_CreatePlacementPolicies.sql" />
    <Build Include="Rules\Stored Procedures\Run_LinkCurrentPolicies.sql" />
    <Build Include="Rules\Stored Procedures\Run_SegmentationRules.sql" />
    <Build Include="dbo\Tables\CarrierTOBA.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierTOBA.sql" />
    <Build Include="dbo\Tables\ClientUnderwriterPremium.sql" />
    <Build Include="APIv1\Tables\PartyAttributesTable.sql" />
    <Build Include="APIv1\Stored Procedures\PopulateAPIv1PartyAttributesTable.sql" />
    <Build Include="BPStaging\Tables\PlacementExtension.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementExtension.sql" />
    <Build Include="APIv1\Views\PlacementPremium_CUP.sql" />
    <Build Include="dbo\Tables\PolicyAttribute.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerRiskDefinitionItem.sql" />
    <Build Include="FMATemp\Tables\AppPlacementPolicy.sql" />
    <Build Include="APIv1\Views\BPCurrentPlacementPolicy.sql" />
    <Build Include="MarineMar\Tables\MarineCargoCoverTypeMapping.sql" />
    <Build Include="MarineMar\Tables\MarineNotRemarketingReasonMapping.sql" />
    <Build Include="MarineMar\Tables\MarineRiskRenewalMapping.sql" />
    <Build Include="Security\Schemas\MarineMar.sql" />
    <Build Include="MarineMar\Tables\MarineCurrencyMapping.sql" />
    <Build Include="Reference\Tables\PartyExternalReference.sql" />
    <Build Include="Security\Schemas\devops.sql" />
    <Build Include="Support\StoredProcedures\DeletePlacements.sql" />
    <Build Include="Support\Tables\DeletePlacementLog.sql" />
    <Build Include="Security\Schemas\Support.sql" />
    <Build Include="Reference\Tables\NotRemarketingReason.sql" />
    <Build Include="dbo\Tables\ContractDocumentElement.sql" />
    <Build Include="BPStaging\Tables\Contract.sql" />
    <Build Include="BPStaging\Tables\ContractDocumentElement.sql" />
    <Build Include="BPStaging\Tables\ContractStatus.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractDocumentElement.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractStatus.sql" />
    <Build Include="dbo\Tables\ElementAttribute.sql" />
    <Build Include="BPStaging\Tables\Element.sql" />
    <Build Include="BPStaging\Tables\ElementAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_Element.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementAttribute.sql" />
    <Build Include="PactConfig\Stored Procedures\RemoveOldExecutionLogEntries.sql" />
    <Build Include="BPStaging\Tables\ContractSection.sql" />
    <Build Include="dbo\Tables\ContractSection.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractSection.sql" />
    <Build Include="MarineMar\Views\vwMarketActivity.sql" />
    <Build Include="MarineMar\Views\vwPlacement.sql" />
    <Build Include="BPStaging\Tables\PlacementTasks.sql" />
    <Build Include="dbo\Tables\PlacementTasks.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementTasks.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadProductsFromElements.sql" />
    <Build Include="BPStaging\Tables\ContractTimeline.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_rpt_ContractTimeline.sql" />
    <Build Include="APIv1\Views\RenewalPlacementTeam.sql" />
    <Build Include="dbo\Views\MDSProductMapping.sql" />
    <Build Include="BPStaging\Tables\ElementTagInclusionRule.sql" />
    <Build Include="BPStaging\Tables\ContractPolicy.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractPolicy.sql" />
    <Build Include="dbo\Tables\ContractPolicy.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerContract.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_NegotiationContract.sql" />
    <Build Include="BPStaging\Tables\ContractRisk.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractRisk.sql" />
    <Build Include="MarineMar\Tables\Placement.sql" />
    <Build Include="MarineMar\Tables\PlacementClientExec.sql" />
    <Build Include="MarineMar\Tables\PlacementBroker.sql" />
    <Build Include="MarineMar\Tables\PlacementClient.sql" />
    <Build Include="MarineMar\Tables\PlacementInsured.sql" />
    <Build Include="MarineMar\Tables\PlacementReinsured.sql" />
    <Build Include="MarineMar\Tables\Policy.sql" />
    <Build Include="MarineMar\Tables\PolicyAccountHandler.sql" />
    <Build Include="MarineMar\Tables\RiskProfileClassOfBusiness.sql" />
    <Build Include="MarineMar\Tables\RiskProfileLineOfBusiness.sql" />
    <Build Include="MarineMar\Tables\RiskProfileCoverType.sql" />
    <Build Include="MarineMar\Tables\RiskProfileNatureOfOperationInterest.sql" />
    <Build Include="MarineMar\Tables\RiskProfileVesselType.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPlacement.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPlacementBroker.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPlacementClient.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPlacementClientExec.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPlacementInsured.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPlacementReinsured.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPolicy.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadPolicyAccountHandler.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadRiskProfileLineOfBusiness.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadRiskProfileCoverType.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadRiskProfileNatureOfOperationInterest.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadRiskProfileVesselType.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadRiskProfileClassOfBusiness.sql" />
    <Build Include="MarineMar\Stored Procedures\Load_MarineMar_NegotiationMarket.sql" />
    <Build Include="MarineMar\Tables\NegotiationMarket.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadMarketResponse.sql" />
    <Build Include="MarineMar\Tables\MarketResponse.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadTeamMember.sql" />
    <Build Include="BPStaging\Tables\TeamMember.sql" />
    <Build Include="Security\Schemas\EPICStaging.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadLegalEntity.sql" />
    <Build Include="BPStaging\Tables\LegalEntity.sql" />
    <Build Include="MarineMar\Tables\MarineOutcomeReasonMapping.sql" />
    <Build Include="MarineMar\Tables\MarineDeclinationReasonMapping.sql" />
    <Build Include="MarineMar\Views\vwMarineMapping.sql" />
    <Build Include="MarineMar\Tables\MarineHullLineOfBusinessMapping.sql" />
    <Build Include="MarineMar\Tables\MarineSpecialRisksNatureOfOperationMapping.sql" />
    <Build Include="MarineMar\Tables\MarineSpecialRisksLayerTypeMapping.sql" />
    <Build Include="MarineMar\Tables\MarineRefGeneric.sql" />
    <Build Include="MarineMar\Tables\MarineRequestQuoteInsurerMapping.sql" />
    <Build Include="MarineMar\Tables\MarineCargoInterestTypeMapping.sql" />
    <Build Include="MarineMar\Tables\MarineRefRiskCode.sql" />
    <Build Include="MarineMar\Tables\MarineHullVesselTypeMapping.sql" />
    <Build Include="MarineMar\Tables\MarineRefProductClass.sql" />
    <Build Include="devops\Functions\AddMissing_PrimaryKeys.sql" />
    <Build Include="devops\Functions\GetSizeByTable.sql" />
    <Build Include="devops\Functions\IsDevEnv.sql" />
    <Build Include="devops\Functions\IsLocalEnv.sql" />
    <Build Include="devops\Functions\IsProdEnv.sql" />
    <Build Include="devops\Functions\IsQAEnv.sql" />
    <Build Include="devops\Functions\IsUatOrIatEnv.sql" />
    <Build Include="devops\Functions\RenameKeys_Foreign.sql" />
    <Build Include="devops\Functions\RenameKeys_ObjectIndexes.sql" />
    <Build Include="BPStaging\Tables\ProgramStructureType.sql" />
    <Build Include="BPStaging\Tables\ElementDelta.sql" />
    <Build Include="BPStaging\Tables\ElementChangeSet.sql" />
    <Build Include="BPStaging\Tables\ElementBranch.sql" />
    <Build Include="BPStaging\Tables\ContractDocumentVersion.sql" />
    <Build Include="BPStaging\Tables\ContractEndorsement.sql" />
    <Build Include="BPStaging\Tables\ContractEndorsementElementChangeSet.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerMarketContract.sql" />
    <Build Include="dbo\Tables\ElementDelta.sql" />
    <Build Include="dbo\Tables\ElementChangeSet.sql" />
    <Build Include="dbo\Tables\ElementBranch.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementDelta.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_NegotiationMarketContract.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_dbo_ElementChangeSet.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementBranch.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ContractEndorsementElementChangeSet.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractDocumentVersion.sql" />
    <Build Include="dbo\Tables\ElementTagDelta.sql" />
    <Build Include="dbo\Tables\LibraryElement.sql" />
    <Build Include="dbo\Tables\ElementAttributeDelta.sql" />
    <Build Include="BPStaging\Tables\ElementAttributeDelta.sql" />
    <Build Include="BPStaging\Tables\ElementTagDelta.sql" />
    <Build Include="BPStaging\Tables\LibraryElement.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementTagDelta.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadLibraryElement.sql" />
    <Build Include="Security\Schemas\PS.sql" />
    <Build Include="COLStaging\Tables\CarrierProduct.sql" />
    <Build Include="COLStaging\Tables\Product.sql" />
    <Build Include="COLStaging\Tables\Tabela_GruposHierarq.sql" />
    <Build Include="Security\Schemas\EclipseStaging.sql" />
    <Build Include="Security\Schemas\COLStaging.sql" />
    <Build Include="Security\Schemas\CMSStaging.sql" />
    <Build Include="COLStaging\Tables\Tabela_TiposCaracter.sql" />
    <Build Include="COLStaging\Tables\Tabela_Caracterists.sql" />
    <Build Include="COLStaging\Tables\Tabela_ClientCaracs.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPolicyOrganisationRole.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPolicyPartyRole.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPolicySection.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPolicySectionProduct.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPolicyServicingRole.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicySectionsIntoProgrammeStore.sql" />
    <Build Include="MarineMar\Tables\MarineRefCountries.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwAttribute.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePartyIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicyAttributeIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePoliciesIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\UpdatePolicyTacitConsecutiveCount.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergeClientUnderwriterPremiumIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergeFacilityPoliciesintoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicyCarrierDetailsIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicyCarriersIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicyClientsIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicyMarketIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicyOrgsIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicySectionProductsIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePolicyWorkersIntoProgrammeStore.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergeProductIntoProgrammeStore.sql" />
    <Build Include="Security\Schemas\SignetStaging.sql" />
    <Build Include="SignetStaging\Tables\Carrier.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrier.sql" />
    <Build Include="SignetStaging\Tables\GroupCode.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierGroup.sql" />
    <Build Include="MarineMar\Tables\SpecieRefCountries.sql" />
    <Build Include="MarineMar\Tables\SpecieRefGeneric.sql" />
    <Build Include="MarineMar\Tables\SpecieRefProductClass.sql" />
    <Build Include="MarineMar\Tables\SpecieRefRiskCode.sql" />
    <Build Include="MarineMar\Tables\SpecieRiskRenewalMapping.sql" />
    <Build Include="MarineMar\Tables\SpecieNotRemarketingReasonMapping.sql" />
    <Build Include="MarineMar\Tables\SpecieRequestQuoteInsurerMapping.sql" />
    <Build Include="MarineMar\Tables\SpecieOutcomeReasonMapping.sql" />
    <Build Include="MarineMar\Tables\SpecieDeclinationReasonMapping.sql" />
    <Build Include="MarineMar\Tables\SpecieCurrencyMapping.sql" />
    <Build Include="MarineMar\Tables\RiskProfileIndustrySectorType.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadRiskProfileIndustrySectorType.sql" />
    <Build Include="MarineMar\Tables\SpecieIndustrySectorTypeMapping.sql" />
    <Build Include="MarineMar\Views\vwSpecieMapping.sql" />
    <Build Include="MarineMar\Tables\Contract.sql" />
    <Build Include="MarineMar\Stored Procedures\LoadContract.sql" />
    <Build Include="MarineMar\Tables\SpeciePanelMemberMapping.sql" />
    <Build Include="MarineMar\Tables\MarinePanelMemberMapping.sql" />
    <Build Include="APIv1\Views\CRMPlacements.sql" />
    <Build Include="dbo\Tables\Industry.sql" />
    <Build Include="dbo\Tables\IndustryType.sql" />
    <Build Include="CMSStaging\Stored Procedures\Load_ref_Industry.sql" />
    <Build Include="EPICStaging\Tables\LineAgencyDefined.sql" />
    <Build Include="dbo\Tables\PolicySectionAttribute.sql" />
    <Build Include="EPICStaging\StoredProcedures\MergeLineAgencyDefinedIntoPolicySectionAttribute.sql" />
    <Build Include="PS\Views\EnabledSourceSystems.sql" />
    <Build Include="BPStaging\Stored Procedures\UpdatePlacementDatesAndStatus.sql" />
    <Build Include="BPStaging\Tables\POPlacementMarketResponseBasis.sql" />
    <Build Include="BPStaging\Tables\ProducingOfficeHubPlacement.sql" />
    <Build Include="BPStaging\Tables\MarketQuoteResponseOverride.sql" />
    <Build Include="PACTStaging\Views\vwParty.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwProduct.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwClientUnderwriterPremium.sql" />
    <Build Include="PactConfig\Views\vwSourceSystemLastRunDate.sql" />
    <Build Include="PACTStaging\Views\vwPolicy.sql" />
    <Build Include="PACTStaging\Views\vwPolicyAttribute.sql" />
    <Build Include="PACTStaging\Views\vwPolicyOrganisationRole.sql" />
    <Build Include="PACTStaging\Views\vwPolicySection.sql" />
    <Build Include="PACTStaging\Views\vwPolicySectionProduct.sql" />
    <Build Include="PACTStaging\Views\vwPolicyServicingRole.sql" />
    <Build Include="PACTStaging\Views\vwPolicyMarket.sql" />
    <Build Include="PACTStaging\Views\vwProductAttribute.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergeProductAttributeIntoProgrammeStore.sql" />
    <Build Include="dbo\Tables\ProductAttribute.sql" />
    <Build Include="PS\Views\vwPolicyAttributeFlat.sql" />
    <Build Include="PACTStaging\Stored Procedures\UpdatePolicyWithAttributes.sql" />
    <Build Include="PS\Views\vwPolicyConsecutiveAutoRenewalCount.sql" />
    <Build Include="dbo\Tables\PlacementPolicy.sql" />
    <Build Include="dbo\Tables\PlacementPolicyRelationshipType.sql" />
    <Build Include="dbo\Tables\PlacementPartyRole.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementPartyRole.sql" />
    <Build Include="BPStaging\Views\InsertPlacementPartyRole.sql" />
    <Build Include="dbo\Tables\PlacementMarket.sql" />
    <Build Include="Rules\Stored Procedures\MergePlacementMarketIntoProgrammeStore.sql" />
    <Build Include="dbo\Tables\ReasonGroup.sql" />
    <Build Include="dbo\Tables\ReasonGroupMapping.sql" />
    <Build Include="PS\Views\vwUnderwriterPremium.sql" />
    <Build Include="BPStaging\Tables\ContractRenewedFrom.sql" />
    <Build Include="SignetStaging\Views\vwCarrier.sql" />
    <Build Include="SignetStaging\Tables\CarrierRestriction.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierRestriction.sql" />
    <Build Include="SignetStaging\Views\vwCarrierRestriction.sql" />
    <Build Include="SignetStaging\Views\vwTOBA.sql" />
    <Build Include="SignetStaging\Tables\TOBA.sql" />
    <Build Include="SignetStaging\Views\vwApprovalStatus.sql" />
    <Build Include="SignetStaging\Tables\ApprovalStatus.sql" />
    <Build Include="SignetStaging\Views\vwCarrierRestrictionDefinition.sql" />
    <Build Include="SignetStaging\Tables\RestrictionCode.sql" />
    <Build Include="SignetStaging\Tables\RatingAgency.sql" />
    <Build Include="SignetStaging\Views\vwCarrierRating.sql" />
    <Build Include="SignetStaging\Tables\PropertyType.sql" />
    <Build Include="SignetStaging\Views\vwCarrierRatingOutlook.sql" />
    <Build Include="SignetStaging\Views\vwCarrierAgencyRating.sql" />
    <Build Include="SignetStaging\Tables\RatingAgencyData.sql" />
    <Build Include="SignetStaging\Tables\ApprovalType.sql" />
    <Build Include="SignetStaging\Tables\CompanyType.sql" />
    <Build Include="SignetStaging\Tables\FatcaReferences.sql" />
    <Build Include="SignetStaging\Tables\CoreCountry.sql" />
    <Build Include="SignetStaging\Tables\CoreCountrySub.sql" />
    <Build Include="SignetStaging\Tables\RatingSequence.sql" />
    <Build Include="SignetStaging\Tables\CarrierStatus.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierStatus.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierRating.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierRatingOutlook.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierAgencyRating.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierRestrictionDefinition.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadCarrierTOBA.sql" />
    <Build Include="SignetStaging\Views\vwRatingAgency.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadRatingAgency.sql" />
    <Build Include="Security\Schemas\ReferenceStaging.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwWillisIndustry.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoIndustry.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwDataSource.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoDataSource.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwCountrySubdivision.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoCountrySubdivision.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwPartyExternalReference.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoPartyExternalReference.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwDataSourceInstance.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoDataSourceInstance.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwGeographyGroupingScheme.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoGeographyGroupingScheme.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwGeographyGroup.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoGeographyGroup.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwCountry.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoCountry.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwWillisIndustrySector.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoIndustrySector.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwGeographyGroupMembership.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoGeographyGroupMembership.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwCity.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoCity.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwPartyRole.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoPartyRole.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwPartyRoleRelationship.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoPartyRoleRelationship.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoParty.sql" />
    <Build Include="ReferenceStaging\Tables\wrk_vwWorker.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoWorker.sql" />
    <Build Include="ReferenceStaging\Tables\wrk_vwWorkerAccount.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoWorkerAccount.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwWillisIndustrySubsector.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoIndustrySubSector.sql" />
    <Build Include="ReferenceStaging\Tables\wrk_vwContactType.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoContactType.sql" />
    <Build Include="ReferenceStaging\Tables\wrk_vwWorkerContact.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoWorkerContact.sql" />
    <Build Include="ReferenceStaging\Tables\wrk_vwEmploymentStatus.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoEmploymentStatus.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwProductClass.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoProductClass.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwProductLine.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoProductLine.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwProduct.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoProduct.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwLegalEntity.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoLegalEntity.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwOffice.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoOffice.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwISIC4Industry.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoISIC4Industry.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwLegacyWTWFinancialStructureMapping.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoFinancialStructureMapping.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwCurrency.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoCurrency.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwExchangeRate.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoExchangeRate.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwLanguage.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoLanguage.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoSIC87Industry.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwSIC87Industry.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwProductMapping.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoActiveDirectory.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwActiveDirectory.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeProductMappingIntoProduct.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadBespokeHeadingReason.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadCoverageBasis.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadDeductibleBasis.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadDeltaType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadLinePercentageBasis.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_OpportunityType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadParticipantFunction.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPremiumAdjustableIndicator.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPremiumType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadRegulatoryClientClassification.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadRiskClassification.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadRiskCode.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadTaxCode.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadTaxRateBasis.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadTerritory.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadTimeZoneRepresention.sql" />
    <Build Include="BPStaging\Tables\BespokeHeadingReason.sql" />
    <Build Include="BPStaging\Tables\ContractType.sql" />
    <Build Include="BPStaging\Tables\CoverageBasis.sql" />
    <Build Include="BPStaging\Tables\DeductibleBasis.sql" />
    <Build Include="BPStaging\Tables\DeltaType.sql" />
    <Build Include="BPStaging\Tables\LinePercentageBasis.sql" />
    <Build Include="BPStaging\Tables\OpportunityType.sql" />
    <Build Include="BPStaging\Tables\ParticipantFunction.sql" />
    <Build Include="BPStaging\Tables\PremiumAdjustableIndicator.sql" />
    <Build Include="BPStaging\Tables\PremiumType.sql" />
    <Build Include="BPStaging\Tables\RegulatoryClientClassification.sql" />
    <Build Include="BPStaging\Tables\RiskClassification.sql" />
    <Build Include="BPStaging\Tables\RiskCode.sql" />
    <Build Include="BPStaging\Tables\TaxCode.sql" />
    <Build Include="BPStaging\Tables\TaxRateBasis.sql" />
    <Build Include="BPStaging\Tables\Territory.sql" />
    <Build Include="BPStaging\Tables\TimeZoneRepresention.sql" />
    <Build Include="Security\Schemas\ref.sql" />
    <Build Include="ref\Tables\BespokeHeadingReason.sql" />
    <Build Include="ref\Tables\ContractType.sql" />
    <Build Include="ref\Tables\CoverageBasis.sql" />
    <Build Include="ref\Tables\DeductibleBasis.sql" />
    <Build Include="ref\Tables\DeltaType.sql" />
    <Build Include="ref\Tables\LinePercentageBasis.sql" />
    <Build Include="ref\Tables\OpportunityType.sql" />
    <Build Include="ref\Tables\ParticipantFunction.sql" />
    <Build Include="ref\Tables\PremiumAdjustableIndicator.sql" />
    <Build Include="ref\Tables\PremiumType.sql" />
    <Build Include="ref\Tables\RegulatoryClientClassification.sql" />
    <Build Include="ref\Tables\RiskClassification.sql" />
    <Build Include="ref\Tables\RiskCode.sql" />
    <Build Include="ref\Tables\TaxCode.sql" />
    <Build Include="ref\Tables\TaxRateBasis.sql" />
    <Build Include="ref\Tables\Territory.sql" />
    <Build Include="ref\Tables\TimeZoneRepresention.sql" />
    <Build Include="Security\Schemas\Task.sql" />
    <Build Include="Task\Tables\ClassOfBusinessItem.sql" />
    <Build Include="Task\Tables\ClassOfBusinessList.sql" />
    <Build Include="Task\Tables\ClientTaskHistory.sql" />
    <Build Include="Task\Tables\Config.sql" />
    <Build Include="Task\Tables\Event.sql" />
    <Build Include="Task\Tables\InsuredTaskHistory.sql" />
    <Build Include="Task\Tables\Log.sql" />
    <Build Include="Task\Tables\MetadataGroup.sql" />
    <Build Include="Task\Tables\MetadataGroupItem.sql" />
    <Build Include="Task\Tables\MetadataItem.sql" />
    <Build Include="Task\Tables\PolicyAudit.sql" />
    <Build Include="Task\Tables\PolicyConfig.sql" />
    <Build Include="Task\Tables\PolicyConfigClassOfBusinessRestrictionList.sql" />
    <Build Include="Task\Tables\PolicyConfigEvent.sql" />
    <Build Include="Task\Tables\PolicyConfigEventClassOfBusinessRestrictionList.sql" />
    <Build Include="Task\Tables\PolicyConfigRole.sql" />
    <Build Include="Task\Tables\PolicyRenewal.sql" />
    <Build Include="Task\Tables\PolicyRenewalTask.sql" />
    <Build Include="Task\Tables\RestrictionType.sql" />
    <Build Include="Task\Tables\Run.sql" />
    <Build Include="Task\Tables\Script.sql" />
    <Build Include="Task\Tables\TaskConfig.sql" />
    <Build Include="Task\Tables\TaskConfigStatus.sql" />
    <Build Include="Task\Tables\TaskConfigStatusChange.sql" />
    <Build Include="Task\StoredProcedures\CheckSentToServicingPlatformEvent.sql" />
    <Build Include="Task\StoredProcedures\CreatePreRenewalTasks.sql" />
    <Build Include="Task\StoredProcedures\GenerateConfigScripts.sql" />
    <Build Include="Task\StoredProcedures\GetDueTasks.sql" />
    <Build Include="Task\StoredProcedures\GetPlacementTaskMetadata.sql" />
    <Build Include="Task\StoredProcedures\GetTaskMetadata.sql" />
    <Build Include="Task\StoredProcedures\GetTaskStatusChangeChildTask.sql" />
    <Build Include="Task\StoredProcedures\InsertGenerator.sql" />
    <Build Include="Task\StoredProcedures\StartRun.sql" />
    <None Include="Scripts\Script.PostDeployment.PopulateTaskSchema.sql" />
    <Build Include="Security\Roles\TaskReaderRole.sql" />
    <Build Include="Security\Roles\TaskControllerRole.sql" />
    <Build Include="Task\StoredProcedures\CleanupPolicyAudit.sql" />
    <Build Include="Task\StoredProcedures\CleanupLog.sql" />
    <Build Include="BPStaging\Tables\EndorsementStatus.sql" />
    <Build Include="ref\Tables\EndorsementStatus.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadEndorsementStatus.sql" />
    <Build Include="BPStaging\Tables\ContractSectionBasis.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractSectionBasis.sql" />
    <Build Include="dbo\Tables\ContractSectionBasis.sql" />
    <Build Include="BPStaging\Tables\ContractSectionBasisType.sql" />
    <Build Include="ref\Tables\ContractSectionBasisType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractSectionBasisType.sql" />
    <Build Include="BPStaging\Tables\ContractSectionRiskCode.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractSectionRiskCode.sql" />
    <Build Include="dbo\Tables\ContractSectionRiskCode.sql" />
    <Build Include="BPStaging\Tables\ContractEndorsementDocumentElement.sql" />
    <Build Include="dbo\Tables\ContractEndorsementDocumentElement.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadContractEndorsementDocumentElement.sql" />
    <Build Include="ref\Tables\AppraisalType.sql" />
    <Build Include="ref\Tables\BrokingRegion.sql" />
    <Build Include="ref\Tables\BrokingSegment.sql" />
    <Build Include="ref\Tables\BrokingSubSegment.sql" />
    <Build Include="ref\Tables\CancellationReason.sql" />
    <Build Include="ref\Tables\ContractStatus.sql" />
    <Build Include="ref\Tables\DeclinationReason.sql" />
    <Build Include="ref\Tables\LayerType.sql" />
    <Build Include="ref\Tables\OutcomeReason.sql" />
    <Build Include="ref\Tables\OutcomeStatus.sql" />
    <Build Include="ref\Tables\PartyRole.sql" />
    <Build Include="ref\Tables\PendingActionReason.sql" />
    <Build Include="ref\Tables\PlacementStatus.sql" />
    <Build Include="ref\Tables\RenewableOption.sql" />
    <Build Include="ref\Tables\ResponseType.sql" />
    <Build Include="ref\Tables\ServicingRole.sql" />
    <None Include="Scripts\reference\ref\SignetPartyRole.sql" />
    <None Include="Scripts\reference\ref\BrokingSubsegment.sql" />
    <None Include="Scripts\reference\ref\PartyRole.sql" />
    <None Include="Scripts\reference\ref\RenewableOption.sql" />
    <Build Include="BPStaging\Tables\DeclinationReason.sql" />
    <Build Include="BPStaging\Tables\BrokingSubSegment.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadDeclinationReason.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadBrokingSubSegment.sql" />
    <Build Include="BPStaging\Tables\OutcomeReason.sql" />
    <Build Include="BPStaging\Tables\OutcomeStatus.sql" />
    <Build Include="BPStaging\Tables\PendingActionReason.sql" />
    <Build Include="BPStaging\Tables\ResponseType.sql" />
    <Build Include="BPStaging\Tables\Role.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadOutcomeReason.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadOutcomeStatus.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPendingActionReason.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadResponseType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadServicingRole.sql" />
    <Build Include="MarineMar\Views\vwPlacementPolicy.sql" />
    <Build Include="MarineMar\Stored Procedures\CalculateContractEPI.sql" />
    <Build Include="MarineMar\Tables\ContractEPI.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePlacement.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePlacementCarrier.sql" />
    <Build Include="FMATemp\StoredProcedures\StageInsured.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePlacementPolicy.sql" />
    <Build Include="FMATemp\StoredProcedures\StageAppPlacementPolicy.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePolicyProduct.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePlacementTeams.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePolicy.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePlacementWorkers.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePlacementUpdates.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePolicyCarrier.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePlacementProducts.sql" />
    <Build Include="FMATemp\StoredProcedures\StageAdditionalDataItem.sql" />
    <Build Include="Reference\Tables\ProductMapping.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoProductMapping.sql" />
    <Build Include="Security\Schemas\ods.sql" />
    <Build Include="ods\Views\vw_erd_PolicyStatus.sql" />
    <Build Include="ods\Views\vw_ps_Contract.sql" />
    <Build Include="ods\Views\vw_ps_MarketResponse.sql" />
    <Build Include="ods\Views\vw_ps_MarketResponseBasis.sql" />
    <Build Include="ods\Views\vw_ps_Negotiation.sql" />
    <Build Include="ods\Views\vw_ps_NegotiationMarket.sql" />
    <Build Include="ods\Views\vw_ps_Placement.sql" />
    <Build Include="ods\Views\vw_ps_PlacementPartyRole.sql" />
    <Build Include="ods\Views\vw_ps_PlacementPolicy.sql" />
    <Build Include="ods\Views\vw_ps_PlacementServicingRole.sql" />
    <Build Include="ods\Views\vw_ps_Policy.sql" />
    <Build Include="ods\Views\vw_ps_RiskProfile.sql" />
    <Build Include="ods\Views\vw_ref_AppraisalType.sql" />
    <Build Include="ods\Views\vw_ref_BrokingRegion.sql" />
    <Build Include="ods\Views\vw_ref_BrokingSegment.sql" />
    <Build Include="ods\Views\vw_ref_BrokingSubSegment.sql" />
    <Build Include="ods\Views\vw_ref_CancellationReason.sql" />
    <Build Include="ods\Views\vw_ref_Carrier.sql" />
    <Build Include="ods\Views\vw_ref_ContractStatus.sql" />
    <Build Include="ods\Views\vw_erd_DataSource.sql" />
    <Build Include="ods\Views\vw_erd_DataSourceInstance.sql" />
    <Build Include="ods\Views\vw_ref_DeclinationReason.sql" />
    <Build Include="ods\Views\vw_ref_Facility.sql" />
    <Build Include="ods\Views\vw_ref_FacilitySection.sql" />
    <Build Include="ods\Views\vw_ref_FacilitySectionCarrier.sql" />
    <Build Include="ods\Views\vw_ref_Geography.sql" />
    <Build Include="ods\Views\vw_ref_InsuranceType.sql" />
    <Build Include="ods\Views\vw_ref_LayerType.sql" />
    <Build Include="ods\Views\vw_ref_OpportunityType.sql" />
    <Build Include="ods\Views\vw_ref_OutcomeReason.sql" />
    <Build Include="ods\Views\vw_ref_OutcomeStatus.sql" />
    <Build Include="ods\Views\vw_ref_Panel.sql" />
    <Build Include="ods\Views\vw_ref_PanelMember.sql" />
    <Build Include="ods\Views\vw_ref_PanelMemberCarrier.sql" />
    <Build Include="ods\Views\vw_ref_Party.sql" />
    <Build Include="ods\Views\vw_ref_PartyRole.sql" />
    <Build Include="ods\Views\vw_ref_PendingActionReason.sql" />
    <Build Include="ods\Views\vw_ref_PlacementPolicyRelationshipType.sql" />
    <Build Include="ods\Views\vw_ref_PlacementStatus.sql" />
    <Build Include="ods\Views\vw_ref_PolicyStatus.sql" />
    <Build Include="ods\Views\vw_ref_ProducingOffice.sql" />
    <Build Include="ods\Views\vw_ref_ProgramType.sql" />
    <Build Include="ods\Views\vw_ref_RenewableOption.sql" />
    <Build Include="ods\Views\vw_ref_ResponseType.sql" />
    <Build Include="ods\Views\vw_ref_ServicingRole.sql" />
    <Build Include="ods\Views\vw_ref_Team.sql" />
    <Build Include="ods\Views\vw_ref_Worker.sql" />
    <Build Include="rpt\views\vw_as_Submission.sql" />
    <Build Include="rpt\views\vw_as_SubmissionDistribution.sql" />
    <Build Include="rpt\views\vw_as_SubmissionDocument.sql" />
    <Build Include="rpt\views\vw_as_SubmissionContainerMarket.sql" />
    <Build Include="PS\Tables\Negotiation.sql" />
    <Build Include="PS\Tables\NegotiationMarket.sql" />
    <Build Include="rpt\views\vw_as_ContractSection.sql" />
    <Build Include="rpt\views\vw_as_ContractSectionBasis.sql" />
    <Build Include="rpt\views\vw_as_ContractSectionRiskCode.sql" />
    <Build Include="rpt\views\vw_as_PlacementProductSummary.sql" />
    <Build Include="rpt\views\vw_as_PlacementPolicyList.sql" />
    <Build Include="Task\StoredProcedures\CopyFromTaskDatabase.sql" />
    <Build Include="ods\Views\vw_ps_NegotiationContract.sql" />
    <Build Include="ods\Views\vw_ps_NegotiationMarketContract.sql" />
    <Build Include="ods\Views\vw_ref_CarrierRelationship.sql" />
    <Build Include="ods\Views\vw_ref_CarrierType.sql" />
    <Build Include="PS\Tables\MarketResponse.sql" />
    <Build Include="Support\StoredProcedures\DeletePlacementDetailForDataSourceInstance.sql" />
    <Build Include="rpt\views\vw_as_ContractEndorsement.sql" />
    <Build Include="rpt\views\vw_as_CarrierSubmission.sql" />
    <Build Include="BPStaging\Tables\ElementTagType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementTagType.sql" />
    <Build Include="ref\Tables\ElementTagType.sql" />
    <Build Include="BPStaging\Tables\ElementTagGroup.sql" />
    <Build Include="ref\Tables\ElementTagGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementTagGroup.sql" />
    <Build Include="ods\Views\vw_ref_ClassOfBusiness.sql" />
    <Build Include="ods\Views\vw_ref_LineOfBusiness.sql" />
    <Build Include="ods\Views\vw_ref_Trigger.sql" />
    <Build Include="ods\Views\vw_ref_CoverageType.sql" />
    <Build Include="ods\Views\vw_ref_Industry.sql" />
    <Build Include="ods\Views\vw_erd_Country.sql" />
    <Build Include="ods\Views\vw_erd_GeographyGroup.sql" />
    <Build Include="ods\Views\vw_erd_GeographyGroupingScheme.sql" />
    <Build Include="ods\Views\vw_erd_GeographyGroupMembership.sql" />
    <Build Include="ods\Views\vw_erd_CountrySubdivision.sql" />
    <Build Include="ods\Views\vw_erd_City.sql" />
    <Build Include="Security\Schemas\psapi.sql" />
    <Build Include="rpt\views\vw_as_PolicyAudit.sql" />
    <Build Include="BPStaging\Tables\ContractDocumentBase.sql" />
    <Build Include="BPStaging\Tables\ElementType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_ElementType.sql" />
    <Build Include="BPStaging\Tables\ElementAttributeType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementAttributeType.sql" />
    <Build Include="PS\Tables\RiskProfile.sql" />
    <Build Include="BPStaging\Tables\RiskStructure.sql" />
    <Build Include="BPStaging\Tables\SelectedReason.sql" />
    <Build Include="BPStaging\Tables\PlacementSystemUser.sql" />
    <Build Include="BPStaging\Tables\RiskLocation.sql" />
    <Build Include="BPStaging\Tables\TerritoryCountry.sql" />
    <Build Include="BPStaging\Tables\MarketKind.sql" />
    <Build Include="BPStaging\Tables\ExtendedReportingPeriod.sql" />
    <Build Include="BPStaging\Tables\JustificationReasonType.sql" />
    <Build Include="BPStaging\Tables\JustificationReason.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadSelectedReason.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementSystemUser.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadRiskLocation.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadTerritoryCountry.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_MarketKind.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadExtendedReportingPeriod.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPaymentPeriod.sql" />
    <Build Include="BPStaging\Tables\PaymentPeriod.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadJustificationReasonType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadJustificationReason.sql" />
    <Build Include="ods\Views\vw_erd_Currency.sql" />
    <Build Include="ods\Views\vw_ref_MarketingDecision.sql" />
    <Build Include="ods\Views\vw_ref_Product.sql" />
    <Build Include="devops\Stored Procedures\DropUnnamedConstraints.sql" />
    <Build Include="rpt\Tables\Date.sql" />
    <Build Include="rpt\views\vw_as_PrimaryInsuredDetails.sql" />
    <Build Include="rpt\Tables\PolicyAudit.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_PolicyAudit.sql" />
    <Build Include="BPStaging\Tables\MarketResponseBasis.sql" />
    <Build Include="PS\Tables\MarketResponseBasis.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketResponse.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketResponseBasis.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_Negotiation.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_NegotiationMarket.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_RiskProfile.sql" />
    <Build Include="CMSStaging\Tables\ref_BUHierarchy.sql" />
    <Build Include="CMSStaging\Tables\ref_BusinessType.sql" />
    <Build Include="CMSStaging\Tables\ref_CompanyOwnership.sql" />
    <Build Include="CMSStaging\Tables\ref_Currency.sql" />
    <Build Include="CMSStaging\Tables\ref_DistributionChannel.sql" />
    <Build Include="CMSStaging\Tables\ref_ExceptionType.sql" />
    <Build Include="CMSStaging\Tables\ref_ExposureMeasure.sql" />
    <Build Include="CMSStaging\Tables\ref_GeographyGroup.sql" />
    <Build Include="CMSStaging\Tables\ref_IndustryGroup.sql" />
    <Build Include="CMSStaging\Tables\ref_Industry.sql" />
    <Build Include="CMSStaging\Tables\ref_GeographyGroupMembership.sql" />
    <Build Include="CMSStaging\Tables\ref_MarketingDecision.sql" />
    <Build Include="CMSStaging\Tables\ref_OutcomeReason.sql" />
    <Build Include="CMSStaging\Tables\ref_PlacementStatus.sql" />
    <Build Include="CMSStaging\Tables\ref_PlacementType.sql" />
    <Build Include="CMSStaging\Tables\ref_PricingFactor.sql" />
    <Build Include="CMSStaging\Tables\ref_Product.sql" />
    <Build Include="CMSStaging\Tables\ref_QuoteOutcome.sql" />
    <Build Include="CMSStaging\Tables\ref_RenewabilityStatus.sql" />
    <Build Include="CMSStaging\Tables\ref_RenewalStatus.sql" />
    <Build Include="CMSStaging\Tables\ref_SubProduct.sql" />
    <Build Include="CMSStaging\Tables\dbo_Policy.sql" />
    <Build Include="CMSStaging\Tables\dbo_QPF.sql" />
    <Build Include="CMSStaging\Tables\dbo_AppetiteResponse.sql" />
    <Build Include="CMSStaging\Tables\dbo_Placement.sql" />
    <Build Include="CMSStaging\Tables\dbo_Layer.sql" />
    <Build Include="CMSStaging\Tables\dbo_PolicyLayerLink.sql" />
    <Build Include="CMSStaging\Tables\dbo_RiskExposure.sql" />
    <Build Include="CMSStaging\Stored Procedures\Load_ref_PlacementStatus.sql" />
    <Build Include="CMSStaging\Stored Procedures\Load_ref_IndustryGroup.sql" />
    <Build Include="CMSStaging\Stored Procedures\Load_dbo_Placement.sql" />
    <Build Include="CMSStaging\Stored Procedures\Load_dbo_PlacementPolicy.sql" />
    <None Include="Scripts\reference\MarineMar\MarineDataMapping.sql" />
    <None Include="Scripts\reference\MarineMar\SpecieDataMapping.sql" />
    <Build Include="rpt\views\vw_as_PlacementProduct.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergePartyAttributestoParty.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_Contract.sql" />
    <Build Include="rpt\Stored Procedures\LoadContractDocument.sql" />
    <Build Include="rpt\Tables\Contract.sql" />
    <Build Include="rpt\Tables\ContractDocument.sql" />
    <Build Include="rpt\views\vw_as_Contract.sql" />
    <Build Include="rpt\views\vw_as_ContractClausesAndConditions.sql" />
    <Build Include="rpt\views\vw_as_ContractLimits.sql" />
    <Build Include="rpt\views\vw_as_ContractRisk.sql" />
    <Build Include="rpt\views\vw_as_Coverage.sql" />
    <Build Include="rpt\views\vw_as_NACommissionRate.sql" />
    <Build Include="PACTStaging\Views\vwProduct.sql" />
    <Build Include="PACTStaging\Stored Procedures\UpdateProductWithAttributes.sql" />
    <Build Include="PS\Tables\CarrierHierarchyExtended.sql" />
    <Build Include="PS\StoredProcedure\LoadCarrierHierarchyExtended.sql" />
    <Build Include="rpt\views\vw_as_PlacementExposureSummary.sql" />
    <Build Include="ref\Tables\Industry.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_Industry.sql" />
    <Build Include="ods\Views\vw_ref_TimeZone.sql" />
    <Build Include="rpt\views\vw_as_AuditDetails.sql" />
    <Build Include="PS\Tables\RiskStructure.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_RiskStructure.sql" />
    <Build Include="ods\Views\vw_ps_RiskStructurePolicy.sql" />
    <Build Include="ods\Views\vw_ps_RiskStructure.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_Staging_RiskStructurePolicy.sql" />
    <Build Include="BPStaging\Tables\PS_Staging_RiskStructurePolicy.sql" />
    <Build Include="BPStaging\Tables\PS_Staging_ContractRiskStructure.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_RiskStructurePolicy.sql" />
    <Build Include="PS\Tables\RiskStructurePolicy.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_RiskStructureContract.sql" />
    <Build Include="PS\Tables\RiskStructureContract.sql" />
    <Build Include="ods\Views\vw_ps_RiskStructureContract.sql" />
    <Build Include="PS\Tables\ElementTagSummary.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ElementTagSummary.sql" />
    <Build Include="ref\Tables\Facility.sql" />
    <Build Include="ref\Tables\FacilitySection.sql" />
    <Build Include="ref\Tables\FacilitySectionCarrier.sql" />
    <Build Include="PACTStaging\Stored Procedures\LoadFacility.sql" />
    <Build Include="PACTStaging\Stored Procedures\LoadFacilitySection.sql" />
    <Build Include="PACTStaging\Stored Procedures\LoadFacilitySectionCarrier.sql" />
    <Build Include="rpt\Tables\MarketInteractionResponse.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_MarketInteractionResponse.sql" />
    <Build Include="rpt\views\vw_as_MarketInteractionResponse.sql" />
    <Build Include="rpt\Tables\SubmissionMarketSelection.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_SubmissionMarketSelection.sql" />
    <Build Include="rpt\views\vw_as_SubmissionMarketSelection.sql" />
    <Build Include="rpt\Tables\PlacementSecurity.sql" />
    <Build Include="rpt\Tables\MarketActivity.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_MarketActivity.sql" />
    <Build Include="rpt\Tables\UserSecurity.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_UserSecurity.sql" />
    <Build Include="rpt\views\vw_as_PlacementSecurity.sql" />
    <Build Include="rpt\views\vw_as_MarketActivity.sql" />
    <Build Include="rpt\views\vw_as_UserSecurity.sql" />
    <Build Include="rpt\Tables\AuditUser.sql" />
    <Build Include="rpt\Stored Procedures\LoadAuditUser.sql" />
    <Build Include="rpt\views\vw_as_AuditUser.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_Market.sql" />
    <Build Include="rpt\Tables\Market.sql" />
    <Build Include="rpt\Tables\PlacementPrimaryRole.sql" />
    <Build Include="rpt\Stored Procedures\LoadPlacementPrimaryRole.sql" />
    <Build Include="rpt\views\vw_as_PlacementSystemRole.sql" />
    <Build Include="rpt\views\vw_as_PlacementSystemUsers.sql" />
    <Build Include="rpt\views\vw_as_PolicyPremium.sql" />
    <Build Include="rpt\views\vw_as_PolicyTeam.sql" />
    <Build Include="rpt\views\vw_as_Product.sql" />
    <Build Include="rpt\views\vw_as_BrazilClient.sql" />
    <Build Include="rpt\views\vw_as_Layers.sql" />
    <Build Include="rpt\views\vw_as_PolicyOrganisation.sql" />
    <Build Include="rpt\views\vw_as_Market.sql" />
    <Build Include="rpt\views\vw_as_PlacementPrimaryRole.sql" />
    <Build Include="FMATemp\Tables\staging_vw_ServicingPlatform.sql" />
    <Build Include="FMATemp\StoredProcedures\StageServicingPlatform.sql" />
    <Build Include="FMATemp\Tables\staging_vw_User.sql" />
    <Build Include="FMATemp\StoredProcedures\StageUser.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Translation.sql" />
    <Build Include="FMATemp\Tables\staging_vw_LookupGroup.sql" />
    <Build Include="FMATemp\StoredProcedures\StageLookupGroup.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Lookup.sql" />
    <Build Include="FMATemp\StoredProcedures\StageLookup.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Industry.sql" />
    <Build Include="FMATemp\StoredProcedures\StageIndustry.sql" />
    <Build Include="FMATemp\Tables\staging_vw_PolicyStatus.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePolicyStatus.sql" />
    <Build Include="FMATemp\StoredProcedures\StageParty.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePartyAddress.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierStatus.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierStatus.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierType.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierType.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierExtended.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierRelationship.sql" />
    <Build Include="FMATemp\StoredProcedures\StageAgency.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierRating.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierRestrictionDefinition.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierRestrictionDefinition.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierRatingOutlookDefinition.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierRatingOutlookDefinition.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierRestriction.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierAgencyRating.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierTOBA.sql" />
    <Build Include="FMATemp\StoredProcedures\StageProduct.sql" />
    <Build Include="FMATemp\StoredProcedures\StageFacility.sql" />
    <Build Include="FMATemp\StoredProcedures\StageFacilitySection.sql" />
    <Build Include="FMATemp\StoredProcedures\StageFacilitySectionCarrier.sql" />
    <Build Include="FMATemp\StoredProcedures\StageFacilityCarrier.sql" />
    <Build Include="FMATemp\StoredProcedures\StageTranslation.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Office.sql" />
    <Build Include="FMATemp\StoredProcedures\StageOffice.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierStatusOverride.sql" />
    <Build Include="FMATemp\Tables\staging_vw_OfficeAddress.sql" />
    <Build Include="FMATemp\StoredProcedures\StageOfficeAddress.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierQIRating.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierQIRating.sql" />
    <Build Include="BPStaging\Tables\ClientProposal.sql" />
    <Build Include="BPStaging\Tables\ClientProposalCoverage.sql" />
    <Build Include="BPStaging\Tables\ClientProposalCoverageNotice.sql" />
    <Build Include="BPStaging\Tables\ClientProposalPlacement.sql" />
    <Build Include="BPStaging\Tables\ClientProposalQuoteComparison.sql" />
    <Build Include="BPStaging\Tables\QuoteComparison.sql" />
    <Build Include="BPStaging\Tables\QuoteComparisonQuote.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ClientProposal.sql" />
    <Build Include="PS\Tables\ClientProposal.sql" />
    <Build Include="PS\Tables\ClientProposalCoverage.sql" />
    <Build Include="PS\Tables\ClientProposalCoverageNotice.sql" />
    <Build Include="PS\Tables\ClientProposalQuoteComparison.sql" />
    <Build Include="PS\Tables\QuoteComparison.sql" />
    <Build Include="PS\Tables\QuoteComparisonQuote.sql" />
    <Build Include="PS\Tables\ClientProposalPlacement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ClientProposalCoverage.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ClientProposalCoverageNotice.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ClientProposalPlacement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ClientProposalQuoteComparison.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_QuoteComparison.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_QuoteComparisonQuote.sql" />
    <Build Include="APIv1\Stored Procedures\StartRenewalRun.sql" />
    <Build Include="BPStaging\Tables\PlacementRuleValidation.sql" />
    <Build Include="BPStaging\Tables\ValidationRuleOutcome.sql" />
    <Build Include="BPStaging\Tables\AppetiteLevel.sql" />
    <Build Include="BPStaging\Tables\AppetiteNarrative.sql" />
    <Build Include="ref\Tables\PanelMemberFacility.sql" />
    <Build Include="ref\Tables\AppetiteLevel.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_AppetiteLevel.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_AppetiteNarrative.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_PlacementRuleValidation.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ValidationRuleOutcome.sql" />
    <Build Include="ods\Views\vw_ref_PanelMemberFacility.sql" />
    <Build Include="ods\Views\vw_ref_AppetiteLevel.sql" />
    <Build Include="ods\Views\vw_ps_AppetiteNarrative.sql" />
    <Build Include="ods\Views\vw_ps_ValidationRuleOutcome.sql" />
    <Build Include="ods\Views\vw_ps_PlacementRuleValidation.sql" />
    <Build Include="BPStaging\Tables\PlacementSystemAudit.sql" />
    <Build Include="dbo\Tables\PlacementSystemAudit.sql" />
    <Build Include="rpt\views\vw_as_ContractTimeline.sql" />
    <Build Include="ods\Views\vw_ps_PlacementExposure.sql" />
    <Build Include="ods\Views\vw_ps_RiskProfileAttribute.sql" />
    <Build Include="rpt\views\vw_as_ContractHeader.sql" />
    <Build Include="rpt\views\vw_as_ContractNegotiation.sql" />
    <Build Include="BPStaging\Views\vw_ElementTagInclusionRule_Export.sql" />
    <None Include="Scripts\reference\PactConfig\SourceSystem.sql" />
    <Build Include="BPStaging\Tables\PricingFactor.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_PricingFactor.sql" />
    <Build Include="ref\Tables\PricingFactor.sql" />
    <Build Include="ods\Views\vw_ref_PricingFactor.sql" />
    <Build Include="PS\Tables\AppetiteNarrative.sql" />
    <Build Include="ods\Views\vw_ps_AppetiteResponse.sql" />
    <Build Include="PS\Tables\AppetiteResponse.sql" />
    <Build Include="PS\Tables\PlacementRuleValidation.sql" />
    <Build Include="PS\Tables\ValidationRuleOutcome.sql" />
    <Build Include="ods\Views\vw_ref_NotRemarketingReason.sql" />
    <Build Include="ods\Views\vw_ref_LegalEntity.sql" />
    <Build Include="ods\Views\vw_ref_ProgramStructureType.sql" />
    <Build Include="rpt\views\vw_as_Party.sql" />
    <Build Include="PACTStaging\Stored Procedures\UpdatePolicyWithCUP.sql" />
    <Build Include="Security\Roles\ProgrammestoreODSConsumerRole.sql" />
    <Build Include="BPStaging\Tables\MarketResponseElement.sql" />
    <Build Include="BPStaging\Tables\ExpiringResponseElement.sql" />
    <Build Include="BPStaging\Tables\ExpiringResponseGroup.sql" />
    <Build Include="BPStaging\Tables\ResponseManagementElement.sql" />
    <Build Include="rpt\views\vw_as_Team.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_NegotiationRiskProfile.sql" />
    <Build Include="ods\Views\vw_ps_NegotiationRiskProfile.sql" />
    <Build Include="PS\Tables\NegotiationRiskProfile.sql" />
    <Build Include="rpt\Stored Procedures\LoadProductHierarchy.sql" />
    <Build Include="rpt\Tables\ProductHierarchy.sql" />
    <Build Include="ods\Views\vw_ps_MarketResponseSplit.sql" />
    <None Include="Scripts\reference\ref\OrganisationLegalEntity.sql" />
    <Build Include="ref\Tables\OrganisationLegalEntity.sql" />
    <Build Include="rpt\views\vw_as_PlacementEvents.sql" />
    <Build Include="rpt\views\vw_as_MarketQIRating.sql" />
    <Build Include="ods\Views\vw_ps_ContractsClausesAndConditions.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_AppetiteResponse.sql" />
    <Build Include="ods\Views\vw_erd_EmploymentStatus.sql" />
    <Build Include="ref\Tables\ExposureType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_ExposureType.sql" />
    <Build Include="BPStaging\Tables\ExposurePeriodGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_ExposurePeriod.sql" />
    <Build Include="ref\Tables\ExposurePeriod.sql" />
    <Build Include="PS\StoredProcedure\LoadPlacementSecurity.sql" />
    <Build Include="ods\Views\vw_ps_PlacementSecurity.sql" />
    <Build Include="rpt\views\vw_as_PlacementUserSecurity.sql" />
    <Build Include="rpt\Tables\PlacementProductSummary.sql" />
    <Build Include="rpt\Stored Procedures\LoadPlacementProductSummary.sql" />
    <Build Include="Support\StoredProcedures\UpdatePlacementSetReadyToSendToServicingPlatform.sql" />
    <Build Include="Support\StoredProcedures\DeleteTaskPolicyAudit.sql" />
    <Build Include="Support\Tables\SupportSplog.sql" />
    <Build Include="CAMStaging\Stored Procedures\Load_ref_PanelMemberFacility.sql" />
    <Build Include="CAMStaging\Stored Procedures\Load_ref_Panel.sql" />
    <Build Include="CAMStaging\Stored Procedures\Load_ref_PanelMember.sql" />
    <Build Include="CAMStaging\Stored Procedures\Load_ref_PanelMemberCarrier.sql" />
    <Build Include="CAMStaging\Stored Procedures\Load_ref_PanelMemberContact.sql" />
    <Build Include="CAMStaging\Tables\Panel.sql" />
    <Build Include="CAMStaging\Tables\PanelMember.sql" />
    <Build Include="CAMStaging\Tables\PanelMemberCarrier.sql" />
    <Build Include="CAMStaging\Tables\PanelMemberContact.sql" />
    <Build Include="CAMStaging\Tables\PanelMemberFacility.sql" />
    <Build Include="Security\Schemas\CAMStaging.sql" />
    <Build Include="ods\Views\vw_erd_PartyRole.sql" />
    <Build Include="BPStaging\Tables\ClientProfile.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ClientProfile.sql" />
    <Build Include="PS\Tables\ClientProfile.sql" />
    <Build Include="ods\Views\vw_ps_ClientProfile.sql" />
    <Build Include="COLStaging\Stored Procedures\Load_COLStaging_Product.sql" />
    <Build Include="COLStaging\Stored Procedures\Load_dbo_PartyAttribute.sql" />
    <Build Include="COLStaging\Stored Procedures\Load_dbo_LookupGroup.sql" />
    <Build Include="ods\Views\vw_ps_LookUp.sql" />
    <Build Include="BPStaging\Stored Procedures\CreatePlacementListener.sql" />
    <Build Include="BPStaging\Stored Procedures\UpdateMarketSelectionIsLeadFlag.sql" />
    <Build Include="rpt\views\vw_as_PlacementRiskProfile.sql" />
    <Build Include="rpt\views\vw_as_Policy.sql" />
    <Build Include="rpt\views\vw_as_PolicyProduct.sql" />
    <Build Include="PS\Tables\PlacementExposureGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_PlacementExposureGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_PlacementExposureSummaryGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_PlacementExposureSummary.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ExposureSummaryElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ExposureSummaryElementRiskDefinitionItem.sql" />
    <Build Include="ods\Views\vw_ref_ExposurePeriod.sql" />
    <Build Include="ods\Views\vw_ps_PlacementExposureGroup.sql" />
    <Build Include="ods\Views\vw_ps_ExposureAttribute.sql" />
    <Build Include="ods\Views\vw_ref_ExposureType.sql" />
    <Build Include="ods\Views\vw_ref_ElementAttributeType.sql" />
    <Build Include="PS\Tables\RiskProfilePlacementExposureGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_RiskProfilePlacementExposureGroup.sql" />
    <Build Include="ods\Views\vw_ps_RiskProfilePlacementExposureGroup.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwClientDetails.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwSegmentation.sql" />
    <Build Include="Reference\Tables\Segmentation.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoSegmentation.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeReferencePartyIntoDboParty.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\Load_Reference_ClientDetails.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwParty.sql" />
    <Build Include="ods\Views\vw_erd_Segmentation.sql" />
    <Build Include="ods\Views\vw_erd_Party.sql" />
    <Build Include="ods\Views\vw_erd_SIC87Industry.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\UpdateOperatingRevenueUSDOnParty.sql" />
    <Build Include="ods\Views\vw_ref_VerticalIndustry.sql" />
    <Build Include="Reference\Tables\SIC87IndustryMapping.sql" />
    <None Include="Scripts\reference\reference\SIC87IndustryMapping.sql" />
    <Build Include="ref\Tables\FinancialSegment.sql" />
    <Build Include="ref\Tables\LegalEntity.sql" />
    <Build Include="ref\Tables\FinancialGeography.sql" />
    <Build Include="Reference\Tables\WTWFinancialGeography.sql" />
    <Build Include="Reference\Tables\WTWFinancialGeographyAddress.sql" />
    <Build Include="Reference\Tables\WTWFinancialSegment.sql" />
    <Build Include="Reference\Tables\WTWLegalEntity.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoWTWFinancialGeography.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwWTWFinancialGeography.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwWTWFinancialGeographyAddress.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwWTWFinancialSegment.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwWTWLegalEntity.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoWTWFinancialGeographyAddress.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoWTWFinancialSegment.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoWTWLegalEntity.sql" />
    <Build Include="rpt\Tables\ContractAuditUser.sql" />
    <Build Include="rpt\Stored Procedures\LoadContractAuditUser.sql" />
    <Build Include="rpt\views\vw_as_ContractAuditUser.sql" />
    <Build Include="ods\Views\vw_erd_Product.sql" />
    <Build Include="ods\Views\vw_erd_ProductLine.sql" />
    <Build Include="ods\Views\vw_erd_ProductClass.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ExpiringResponseElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ExpiringResponseGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_MarketResponseElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ResponseManagementElement.sql" />
    <Build Include="ods\Views\vw_ps_MarketResponseAttribute.sql" />
    <Build Include="ref\Tables\NegotiationType.sql" />
    <Build Include="ods\Views\vw_ps_PlacementMarketValidation.sql" />
    <Build Include="PS\StoredProcedure\LoadGlobalPartySecurity.sql" />
    <Build Include="ods\Views\vw_ps_GlobalPartySecurity.sql" />
    <None Include="Scripts\rules\PlacementRenewal.sql" />
    <Build Include="ods\Stored Procedures\GetPipeline.sql" />
    <Build Include="Reference\Tables\VerticalIndustry.sql" />
    <Build Include="BPStaging\Tables\RequestedCoverageElement.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerRequestedCoverageElement.sql" />
    <Build Include="Task\Tables\PolicyRenewalStatusMapping.sql" />
    <Build Include="ADF\Stored Procedures\StartInstance.sql" />
    <Build Include="Security\Schemas\ADF.sql" />
    <Build Include="ADF\Tables\InstanceLog.sql" />
    <Build Include="ADF\Stored Procedures\InitialiseProcessSession.sql" />
    <Build Include="ADF\Tables\Connection.sql" />
    <Build Include="ADF\Tables\ProcessSession.sql" />
    <Build Include="ADF\Stored Procedures\GetEntryProcesses.sql" />
    <Build Include="ADF\Stored Procedures\LogStartEvent.sql" />
    <None Include="Scripts\reference\ADF\Connection.sql" />
    <Build Include="ADF\Function\GetDataSourceInstanceList.sql" />
    <Build Include="ADF\Tables\EventLog.sql" />
    <Build Include="BPStaging\Tables\ElementTypeMetaTag.sql" />
    <Build Include="BPStaging\Tables\MetaTag.sql" />
    <Build Include="BPStaging\Tables\PlacementServiceSatisfactionValidation.sql" />
    <Build Include="BPStaging\Tables\ServiceLevelIssue.sql" />
    <Build Include="BPStaging\Tables\ServiceSatisfactionIssueOutcome.sql" />
    <Build Include="BPStaging\Tables\ServiceLevel.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_RequestedCoverageElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerRequestedCoverageElement.sql" />
    <Build Include="PS\Tables\Specification.sql" />
    <Build Include="PS\Tables\SpecificationAttribute.sql" />
    <Build Include="PS\Tables\NegotiationSpecification.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_Specification.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_NegotiationSpecification.sql" />
    <Build Include="ods\Views\vw_ps_Specification.sql" />
    <Build Include="ods\Views\vw_ps_NegotiationSpecification.sql" />
    <Build Include="ods\Views\vw_ps_SpecificationAttribute.sql" />
    <Build Include="ods\Views\vw_ref_PremiumRange.sql" />
    <Build Include="ref\Tables\PremiumRange.sql" />
    <None Include="Scripts\reference\ref\PremiumRange.sql" />
    <None Include="Scripts\reference\ADF\ProcessType.sql" />
    <Build Include="ADF\Stored Procedures\EndInstance.sql" />
    <None Include="Scripts\reference\ADF\ProcessInterdependency.sql" />
    <Build Include="ADF\Stored Procedures\GetListofPlacementtoSendSB.sql" />
    <Build Include="ADF\Stored Procedures\LogPlacementSentToServiceBus.sql" />
    <Build Include="ref\Tables\SourceQuery.sql" />
    <Build Include="ADF\Stored Procedures\GetRefreshCompleteMessageToSendASB.sql" />
    <Build Include="ADF\Stored Procedures\GenerateSourceSql.sql" />
    <Build Include="ods\Views\vw_ps_ElementTagInclusionRule.sql" />
    <Build Include="PS\Tables\ASBLastReadTime.sql" />
    <Build Include="PS\StoredProcedure\ReadPlacementStatus.sql" />
    <Build Include="PS\StoredProcedure\UpdatePlacementServiceBusCompletion.sql" />
    <Build Include="PS\StoredProcedure\LoadElementTimeline.sql" />
    <Build Include="PS\Tables\ElementTimeline.sql" />
    <Build Include="ADF\Tables\Process.sql" />
    <Build Include="ADF\Tables\ProcessType.sql" />
    <Build Include="ADF\Tables\ProcessInterdependency.sql" />
    <Build Include="ADF\Stored Procedures\GetProcessDetail.sql" />
    <None Include="Scripts\reference\ADF\Process.sql" />
    <Build Include="ADF\Function\ShouldProcessRun.sql" />
    <Build Include="ADF\Stored Procedures\FinaliseProcessSession.sql" />
    <Build Include="ADF\Views\PartitionedTable.sql" />
    <Build Include="ADF\Stored Procedures\RollonPartition.sql" />
    <Build Include="ADF\Stored Procedures\RecycleIdentityYMD.sql" />
    <Build Include="ADF\Stored Procedures\LogEndEvent.sql" />
    <Build Include="ADF\Stored Procedures\LogStepEvent.sql" />
    <Build Include="ADF\Stored Procedures\LogSystemError.sql" />
    <Build Include="ADF\Views\PartitionedTablePartition.sql" />
    <Build Include="Storage\pfr_ADF_EventLog.sql" />
    <Build Include="Storage\ps_ADF_EventLog.sql" />
    <Build Include="Support\StoredProcedures\GetPartitionInfo.sql" />
    <Build Include="Storage\pfr_ADF_ProcessSession.sql" />
    <Build Include="Storage\ps_ADF_ProcessSession.sql" />
    <Build Include="ADF\Stored Procedures\CaptureEventLogXml.sql" />
    <Build Include="ADF\Stored Procedures\GetConnection.sql" />
    <Build Include="ADF\Tables\LoadType.sql" />
    <Build Include="ADF\Tables\InstanceConfig.sql" />
    <None Include="Scripts\reference\dbo\CarrierMappingType.sql" />
    <None Include="Scripts\reference\dbo\CarrierType.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierAgency.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierAgency.sql" />
    <Build Include="ref\Tables\AgencyType.sql" />
    <None Include="Scripts\reference\ref\AgencyType.sql" />
    <Build Include="ADF\Views\vwProcessInterdependency.sql" />
    <Build Include="APIv1\Stored Procedures\UpdatePlacementServiceBusCompletion.sql" />
    <Build Include="Support\StoredProcedures\OverrideProcessLastUpdatedDate.sql" />
    <Build Include="ADF\Tables\ProcessOverride.sql" />
    <Build Include="ADF\Stored Procedures\LogErrorEvent.sql" />
    <Build Include="ADF\Stored Procedures\StartLogAudit.sql" />
    <Build Include="ADF\Tables\Audit.sql" />
    <Build Include="ADF\Stored Procedures\EndLogAudit.sql" />
    <Build Include="ADF\Stored Procedures\GenerateExecuteForStoredProcedure.sql" />
    <Build Include="ReferenceStaging\Tables\ServiceBusMDSInterimTable_Br.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\StageServiceBusMDSInterimTable_Br.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\UpdateSentForMappingDateOnProduct.sql" />
    <Build Include="Support\StoredProcedures\SetBPPolicyReload.sql" />
    <Build Include="ADF\Stored Procedures\SetLoadType.sql" />
    <Build Include="FMATemp\StoredProcedures\TruncateTranslation.sql" />
    <Build Include="Rules\Stored Procedures\EnabledDataSources_Run_IncludeForRenewal.sql" />
    <Build Include="ADF\Stored Procedures\GenerateServiceBusMessage.sql" />
    <Build Include="Support\StoredProcedures\SetLoadType.sql" />
    <Build Include="ADF\Views\vwActiveProcessInterdependencyInternal.sql" />
    <Build Include="ADF\Views\vwActiveProcessInterdependency.sql" />
    <Build Include="ADF\Stored Procedures\RolloffPartition.sql" />
    <Build Include="ADF\Tables\PartitionConfig.sql" />
    <None Include="Scripts\reference\ADF\PartitionConfig.sql" />
    <Build Include="rpt\views\vw_as_ContractBuilderElementAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_PlacementServiceSatisfactionValidation.sql" />
    <Build Include="PS\Tables\PlacementServiceSatisfactionValidation.sql" />
    <Build Include="ref\Tables\ServiceLevel.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadServiceLevel.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadServiceLevelIssue.sql" />
    <Build Include="ref\Tables\ServiceLevelIssue.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ServiceSatisfactionIssueOutcome.sql" />
    <Build Include="PS\Tables\ServiceSatisfactionIssueOutcome.sql" />
    <Build Include="ods\Views\vw_ref_ServiceLevelIssue.sql" />
    <Build Include="ods\Views\vw_ref_ServiceLevel.sql" />
    <Build Include="ods\Views\vw_ps_PlacementServiceSatisfactionValidation.sql" />
    <Build Include="ods\Views\vw_ps_ServiceSatisfactionIssueOutcome.sql" />
    <Build Include="BPStaging\Tables\ActionValidationRule.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ActionValidationRule.sql" />
    <Build Include="PS\Tables\ActionValidationRule.sql" />
    <Build Include="ADF\Function\GetUpdatedDateWhenDependentsAllSuccessful.sql" />
    <Build Include="Support\StoredProcedures\OverrideProcessJSONConfig.sql" />
    <Build Include="Support\StoredProcedures\Instance_Help.sql" />
    <Build Include="PS\StoredProcedure\UpdateTranslationLanguage.sql" />
    <Build Include="ADF\Tables\ProcessSessionStatus.sql" />
    <Build Include="ADF\Function\PSS_Pending.sql" />
    <Build Include="ADF\Function\PSS_Starting.sql" />
    <Build Include="ADF\Function\PSS_Running.sql" />
    <Build Include="ADF\Function\PSS_Succeeded.sql" />
    <Build Include="ADF\Function\PSS_Failed.sql" />
    <Build Include="ADF\Stored Procedures\ProcessSessionRunning.sql" />
    <Build Include="ADF\Stored Procedures\ProcessSessionStarting.sql" />
    <Build Include="ADF\Tables\InstanceProcessInterdependency.sql" />
    <Build Include="Support\StoredProcedures\GetProcessMermaid.sql" />
    <Build Include="Support\StoredProcedures\GetProcessMermaid_Recursive.sql" />
    <Build Include="ADF\Views\vwProcessSession.sql" />
    <Build Include="ADF\Stored Procedures\GetNextProcessesToInvoke.sql" />
    <Build Include="Support\StoredProcedures\UpdateProcessIsDisabled.sql" />
    <Build Include="devops\Functions\IsUnitTestEnv.sql" />
    <Build Include="ADF\Stored Procedures\ProcessSessionTruncateSinkTable.sql" />
    <Build Include="CMAStaging\Stored Procedures\LoadCarrierQIData.sql" />
    <Build Include="CMAStaging\Stored Procedures\LoadQITeamMapping.sql" />
    <Build Include="CMAStaging\Tables\ExecTable.sql" />
    <Build Include="CMAStaging\Tables\QITeamMapping.sql" />
    <Build Include="Security\Schemas\CMAStaging.sql" />
    <Build Include="PS\Tables\CarrierQIData.sql" />
    <Build Include="ADF\Stored Procedures\SprocExecutionLogStart.sql" />
    <Build Include="ADF\Stored Procedures\SprocExecutionLogEnd.sql" />
    <Build Include="ADF\Stored Procedures\SprocExecutionLogUpdate.sql" />
    <Build Include="ADF\Stored Procedures\SprocExecutionLogError.sql" />
    <Build Include="ADF\Stored Procedures\ProcessSessionCheckSprocLog.sql" />
    <Build Include="ADF\Stored Procedures\ErrorLogAudit.sql" />
    <Build Include="ADF\Function\PSS_Skipped.sql" />
    <Build Include="psapi\Stored Procedures\GetMappedValues.sql" />
    <Build Include="psapi\Tables\SourceValueMapping.sql" />
    <Build Include="Security\Roles\PlacementStoreAPIConsumerRole.sql" />
    <None Include="Scripts\reference\psapi\ValueMapping_NA.sql" />
    <None Include="Scripts\reference\psapi\ValueMapping.sql" />
    <None Include="Scripts\reference\psapi\ValueMapping_BP.sql" />
    <Build Include="devops\Functions\IsUatEnv.sql" />
    <Build Include="devops\Functions\IsIatEnv.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CarrierAdditionalData.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCarrierAdditionalData.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwPartyCheckResultHistory.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwCheckType.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwPartyVerificationStatus.sql" />
    <Build Include="ReferenceStaging\Tables\rpt_vwAuthorizationStatus.sql" />
    <Build Include="Reference\Tables\AuthorizationStatus.sql" />
    <Build Include="Reference\Tables\CheckType.sql" />
    <Build Include="Reference\Tables\PartyCheckResultHistory.sql" />
    <Build Include="Reference\Tables\PartyVerificationStatus.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoCheckType.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoAuthorizationStatus.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoPartyCheckResultHistory.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoPartyVerificationStatus.sql" />
    <Build Include="FMATemp\Tables\staging_vw_PartyCheckResult.sql" />
    <Build Include="FMATemp\StoredProcedures\StagePartyCheckResult.sql" />
    <Build Include="APIv1\Views\PartyCheckResult.sql" />
    <Build Include="dbo\Tables\DocumentTemplateElement.sql" />
    <Build Include="BPStaging\Tables\DocumentTemplateElement.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadDocumentTemplateElement.sql" />
    <None Include="Scripts\reference\dbo\AddFMAProducts.sql" />
    <None Include="Scripts\reference\dbo\PopulateBrazilLookupData.sql" />
    <None Include="Scripts\reference\dbo\BusinessContext.sql" />
    <None Include="Scripts\reference\dbo\CarrierAlias.sql" />
    <None Include="Scripts\reference\RefConfig\CodeList.sql" />
    <None Include="Scripts\reference\dbo\ReasonGroup.sql" />
    <None Include="Scripts\reference\dbo\ReasonGroupMapping.sql" />
    <None Include="Scripts\reference\dbo\IndexationType.sql" />
    <None Include="Scripts\reference\dbo\IndustryType.sql" />
    <None Include="Scripts\reference\reference\NotRemarketingReason.sql" />
    <None Include="Scripts\reference\dbo\PlacementCompletionStatus.sql" />
    <None Include="Scripts\reference\dbo\PlacementPolicyRelationshipType.sql" />
    <None Include="Scripts\reference\dbo\PlacementSystemStatus.sql" />
    <None Include="Scripts\reference\reference\WillisCoreCountry.sql" />
    <None Include="Scripts\reference\reference\WillisCoreCountrySub.sql" />
    <None Include="Scripts\reference\dbo\RefTranslations.en-GB.sql" />
    <None Include="Scripts\rules\RenewalPlacementDataSourceInstance.sql" />
    <None Include="Scripts\reference\PactConfig\RuleType.sql" />
    <None Include="Scripts\reference\PactConfig\RunType.sql" />
    <None Include="Scripts\reference\PactConfig\rptAttribute.sql" />
    <None Include="Scripts\reference\SignetStaging\CarrierStatus.sql" />
    <None Include="Scripts\reference\RefConfig\IncludedColumns.sql" />
    <None Include="Scripts\reference\RefConfig\IncludedLocales.sql" />
    <None Include="Scripts\reference\RefConfig\IncludedTables.sql" />
    <None Include="Scripts\reference\PactConfig\EnsureOverrideFlagsExist.sql" />
    <None Include="Scripts\reference\rpt\Date.sql" />
    <Build Include="ADF\Tables\ErrorLog.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadAppraisalType.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadBrokingSegment.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadLayerType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_CancellationReason.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadRegion.sql" />
    <Build Include="BPStaging\Tables\AppraisalType.sql" />
    <Build Include="BPStaging\Tables\BrokingSegment.sql" />
    <Build Include="BPStaging\Tables\LayerType.sql" />
    <Build Include="BPStaging\Tables\PlacementCancellationReason.sql" />
    <Build Include="BPStaging\Tables\Region.sql" />
    <Build Include="COLStaging\Stored Procedures\Load_dbo_Product.sql" />
    <Build Include="PS\Tables\RiskStructureMarketResponse.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_RiskStructureMarketResponse.sql" />
    <Build Include="SignetStaging\Stored Procedures\ApplyCarrierDataMasking.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCountry.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCountrySubdivision.sql" />
    <Build Include="FMATemp\StoredProcedures\StageCurrency.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Country.sql" />
    <Build Include="FMATemp\Tables\staging_vw_CountrySubdivision.sql" />
    <Build Include="FMATemp\Tables\staging_vw_Currency.sql" />
    <Build Include="PS\Tables\ContractRiskProfile.sql" />
    <Build Include="Reference\Tables\ApprovedCarrier.sql" />
    <Build Include="SignetStaging\Stored Procedures\LoadApprovedCarrier.sql" />
    <Build Include="SignetStaging\Tables\AssociatedCarriersRIMS.sql" />
    <Build Include="PS\Views\vwSpecificationLayerFlat.sql" />
    <Build Include="PS\Tables\RiskStructureSpecification.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_RiskStructureSpecification.sql" />
    <Build Include="ods\Views\vw_ps_RiskStructureSpecification.sql" />
    <Build Include="rpt\views\vw_as_UserRole.sql" />
    <Build Include="rpt\views\vw_as_RelatedPlacements.sql" />
    <Build Include="APIv1\Functions\fn_PolicyCarriers.sql" />
    <Build Include="PACTStaging\Stored Procedures\UpdateProductWithParentProduct.sql" />
    <Build Include="Support\StoredProcedures\DeleteRelatedData.sql" />
    <Build Include="Support\StoredProcedures\RemoveOldPolicyData.sql" />
    <Build Include="Support\Types\TableOfIdsType.sql" />
    <Build Include="Support\StoredProcedures\DeleteExchangeRate.sql" />
    <Build Include="BPStaging\Tables\MarketQuoteResponse.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketQuoteResponseLegacy.sql" />
    <Build Include="PS\Tables\MarketQuoteResponse.sql" />
    <Build Include="ods\Views\vw_ps_MarketResponsePlacementPolicy.sql" />
    <Build Include="BPStaging\Tables\VerticalIndustry.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_Reference_VerticalIndustry.sql" />
    <Build Include="PACTStaging\Tables\eGlobal_PLH_POOLHEADER.sql" />
    <Build Include="PACTStaging\Tables\eGlobal_POL_POOLS.sql" />
    <Build Include="BPStaging\Tables\PlacementStatus.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementStatus.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwFinancialStructureAttribute.sql" />
    <Build Include="PACTStaging\Stored Procedures\Load_ref_FinancialStructureAttribute.sql" />
    <Build Include="ref\Tables\FinancialStructureAttribute.sql" />
    <Build Include="Support\StoredProcedures\RemoveDisabledSourcePolicyData.sql" />
    <Build Include="Rules\Stored Procedures\Run_PlacementRenewal.sql" />
    <Build Include="Support\StoredProcedures\DeletePolicyOrganisationDuplicate.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketQuoteResponse.sql" />
    <Build Include="ADF\Stored Procedures\Maintenance.sql" />
    <Build Include="BPStaging\Tables\OpportunityPlacement.sql" />
    <Build Include="dbo\Tables\PlacementOpportunity.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementOpportunity.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_ElementAttributeReferenceOption.sql" />
    <Build Include="BPStaging\Tables\ElementAttributeReferenceOption.sql" />
    <Build Include="ods\Views\vw_ref_OptionReference.sql" />
    <Build Include="ref\Tables\ElementAttributeReferenceOption.sql" />
    <Build Include="Task\StoredProcedures\ReprocessLastNumberOfDaysPolicies.sql" />
    <Build Include="PS\Tables\PlacementStatusHistory.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwProductAttribute.sql" />
    <Build Include="PS\StoredProcedure\GetPlacementDetails.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_PlacementStatusHistory.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_InsuredType.sql" />
    <Build Include="BPStaging\Tables\InsuredType.sql" />
    <Build Include="ref\Tables\InsuredType.sql" />
    <Build Include="ref\Tables\InsuredTypeMapping.sql" />
    <Build Include="Support\StoredProcedures\UpdatePlacementListener.sql" />
    <Build Include="BPStaging\Tables\AdditionalDataItem.sql" />
    <Build Include="PS\Tables\LibAdditionalDataItem.sql" />
    <Build Include="BPStaging\Tables\LibAdditionalDataItem.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_LibAdditionalDataItem.sql" />
    <Build Include="PS\Tables\AdditionalDataItem.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_AdditionalDataItem.sql" />
    <Build Include="ods\Views\vw_ps_AdditionalDataItem.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractDocumentBase.sql" />
    <Build Include="PS\Tables\ContractDocumentBase.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_Contract.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ElementTag.sql" />
    <Build Include="PS\Tables\ElementTag.sql" />
    <Build Include="PS\Tables\Element.sql" />
    <Build Include="PS\Tables\Contract.sql" />
    <Build Include="PS\Tables\FinancialSegmentHierarchyTable.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_MTAType.sql" />
    <Build Include="BPStaging\Tables\MTAType.sql" />
    <Build Include="ods\Views\vw_ref_MTAType.sql" />
    <Build Include="ref\Tables\MTAType.sql" />
    <Build Include="ods\Views\vw_ps_ContractVersion.sql" />
    <Build Include="PS\Tables\ContractEndorsement.sql" />
    <Build Include="ods\Views\vw_ps_ContractEndorsement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractEndorsement.sql" />
    <Build Include="ods\Views\vw_ps_ContractProgramme.sql" />
    <Build Include="PS\Tables\ContractAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractAttribute.sql" />
    <Build Include="ods\Views\vw_ps_ContractAttribute.sql" />
    <Build Include="BPStaging\Tables\ElementCompositionLinkedElement.sql" />
    <Build Include="BPStaging\Tables\ElementCompositionLinkedElementBranch.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ElementCompositionLinkedElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ElementCompositionLinkedElementBranch.sql" />
    <Build Include="PS\Tables\ElementCompositionLinkedElement.sql" />
    <Build Include="PS\Tables\ElementCompositionLinkedElementBranch.sql" />
    <Build Include="BPStaging\Tables\BoundPosition.sql" />
    <Build Include="BPStaging\Tables\BoundPositionPlacementPolicy.sql" />
    <Build Include="BPStaging\Tables\BoundPositionType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_BoundPosition.sql" />
    <Build Include="ref\Tables\BoundPositionType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_BoundPositionPlacementPolicy.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_BoundPositionType.sql" />
    <Build Include="PS\Tables\QITeamMapping.sql" />
    <Build Include="Support\StoredProcedures\TurnOnDisabledForeignKeys.sql" />
    <Build Include="rpt\views\vw_as_AuditDetailsElement.sql" />
    <Build Include="dbo\Views\vwSubmissionContainer.sql" />
    <Build Include="dbo\Views\vwSubmissionMarket.sql" />
    <Build Include="dbo\Views\vwMarketResponse.sql" />
    <Build Include="dbo\Views\vwMarketResponseBasis.sql" />
    <Build Include="PS\StoredProcedure\UpdateASBLastReadTime.sql" />
    <Build Include="Support\StoredProcedures\UpdateASBLastReadTime.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_AdjustmentResponseElement.sql" />
    <Build Include="BPStaging\Tables\AdjustmentResponseElement.sql" />
    <Build Include="BPStaging\Tables\AdjustmentResponseGroup.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_AdjustmentResponseGroup.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPolicyAttribute.sql" />
    <Build Include="Task\StoredProcedures\GetRestrictedClassProductDetails.sql" />
    <Build Include="Task\StoredProcedures\GetPolicyClassProductDetails.sql" />
    <Build Include="rpt\views\vw_as_ClassOfBusiness.sql" />
    <Build Include="rpt\views\vw_as_PolicyBrokingRegionMapping.sql" />
    <Build Include="APIv1\Views\PlacementTasks.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ResponseManagementElementRiskDefinitionElement.sql" />
    <Build Include="BPStaging\Tables\ResponseManagementElementRiskDefinitionElement.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_ContractElementAttribute.sql" />
    <Build Include="rpt\Tables\ContractElementAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketQuoteResponseHistoric.sql" />
    <Build Include="dbo\Tables\PolicyMarket.sql" />
    <Build Include="BPStaging\Tables\FollowType.sql" />
    <Build Include="ref\Tables\FollowType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_FollowType.sql" />
    <Build Include="ods\Views\vw_ref_FollowType.sql" />
    <Build Include="PS\Tables\ContractDocumentVersion.sql" />
    <Build Include="ods\Views\vw_ps_ContractProgrammeAttribute.sql" />
    <Build Include="ods\Views\vw_ps_ContractVersionAttribute.sql" />
    <Build Include="ods\Views\vw_ps_ContractEndorsementAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractProgrammeAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractVersionAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractEndorsementAttribute.sql" />
    <Build Include="PS\Tables\ContractEndorsementAttribute.sql" />
    <Build Include="PS\Tables\ContractProgrammeAttribute.sql" />
    <Build Include="PS\Tables\ContractVersionAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ContractStatusHistory.sql" />
    <Build Include="BPStaging\Tables\PolicyRefType.sql" />
    <Build Include="BPStaging\Tables\MarketQuoteResponsePolicyRef.sql" />
    <Build Include="ref\Tables\PolicyRefType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_PolicyRefType.sql" />
    <Build Include="PS\Tables\MarketQuoteResponsePolicyRef.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketQuoteResponsePolicyRef.sql" />
    <Build Include="ods\Views\vw_ref_PolicyRefType.sql" />
    <Build Include="ods\Views\vw_ps_MarketQuoteResponsePolicyRef.sql" />
    <Build Include="BPStaging\Tables\UserGroup.sql" />
    <Build Include="ref\Tables\Team.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_Team.sql" />
    <Build Include="rpt\views\vw_as_RiskAttribute.sql" />
    <Build Include="rpt\views\vw_as_PlacementPartyRole.sql" />
    <Build Include="PS\Tables\ContractClauseAndCondition.sql" />
    <Build Include="PACTStaging\Stored Procedures\UpdatePolicyWithEclipseAuthorizedDatePolicyAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_ProgramStructureType.sql" />
    <Build Include="ref\Tables\ProgramStructureType.sql" />
    <Build Include="rpt\Tables\Organisation.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_Organisation.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_CarrierMatching.sql" />
    <Build Include="rpt\Tables\CarrierMatching.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_dbo_SubmissionDistribution.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_dbo_SubmissionRecipient.sql" />
    <Build Include="PS\Tables\PlacementDatesExclusion.sql" />
    <Build Include="Support\StoredProcedures\UpdatePlacementDatesExclusion.sql" />
    <Build Include="PS\Tables\PlacementDatesExclusionHistory.sql" />
    <Build Include="rpt\views\vw_as_CarrierMatching.sql" />
    <Build Include="PactConfig\Functions\GetControlValue_Int.sql" />
    <Build Include="PactConfig\Stored Procedures\SetControlValue_Int.sql" />
    <Build Include="Task\Tables\PolicyConfigRoleHistory.sql" />
    <Build Include="Task\Tables\EventHistory.sql" />
    <Build Include="Task\Tables\PolicyConfigHistory.sql" />
    <Build Include="Task\Tables\PolicyConfigEventHistory.sql" />
    <Build Include="Task\Tables\MetadataItemHistory.sql" />
    <Build Include="Task\Tables\MetadataGroupItemHistory.sql" />
    <Build Include="Task\Tables\MetadataGroupHistory.sql" />
    <Build Include="Task\Tables\PolicyConfigClassOfBusinessRestrictionListHistory.sql" />
    <Build Include="Task\Tables\PolicyConfigEventClassOfBusinessRestrictionListHistory.sql" />
    <Build Include="Task\Tables\PolicyRenewalStatusMappingHistory.sql" />
    <Build Include="Task\Tables\RestrictionTypeHistory.sql" />
    <Build Include="Task\Tables\ScriptHistory.sql" />
    <Build Include="Task\Tables\TaskConfigHistory.sql" />
    <Build Include="Task\Tables\ConfigHistory.sql" />
    <Build Include="Task\Tables\TaskConfigStatusHistory.sql" />
    <Build Include="Task\Tables\TaskConfigStatusChangeHistory.sql" />
    <Build Include="Task\Tables\ClassOfBusinessListHistory.sql" />
    <Build Include="Task\Tables\ClassOfBusinessItemHistory.sql" />
    <Build Include="devops\Functions\GenerateTemporalHistoryRetention.sql" />
    <Build Include="devops\Stored Procedures\SetTemporalHistoryRetention.sql" />
    <Build Include="Rules\Tables\PlacementServiceBusHistory.sql" />
    <Build Include="dbo\Tables\PlacementListenerHistory.sql" />
    <Build Include="PS\Tables\NegotiationContract.sql" />
    <Build Include="devops\Stored Procedures\UpdateIndex.sql" />
    <Build Include="PS\Tables\NegotiationMarketContract.sql" />
    <Build Include="Eclipse\Tables\UWGroupVersion.sql" />
    <Build Include="Eclipse\Tables\Role.sql" />
    <Build Include="Security\Schemas\Eclipse.sql" />
    <Build Include="Eclipse\Tables\PoolMember.sql" />
    <Build Include="EclipseStaging\Stored Procedures\Load_Eclipse_PoolMember.sql" />
    <Build Include="EclipseStaging\Stored Procedures\Load_Eclipse_Role.sql" />
    <Build Include="EclipseStaging\Stored Procedures\Load_Eclipse_UWGroupVersion.sql" />
    <Build Include="EclipseStaging\Tables\Policy.sql" />
    <Build Include="EclipseStaging\Tables\PolicyBrokerOrder.sql" />
    <Build Include="EclipseStaging\Tables\PolicyMarket.sql" />
    <Build Include="EclipseStaging\Tables\PoolMember.sql" />
    <Build Include="EclipseStaging\Tables\Role.sql" />
    <Build Include="EclipseStaging\Tables\UWGroupVersion.sql" />
    <Build Include="Security\Schemas\PASStaging.sql" />
    <Build Include="PASStaging\Tables\rpt_vwCurrency.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicyStatus.sql" />
    <Build Include="Security\Schemas\PAS.sql" />
    <Build Include="PAS\Tables\Currency.sql" />
    <Build Include="ref\Tables\MetaTag.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_MetaTag.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_ElementTypeMetaTag.sql" />
    <Build Include="ref\Tables\ElementTypeMetaTag.sql" />
    <Build Include="BPStaging\Tables\SubmissionMarket_v2.sql" />
    <Build Include="BPStaging\Tables\Submission.sql" />
    <Build Include="BPStaging\Tables\SubmissionMarketRecipient.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerMarket.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerCarrier.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerFacility.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerPanel.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerPanelMember.sql" />
    <Build Include="BPStaging\Tables\SubmissionContainerThirdPartyMarket.sql" />
    <Build Include="BPStaging\Tables\SubmissionDocument.sql" />
    <Build Include="BPStaging\Tables\SubmissionPortalUser.sql" />
    <Build Include="BPStaging\Tables\PortalUserDocument.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_Organisation.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_OrganisationAddress.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_OrganisationAttribute.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_OrganisationRole.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_Worker.sql" />
    <Build Include="PASStaging\Tables\rpt_vwOrganisation.sql" />
    <Build Include="PASStaging\Tables\rpt_vwOrganisationAddress.sql" />
    <Build Include="PASStaging\Tables\rpt_vwOrganisationAttribute.sql" />
    <Build Include="PASStaging\Tables\rpt_vwOrganisationRole.sql" />
    <Build Include="PASStaging\Tables\rpt_vwWorker.sql" />
    <Build Include="PAS\Tables\Organisation.sql" />
    <Build Include="PAS\Tables\OrganisationAddress.sql" />
    <Build Include="PAS\Tables\OrganisationAttribute.sql" />
    <Build Include="PAS\Tables\OrganisationRole.sql" />
    <Build Include="PAS\Tables\Worker.sql" />
    <Build Include="PS\Tables\MarketResponseElement.sql" />
    <Build Include="PS\Tables\MarketResponseElementAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketResponseElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_MarketResponseElementAttribute.sql" />
    <Build Include="PS\Tables\ExposureElement.sql" />
    <Build Include="PS\Tables\ExposureElementAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ExposureElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_ExposureElementAttribute.sql" />
    <Build Include="BP\Tables\PortalUserDocument.sql" />
    <Build Include="BP\Tables\Submission.sql" />
    <Build Include="BP\Tables\SubmissionContainerCarrier.sql" />
    <Build Include="BP\Tables\SubmissionContainerFacility.sql" />
    <Build Include="BP\Tables\SubmissionContainerMarket.sql" />
    <Build Include="BP\Tables\SubmissionContainerPanel.sql" />
    <Build Include="BP\Tables\SubmissionContainerPanelMember.sql" />
    <Build Include="BP\Tables\SubmissionContainerThirdPartyMarket.sql" />
    <Build Include="BP\Tables\SubmissionDocument.sql" />
    <Build Include="BP\Tables\SubmissionMarket.sql" />
    <Build Include="BP\Tables\SubmissionMarketRecipient.sql" />
    <Build Include="BP\Tables\SubmissionPortalUser.sql" />
    <Build Include="Security\Schemas\BP.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_PortalUserDocument.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerRiskDefinitionItem.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerCarrier.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionPortalUser.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerFacility.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerMarket.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerPanel.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerPanelMember.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionContainerThirdPartyMarket.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionDocument.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionMarket.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_SubmissionMarketRecipient.sql" />
    <Build Include="BP\Tables\AdjustmentResponseElement.sql" />
    <Build Include="BP\Tables\AdjustmentResponseGroup.sql" />
    <Build Include="BP\Tables\ExpiringResponseElement.sql" />
    <Build Include="BP\Tables\ExpiringResponseGroup.sql" />
    <Build Include="BP\Tables\ExposureSummaryElement.sql" />
    <Build Include="BP\Tables\ExposureSummaryElementRiskDefinitionItem.sql" />
    <Build Include="BP\Tables\MarketResponseElement.sql" />
    <Build Include="BP\Tables\PlacementExposureSummary.sql" />
    <Build Include="BP\Tables\PlacementExposureSummaryGroup.sql" />
    <Build Include="BP\Tables\RequestedCoverageElement.sql" />
    <Build Include="BP\Tables\ResponseManagementElement.sql" />
    <Build Include="BP\Tables\ResponseManagementElementRiskDefinitionElement.sql" />
    <Build Include="BP\Tables\SubmissionContainerRequestedCoverageElement.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPartyRole.sql" />
    <None Include="Scripts\reference\reference\PartyRole.sql" />
    <Build Include="PASStaging\Tables\rpt_vwOpportunityType.sql" />
    <Build Include="PASStaging\Tables\rpt_vwInsuranceType.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_InsuranceType.sql" />
    <Build Include="PAS\Tables\InsuranceType.sql" />
    <Build Include="PAS\Tables\PolicySectionAttribute.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicySectionAttribute.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicySectionAttribute.sql" />
    <Build Include="PAS\Tables\PolicyStatus.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicyStatus.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_Currency.sql" />
    <Build Include="PASStaging\Tables\rpt_vwRefInsuranceType.sql" />
    <Build Include="PAS\Tables\RefInsuranceType.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_RefInsuranceType.sql" />
    <Build Include="PASStaging\Tables\rpt_vwServicingRole.sql" />
    <Build Include="PAS\Tables\ServicingRole.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_ServicingRole.sql" />
    <Build Include="PASStaging\Tables\rpt_vwRefPolicyStatus.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_RefPolicyStatus.sql" />
    <Build Include="PAS\Tables\RefPolicyStatus.sql" />
    <Build Include="rpt\Tables\PremiumCategory.sql" />
    <None Include="Scripts\reference\rpt\PremiumCategory.sql" />
    <Build Include="PASStaging\Tables\rpt_vwGeography.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_Geography.sql" />
    <Build Include="PAS\Tables\Geography.sql" />
    <Build Include="PAS\Tables\PolicySectionStatus.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicySectionStatus.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicySectionStatus.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicyType.sql" />
    <Build Include="PAS\Tables\PolicyType.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicyType.sql" />
    <Build Include="PASStaging\Tables\rpt_vwCarrierHierarchy.sql" />
    <Build Include="Reference\Tables\ClientDetails.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPartyAddress.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PartyAddress.sql" />
    <Build Include="ref\Tables\RefInsuranceType.sql" />
    <Build Include="ref\Tables\InsuranceType.sql" />
    <Build Include="dbo\Tables\Geography.sql" />
    <Build Include="dbo\Tables\ServicingRole.sql" />
    <Build Include="dbo\Tables\PolicySectionStatus.sql" />
    <Build Include="dbo\Tables\PolicyStatus.sql" />
    <Build Include="dbo\Tables\PartyAddress.sql" />
    <Build Include="dbo\Tables\PolicyType.sql" />
    <Build Include="BP\Tables\SubmissionContainerRiskDefinitionItem.sql" />
    <Build Include="PACTStaging\Stored Procedures\MergeIntoPartyAddress.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPartyAddress.sql" />
    <Build Include="PACTStaging\Views\vwPolicyPartyRole.sql" />
    <Build Include="PACTStaging\Views\vwPolicySectionFacility.sql" />
    <Build Include="ReferenceStaging\Stored Procedures\MergeIntoDboPartyAddress.sql" />
    <Build Include="PAS\Tables\Party.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_Party.sql" />
    <Build Include="PASStaging\Tables\rpt_vwParty.sql" />
    <Build Include="PAS\Tables\PartyAttribute.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPartyAttribute.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PartyAttribute.sql" />
    <Build Include="PASStaging\Tables\rpt_vwProduct.sql" />
    <Build Include="PAS\Tables\Product.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_Product.sql" />
    <Build Include="PASStaging\Tables\rpt_vwProductAttribute.sql" />
    <Build Include="PAS\Tables\ProductAttribute.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_ProductAttribute.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicy.sql" />
    <Build Include="PAS\Tables\Policy.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_Policy.sql" />
    <Build Include="PAS\Tables\OpportunityType.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_OpportunityType.sql" />
    <Build Include="PASStaging\Tables\rpt_vwLegalEntity.sql" />
    <Build Include="PAS\Tables\LegalEntity.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_LegalEntity.sql" />
    <Build Include="PASStaging\Tables\rpt_vwFinancialGeography.sql" />
    <Build Include="PAS\Tables\FinancialGeography.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_FinancialGeography.sql" />
    <Build Include="PASStaging\Tables\rpt_vwFinancialSegment.sql" />
    <Build Include="PAS\Tables\FinancialSegment.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_FinancialSegment.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicySection.sql" />
    <Build Include="PAS\Tables\PolicySection.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicySection.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicyAttribute.sql" />
    <Build Include="PAS\Tables\PolicyAttribute.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicyAttribute.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicySectionProduct.sql" />
    <Build Include="PAS\Tables\PolicySectionProduct.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicySectionProduct.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicyPartyRole.sql" />
    <Build Include="PAS\Tables\PolicyPartyRole.sql" />
    <Build Include="PAS\Tables\PartyRole.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PartyRole.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicyPartyRole.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicyOrganisationRole.sql" />
    <Build Include="PAS\Tables\PolicyOrganisationRole.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicyOrganisationRole.sql" />
    <Build Include="PASStaging\Tables\rpt_vwPolicyWorkerRole.sql" />
    <Build Include="PAS\Tables\PolicyWorkerRole.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_PolicyWorkerRole.sql" />
    <Build Include="devops\Tables\ScriptDeploymentRegister.sql" />
    <Build Include="PASStaging\Tables\rpt_vwTransactionSummaryUSD.sql" />
    <Build Include="PAS\Tables\TransactionSummaryUSD.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_PAS_TransactionSummaryUSD.sql" />
    <Build Include="PACTStaging\Views\vwPolicySectionCarrier.sql" />
    <Build Include="PS\Tables\Organisation.sql" />
    <Build Include="PAS\Stored Procedures\Load_PS_Organisation.sql" />
    <Build Include="PS\Tables\OrganisationAddress.sql" />
    <Build Include="PS\Tables\OrganisationRelationship.sql" />
    <Build Include="PAS\Stored Procedures\Load_PS_OrganisationAddress.sql" />
    <Build Include="PAS\Stored Procedures\Load_PS_OrganisationRelationship.sql" />
    <Build Include="PS\Tables\OrganisationRole.sql" />
    <Build Include="PAS\Stored Procedures\Load_PS_OrganisationRole.sql" />
    <Build Include="PS\Tables\Worker.sql" />
    <Build Include="PAS\Stored Procedures\Load_PS_Worker.sql" />
    <Build Include="PAS\Stored Procedures\Load_APIv1_OrganisationHierarchyTable.sql" />
    <Build Include="rpt\Tables\SubmissionDistribution.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_SubmissionDistribution.sql" />
    <Build Include="BPStaging\Tables\PortalRejectionReason.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_PortalRejectionReason.sql" />
    <Build Include="BP\Tables\PortalRejectionReason.sql" />
    <Build Include="BP\Tables\ContractEndorsementElementChangeSet.sql" />
    <Build Include="rpt\Tables\Bands.sql" />
    <None Include="Scripts\reference\rpt\Bands.sql" />
    <Build Include="ods\Views\vw_erd_ExchangeRate.sql" />
    <Build Include="BPStaging\Tables\RiskAndAnalyticsRunMapping.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_RiskAndAnalyticsRunMapping.sql" />
    <Build Include="BP\Tables\RiskAndAnalyticsRunMapping.sql" />
    <Build Include="BPStaging\Tables\RiskAndAnalyticsModelType.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_RiskAndAnalyticsModelType.sql" />
    <Build Include="ref\Tables\RiskAndAnalyticsModelType.sql" />
    <Build Include="rpt\Tables\MarketResponseElementAttribute.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_MarketResponseElementAttribute.sql" />
    <Build Include="rpt\views\vw_as_MarketResponseElementAttribute.sql" />
    <Build Include="ref\Tables\PanelMemberCarrier.sql" />
    <Build Include="PS\Tables\SubmissionPortalSummary.sql" />
    <Build Include="BP\Tables\BoundPosition.sql" />
    <Build Include="BP\Tables\BoundPositionPlacementPolicy.sql" />
    <Build Include="ref\Tables\EligibilityRule.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_ref_EligibilityRule.sql" />
    <Build Include="BPStaging\Tables\AutoFollowMarketEligibilityRule.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_AutoFollowMarketEligibilityRule.sql" />
    <Build Include="BP\Tables\AutoFollowMarketEligibilityRule.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_MarketResponseAutoFollowEligibilityRule.sql" />
    <Build Include="BPStaging\Tables\MarketResponseAutoFollowEligibilityRule.sql" />
    <Build Include="BP\Tables\MarketResponseAutoFollowEligibilityRule.sql" />
    <Build Include="ref\Tables\Panel.sql" />
    <Build Include="ref\Tables\PanelMember.sql" />
    <Build Include="ref\Tables\PanelMemberContact.sql" />
    <Build Include="PS\Tables\SpecificationElement.sql" />
    <Build Include="PS\Tables\SpecificationElementAttribute.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_SpecificationElement.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_PS_SpecificationElementAttribute.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_PlacementExposure.sql" />
    <Build Include="rpt\Tables\PlacementExposure.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_ContractLimit.sql" />
    <Build Include="rpt\Tables\ContractLimit.sql" />
    <Build Include="rpt\Tables\ProgrammeContractLimit.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_ProgrammeContractLimit.sql" />
    <Build Include="rpt\Tables\ContractEndorsementLimit.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_ContractEndorsementLimit.sql" />
    <Build Include="rpt\Tables\ContractVersionLimit.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_ContractVersionLimit.sql" />
    <Build Include="ADF\Stored Procedures\StoredProcStartLog.sql" />
    <Build Include="ADF\Stored Procedures\StoredProcEndLog.sql" />
    <Build Include="ADF\Stored Procedures\StoredProcErrorLog.sql" />
    <Build Include="ADF\Stored Procedures\StoredProcSetSqlLog.sql" />
    <Build Include="ADF\Tables\StoredProcLog.sql" />
    <Build Include="ADF\Stored Procedures\StoredProcTrimLog.sql" />
    <Build Include="PS\Tables\ProductAttribute.sql" />
    <Build Include="COLStaging\Stored Procedures\Load_PS_ProductAttribute.sql" />
    <Build Include="COLStaging\Stored Procedures\Load_dbo_Lookup.sql" />
    <Build Include="Rules\Stored Procedures\Load_dbo_PlacementProduct.sql" />
    <Build Include="BPStaging\Tables\RiskRequest.sql" />
    <Build Include="BP\Tables\RiskRequest.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_RiskRequest.sql" />
    <Build Include="PS\StoredProcedure\Load_PS_ContractClauseAndCondition.sql" />
    <Build Include="PS\StoredProcedure\Load_PS_ContractVersionClauseAndCondition.sql" />
    <Build Include="PS\Tables\ContractVersionClauseAndCondition.sql" />
    <Build Include="PS\StoredProcedure\Load_PS_ContractEndorsementClauseAndCondition.sql" />
    <Build Include="PS\Tables\ContractEndorsementClauseAndCondition.sql" />
    <Build Include="PS\StoredProcedure\Load_PS_ContractProgrammeClauseAndCondition.sql" />
    <Build Include="PS\Tables\ContractProgrammeClauseAndCondition.sql" />
    <Build Include="BPStaging\Tables\MarketResponseAffirmationQuestion.sql" />
    <Build Include="BPStaging\Tables\AffirmationQuestion.sql" />
    <Build Include="BPStaging\Tables\AffirmationQuestionSet.sql" />
    <Build Include="BP\Tables\AffirmationQuestion.sql" />
    <Build Include="BP\Tables\AffirmationQuestionSet.sql" />
    <Build Include="BP\Tables\MarketResponseAffirmationQuestion.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_AffirmationQuestion.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_AffirmationQuestionSet.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_MarketResponseAffirmationQuestion.sql" />
    <Build Include="BPStaging\Tables\PlacementAffirmationQuestion.sql" />
    <Build Include="BP\Tables\PlacementAffirmationQuestion.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_PlacementAffirmationQuestion.sql" />
    <Build Include="ref\Tables\ElementType.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_ref_PartyRole.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_RiskAttribute.sql" />
    <Build Include="rpt\Tables\RiskAttribute.sql" />
    <Build Include="ods\Views\vw_ps_PolicyAttribute.sql" />
    <Build Include="BPStaging\Tables\MarketQuoteResponseAcceptedDates.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_ref_RefInsuranceType.sql" />
    <Build Include="ref\Tables\MarketKind.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_ref_InsuranceType.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_ref_OpportunityType.sql" />
    <Build Include="rpt\views\vw_as_SecurityTeam.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_dbo_Carrier_GlobalParent.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_dbo_Carrier_OperatingCompany.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_dbo_Carrier.sql" />
    <Build Include="PS\StoredProcedure\Load_dbo_Carrier.sql" />
    <Build Include="PS\StoredProcedure\Load_dbo_CarrierMapping.sql" />
    <Build Include="Security\Schemas\WIBSStaging.sql" />
    <Build Include="WIBSStaging\Tables\WIBS_PBANAMandCompPrd.sql" />
    <Build Include="WIBSStaging\Stored Procedures\Load_dbo_CarrierMapping.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_ref_LegalEntity.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_ref_FinancialGeography.sql" />
    <Build Include="PASStaging\Stored Procedures\Load_ref_FinancialSegment.sql" />
    <Build Include="PS\StoredProcedure\Load_PS_FinancialSegmentHierarchyTable.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="PlacementStoreDB.LocalDEV.publish.xml" />
    <Build Include="dbo\Tables\RiskLocation.sql" />
    <Build Include="Security\Schemas\PSConfig.sql" />
    <Build Include="Security\Schemas\BPStaging.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementAttributeDelta.sql" />
    <Build Include="PACTStaging\Tables\rpt_vwPolicy.sql" />
    <Build Include="Security\Schemas\PACTStaging.sql" />
    <None Include="PlacementStore_UnitTestDB.LocalDEV.publish.xml" />
    <Build Include="BPStaging\Stored Procedures\LoadElementAttributeCache.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadElementCache.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_dbo_ElementTagCache.sql" />
    <Build Include="dbo\Tables\ElementAttributeCache.sql" />
    <Build Include="dbo\Tables\ElementCache.sql" />
    <Build Include="dbo\Tables\ElementTagCache.sql" />
    <None Include="Scripts\reference\dbo\DataSourceInstanceIdMapping.sql" />
    <None Include="Scripts\reference\dbo\PlacementSystemRoleMapping.sql" />
    <Build Include="rpt\views\vw_as_ContractDocument.sql" />
    <None Include="PlacementStoreDB.Container.publish.xml" />
    <None Include="Compare\container\container2proj.scmp" />
    <None Include="Compare\container\proj2container.scmp" />
    <None Include="Compare\dev\dev2proj.scmp" />
    <None Include="Compare\local\local2proj.scmp" />
    <None Include="Compare\local\proj2local.scmp" />
    <None Include="Compare\prod\prod2proj.scmp" />
    <None Include="Compare\prod\proj2prod.scmp" />
    <None Include="Compare\qa\proj2qa.scmp" />
    <None Include="Compare\qa\qa2proj.scmp" />
    <None Include="Compare\uat\proj2uat.scmp" />
    <None Include="Compare\uat\uat2proj.scmp" />
    <None Include="Compare\dev\proj2dev.scmp" />
    <None Include="Compare\iat\iat2proj.scmp" />
    <None Include="Compare\iat\proj2iat.scmp" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementSystemAudit.sql" />
    <Build Include="Security\Roles\DevReadOnly.sql" />
    <None Include="sqlDeployOptions.json" />
    <None Include="Scripts\reference\ref\NegotiationType.sql" />
    <None Include="Scripts\reference\ref\SourceQuery.sql" />
    <None Include="Scripts\reference\ADF\LoadType.sql" />
    <None Include="Scripts\reference\ADF\InstanceConfig.sql" />
    <Build Include="ods\Views\vw_ps_RiskStructureMarketResponse.sql" />
    <None Include="Scripts\reference\ADF\ProcessSessionStatus.sql" />
    <None Include="Scripts\reference\psapi\ValueMapping_Spain.sql" />
    <None Include="Scripts\reference\ref\InsuredTypeMapping.sql" />
    <None Include="Scripts\reference\psapi\ValueMapping_NL.sql" />
    <Build Include="PS\Tables\ContractStatusHistory.sql" />
    <None Include="Scripts\reference\reference\DataSource.sql" />
    <None Include="Scripts\reference\reference\DataSourceInstance.sql" />
    <None Include="Security\Roles\ProgrammeStoreMarineMarConsumerRole.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Compare\container\container2proj.scmp" />
    <None Include="Compare\container\proj2container.scmp" />
    <None Include="Compare\dev\dev2proj.scmp" />
    <None Include="Compare\local\local2proj.scmp" />
    <None Include="Compare\local\proj2local.scmp" />
    <None Include="Compare\prod\prod2proj.scmp" />
    <None Include="Compare\prod\proj2prod.scmp" />
    <None Include="Compare\qa\proj2qa.scmp" />
    <None Include="Compare\qa\qa2proj.scmp" />
    <None Include="Compare\uat\proj2uat.scmp" />
    <None Include="Compare\uat\uat2proj.scmp" />
    <None Include="Compare\dev\proj2dev.scmp" />
    <None Include="Compare\container\container2proj.scmp" />
    <None Include="Compare\container\proj2container.scmp" />
    <None Include="Compare\dev\dev2proj.scmp" />
    <None Include="Compare\local\local2proj.scmp" />
    <None Include="Compare\local\proj2local.scmp" />
    <None Include="Compare\prod\prod2proj.scmp" />
    <None Include="Compare\prod\proj2prod.scmp" />
    <None Include="Compare\qa\proj2qa.scmp" />
    <None Include="Compare\qa\qa2proj.scmp" />
    <None Include="Compare\uat\proj2uat.scmp" />
    <None Include="Compare\uat\uat2proj.scmp" />
    <None Include="Compare\dev\proj2dev.scmp" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="Scripts\Script.PostDeployment.sql" />
    <None Include="Scripts\Script.PostDeployment.Security.sql" />
    <None Include="Scripts\Script.PostDeployment.OneOffScript.sql" />
    <None Include="Scripts\Script.PostDeployment.DataMasking.sql" />
    <None Include="Scripts\rules\Exclusion.sql" />
    <None Include="Scripts\rules\IncludeForRenewal.sql" />
    <None Include="Scripts\rules\PlacementName.sql" />
    <None Include="Scripts\rules\Region.sql" />
    <None Include="Scripts\rules\Segment.sql" />
    <None Include="Scripts\rules\SubSegment.sql" />
    <Build Include="ods\Views\vw_ref_JustificationReason.sql" />
    <Build Include="ods\Views\vw_ref_JustificationReasonType.sql" />
    <Build Include="ods\Views\vw_ref_MarketKind.sql" />
    <Build Include="BPStaging\Tables\ExposureSummaryElement.sql" />
    <Build Include="BPStaging\Tables\ExposureSummaryElementRiskDefinitionItem.sql" />
    <Build Include="BPStaging\Tables\ExposureType.sql" />
    <Build Include="BPStaging\Tables\PlacementExposureSummary.sql" />
    <Build Include="BPStaging\Tables\PlacementExposureSummaryGroup.sql" />
    <Build Include="PS\Tables\PlacementSecurity.sql" />
    <Build Include="ods\Stored Procedures\GetPlacementDetail.sql" />
    <Build Include="PS\Tables\GlobalPartySecurity.sql" />
    <Build Include="ods\Views\vw_ref_PartyAttribute.sql" />
    <Build Include="ods\Views\vw_ps_ContractRiskProfile.sql" />
    <Build Include="BPStaging\Stored Procedures\LoadPlacementRiskDefinitionElement.sql" />
    <Build Include="PS\Views\vwRiskStructure.sql" />
    <Build Include="APIv1\Tables\OrganisationHierarchyTable.sql" />
    <Build Include="APIv1\Views\CountrySubdivision.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_Submission.sql" />
    <Build Include="PAS\Tables\PartyAddress.sql" />
    <Build Include="PS\Views\vwToAverageExchangeRate.sql" />
    <Build Include="BPStaging\Tables\EligibilityRule.sql" />
    <Build Include="BPStaging\Tables\ContractRiskCode.sql" />
    <Build Include="BPStaging\Stored Procedures\Load_BP_ContractRiskCode.sql" />
    <Build Include="BP\Tables\ContractRiskCode.sql" />
    <Build Include="rpt\Stored Procedures\Load_rpt_PlacementSecurity.sql" />
  </ItemGroup>
  <ItemGroup>
    <PreDeploy Include="Scripts\Script.PreDeployment.sql" />
    <None Include="Scripts\Script.PreDeployment.OneOffScript.sql" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Condition="'$(NetCoreBuild)' == 'true'">
      <Version>1.0.0</Version>
      <PrivateAssets>All</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ArtifactReference Include="$(DacPacRootPath)\Extensions\Microsoft\SQLDB\Extensions\SqlServer\AzureV12\SqlSchemas\master.dacpac" Condition="'$(NetCoreBuild)' != 'true'">
      <SuppressMissingDependenciesErrors>False</SuppressMissingDependenciesErrors>
      <DatabaseVariableLiteralValue>master</DatabaseVariableLiteralValue>
    </ArtifactReference>
    <ArtifactReference Include="$(SystemDacpacsLocation)\SystemDacpacs\AzureV12\master.dacpac" Condition="'$(NetCoreBuild)' == 'true'">
      <SuppressMissingDependenciesErrors>False</SuppressMissingDependenciesErrors>
      <DatabaseVariableLiteralValue>master</DatabaseVariableLiteralValue>
    </ArtifactReference>
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="PlacementStore.refactorlog" />
  </ItemGroup>
  <Import Project="$(NETCoreTargetsPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" Condition="'$(NetCoreBuild)' == 'true'" />
  <Target Name="BeforeBuild">
    <Delete Files="$(BaseIntermediateOutputPath)\project.assets.json" />
  </Target>
</Project>