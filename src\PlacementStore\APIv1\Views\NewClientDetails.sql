/*
Lineage
Id=dbo.NewClient.Id
NewClientId=dbo.NewClient.NewClientId
Name=dbo.NewClient.Name
CompanyRegistrationNumber=dbo.NewClient.CompanyRegistrationNumber
ShortName=dbo.NewClient.ShortName
Location=dbo.NewClient.Location
LocalIndustryId=dbo.Lookup.LocalCode
LocalBranchId=dbo.Lookup.LocalCode
LocalSegmentationId=dbo.Lookup.LocalCode
LocalTurnoverId=dbo.Lookup.LocalCode
LocalOwnerId=dbo.Lookup.LocalCode
AddressLine1=dbo.NewClient.AddressLine1
AddressLine2=dbo.NewClient.AddressLine2
AddressLine3=dbo.NewClient.AddressLine3
AddressLine4=dbo.NewClient.AddressLine4
City=dbo.NewClient.City
State=dbo.NewClient.State
ZipCode=dbo.NewClient.ZipCode
CountryId=dbo.NewClient.CountryId
DataSourceInstanceId=dbo.NewClient.DataSourceInstanceId
LocalProgramTypeId=dbo.Lookup.LocalCode
CreatedUTCDate=dbo.NewClient.CreatedUTCDate
CreatedUser=dbo.NewClient.CreatedUser
LastUpdatedUTCDate=dbo.NewClient.LastUpdatedUTCDate
LastUpdatedUser=dbo.NewClient.LastUpdatedUser
GlobalPartyId=dbo.Party.GlobalPartyId
PlacementDataSourceInstanceId=dbo.NewClient.PlacementDataSourceInstanceId
IsValidated=dbo.NewClient.IsValidated
HasBeenSentToCOL=dbo.ClientMapping.Id
*/
/*
    Used in COL
*/
CREATE VIEW APIv1.NewClientDetails
AS

SELECT
    NC.Id
  , NC.NewClientId
  , NC.Name
  , NC.CompanyRegistrationNumber
  , NC.ShortName
  , NC.Location
  , LocalIndustryId = I.LocalCode
  , LocalBranchId = B.LocalCode
  , LocalSegmentationId = S.LocalCode
  , LocalTurnoverId = T.LocalCode
  , LocalOwnerId = O.LocalCode
  , NC.AddressLine1
  , NC.AddressLine2
  , NC.AddressLine3
  , NC.AddressLine4
  , NC.City
  , NC.State
  , NC.ZipCode
  , NC.CountryId
  , NC.DataSourceInstanceId
  , LocalProgramTypeId = PT.LocalCode
  , NC.CreatedUTCDate
  , NC.CreatedUser
  , NC.LastUpdatedUTCDate
  , NC.LastUpdatedUser
  , P.GlobalPartyId
  , NC.PlacementDataSourceInstanceId
  , NC.IsValidated
  , HasBeenSentToCOL = CONVERT(BIT
                             , CASE WHEN CM.Id IS NULL
                                        THEN 0
                                    ELSE 1 END
                       )
FROM
    dbo.NewClient NC
    LEFT JOIN dbo.Lookup I
        ON I.Id = NC.IndustryId

    LEFT JOIN dbo.Lookup B
        ON B.Id = NC.BranchId

    LEFT JOIN dbo.Lookup S
        ON S.Id = NC.SegmentationId

    LEFT JOIN dbo.Lookup T
        ON T.Id = NC.TurnOverId

    LEFT JOIN dbo.Lookup O
        ON O.Id = NC.OwnerId

    LEFT JOIN dbo.Lookup PT
        ON PT.Id = NC.ProgramTypeId

    LEFT JOIN dbo.Party P
        ON P.PartyId = NC.PartyId

    LEFT JOIN dbo.ClientMapping CM
        ON CM.ClientRef = NC.CompanyRegistrationNumber
           AND CM.DataSourceInstanceId = NC.DataSourceInstanceId
WHERE
    NC.IsDeleted = 0
    AND NC.IsError = 0;