/*
Lineage
ref.PricingFactor.PricingFactorId=BPStaging.PricingFactor.Id
ref.PricingFactor.PricingFactorKey=BPStaging.PricingFactor.LabelTranslationKey
ref.PricingFactor.PricingFactor=BPStaging.PricingFactor.Text
ref.PricingFactor.SourceUpdatedDate=BPStaging.PricingFactor.ValidFrom
ref.PricingFactor.IsDeprecated=BPStaging.PricingFactor.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_PricingFactor
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.PricingFactor';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.PricingFactor T
    USING (
        SELECT
            PricingFactorId = Id
          , PricingFactorKey = LabelTranslationKey
          , PricingFactor = Text
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.PricingFactor
    ) S
    ON T.PricingFactorId = S.PricingFactorId
    WHEN NOT MATCHED
        THEN INSERT (
                 PricingFactorId
               , DataSourceInstanceId
               , PricingFactorKey
               , PricingFactor
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.PricingFactorId
                   , S.DataSourceInstanceId
                   , S.PricingFactorKey
                   , S.PricingFactor
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.PricingFactorId
                               , T.DataSourceInstanceId
                               , T.PricingFactorKey
                               , T.PricingFactor
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.PricingFactorId
                               , S.DataSourceInstanceId
                               , S.PricingFactorKey
                               , S.PricingFactor
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PricingFactorKey = S.PricingFactorKey
               , T.PricingFactor = S.PricingFactor
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);