/*
Lineage
PartyId=dbo.Party.PartyId
DataSourceInstanceId=dbo.Party.DataSourceInstanceId
PartyCode=dbo.Party.PartyCode
PartyName=PAS.PartyAttribute.EclipseRoleDsc
PartyName=dbo.Party.PartyName
Geography=PAS.Geography.Geography
CountryId=Reference.Country.CountryId
CountryName=Reference.Country.CountryName
EmployeeCount=Reference.Party.EmployeeCount
IsIndividual=Reference.Party.IsIndividual
OperatingRevenue=Reference.Party.OperatingRevenueUSD
GlobalPartyId=Reference.Party.PartyId
GlobalPartyName=Reference.Party.PartyName
ParentGlobalPartyId=dbo.Party.GlobalPartyId
PACTPartyId=dbo.Party.PACTPartyId
PartyLevel1Name=dbo.Party.PartyName
PartyLevel2Name=dbo.Party.PartyName
PartyLevel3Name=dbo.Party.PartyName
PartyNode=dbo.Party.PartyId
InsuredTypeId=ref.InsuredType.InsuredTypeId
LastUpdatedUTCDate=dbo.Party.ETLUpdatedDate
LastUpdatedUTCDate=dbo.PolicyPartyRelationship.ETLUpdatedDate
LastUpdatedUTCDate=ref.PartyRole.ETLUpdatedDate
LastUpdatedUTCDate=Reference.Party.ETLUpdatedDate
LastUpdatedUTCDate=Reference.PartyRoleRelationship.ETLUpdatedDate
LastUpdatedUTCDate=PAS.Geography.ETLUpdatedDate
LastUpdatedUTCDate=Reference.Country.ETLUpdatedDate
LastUpdatedUTCDate=Reference.PartyRole.ETLUpdatedDate
LastUpdatedUTCDate=Reference.PartyExternalReference.ETLUpdatedDate
PartyKey=dbo.Party.BusinessKey
PartyKey=dbo.Party.PartyCode
PartyKey=dbo.Party.PartyKey
CompanyRegistrationNumber=dbo.Party.CompanyRegistrationNumber
IndustryId=APIv1.PartyAttributesTable.IndustryId
IndustryId=Reference.Party.IndustrySubSectorId
OwnerId=APIv1.PartyAttributesTable.OwnerId
ProgramTypeId=APIv1.PartyAttributesTable.ProgramTypeId
TurnoverId=APIv1.PartyAttributesTable.TurnoverId
SegmentationId=APIv1.PartyAttributesTable.SegmentationId
BranchId=APIv1.PartyAttributesTable.BranchId
ShortName=dbo.Party.ShortName
IsDeleted=dbo.Party.IsDeleted
DUNSNumber=Reference.PartyExternalReference.PartyExternalReference
IsDeprecated=dbo.Party.IsDeleted
IsDeprecated=dbo.Party.IsActive
AlternativeName=dbo.Party.PartyName
*/

CREATE VIEW APIv1.PartyHierarchy
AS

WITH Parties AS (
    SELECT
        PartyId
      , DataSourceInstanceId
      , PartyName
      , PartyLevel1Name = PartyName
      , PartyLevel2Name = CAST(NULL AS NVARCHAR(500))
      , PartyLevel3Name = CAST(NULL AS NVARCHAR(500))
      , PACTPartyId
      , LevelNum = 0
      , PartyNode = CAST(CONCAT('/', PartyId, '/') AS VARCHAR(255))
      , GlobalPartyId
      , ParentGlobalPartyId = GlobalPartyId
    FROM
        dbo.Party
    WHERE
        ParentPartyId IS NULL
    UNION ALL
    SELECT
        p.PartyId
      , p.DataSourceInstanceId
      , p.PartyName
      , pp.PartyLevel1Name
      , PartyLevel2Name = CASE WHEN pp.LevelNum = 0
                                   THEN p.PartyName
                               ELSE pp.PartyLevel2Name END
      , PartyLevel3Name = CASE WHEN pp.LevelNum = 1
                                   THEN p.PartyName
                               ELSE pp.PartyLevel3Name END
      , p.PACTPartyId
      , LevelNum = pp.LevelNum + 1
      , PartyNode = CAST(CONCAT(pp.PartyNode, p.PartyId, '/') AS VARCHAR(255))
      , GlobalPartyId = p.GlobalPartyId -- Shouldn't be exposing the Parent GCId for a record
      , ParentGlobalPartyId = pp.GlobalPartyId
    FROM
        dbo.Party p
        INNER JOIN Parties pp
            ON p.ParentPartyId = pp.PartyId
               AND p.DataSourceInstanceId = pp.DataSourceInstanceId
)
SELECT
    PartyId = p.PartyId
  , DataSourceInstanceId = p.DataSourceInstanceId
  , xp.PartyCode
  , PartyName = COALESCE(TRIM(ppa.EclipseRoleDsc), xp.PartyName)
  , g.Geography
  , rc.CountryId
  , rc.CountryName
  , rp.EmployeeCount
  , rp.IsIndividual
  , rp.OperatingRevenue
  , GlobalPartyId = rp.PartyId
  , GlobalPartyName = rp.PartyName
  , p.ParentGlobalPartyId
  , PACTPartyId = p.PACTPartyId
  , p.PartyLevel1Name
  , p.PartyLevel2Name
  , p.PartyLevel3Name
  , p.LevelNum
  , PartyNode = CAST(p.PartyNode AS HIERARCHYId)
  , it.InsuredTypeId
  , LastUpdatedUTCDate = (
        SELECT MAX(value.v)
        FROM (
            VALUES (
                xp.ETLUpdatedDate
            )
                 , (
                ppr.ETLUpdatedDate
            )
                 , (
                rp.ETLUpdatedDate
            )
                 , (
                g.ETLUpdatedDate
            )
                 , (
                rc.ETLUpdatedDate
            )
                 , (
                ppr.ETLUpdatedDate
            )
                 , (
                ro.ETLUpdatedDate
            )
                 , (
                ptyattr.ETLUpdatedDate
            )
        ) value (v)
    )
  /*
  BizCore wants to use the BusinessKey as a searchable value
  EPIC US and Canada want the PartyCode as a searchable value
  */
  , PartyKey = CASE WHEN p.DataSourceInstanceId = 50500
                        THEN xp.BusinessKey
                    WHEN p.DataSourceInstanceId = 50001
                        THEN xp.PartyCode
                    ELSE xp.PartyKey END
  , xp.CompanyRegistrationNumber
  , IndustryId = ISNULL(bi.IndustryId, rp.IndustrySubSectorId)
  , OwnerId = bi.OwnerId
  , ProgramTypeId = bi.ProgramTypeId
  , TurnoverId = bi.TurnoverId
  , SegmentationId = bi.SegmentationId
  , BranchId = bi.BranchId
  , xp.ShortName
  , xp.IsDeleted
  , DUNSNumber = CAST(ptyattr.DUNSNUNmber AS NVARCHAR(20))/* This is from Willis Reference rather than the one available in PACT */
  , IsDeprecated = CAST(CASE WHEN xp.IsActive = 0
                                 THEN 1
                             ELSE xp.IsDeleted END AS BIT)
  , AlternativeName = xp.PartyName
FROM
    Parties p WITH (NOLOCK)
    INNER JOIN dbo.Party xp WITH (NOLOCK)
        ON xp.PartyId = p.PartyId

    LEFT JOIN (
        SELECT
            par.PartyId
          , par.IsIndividual
          , par.PartyName
          , par.EmployeeCount
          , OperatingRevenue = par.OperatingRevenueUSD
          , rppr.PartyRoleId
          , par.IndustrySubSectorId
          , ETLUpdatedDate = (
                SELECT MAX(value.v) FROM (VALUES (par.ETLUpdatedDate), (rppr.ETLUpdatedDate)) value (v)
            )
        FROM
            Reference.Party par WITH (NOLOCK)
            LEFT JOIN (
                SELECT
                    PartyId
                  , PartyRoleId = MIN(PartyRoleId)
                  , ETLUpdatedDate = MAX(ETLUpdatedDate)
                FROM
                    Reference.PartyRoleRelationship WITH (NOLOCK)
                WHERE
                    PartyRoleId IN (
                        100, 101, 106
                    ) --client/(re)insured
                GROUP BY
                    PartyId
            ) rppr
                ON rppr.PartyId = par.PartyId
    ) rp
        ON rp.PartyId = ISNULL(p.GlobalPartyId, TRY_CAST(xp.SourcePartyId AS INT)) -- SourcePartyId is where Servicing Platforms provide GlobalPartyId

    LEFT JOIN PAS.Geography g WITH (NOLOCK)
        ON g.GeographyKey = xp.GeographyKey
            AND g.DataSourceInstanceId = xp.DataSourceInstanceId

    LEFT JOIN Reference.Country rc WITH (NOLOCK)
        ON rc.CountryId = g.GlobalCountryId

    LEFT JOIN (
        SELECT
            ppr.PartyId
          , GlobalPartyRoleId = MIN(pr.GlobalPartyRoleId)
          , ETLUpdatedDate = MAX(CASE WHEN ppr.ETLUpdatedDate > pr.ETLUpdatedDate
                                          THEN ppr.ETLUpdatedDate
                                      ELSE pr.ETLUpdatedDate END
                             )
        FROM
            dbo.PolicyPartyRelationship ppr WITH (NOLOCK)
            INNER JOIN ref.PartyRole pr WITH (NOLOCK)
                ON ppr.PartyRoleId = pr.PartyRoleId
        WHERE
            pr.GlobalPartyRoleId <> 102
        GROUP BY
            ppr.PartyId
    ) ppr
        ON ppr.PartyId = p.PartyId

    LEFT JOIN Reference.PartyRole ro WITH (NOLOCK)
        ON ro.PartyRoleId = ISNULL(ppr.GlobalPartyRoleId, rp.PartyRoleId)

    LEFT JOIN APIv1.PartyAttributesTable bi WITH (NOLOCK)
        ON bi.PartyId = p.PartyId

    -- Temporary JOIN until dbo.Party is migrated to PS.Party where the DUNSNumber will be populated into PS.Party

    LEFT JOIN (
        SELECT
            p.PartyId
          , DUNSNUNmber = per.PartyExternalReference
          , per.ETLUpdatedDate
        FROM
            Reference.PartyExternalReference per WITH (NOLOCK)
            INNER JOIN dbo.Party p WITH (NOLOCK)
                ON per.DataSourceInstanceId = 50023 --Dun and Bradstreet
                   AND p.GlobalPartyId = per.PartyId
        WHERE
            per.IsDeleted = 0
    ) ptyattr
        ON ptyattr.PartyId = p.PartyId

    LEFT JOIN PAS.Party pasp WITH(NOLOCK) ON pasp.PASPartyId = p.PACTPartyId
		AND pasp.DataSourceInstanceId = p.DataSourceInstanceId

	LEFT JOIN PAS.PartyAttribute ppa WITH(NOLOCK) ON ppa.PartyKey = pasp.PartyKey
		AND ppa.DataSourceInstanceId = p.DataSourceInstanceId

    LEFT JOIN ref.InsuredTypeMapping itm WITH (NOLOCK)
        ON itm.DataSourceInstanceId = 50366
           AND itm.SourceDataSourceInstanceId = p.DataSourceInstanceId
           AND (
               itm.SourceInsuredTypeKey = 'SourceQueryId|' + CAST(xp.SourceQueryId AS NVARCHAR(100))
               OR itm.SourceInsuredTypeKey = 'PartyAttributesRole|' + ppa.EclipseRole
           )

    LEFT JOIN ref.InsuredType it WITH (NOLOCK)
        ON it.DataSourceInstanceId = 50366
           AND it.InsuredTypeKey = ISNULL(itm.InsuredTypeKey, '3') -- Default to Client
WHERE
    COALESCE(ppa.EclipseRoleDsc, xp.PartyName) IS NOT NULL;