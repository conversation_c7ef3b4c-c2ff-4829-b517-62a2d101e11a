/*
Lineage
PS.ClientProposal.Id=BPStaging.ClientProposal.Id
PS.ClientProposal.Header=BPStaging.ClientProposal.Header
PS.ClientProposal.Name=BPStaging.ClientProposal.Name
PS.ClientProposal.ProposalDate=BPStaging.ClientProposal.ProposalDate
PS.ClientProposal.RevisedDate=BPStaging.ClientProposal.RevisedDate
PS.ClientProposal.IncludeClientServicesTeam=BPStaging.ClientProposal.IncludeClientServicesTeam
PS.ClientProposal.IncludeClientServices=BPStaging.ClientProposal.IncludeClientServices
PS.ClientProposal.IsClientProposalDeliveredElectronically=BPStaging.ClientProposal.IsClientProposalDeliveredElectronically
PS.ClientProposal.IncludeExecutiveSummary=BPStaging.ClientProposal.IncludeExecutiveSummary
PS.ClientProposal.ExecutiveSummaryTemplateText=BPStaging.ClientProposal.ExecutiveSummaryTemplateText
PS.ClientProposal.ExecutiveSummaryProducingOffice=BPStaging.ClientProposal.ExecutiveSummaryProducingOffice
PS.ClientProposal.ExecutiveSummaryLineOfBusiness=BPStaging.ClientProposal.ExecutiveSummaryLineOfBusiness
PS.ClientProposal.ExecutiveSummaryClient=BPStaging.ClientProposal.ExecutiveSummaryClient
PS.ClientProposal.ExecutiveSummaryPeriod=BPStaging.ClientProposal.ExecutiveSummaryPeriod
PS.ClientProposal.IncludeAdditionalCoverage=BPStaging.ClientProposal.IncludeAdditionalCoverage
PS.ClientProposal.AdditionalCoverage=BPStaging.ClientProposal.AdditionalCoverage
PS.ClientProposal.FinancedAmount=BPStaging.ClientProposal.FinancedAmount
PS.ClientProposal.FinancedAmountCurrencyId=BPStaging.ClientProposal.FinancedAmountCurrencyId
PS.ClientProposal.APRRangeFrom=BPStaging.ClientProposal.APRRangeFrom
PS.ClientProposal.APRRangeTo=BPStaging.ClientProposal.APRRangeTo
PS.ClientProposal.IncludeExposureAndRateBreakdown=BPStaging.ClientProposal.IncludeExposureAndRateBreakdown
PS.ClientProposal.IncludeProgramSchematic=BPStaging.ClientProposal.IncludeProgramSchematic
PS.ClientProposal.IncludeBTCDFullWording=BPStaging.ClientProposal.IncludeBTCDFullWording
PS.ClientProposal.IncludeDetailedFinancialAnalysis=BPStaging.ClientProposal.IncludeDetailedFinancialAnalysis
PS.ClientProposal.SourceUpdatedDate=BPStaging.ClientProposal.ValidTo
PS.ClientProposal.SourceUpdatedDate=BPStaging.ClientProposal.ValidFrom
PS.ClientProposal.IsDeleted=BPStaging.ClientProposal.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_ClientProposal
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ClientProposal';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ClientProposal)
BEGIN TRY
    MERGE PS.ClientProposal T
    USING (
        SELECT
            Id
          , Header
          , Name
          , ProposalDate
          , RevisedDate
          , IncludeClientServicesTeam
          , IncludeClientServices
          , IsClientProposalDeliveredElectronically
          , IncludeExecutiveSummary
          , ExecutiveSummaryTemplateText
          , ExecutiveSummaryProducingOffice
          , ExecutiveSummaryLineOfBusiness
          , ExecutiveSummaryClient
          , ExecutiveSummaryPeriod
          , IncludeAdditionalCoverage
          , AdditionalCoverage
          , FinancedAmount
          , FinancedAmountCurrencyId
          , APRRangeFrom
          , APRRangeTo
          , IncludeExposureAndRateBreakdown
          , IncludeProgramSchematic
          , IncludeBTCDFullWording
          , IncludeDetailedFinancialAnalysis
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , Header
          , Name
          , ProposalDate
          , RevisedDate
          , IncludeClientServicesTeam
          , IncludeClientServices
          , IsClientProposalDeliveredElectronically
          , IncludeExecutiveSummary
          , ExecutiveSummaryTemplateText
          , ExecutiveSummaryProducingOffice
          , ExecutiveSummaryLineOfBusiness
          , ExecutiveSummaryClient
          , ExecutiveSummaryPeriod
          , IncludeAdditionalCoverage
          , AdditionalCoverage
          , FinancedAmount
          , FinancedAmountCurrencyId
          , APRRangeFrom
          , APRRangeTo
          , IncludeExposureAndRateBreakdown
          , IncludeProgramSchematic
          , IncludeBTCDFullWording
          , IncludeDetailedFinancialAnalysis
          , ValidTo
          , ValidFrom
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ClientProposal cp
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , Header
               , Name
               , ProposalDate
               , RevisedDate
               , IncludeClientServicesTeam
               , IncludeClientServices
               , IsClientProposalDeliveredElectronically
               , IncludeExecutiveSummary
               , ExecutiveSummaryTemplateText
               , ExecutiveSummaryProducingOffice
               , ExecutiveSummaryLineOfBusiness
               , ExecutiveSummaryClient
               , ExecutiveSummaryPeriod
               , IncludeAdditionalCoverage
               , AdditionalCoverage
               , FinancedAmount
               , FinancedAmountCurrencyId
               , APRRangeFrom
               , APRRangeTo
               , IncludeExposureAndRateBreakdown
               , IncludeProgramSchematic
               , IncludeBTCDFullWording
               , IncludeDetailedFinancialAnalysis
               , SourceUpdatedDate
               , IsDeleted
               , DataSourceInstanceId
             )
             VALUES
                 (
                     S.Id
                   , S.Header
                   , S.Name
                   , S.ProposalDate
                   , S.RevisedDate
                   , S.IncludeClientServicesTeam
                   , S.IncludeClientServices
                   , S.IsClientProposalDeliveredElectronically
                   , S.IncludeExecutiveSummary
                   , S.ExecutiveSummaryTemplateText
                   , S.ExecutiveSummaryProducingOffice
                   , S.ExecutiveSummaryLineOfBusiness
                   , S.ExecutiveSummaryClient
                   , S.ExecutiveSummaryPeriod
                   , S.IncludeAdditionalCoverage
                   , S.AdditionalCoverage
                   , S.FinancedAmount
                   , S.FinancedAmountCurrencyId
                   , S.APRRangeFrom
                   , S.APRRangeTo
                   , S.IncludeExposureAndRateBreakdown
                   , S.IncludeProgramSchematic
                   , S.IncludeBTCDFullWording
                   , S.IncludeDetailedFinancialAnalysis
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.Header
      , T.Name
      , T.ProposalDate
      , T.RevisedDate
      , T.IncludeClientServicesTeam
      , T.IncludeClientServices
      , T.IsClientProposalDeliveredElectronically
      , T.IncludeExecutiveSummary
      , T.ExecutiveSummaryTemplateText
      , T.ExecutiveSummaryProducingOffice
      , T.ExecutiveSummaryLineOfBusiness
      , T.ExecutiveSummaryClient
      , T.ExecutiveSummaryPeriod
      , T.IncludeAdditionalCoverage
      , T.AdditionalCoverage
      , T.FinancedAmount
      , T.FinancedAmountCurrencyId
      , T.APRRangeFrom
      , T.APRRangeTo
      , T.IncludeExposureAndRateBreakdown
      , T.IncludeProgramSchematic
      , T.IncludeBTCDFullWording
      , T.IncludeDetailedFinancialAnalysis
      , T.SourceUpdatedDate
      , T.IsDeleted
      , T.DataSourceInstanceId
    INTERSECT
    SELECT
        S.Header
      , S.Name
      , S.ProposalDate
      , S.RevisedDate
      , S.IncludeClientServicesTeam
      , S.IncludeClientServices
      , S.IsClientProposalDeliveredElectronically
      , S.IncludeExecutiveSummary
      , S.ExecutiveSummaryTemplateText
      , S.ExecutiveSummaryProducingOffice
      , S.ExecutiveSummaryLineOfBusiness
      , S.ExecutiveSummaryClient
      , S.ExecutiveSummaryPeriod
      , S.IncludeAdditionalCoverage
      , S.AdditionalCoverage
      , S.FinancedAmount
      , S.FinancedAmountCurrencyId
      , S.APRRangeFrom
      , S.APRRangeTo
      , S.IncludeExposureAndRateBreakdown
      , S.IncludeProgramSchematic
      , S.IncludeBTCDFullWording
      , S.IncludeDetailedFinancialAnalysis
      , S.SourceUpdatedDate
      , S.IsDeleted
      , S.DataSourceInstanceId
)
        THEN UPDATE SET
                 T.Header = S.Header
               , T.Name = S.Name
               , T.ProposalDate = S.ProposalDate
               , T.RevisedDate = S.RevisedDate
               , T.IncludeClientServicesTeam = S.IncludeClientServicesTeam
               , T.IncludeClientServices = S.IncludeClientServices
               , T.IsClientProposalDeliveredElectronically = S.IsClientProposalDeliveredElectronically
               , T.IncludeExecutiveSummary = S.IncludeExecutiveSummary
               , T.ExecutiveSummaryTemplateText = S.ExecutiveSummaryTemplateText
               , T.ExecutiveSummaryProducingOffice = S.ExecutiveSummaryProducingOffice
               , T.ExecutiveSummaryLineOfBusiness = S.ExecutiveSummaryLineOfBusiness
               , T.ExecutiveSummaryClient = S.ExecutiveSummaryClient
               , T.ExecutiveSummaryPeriod = S.ExecutiveSummaryPeriod
               , T.IncludeAdditionalCoverage = S.IncludeAdditionalCoverage
               , T.AdditionalCoverage = S.AdditionalCoverage
               , T.FinancedAmount = S.FinancedAmount
               , T.FinancedAmountCurrencyId = S.FinancedAmountCurrencyId
               , T.APRRangeFrom = S.APRRangeFrom
               , T.APRRangeTo = S.APRRangeTo
               , T.IncludeExposureAndRateBreakdown = S.IncludeExposureAndRateBreakdown
               , T.IncludeProgramSchematic = S.IncludeProgramSchematic
               , T.IncludeBTCDFullWording = S.IncludeBTCDFullWording
               , T.IncludeDetailedFinancialAnalysis = S.IncludeDetailedFinancialAnalysis
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);