/*
Lineage
ref.PremiumAdjustableIndicator.PremiumAdjustableIndicatorId=BPStaging.PremiumAdjustableIndicator.Id
ref.PremiumAdjustableIndicator.PremiumAdjustableIndicatorKey=BPStaging.PremiumAdjustableIndicator.LabelTranslationKey
ref.PremiumAdjustableIndicator.PremiumAdjustableIndicator=BPStaging.PremiumAdjustableIndicator.Text
ref.PremiumAdjustableIndicator.SourceUpdatedDate=BPStaging.PremiumAdjustableIndicator.ValidFrom
ref.PremiumAdjustableIndicator.IsDeprecated=BPStaging.PremiumAdjustableIndicator.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadPremiumAdjustableIndicator
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.PremiumAdjustableIndicator';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.PremiumAdjustableIndicator T
    USING (
        SELECT
            PremiumAdjustableIndicatorId = Id
          , DataSourceInstanceId = 50366
          , PremiumAdjustableIndicatorKey = LabelTranslationKey
          , PremiumAdjustableIndicator = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.PremiumAdjustableIndicator
    ) S
    ON T.PremiumAdjustableIndicatorId = S.PremiumAdjustableIndicatorId
    WHEN NOT MATCHED
        THEN INSERT (
                 PremiumAdjustableIndicatorId
               , DataSourceInstanceId
               , PremiumAdjustableIndicatorKey
               , PremiumAdjustableIndicator
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.PremiumAdjustableIndicatorId
                   , S.DataSourceInstanceId
                   , S.PremiumAdjustableIndicatorKey
                   , S.PremiumAdjustableIndicator
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.PremiumAdjustableIndicatorId
                               , T.DataSourceInstanceId
                               , T.PremiumAdjustableIndicatorKey
                               , T.PremiumAdjustableIndicator
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.PremiumAdjustableIndicatorId
                               , S.DataSourceInstanceId
                               , S.PremiumAdjustableIndicatorKey
                               , S.PremiumAdjustableIndicator
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.PremiumAdjustableIndicatorId = S.PremiumAdjustableIndicatorId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PremiumAdjustableIndicatorKey = S.PremiumAdjustableIndicatorKey
               , T.PremiumAdjustableIndicator = S.PremiumAdjustableIndicator
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
