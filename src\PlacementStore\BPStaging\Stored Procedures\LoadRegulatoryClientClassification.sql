/*
Lineage
ref.RegulatoryClientClassification.RegulatoryClientClassificationId=BPStaging.RegulatoryClientClassification.Id
ref.RegulatoryClientClassification.RegulatoryClientClassificationKey=BPStaging.RegulatoryClientClassification.LabelTranslationKey
ref.RegulatoryClientClassification.RegulatoryClientClassification=BPStaging.RegulatoryClientClassification.Text
ref.RegulatoryClientClassification.SourceUpdatedDate=BPStaging.RegulatoryClientClassification.ValidFrom
ref.RegulatoryClientClassification.IsDeprecated=BPStaging.RegulatoryClientClassification.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadRegulatoryClientClassification
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.RegulatoryClientClassification';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.RegulatoryClientClassification T
    USING (
        SELECT
            RegulatoryClientClassificationId = Id
          , DataSourceInstanceId = 50366
          , RegulatoryClientClassificationKey = LabelTranslationKey
          , RegulatoryClientClassification = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.RegulatoryClientClassification
    ) S
    ON T.RegulatoryClientClassificationId = S.RegulatoryClientClassificationId
    WHEN NOT MATCHED
        THEN INSERT (
                 RegulatoryClientClassificationId
               , DataSourceInstanceId
               , RegulatoryClientClassificationKey
               , RegulatoryClientClassification
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.RegulatoryClientClassificationId
                   , S.DataSourceInstanceId
                   , S.RegulatoryClientClassificationKey
                   , S.RegulatoryClientClassification
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.RegulatoryClientClassificationId
                               , T.DataSourceInstanceId
                               , T.RegulatoryClientClassificationKey
                               , T.RegulatoryClientClassification
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.RegulatoryClientClassificationId
                               , S.DataSourceInstanceId
                               , S.RegulatoryClientClassificationKey
                               , S.RegulatoryClientClassification
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.RegulatoryClientClassificationId = S.RegulatoryClientClassificationId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RegulatoryClientClassificationKey = S.RegulatoryClientClassificationKey
               , T.RegulatoryClientClassification = S.RegulatoryClientClassification
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);