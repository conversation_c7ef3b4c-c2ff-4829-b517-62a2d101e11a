/*
Lineage
BPStaging.Placement.ServicingPlatformId=dbo.DataSourceInstanceIdMapping.DataSourceInstanceId
BPStaging.Placement.ServicingPlatformId=ref.Team.DataSourceInstanceId
dbo.Placement.PlacementName=BPStaging.Placement.Description
dbo.Placement.PlacementStatusId=ref.PlacementStatus.PlacementStatusId
dbo.Placement.InceptionDate=BPStaging.Placement.InceptionDate
dbo.Placement.ExpiryDate=BPStaging.Placement.ExpiryDate
dbo.Placement.ExpiryDate=BPStaging.Placement.InceptionDate
dbo.Placement.IsDeleted=BPStaging.Placement.ValidTo
dbo.Placement.CancellationReasonId=BPStaging.Placement.CancellationReasonId
dbo.Placement.RenewedFromPlacementId=BPStaging.Placement.PsId
dbo.Placement.RenewedFromPlacementId=dbo.Placement.PlacementId
dbo.Placement.PlacementSystemId=BPStaging.Placement.Id
dbo.Placement.ServicingPlatformId=dbo.DataSourceInstanceIdMapping.DataSourceInstanceId
dbo.Placement.ServicingPlatformId=ref.Team.DataSourceInstanceId
dbo.Placement.PlacementOpportunityTypeId=ref.OpportunityType.OpportunityTypeId
dbo.Placement.AppraisalTypeId=BPStaging.Placement.AppraisalTypeId
dbo.Placement.RefInsuranceTypeId=BPStaging.Placement.InsuranceTypeId
dbo.Placement.InstructionDetails=BPStaging.Placement.InstructionDetails
dbo.Placement.RiskLocationId=BPStaging.Placement.RiskLocationId
dbo.Placement.Comments=BPStaging.Placement.Comments
dbo.Placement.BrokingSegmentId=BPStaging.Placement.BrokingSegmentId
dbo.Placement.BrokingRegionId=BPStaging.Placement.RegionId
dbo.Placement.BrokingSubSegmentId=BPStaging.Placement.BrokingSubSegmentId
dbo.Placement.IndustryId=BPStaging.Placement.IndustryId
dbo.Placement.ProducingOfficeId=BPStaging.Placement.ProducingOfficeId
dbo.Placement.PlacementCompletionStatusId=dbo.DataSourceInstanceIdMapping.DataSourceInstanceId
dbo.Placement.PlacementCompletionStatusId=ref.Team.DataSourceInstanceId
dbo.Placement.ProgramTypeId=BPStaging.Placement.ProgramTypeId
dbo.Placement.RenewableOptionId=BPStaging.Placement.RenewableOptionId
dbo.Placement.NotRemarketingReasonId=BPStaging.Placement.NotRemarketingReasonId
dbo.Placement.InceptionStartTime=BPStaging.Placement.InceptionStartTime
dbo.Placement.ExpiryStartTime=BPStaging.Placement.ExpiryStartTime
dbo.Placement.TimeZoneRepresentionId=BPStaging.Placement.TimeZoneRepresentionId
dbo.Placement.PricingFactorId=BPStaging.Placement.PricingFactorId
dbo.Placement.VerticalIndustryId=BPStaging.Placement.VerticalIndustryId
dbo.Placement.MTACreatedFromPlacementId=dbo.Placement.PlacementId
dbo.Placement.MTATypeId=ref.MTAType.MTATypeId
dbo.Placement.SourceUpdatedDate=BPStaging.Placement.ValidTo
dbo.Placement.SourceUpdatedDate=BPStaging.Placement.ValidFrom
dbo.Placement.RenewedToPlacementId=dbo.Placement.PlacementId
*/
CREATE PROCEDURE BPStaging.LoadPlacement
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @RenewedFromUpdatedCount INT = 0;
DECLARE @RenewedToIdYear1UpdatedCount INT = 0;
DECLARE @RenewedToIdYear2UpdatedCount INT = 0;
DECLARE @MTACreatedFromUpdatedCount INT = 0;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

-- If there are no Placements in BPStatging none of this is run.
IF EXISTS (SELECT * FROM BPStaging.Placement)
BEGIN TRY
    SET @Action = N'Update [dbo].[Placement];';

    -- for the purpose of Whitelabelling, need to update the placements with datasource instance through placement teams.
    -- Update ServicingPlatformId as null for all placements which had ServicingPlatformId on source view
    UPDATE BPStaging.Placement
    SET ServicingPlatformId = NULL;

    -- First update from Region
    UPDATE
        BP
    SET
        BP.ServicingPlatformId = DSIM.DataSourceInstanceId
    FROM
        BPStaging.Placement BP
        INNER JOIN (
            SELECT
                A.Id
              , A.DataSourceInstanceId
            FROM (
                SELECT
                    BP.Id
                  , Region.DataSourceInstanceId
                  , RowNumber = ROW_NUMBER() OVER (PARTITION BY BP.Id ORDER BY Region.RuleOrder ASC)
                FROM
                    BPStaging.Placement BP
                    INNER JOIN dbo.DataSourceInstanceIdMapping Region
                        ON BP.RegionId = Region.BrokingRegionId
                           AND ISNULL(BP.BrokingSegmentId, -999999) = (CASE WHEN Region.BrokingSegmentId = -999999
                                                                                THEN ISNULL(
                                                                                         BP.BrokingSegmentId, -999999
                                                                                     )
                                                                            ELSE Region.BrokingSegmentId END
                                                                      )
                           AND ISNULL(BP.BrokingSubSegmentId, -999999) = (CASE WHEN Region.BrokingSubSegmentId = -999999
                                                                                   THEN ISNULL(
                                                                                            BP.BrokingSubSegmentId
                                                                                          , -999999
                                                                                        )
                                                                               ELSE Region.BrokingSubSegmentId END
                                                                         )
                           AND ISNULL(BP.InsuranceTypeId, -999999) = (CASE WHEN Region.InsuranceTypeId = -999999
                                                                               THEN ISNULL(BP.InsuranceTypeId, -999999)
                                                                           ELSE Region.InsuranceTypeId END
                                                                     )
                           AND Region.IsDeleted = 0
            ) A
            WHERE
                A.RowNumber = 1
        ) DSIM
            ON DSIM.Id = BP.Id;

    -- If region didnt update everything, then update from Placement Teams from the Parent placement (preferred) or placement
    UPDATE BP
    SET BP.ServicingPlatformId = T.DataSourceInstanceId
    FROM
        BPStaging.Placement BP
        LEFT JOIN dbo.Placement PP
            ON BP.PsId = PP.PlacementId

        LEFT JOIN dbo.Placement P
            ON BP.Id = P.PlacementSystemId
               AND P.DataSourceInstanceId = 50366

        INNER JOIN dbo.PlacementTeams PT
            ON ISNULL(PP.PlacementId, P.PlacementId) = PT.PlacementId

        LEFT JOIN ref.Team T
            ON T.TeamId = PT.TeamId
    WHERE
        BP.ServicingPlatformId IS NULL
        AND PT.IsDeleted = 0;

    -- Then default rest to 50366
    UPDATE BPStaging.Placement
    SET ServicingPlatformId = 50366
    WHERE
        ServicingPlatformId IS NULL;

    -- Merge change from BP
    MERGE INTO dbo.Placement T
    USING (
        SELECT
            TruncDescription = CAST(inner_select.Description AS NVARCHAR(255))
          , PS.PlacementStatusId
          , inner_select.InceptionDate
          , ExpiryDate = ISNULL(inner_select.ExpiryDate, DATEADD(YEAR, 1, inner_select.InceptionDate))
          , inner_select.CancellationReasonId
          , inner_select.PsId
          , inner_select.Id
          , inner_select.ServicingPlatformId
          , PlacementOpportunityTypeId = OT.OpportunityTypeId
          , inner_select.AppraisalTypeId
          , inner_select.InsuranceTypeId
          , inner_select.InstructionDetails
          , inner_select.RiskLocationId
          , inner_select.Comments
          , inner_select.BrokingSegmentId
          , BrokingRegionId = inner_select.RegionId
          , inner_select.BrokingSubSegmentId
          , inner_select.IndustryId
          , inner_select.ProducingOfficeId
          , inner_select.ProgramTypeId
          , RenewedFromPlacementId = COALESCE(rfp.PlacementId, inner_select.PsId)
          , inner_select.RenewableOptionId
          , inner_select.NotRemarketingReasonId
          , DataSourceInstanceId = 50366
          , inner_select.InceptionStartTime
          , inner_select.ExpiryStartTime
          , inner_select.TimeZoneRepresentionId
          , inner_select.PricingFactorId
          , inner_select.VerticalIndustryId
          , MTACreatedFromPlacementId = mtap.PlacementId
          , MTATypeId = MT.MTATypeId
          , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                         THEN inner_select.ValidTo
                                     ELSE inner_select.ValidFrom END
          , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            Id
          , PsId
          , Description
          , PlacementStatusId
          , InceptionDate
          , OpportunityTypeId
          , InsuranceTypeId
          , AppraisalTypeId
          , InstructionDetails
          , ValidFrom
          , ValidTo
          , ServicingPlatformId
          , CancellationReasonId
          , ExpiryDate
          , Comments
          , RiskLocationId
          , IndustryId
          , BrokingSegmentId
          , ProgramTypeId
          , RenewedFromPlacementId
          , RegionId
          , BrokingSubSegmentId
          , TacitCount
          , ProducingOfficeId
          , TotalPremium
          , MinimumPremium
          , DepositPremium
          , PaymentPeriodId
          , PremiumExtendedReportingPeriodId
          , TotalPremiumCurrencyTypeId
          , MinimumPremiumCurrencyTypeId
          , DepositPremiumCurrencyTypeId
          , NotRemarketingReason
          , ScopeId
          , RiskDefinitionElementId
          , NotRemarketingReasonId
          , ExpiringRiskDefinitionElementId
          , RenewableOptionId
          , UserGroupId
          , AllowNoExpiringPolicy
          , InceptionStartTime
          , ExpiryStartTime
          , TimeZoneRepresentionId
          , PricingFactorId
          , VerticalIndustryId
          , MTACreatedFromPlacementId
          , MTATypeId
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.Placement
    ) inner_select
             LEFT JOIN dbo.Placement rfp
                 ON rfp.PlacementSystemId IS NOT NULL
                    AND rfp.PlacementSystemId = inner_select.RenewedFromPlacementId
                    AND rfp.DataSourceInstanceId = 50366

             LEFT JOIN dbo.Placement mtap
                 ON mtap.PlacementSystemId = inner_select.MTACreatedFromPlacementId
                    AND mtap.DataSourceInstanceId = 50366

             LEFT JOIN ref.PlacementStatus PS
                 ON PS.PlacementStatusKey = CAST(inner_select.PlacementStatusId AS NVARCHAR(100))
                    AND PS.DataSourceInstanceId = 50366

             LEFT JOIN ref.OpportunityType OT
                 ON OT.OpportunityTypeKey = CAST(inner_select.OpportunityTypeId AS NVARCHAR(255))
                    AND OT.DataSourceInstanceId = 50366

             LEFT JOIN ref.MTAType MT
                 ON MT.MTATypeKey = CAST(inner_select.MTATypeId AS NVARCHAR(255))
                    AND MT.DataSourceInstanceId = 50366
        WHERE
            inner_select.RowNo = 1
    ) S
    ON S.Id = T.PlacementSystemId
       AND S.DataSourceInstanceId = T.DataSourceInstanceId
    WHEN MATCHED AND NOT EXISTS (
    -- As this replaces using HASHBYTES which are case insensitive using COLLATE on strings.
    SELECT
        T.PlacementName COLLATE SQL_Latin1_General_CP1_CS_AS
      , T.PlacementStatusId
      , T.InceptionDate
      , T.RenewedFromPlacementId
      , T.ServicingPlatformId
      , T.ExpiryDate
      , T.PlacementOpportunityTypeId
      , T.CancellationReasonId
      , T.AppraisalTypeId
      , T.RefInsuranceTypeId
      , T.RiskLocationId
      , T.Comments COLLATE SQL_Latin1_General_CP1_CS_AS
      , T.ProducingOfficeId
      , T.InstructionDetails COLLATE SQL_Latin1_General_CP1_CS_AS
      , T.BrokingSegmentId
      , T.IndustryId
      , T.BrokingSubSegmentId
      , T.BrokingRegionId
      , T.ProgramTypeId
      , T.RenewableOptionId
      , T.NotRemarketingReasonId
      , T.DataSourceInstanceId
      , T.InceptionStartTime
      , T.ExpiryStartTime
      , T.TimeZoneRepresentionId
      , T.PricingFactorId
      , T.IsDeleted
      , T.VerticalIndustryId
      , T.MTACreatedFromPlacementId
      , T.MTATypeId
    INTERSECT
    SELECT
        S.TruncDescription COLLATE SQL_Latin1_General_CP1_CS_AS
      , S.PlacementStatusId
      , S.InceptionDate
      , COALESCE(S.RenewedFromPlacementId, T.RenewedFromPlacementId)
      , CASE WHEN S.ServicingPlatformId = 50366
                  OR T.ServicingPlatformId IS NULL
                 THEN T.ServicingPlatformId
             ELSE S.ServicingPlatformId END
      , S.ExpiryDate
      , S.PlacementOpportunityTypeId
      , S.CancellationReasonId
      , S.AppraisalTypeId
      , S.InsuranceTypeId
      , S.RiskLocationId
      , S.Comments COLLATE SQL_Latin1_General_CP1_CS_AS
      , S.ProducingOfficeId
      , S.InstructionDetails COLLATE SQL_Latin1_General_CP1_CS_AS
      , S.BrokingSegmentId
      , S.IndustryId
      , S.BrokingSubSegmentId
      , S.BrokingRegionId
      , S.ProgramTypeId
      , S.RenewableOptionId
      , S.NotRemarketingReasonId
      , S.DataSourceInstanceId
      , S.InceptionStartTime
      , S.ExpiryStartTime
      , S.TimeZoneRepresentionId
      , S.PricingFactorId
      , S.IsDeleted
      , S.VerticalIndustryId
      , S.MTACreatedFromPlacementId
      , S.MTATypeId
)
        THEN UPDATE SET
                 T.PlacementName = S.TruncDescription
               , T.PlacementStatusId = S.PlacementStatusId
               , T.InceptionDate = S.InceptionDate
               , T.RenewedFromPlacementId = COALESCE(S.RenewedFromPlacementId, T.RenewedFromPlacementId) --don't overwrite to blank
               , T.LastUpdatedUser = 'FMAImport'
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.ServicingPlatformId = CASE WHEN S.ServicingPlatformId = 50366
                                                   OR T.ServicingPlatformId IS NULL
                                                  THEN T.ServicingPlatformId
                                              ELSE S.ServicingPlatformId END
               , T.ExpiryDate = S.ExpiryDate
               , T.PlacementOpportunityTypeId = S.PlacementOpportunityTypeId
               , T.CancellationReasonId = S.CancellationReasonId
               , T.AppraisalTypeId = S.AppraisalTypeId
               , T.RefInsuranceTypeId = S.InsuranceTypeId
               , T.InstructionDetails = S.InstructionDetails
               , T.RiskLocationId = S.RiskLocationId
               , T.Comments = S.Comments
               , T.ProducingOfficeId = S.ProducingOfficeId
               , T.BrokingSegmentId = S.BrokingSegmentId
               , T.IndustryId = S.IndustryId
               , T.BrokingSubSegmentId = S.BrokingSubSegmentId
               , T.BrokingRegionId = S.BrokingRegionId
               /*
                    This is used by COL.
                        3 means it has been sent.
                        1 is ready to send.
                        The intention seems to be if there is an update and it has not been successfully sent (3) then reset it back to 1 (ready to send)
               */
               , T.PlacementCompletionStatusId = CASE WHEN S.ServicingPlatformId = 50003
                                                           AND ISNULL(T.PlacementCompletionStatusId, 1) <> 3
                                                          THEN 1
                                                      ELSE T.PlacementCompletionStatusId END
               , T.ProgramTypeId = S.ProgramTypeId
               , T.RenewableOptionId = S.RenewableOptionId
               , T.NotRemarketingReasonId = S.NotRemarketingReasonId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.InceptionStartTime = S.InceptionStartTime
               , T.ExpiryStartTime = S.ExpiryStartTime
               , T.TimeZoneRepresentionId = S.TimeZoneRepresentionId
               , T.PricingFactorId = S.PricingFactorId
               , T.IsDeleted = S.IsDeleted
               , T.VerticalIndustryId = S.VerticalIndustryId
               , T.MTACreatedFromPlacementId = S.MTACreatedFromPlacementId
               , T.MTATypeId = S.MTATypeId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED
        THEN INSERT (
                 PlacementName
               , PlacementStatusId
               , InceptionDate
               , ExpiryDate
               , IsDeleted
               , CancellationReasonId
               , RenewedFromPlacementId
               , CreatedUser
               , CreatedUTCDate
               , LastUpdatedUser
               , LastUpdatedUTCDate
               , PlacementSystemId
               , ServicingPlatformId
               , PlacementOpportunityTypeId
               , AppraisalTypeId
               , RefInsuranceTypeId
               , InstructionDetails
               , RiskLocationId
               , Comments
               , BrokingSegmentId
               , BrokingRegionId
               , BrokingSubSegmentId
               , IndustryId
               , ProducingOfficeId
               , PlacementCompletionStatusId
               , ProgramTypeId
               , RenewableOptionId
               , NotRemarketingReasonId
               , DataSourceInstanceId
               , InceptionStartTime
               , ExpiryStartTime
               , TimeZoneRepresentionId
               , PricingFactorId
               , VerticalIndustryId
               , MTACreatedFromPlacementId
               , MTATypeId
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.TruncDescription
                   , S.PlacementStatusId
                   , S.InceptionDate
                   , S.ExpiryDate
                   , S.IsDeleted
                   , S.CancellationReasonId
                   , S.PsId -- For an insert it uses this but not for an update. Left as historical.
                   , 'FMAImport' -- Not sure why it does this only for insert but historical so left
                   , GETUTCDATE()
                   , 'FMAImport'
                   , GETUTCDATE()
                   , S.Id
                   , S.ServicingPlatformId
                   , S.PlacementOpportunityTypeId
                   , S.AppraisalTypeId
                   , S.InsuranceTypeId
                   , S.InstructionDetails
                   , S.RiskLocationId
                   , S.Comments
                   , S.BrokingSegmentId
                   , S.BrokingRegionId
                   , S.BrokingSubSegmentId
                   , S.IndustryId
                   , S.ProducingOfficeId
                   , CASE WHEN S.ServicingPlatformId = 50003
                              THEN 1 /* Set to ready to send for COL. */
                          ELSE NULL END
                   , S.ProgramTypeId
                   , S.RenewableOptionId
                   , S.NotRemarketingReasonId
                   , S.DataSourceInstanceId
                   , S.InceptionStartTime
                   , S.ExpiryStartTime
                   , S.TimeZoneRepresentionId
                   , S.PricingFactorId
                   , S.VerticalIndustryId
                   , S.MTACreatedFromPlacementId
                   , S.MTATypeId
                   , S.SourceUpdatedDate
                 )
    OUTPUT $ACTION
    INTO @Actions;

    -- Get the counts of inserts and updates from the merge.
    SELECT
        @InsertedCount = @InsertedCount + SUM(CASE WHEN Change = 'INSERT'
                                                       THEN 1
                                                   ELSE 0 END
                                          )
      , @UpdatedCount = @UpdatedCount + SUM(CASE WHEN Change = 'UPDATE'
                                                     THEN 1
                                                 ELSE 0 END
                                        )
    -- No deletes to record.
    FROM
        @Actions;

    --set renewed from id
    UPDATE pl
    SET pl.RenewedFromPlacementId = pf.PlacementId
    FROM
        dbo.Placement pl
        INNER JOIN BPStaging.Placement stg
            ON pl.PlacementSystemId = stg.Id
               AND pl.DataSourceInstanceId = 50366

        INNER JOIN (
            SELECT
                PlacementId
              , PlacementSystemId
            FROM
                dbo.Placement
            WHERE
                PlacementSystemId IS NOT NULL
                AND DataSourceInstanceId = 50366
        ) pf
            ON stg.RenewedFromPlacementId = pf.PlacementSystemId
    WHERE
        pl.PlacementSystemId IS NOT NULL
        AND pl.DataSourceInstanceId = 50366
        AND ISNULL(pl.RenewedFromPlacementId, 0) <> pf.PlacementId;

    -- PBI 136146 - Too many updates being recorded. Count them separately.
    SELECT @RenewedFromUpdatedCount = @@ROWCOUNT;

    SET @Action =
        @Action + N' Updates setting Renewed From - ' + CONVERT(NVARCHAR(10), ISNULL(@RenewedFromUpdatedCount, 0))
        + N',';

    -- Update renewed to for 1st time renewals from PS
    UPDATE pl
    SET pl.RenewedToPlacementId = rt.PlacementId
    FROM
        dbo.Placement pl
        INNER JOIN dbo.Placement rt
            ON pl.PlacementId = rt.RenewedFromPlacementId
    WHERE
        pl.RenewedToPlacementId IS NULL;

    -- PBI 136146 - Too many updates being recorded. Count them separately.
    SELECT @RenewedToIdYear1UpdatedCount = @@ROWCOUNT;

    SET @Action =
        @Action + N' Updates setting 1st time Renewed To - '
        + CONVERT(NVARCHAR(10), ISNULL(@RenewedToIdYear1UpdatedCount, 0)) + N',';

    /* Had performance problems with this. Added a feature flag so we can turn it off if necessary */
    IF PactConfig.GetControlValue_Bit('TurnOffSetRenewalIDYear2') = 0
    BEGIN
        -- Set renewed to id from BP for year 2+
        DROP TABLE IF EXISTS #filtered_placements;

        SELECT
            PlacementId
          , PlacementSystemId
        INTO #filtered_placements
        FROM
            dbo.Placement
        WHERE
            DataSourceInstanceId = 50366
            AND PlacementSystemId IS NOT NULL;

        CREATE INDEX IX_#filtered_placements
        ON #filtered_placements
        (
            PlacementSystemId
        )
        INCLUDE
        (
            PlacementId
        );

        /* This code looks very similar to the update above except it only works if it came in via a staging record. */
        /* This used to rely on the RenewedToPlacementId that was computed and put in the staging record.            */
        /* That is no longer done but we can effectively do roughly the same thing here by looking up the Renewed    */
        /* From and finding the placement that links to to point it at the correct placement.                        */
        /* The previous code can't change a value but this can. Is this really only year 2+ ????                     */
        UPDATE pl
        SET pl.RenewedToPlacementId = plact.PlacementId
        FROM
            BPStaging.Placement stg
            INNER JOIN #filtered_placements pf
                ON pf.PlacementSystemId = stg.RenewedFromPlacementId

            INNER JOIN #filtered_placements plact
                ON plact.PlacementSystemId = stg.Id

            INNER JOIN dbo.Placement pl
                ON pl.PlacementId = pf.PlacementId
        WHERE
            pl.DataSourceInstanceId = 50366
            AND pl.PlacementSystemId IS NOT NULL
            AND (
                pl.RenewedToPlacementId IS NULL
                OR pl.RenewedToPlacementId <> plact.PlacementId
            );

        -- PBI 136146 - Too many updates being recorded. Count them separately.
        SELECT @RenewedToIdYear2UpdatedCount = @@ROWCOUNT;

        SET @Action =
            @Action + N' Updates setting year 2+ Renewed To - '
            + CONVERT(NVARCHAR(10), ISNULL(@RenewedToIdYear2UpdatedCount, 0)) + N'.';
    END;

    --Set MTA MTACreatedFromPlacementId
    UPDATE pl
    SET pl.MTACreatedFromPlacementId = pmta.PlacementId
    FROM
        dbo.Placement pl
        INNER JOIN BPStaging.Placement stg
            ON pl.PlacementSystemId = stg.Id
               AND pl.DataSourceInstanceId = 50366

        INNER JOIN (
            SELECT
                PlacementId
              , PlacementSystemId
            FROM
                dbo.Placement
            WHERE
                PlacementSystemId IS NOT NULL
                AND DataSourceInstanceId = 50366
        ) pmta
            ON stg.MTACreatedFromPlacementId = pmta.PlacementSystemId
    WHERE
        pl.PlacementSystemId IS NOT NULL
        AND pl.DataSourceInstanceId = 50366
        AND ISNULL(pl.MTACreatedFromPlacementId, 0) <> pmta.PlacementId;

    SELECT @MTACreatedFromUpdatedCount = @@ROWCOUNT;

    SET @Action =
        @Action + N' Updates setting MTA Created From - '
        + CONVERT(NVARCHAR(10), ISNULL(@MTACreatedFromUpdatedCount, 0)) + N',';

-- PBI 158421 - Code extracted into separate SP ([BPStaging].[UpdatePlacementDatesAndStatus])
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;