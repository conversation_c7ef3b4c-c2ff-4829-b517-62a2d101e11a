/*
Lineage
ref.ExposurePeriod.ExposurePeriodId=BPStaging.ExposurePeriodGroup.Id
ref.ExposurePeriod.ExposurePeriodKey=BPStaging.ExposurePeriodGroup.LabelTranslationKey
ref.ExposurePeriod.ExposurePeriod=BPStaging.ExposurePeriodGroup.Text
ref.ExposurePeriod.SourceUpdatedDate=BPStaging.ExposurePeriodGroup.ValidFrom
ref.ExposurePeriod.IsDeprecated=BPStaging.ExposurePeriodGroup.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_ExposurePeriod
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ExposurePeriod';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.ExposurePeriod T
    USING (
        SELECT
            ExposurePeriodId = Id
          , ExposurePeriodKey = LabelTranslationKey
          , ExposurePeriod = Text
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.ExposurePeriodGroup
    ) S
    ON T.ExposurePeriodId = S.ExposurePeriodId
    WHEN NOT MATCHED
        THEN INSERT (
                 ExposurePeriodId
               , DataSourceInstanceId
               , ExposurePeriodKey
               , ExposurePeriod
               , SourceUpdatedDate
               , IsDeprecated
               , ETLCreatedDate
               , ETLUpdatedDate
             )
             VALUES
                 (
                     S.ExposurePeriodId
                   , S.DataSourceInstanceId
                   , S.ExposurePeriodKey
                   , S.ExposurePeriod
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                   , GETUTCDATE()
                   , GETUTCDATE()
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.ExposurePeriodKey
                               , T.ExposurePeriod
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.ExposurePeriodKey
                               , S.ExposurePeriod
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ExposurePeriodKey = S.ExposurePeriodKey
               , T.ExposurePeriod = S.ExposurePeriod
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);