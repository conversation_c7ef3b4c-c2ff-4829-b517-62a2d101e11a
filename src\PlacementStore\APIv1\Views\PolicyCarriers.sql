/*
Lineage
PolicyId=dbo.Policy.PolicyId
LastUpdatedUTCDate=dbo.PolicyPartyRelationship.ETLUpdatedDate
PartyId=dbo.PolicyPartyRelationship.PartyId
ExpiryDate=dbo.Policy.ExpiryDate
FacilityId=ref.Facility.PSFacilityPolicyId
FacilitySectionId=ref.FacilitySection.PSFacilityPolicySectionId
MarketKindId=ref.Facility.PSFacilityPolicyId
Incumbent=dbo.PolicyPartyRelationship.PACTPolicyPartyRoleId
IsLead=dbo.PolicyPartyRelationship.IsPrimaryParty
IsLead=ref.Facility.PSFacilityPolicyId
IsLapsed=ref.Facility.PSFacilityPolicyId
IsLapsed=ref.Facility.InceptionDate
IsLapsed=ref.Facility.ExpiryDate
*/
/*
NB: This logic is duplicated in the function APIv1.fn_PolicyCarriers to make the query more efficient when called from Task.GetTaskMetadata.

!!!!!WHEN CHANGING THIS LOGIC, ENSURE THAT THE FUNCTION APIv1.fn_PolicyCarriers IS ALSO UPDATED!!!!!
*/
CREATE VIEW APIv1.PolicyCarriers
AS
SELECT
    PolicyId
  , LastUpdatedUTCDate = MAX(ETLUpdatedDate)
  , pc.PartyId
  , ExpiryDate = MAX(ExpiryDate)
  , FacilityId
  , FacilitySectionId
  , pc.MarketKindId
  , Incumbent = MAX(pc.Incumbent)
  , IsLead = MAX(pc.IsLead)
  , IsLapsed = MAX(pc.IsLapsed)
FROM (
    SELECT DISTINCT
           P.PolicyId
         , PPR.ETLUpdatedDate --This should be based on Policy Party Relationship dates and its added in PPR table 
         , PartyId = CASE WHEN f.PSFacilityPolicyId IS NULL
                              THEN PPR.PartyId END
         , P.ExpiryDate
         , FacilityId = f.PSFacilityPolicyId
         , FacilitySectionId = fs.PSFacilityPolicySectionId
         , MarketKindId = CASE WHEN f.PSFacilityPolicyId IS NOT NULL
                                   THEN 2
                               ELSE 1 END
         , Incumbent = CASE WHEN PPR.PACTPolicyPartyRoleId IS NOT NULL
                                THEN 1
                            ELSE 0 END
         , IsLead = CASE WHEN f.PSFacilityPolicyId IS NOT NULL
                             THEN 0
                         ELSE PPR.IsPrimaryParty END --we can't tell if facility is lead overall
         , IsLapsed = CASE WHEN f.PSFacilityPolicyId IS NULL
                               THEN 0 --mark open market as valid
                           WHEN f.PSFacilityPolicyId IS NOT NULL
                                AND DATEADD(DAY, 1, P.ExpiryDate) BETWEEN f.InceptionDate AND f.ExpiryDate
                               THEN 0 --valid renewal facility 
                           ELSE 1 END
    FROM
        dbo.Policy P
        INNER JOIN dbo.PolicyPartyRelationship PPR
            ON PPR.PolicyId = P.PolicyId

        INNER JOIN ref.PartyRole pr
            ON PPR.PartyRoleId = pr.PartyRoleId
               AND pr.IsDeprecated = 0

        LEFT JOIN ref.PartyRole parpr
            ON PPR.ParentPartyRoleId = parpr.PartyRoleId
               AND parpr.IsDeprecated = 0

        LEFT JOIN dbo.PolicyCarrierDetails pcd
            ON PPR.PartyId = pcd.PartyId
               AND PPR.PolicyId = pcd.PolicyId
               AND PPR.PartyRoleId = pcd.PartyRoleId
               AND pcd.IsDeleted = 0

        LEFT JOIN ref.FacilitySection fs
            ON pcd.LinkedFacilityPolicySectionId = fs.PSFacilityPolicySectionId

        LEFT JOIN ref.Facility f
            ON fs.FacilityId = f.FacilityId
    WHERE
        pr.GlobalPartyRoleId = '102' --carrier
        AND P.IsDeleted = 0
        AND pr.PartyRoleKey NOT IN (
                'BUREAU', 'BILLCO', 'SHAREBROKER'
            ) --exclude bureau and billing co 
        AND ISNULL(parpr.PartyRoleKey, 'N/A') NOT IN (
                'CONSORTIUM', 'POOL'
            ) --exclude children of pool or consortium
        AND PPR.IsDeleted = 0 --AND ppr.Incumbent = 1
                              --AND (ISNULL(ppr.FacilityId,0) = 0 OR (ppr.FacilityId > 0 AND ppr.CreatedUser != 'FMAImport')) --ignore FMA imported facilities
        AND P.RuleId > 0 --in scope
    UNION ALL

    --incumbent facility members
    SELECT DISTINCT
           P.PolicyId
         , LastUpdatedUTCDate = PPR.ETLUpdatedDate --This should be based on Policy Party Relationship dates and its added in PPR table 
         , PPR.PartyId
         , P.ExpiryDate
         , FacilityId = f.PSFacilityPolicyId
         , FacilitySectionId = fs.PSFacilityPolicySectionId
         , MarketKindId = 3
         , Incumbent = CASE WHEN PPR.PACTPolicyPartyRoleId IS NOT NULL
                                THEN 1
                            ELSE 0 END
         , IsLead = MAX(CAST(PPR.IsPrimaryParty AS INT)) OVER (PARTITION BY
                                                                   P.PolicyId
                                                                 , f.PSFacilityPolicyId
                                                                 , fs.PSFacilityPolicySectionId
                                                                 , PPR.PartyId
                                                               ORDER BY
                                                                   P.PolicyId
                                                                 , f.PSFacilityPolicyId
                                                                 , fs.PSFacilityPolicySectionId
                                                                 , PPR.PartyId
                                                         )
         , IsLapsed = CASE WHEN f.PSFacilityPolicyId IS NOT NULL
                                AND DATEADD(DAY, 1, P.ExpiryDate) BETWEEN f.InceptionDate AND f.ExpiryDate
                               THEN 0 --valid facility
                           ELSE 1 END
    FROM
        dbo.Policy P
        INNER JOIN dbo.PolicyPartyRelationship PPR
            ON PPR.PolicyId = P.PolicyId

        INNER JOIN ref.PartyRole pr
            ON PPR.PartyRoleId = pr.PartyRoleId
               AND pr.IsDeprecated = 0

        LEFT JOIN ref.PartyRole parpr
            ON PPR.ParentPartyRoleId = parpr.PartyRoleId
               AND parpr.IsDeprecated = 0

        LEFT JOIN dbo.PolicyCarrierDetails pcd
            ON PPR.PartyId = pcd.PartyId
               AND PPR.PolicyId = pcd.PolicyId
               AND PPR.PartyRoleId = pcd.PartyRoleId
               AND pcd.IsDeleted = 0

        INNER JOIN ref.FacilitySection fs
            ON pcd.LinkedFacilityPolicySectionId = fs.PSFacilityPolicySectionId

        LEFT JOIN ref.Facility f
            ON fs.FacilityId = f.FacilityId

        LEFT JOIN APIv1.FacilityCarriers fc
            ON f.PSFacilityPolicyId = fc.FacilityId
               AND fc.CarrierId = PPR.PartyId
               AND fc.IsDeleted = 0
    WHERE
        pr.GlobalPartyRoleId = '102' --carrier
        AND P.IsDeleted = 0
        AND pr.PartyRoleKey NOT IN (
                'BUREAU', 'BILLCO', 'SHAREBROKER'
            ) --exclude bureau and billing co 
        AND ISNULL(parpr.PartyRoleKey, 'N/A') NOT IN (
                'CONSORTIUM', 'POOL'
            ) --exclude children of pool or consortium
        AND PPR.IsDeleted = 0 --AND ppr.Incumbent = 1
        AND P.RuleId > 0 --in scope
) pc
GROUP BY
    PolicyId
  , pc.PartyId
  , pc.MarketKindId
  , FacilityId
  , FacilitySectionId;
