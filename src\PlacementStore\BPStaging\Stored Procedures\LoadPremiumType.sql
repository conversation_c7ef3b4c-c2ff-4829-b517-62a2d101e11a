/*
Lineage
ref.PremiumType.PremiumTypeId=BPStaging.PremiumType.Id
ref.PremiumType.PremiumTypeKey=BPStaging.PremiumType.LabelTranslationKey
ref.PremiumType.PremiumType=BPStaging.PremiumType.Text
ref.PremiumType.SourceUpdatedDate=BPStaging.PremiumType.ValidFrom
ref.PremiumType.IsDeprecated=BPStaging.PremiumType.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadPremiumType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.PremiumType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.PremiumType T
    USING (
        SELECT
            PremiumTypeId = Id
          , DataSourceInstanceId = 50366
          , PremiumTypeKey = LabelTranslationKey
          , PremiumType = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.PremiumType
    ) S
    ON T.PremiumTypeId = S.PremiumTypeId
    WHEN NOT MATCHED
        THEN INSERT (
                 PremiumTypeId
               , DataSourceInstanceId
               , PremiumTypeKey
               , PremiumType
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.PremiumTypeId
                   , S.DataSourceInstanceId
                   , S.PremiumTypeKey
                   , S.PremiumType
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.PremiumTypeId
                               , T.DataSourceInstanceId
                               , T.PremiumTypeKey
                               , T.PremiumType
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.PremiumTypeId
                               , S.DataSourceInstanceId
                               , S.PremiumTypeKey
                               , S.PremiumType
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.PremiumTypeId = S.PremiumTypeId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PremiumTypeKey = S.PremiumTypeKey
               , T.PremiumType = S.PremiumType
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
