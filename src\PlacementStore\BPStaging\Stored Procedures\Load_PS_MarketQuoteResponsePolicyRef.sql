/*
Lineage
PS.MarketQuoteResponsePolicyRef.MarketQuoteResponsePolicyRefKey=BPStaging.MarketQuoteResponsePolicyRef.Id
PS.MarketQuoteResponsePolicyRef.MarketQuoteResponseId=PS.MarketQuoteResponse.MarketQuoteResponseId
PS.MarketQuoteResponsePolicyRef.PolicyRefTypeId=ref.PolicyRefType.PolicyRefTypeId
PS.MarketQuoteResponsePolicyRef.PolicyRef=BPStaging.MarketQuoteResponsePolicyRef.PolicyRef
PS.MarketQuoteResponsePolicyRef.SourceUpdatedDate=BPStaging.MarketQuoteResponsePolicyRef.ValidTo
PS.MarketQuoteResponsePolicyRef.SourceUpdatedDate=BPStaging.MarketQuoteResponsePolicyRef.ValidFrom
PS.MarketQuoteResponsePolicyRef.IsDeleted=BPStaging.MarketQuoteResponsePolicyRef.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_MarketQuoteResponsePolicyRef
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketQuoteResponsePolicyRef';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.MarketQuoteResponsePolicyRef t
    USING (
        SELECT
            MarketQuoteResponsePolicyRefKey = CONCAT('MQRPR|', s.Id)
          , DataSourceInstanceId = 50366
          , mqr.MarketQuoteResponseId
          , rrt.PolicyRefTypeId
          , s.PolicyRef
          , SourceUpdatedDate = IIF(YEAR(s.ValidTo) < 9999, s.ValidTo, s.ValidFrom)
          , IsDeleted = IIF(YEAR(s.ValidTo) < 9999, 1, 0)
        FROM (
        SELECT
            Id
          , MarketQuoteResponseId
          , PolicyRefTypeId
          , PolicyRef
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.MarketQuoteResponsePolicyRef
    ) s
             INNER JOIN PS.MarketQuoteResponse mqr
                 ON mqr.MarketQuoteResponseId = s.MarketQuoteResponseId
                    AND mqr.DataSourceInstanceId = 50366

             INNER JOIN ref.PolicyRefType rrt
                 ON rrt.PolicyRefTypeKey = CONVERT(VARCHAR(50), s.PolicyRefTypeId)
                    AND mqr.DataSourceInstanceId = 50366
        WHERE
            s.RowNo = 1
    ) s
    ON s.MarketQuoteResponsePolicyRefKey = t.MarketQuoteResponsePolicyRefKey
       AND s.DataSourceInstanceId = t.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 MarketQuoteResponsePolicyRefKey
               , MarketQuoteResponseId
               , PolicyRefTypeId
               , PolicyRef
               , DataSourceInstanceId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.MarketQuoteResponsePolicyRefKey
                   , s.MarketQuoteResponseId
                   , s.PolicyRefTypeId
                   , s.PolicyRef
                   , s.DataSourceInstanceId
                   , s.SourceUpdatedDate
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.MarketQuoteResponsePolicyRefKey
                               , s.MarketQuoteResponseId
                               , s.PolicyRefTypeId
                               , s.PolicyRef
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.MarketQuoteResponsePolicyRefKey
                               , t.MarketQuoteResponseId
                               , t.PolicyRefTypeId
                               , t.PolicyRef
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.MarketQuoteResponsePolicyRefKey = s.MarketQuoteResponsePolicyRefKey
               , t.MarketQuoteResponseId = s.MarketQuoteResponseId
               , t.PolicyRefTypeId = s.PolicyRefTypeId
               , t.PolicyRef = s.PolicyRef
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.IsDeleted = s.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;