/*
Lineage
dbo.PlacementPremium.PlacementId=dbo.Placement.PlacementId
dbo.PlacementPremium.TotalPremium=BPStaging.Placement.TotalPremium
dbo.PlacementPremium.TotalPremiumCurrencyTypeId=BPStaging.Placement.TotalPremiumCurrencyTypeId
dbo.PlacementPremium.MinimumPremium=BPStaging.Placement.MinimumPremium
dbo.PlacementPremium.MinimumPremiumCurrencyTypeId=BPStaging.Placement.MinimumPremiumCurrencyTypeId
dbo.PlacementPremium.DepositPremium=BPStaging.Placement.DepositPremium
dbo.PlacementPremium.DepositPremiumCurrencyTypeId=BPStaging.Placement.DepositPremiumCurrencyTypeId
dbo.PlacementPremium.PaymentPeriodId=BPStaging.Placement.PaymentPeriodId
dbo.PlacementPremium.PremiumExtendedReportingPeriodId=BPStaging.Placement.PremiumExtendedReportingPeriodId
dbo.PlacementPremium.IsDeleted=BPStaging.Placement.ValidTo
dbo.PlacementPremium.SourceUpdatedDate=BPStaging.Placement.ValidTo
dbo.PlacementPremium.SourceUpdatedDate=BPStaging.Placement.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadPlacementPremium
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementPremium';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.Placement)
BEGIN TRY
    MERGE dbo.PlacementPremium T
    USING (
        SELECT
            P.PlacementId
          , inner_select.Id
          , inner_select.TotalPremium
          , inner_select.TotalPremiumCurrencyTypeId
          , inner_select.MinimumPremium
          , inner_select.MinimumPremiumCurrencyTypeId
          , inner_select.DepositPremium
          , inner_select.DepositPremiumCurrencyTypeId
          , inner_select.PaymentPeriodId
          , inner_select.PremiumExtendedReportingPeriodId
          , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                         THEN inner_select.ValidTo
                                     ELSE inner_select.ValidFrom END
          , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            Id
          , ValidFrom
          , ValidTo
          , TotalPremium
          , TotalPremiumCurrencyTypeId
          , MinimumPremium
          , MinimumPremiumCurrencyTypeId
          , DepositPremium
          , DepositPremiumCurrencyTypeId
          , PaymentPeriodId
          , PremiumExtendedReportingPeriodId
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.Placement
    ) inner_select
             INNER JOIN dbo.Placement P
                 ON inner_select.Id = P.PlacementSystemId
                    AND P.DataSourceInstanceId = 50366
        WHERE
            inner_select.RowNo = 1
    ) S
    ON S.PlacementId = T.PlacementId
    WHEN NOT MATCHED AND (/* Only insert if we have an amount. */
                         S.TotalPremium IS NOT NULL
                         OR S.MinimumPremium IS NOT NULL
                         OR S.DepositPremium IS NOT NULL
                         OR S.PaymentPeriodId IS NOT NULL
                         OR S.PremiumExtendedReportingPeriodId IS NOT NULL
                     )
        THEN INSERT (
                 PlacementId
               , TotalPremium
               , TotalPremiumCurrencyTypeId
               , MinimumPremium
               , MinimumPremiumCurrencyTypeId
               , DepositPremium
               , DepositPremiumCurrencyTypeId
               , PaymentPeriodId
               , PremiumExtendedReportingPeriodId
               , CreatedUTCDate
               , CreatedUser
               , LastUpdatedUTCDate
               , LastUpdatedUser
               , IsDeleted
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.PlacementId
                   , S.TotalPremium
                   , S.TotalPremiumCurrencyTypeId
                   , S.MinimumPremium
                   , S.MinimumPremiumCurrencyTypeId
                   , S.DepositPremium
                   , S.DepositPremiumCurrencyTypeId
                   , S.PaymentPeriodId
                   , S.PremiumExtendedReportingPeriodId
                   , GETUTCDATE()
                   , 'FMAUser'
                   , GETUTCDATE()
                   , 'FMAUser'
                   , S.IsDeleted
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.TotalPremium
      , S.TotalPremiumCurrencyTypeId
      , S.MinimumPremium
      , S.MinimumPremiumCurrencyTypeId
      , S.DepositPremium
      , S.DepositPremiumCurrencyTypeId
      , S.PaymentPeriodId
      , S.PremiumExtendedReportingPeriodId
      , S.IsDeleted
    INTERSECT
    SELECT
        T.TotalPremium
      , T.TotalPremiumCurrencyTypeId
      , T.MinimumPremium
      , T.MinimumPremiumCurrencyTypeId
      , T.DepositPremium
      , T.DepositPremiumCurrencyTypeId
      , T.PaymentPeriodId
      , T.PremiumExtendedReportingPeriodId
      , T.IsDeleted
)
        THEN UPDATE SET
                 T.TotalPremium = S.TotalPremium
               , T.TotalPremiumCurrencyTypeId = S.TotalPremiumCurrencyTypeId
               , T.MinimumPremium = S.MinimumPremium
               , T.MinimumPremiumCurrencyTypeId = S.MinimumPremiumCurrencyTypeId
               , T.DepositPremium = S.DepositPremium
               , T.DepositPremiumCurrencyTypeId = S.DepositPremiumCurrencyTypeId
               , T.PaymentPeriodId = S.PaymentPeriodId
               , T.PremiumExtendedReportingPeriodId = S.PremiumExtendedReportingPeriodId
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.LastUpdatedUser = 'FMAUser'
               , T.IsDeleted = S.IsDeleted
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
