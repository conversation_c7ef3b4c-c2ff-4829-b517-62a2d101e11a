/*
Lineage
dbo.PlacementStructure.PlacementId=dbo.Placement.PlacementId
dbo.PlacementStructure.PlacementSystemId=BPStaging.RiskStructure.PlacementId
dbo.PlacementStructure.PlacementSystemStructureId=BPStaging.RiskStructure.Id
dbo.PlacementStructure.ProductId=BPStaging.RiskStructure.ProductId
dbo.PlacementStructure.SourceUpdatedDate=BPStaging.RiskStructure.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadPlacementStructure
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementStructure';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.PlacementStructure T
    USING (
        SELECT
            P.PlacementId
          , PlacementSystemId = PS.PlacementId
          , PlacementSystemStructureId = PS.Id
          , PS.ProductId
          , SourceUpdatedDate = PS.ValidFrom
          , IsDeleted = CAST(0 AS BIT)
        FROM
            BPStaging.RiskStructure PS
            INNER JOIN dbo.Placement P
                ON P.PlacementSystemId = PS.PlacementId
                   AND P.DataSourceInstanceId = 50366
    ) S
    ON S.PlacementSystemStructureId = T.PlacementSystemStructureId
    WHEN NOT MATCHED
        THEN INSERT (
                 PlacementId
               , PlacementSystemId
               , PlacementSystemStructureId
               , ProductId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.PlacementId
                   , S.PlacementSystemId
                   , S.PlacementSystemStructureId
                   , S.ProductId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.PlacementId
                               , S.PlacementSystemId
                               , S.PlacementSystemStructureId
                               , S.ProductId
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.PlacementId
                               , T.PlacementSystemId
                               , T.PlacementSystemStructureId
                               , T.ProductId
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.PlacementId = S.PlacementId
               , T.PlacementSystemId = S.PlacementSystemId
               , T.PlacementSystemStructureId = S.PlacementSystemStructureId
               , T.ProductId = S.ProductId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
GO