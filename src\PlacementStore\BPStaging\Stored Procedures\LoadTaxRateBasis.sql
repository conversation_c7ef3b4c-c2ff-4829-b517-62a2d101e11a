/*
Lineage
ref.TaxRateBasis.TaxRateBasisId=BPStaging.TaxRateBasis.Id
ref.TaxRateBasis.TaxRateBasisKey=BPStaging.TaxRateBasis.LabelTranslationKey
ref.TaxRateBasis.TaxRateBasis=BPStaging.TaxRateBasis.Text
ref.TaxRateBasis.SourceUpdatedDate=BPStaging.TaxRateBasis.ValidFrom
ref.TaxRateBasis.IsDeprecated=BPStaging.TaxRateBasis.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadTaxRateBasis
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.TaxRateBasis';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.TaxRateBasis T
    USING (
        SELECT
            TaxRateBasisId = Id
          , DataSourceInstanceId = 50366
          , TaxRateBasisKey = LabelTranslationKey
          , TaxRateBasis = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.TaxRateBasis
    ) S
    ON T.TaxRateBasisId = S.TaxRateBasisId
    WHEN NOT MATCHED
        THEN INSERT (
                 TaxRateBasisId
               , DataSourceInstanceId
               , TaxRateBasisKey
               , TaxRateBasis
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.TaxRateBasisId
                   , S.DataSourceInstanceId
                   , S.TaxRateBasisKey
                   , S.TaxRateBasis
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.TaxRateBasisId
                               , T.DataSourceInstanceId
                               , T.TaxRateBasisKey
                               , T.TaxRateBasis
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.TaxRateBasisId
                               , S.DataSourceInstanceId
                               , S.TaxRateBasisKey
                               , S.TaxRateBasis
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.TaxRateBasisId = S.TaxRateBasisId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.TaxRateBasisKey = S.TaxRateBasisKey
               , T.TaxRateBasis = S.TaxRateBasis
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
