/*
Lineage
dbo.PlacementPolicy.SourcePlacementPolicyId=BPStaging.PlacementPolicy.Id
dbo.PlacementPolicy.PlacementId=dbo.Placement.PlacementId
dbo.PlacementPolicy.PolicyId=dbo.Policy.PolicyId
dbo.PlacementPolicy.PlacementPolicyRelationshipTypeId=BPStaging.PlacementPolicy.PolicyTypeId
dbo.PlacementPolicy.IsDeleted=BPStaging.PlacementPolicy.ValidTo
dbo.PlacementPolicy.IsDeleted=dbo.Placement.PlacementStatusId
dbo.PlacementPolicy.IsDeleted=dbo.Placement.CancellationReasonId
dbo.PlacementPolicy.SourceUpdatedDate=BPStaging.PlacementPolicy.ValidTo
dbo.PlacementPolicy.SourceUpdatedDate=BPStaging.PlacementPolicy.ValidFrom
dbo.PlacementPolicy.ServicingPlatformId=dbo.Placement.ServicingPlatformId
*/
CREATE PROCEDURE BPStaging.LoadPlacementPolicies
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementPolicy';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.PlacementPolicy)
BEGIN TRY
    DECLARE @PlacementStatusId_Merged INT =
            (
                SELECT PlacementStatusId FROM ref.PlacementStatus WHERE PlacementStatusKey = '4' AND DataSourceInstanceId = 50366
            );

    MERGE INTO dbo.PlacementPolicy T
    USING (
        SELECT
            PO.PolicyId
          , inner_select.SourcePlacementPolicyId
          , PL.PlacementId
          , DataSourceInstanceId = 50366
          , PlacementPolicyRelationshipTypeId = inner_select.PolicyTypeId
          , PL.ServicingPlatformId
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             WHEN PL.PlacementStatusId = @PlacementStatusId_Merged
                                  OR PL.CancellationReasonId = 6 /* Undo Merge */
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            PolicyId = PSPolicyID
          , SourcePlacementPolicyId = Id
          , PlacementId
          , PolicyTypeId
          , ValidTo
          , ValidFrom
          , RowNo = ROW_NUMBER() OVER (PARTITION BY PlacementId, PSPolicyID ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.PlacementPolicy
    ) inner_select
             INNER JOIN dbo.Placement PL
                 ON PL.PlacementSystemId = inner_select.PlacementId
                    AND PL.DataSourceInstanceId = 50366

             INNER JOIN dbo.Policy PO
                 ON PO.PolicyId = inner_select.PolicyId
        WHERE
            inner_select.RowNo = 1
    ) S
    ON S.PlacementId = T.PlacementId
       AND S.PolicyId = T.PolicyId
       AND S.DataSourceInstanceId = T.DataSourceInstanceId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 SourcePlacementPolicyId
               , PlacementId
               , PolicyId
               , DataSourceInstanceId
               , PlacementPolicyRelationshipTypeId
               , IsDeleted
               , CreatedDate
               , UpdatedDate
               , SourceUpdatedDate
               , ServicingPlatformId
             )
             VALUES
                 (
                     S.SourcePlacementPolicyId
                   , S.PlacementId
                   , S.PolicyId
                   , S.DataSourceInstanceId
                   , S.PlacementPolicyRelationshipTypeId
                   , S.IsDeleted
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.ServicingPlatformId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.SourcePlacementPolicyId
      , T.DataSourceInstanceId
      , T.PlacementPolicyRelationshipTypeId
      , T.IsDeleted
      , T.ServicingPlatformId
    INTERSECT
    SELECT
        S.SourcePlacementPolicyId
      , S.DataSourceInstanceId
      , S.PlacementPolicyRelationshipTypeId
      , S.IsDeleted
      , S.ServicingPlatformId
)
        THEN UPDATE SET
                 T.SourcePlacementPolicyId = S.SourcePlacementPolicyId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PlacementPolicyRelationshipTypeId = S.PlacementPolicyRelationshipTypeId
               , T.IsDeleted = S.IsDeleted
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.UpdatedDate = GETUTCDATE()
               , T.ServicingPlatformId = S.ServicingPlatformId
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    /* 
            If there are Placements marked as Merged then we need to ensure their 
            PlacementPolicy links are logically deleted if they're not already. 
            The MERGE will catch any that are in the staging table. 
            This will catch ones where only the Placement has changed.
        */
    UPDATE pp
    SET
        pp.IsDeleted = 1
      , pp.UpdatedDate = GETUTCDATE()
    FROM
        dbo.PlacementPolicy pp
        INNER JOIN dbo.Placement pl
            ON pl.PlacementId = pp.PlacementId
    WHERE
        pp.IsDeleted = 0
        AND pp.DataSourceInstanceId = 50366
        AND (
            pl.PlacementStatusId = @PlacementStatusId_Merged
            OR pl.CancellationReasonId = 6 /* Undo Merge */
        );
    ;

    SET @UpdatedCount = ISNULL(@UpdatedCount, 0) + @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;