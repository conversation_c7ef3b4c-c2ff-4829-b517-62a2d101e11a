/*
Lineage
dbo.PlacementSystemTable.TableId=BPStaging.PlacementSystemTable.AuditedTemporalTableId
dbo.PlacementSystemTable.SchemaName=BPStaging.PlacementSystemTable.SchemaName
dbo.PlacementSystemTable.TableName=BPStaging.PlacementSystemTable.TableName
dbo.PlacementSystemTable.SourceUpdatedDate=BPStaging.PlacementSystemTable.ModifiedDate
*/
CREATE PROCEDURE BPStaging.LoadPlacementSystemTable
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementSystemTable';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.PlacementSystemTable
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE dbo.PlacementSystemTable T
    USING (
        SELECT
            TableId = AuditedTemporalTableId
          , SchemaName
          , TableName
          , SourceUpdatedDate = ModifiedDate
        FROM
            BPStaging.PlacementSystemTable
    ) S
    ON T.TableId = S.TableId
    WHEN NOT MATCHED
        THEN INSERT (
                 TableId
               , SchemaName
               , TableName
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.TableId
                   , S.SchemaName
                   , S.TableName
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.TableId
      , T.SchemaName
      , T.TableName
      , T.IsDeleted
    INTERSECT
    SELECT
        S.TableId
      , S.SchemaName
      , S.TableName
      , CAST(0 AS BIT)
)
        THEN UPDATE SET
                 T.TableId = S.TableId
               , T.SchemaName = S.SchemaName
               , T.TableName = S.TableName
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = 0
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);