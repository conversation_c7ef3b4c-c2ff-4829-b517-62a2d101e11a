CREATE TABLE BP.RiskRequest (
    Id                      INT           NOT NULL
  , RiskDefinitionElementId INT           NOT NULL
  , ProductId               INT           NOT NULL
  , RiskRequestTypeId       INT           NOT NULL
  , ParentId                INT           NULL
  , RiskPolicyFormId        INT           NULL
  , YoyComparison           BIT           NOT NULL
  , AddlModelingReqd        BIT           NOT NULL
  , RiskPriorityLevelId     INT           NOT NULL
  , CurrencyTypeId          INT           NOT NULL
  , Comments                NVARCHAR(255) NULL
  , RequestSubmitted        BIT           NOT NULL
  , CreatedByUserId         INT           NOT NULL
  , PlacementId             INT           NULL
  , SourceUpdatedDate       DATETIME2(7)  NOT NULL
  , ETLCreatedDate          DATETIME2(7)  NOT NULL
  , ETLUpdatedDate          DATETIME2(7)  NOT NULL
  , IsDeleted               BIT           NOT NULL
        DEFAULT (0)
  , CONSTRAINT PK_BP_RiskRequest
        PRIMARY KEY
        (
            Id
        )
);
