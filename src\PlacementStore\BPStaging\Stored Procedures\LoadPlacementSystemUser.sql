/*
Lineage
dbo.PlacementSystemUser.UserId=BPStaging.PlacementSystemUser.Id
dbo.PlacementSystemUser.UserPrincipalName=BPStaging.PlacementSystemUser.UserPrincipalName
dbo.PlacementSystemUser.GivenName=BPStaging.PlacementSystemUser.GivenName
dbo.PlacementSystemUser.Surname=BPStaging.PlacementSystemUser.Surname
dbo.PlacementSystemUser.SupportedLanguageId=BPStaging.PlacementSystemUser.SupportedLanguageId
dbo.PlacementSystemUser.ActiveDirectoryId=Reference.WorkerAccount.ActiveDirectoryId
dbo.PlacementSystemUser.GlobalWorkerId=Reference.WorkerAccount.WorkerId
dbo.PlacementSystemUser.SourceUpdatedDate=BPStaging.PlacementSystemUser.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadPlacementSystemUser
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementSystemUser';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.PlacementSystemUser T
    USING (
        SELECT
            UserId = psu.Id
          , UserPrincipalName = psu.UserPrincipalName
          , GivenName = psu.GivenName
          , Surname = psu.Surname
          , SupportedLanguageId = psu.SupportedLanguageId
          , SourceUpdatedDate = psu.ValidFrom
          , DataSourceInstanceId = 50366
          , IsDeprecated = 0
          , ActiveDirectoryId = wa.ActiveDirectoryId
          , GlobalWorkerId = wa.WorkerId
        FROM
            BPStaging.PlacementSystemUser psu
            LEFT JOIN Reference.WorkerAccount wa
                ON psu.ActiveDirectoryId = wa.ActiveDirectoryId
                   AND wa.IsDeleted = 0
    ) S
    ON T.UserId = S.UserId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , UserId
               , UserPrincipalName
               , GivenName
               , Surname
               , SupportedLanguageId
               , LastUpdatedUTCDate
               , IsDeprecated
               , ActiveDirectoryId
               , GlobalWorkerId
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.UserId
                   , S.UserPrincipalName
                   , S.GivenName
                   , S.Surname
                   , S.SupportedLanguageId
                   , GETUTCDATE()
                   , S.IsDeprecated
                   , S.ActiveDirectoryId
                   , S.GlobalWorkerId
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.UserPrincipalName
                               , T.GivenName
                               , T.Surname
                               , T.SupportedLanguageId
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                               , T.ActiveDirectoryId
                               , T.GlobalWorkerId
                             INTERSECT
                             SELECT
                                 S.UserPrincipalName
                               , S.GivenName
                               , S.Surname
                               , S.SupportedLanguageId
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                               , S.ActiveDirectoryId
                               , S.GlobalWorkerId
                         )
        THEN UPDATE SET
                 T.GivenName = S.GivenName
               , T.Surname = S.Surname
               , T.SupportedLanguageId = S.SupportedLanguageId
               , T.UserPrincipalName = S.UserPrincipalName
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
               , T.ActiveDirectoryId = S.ActiveDirectoryId
               , T.GlobalWorkerId = S.GlobalWorkerId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
