/*
Lineage - approach to be validated as stored proc returns 2 result sets
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.PlacementID=dbo.Placement.PlacementId
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.BrokingPlatformPlacementID=dbo.Placement.PlacementSystemId
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.InceptionDate=dbo.Placement.InceptionDate
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.ExpiryDate=dbo.Placement.ExpiryDate
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.BusinessTypeID=ref.OpportunityType.OpportunityType
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.ClientID=dbo.Party.PartyId
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.NewClientID=dbo.NewClient.NewClientId
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.LocalClientID=dbo.Party.PartyId
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.ExpiredLeadCarrierID=dbo.Party.PartyKey
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.ExpiredPolicyNumber=dbo.Policy.PolicyReference
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.Comments=dbo.Placement.Comments
APIv1.GetPlacementsReadyToSendToServicingPlatform.Placement.Comments=dbo.Placement.InstructionDetails
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.PlacementID=dbo.Placement.PlacementId
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.IsLead=APIv1.PlacementMarketInteractions.QuotedToLead
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.IsIncumbent=APIv1.PlacementMarketInteractions.IsIncumbent
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.CarrierID=APIv1.PlacementMarketInteractions.PartyKey
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.CarrierName=APIv1.PlacementMarketInteractions.PartyName
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.Premium=APIv1.PlacementMarketInteractions.Premium
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.PremiumCurrencyCode=Reference.Currency.CurrencyAlphaCode
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.CommissionRate=APIv1.PlacementMarketInteractions.CommissionRate
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.CommissionValue=APIv1.PlacementMarketInteractions.Premium
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.CommissionValue=APIv1.PlacementMarketInteractions.CommissionRate
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.OfferedPercentage=APIv1.PlacementMarketInteractions.OfferedLineRate
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.ProductID=dbo.Product.ProductKey
APIv1.GetPlacementsReadyToSendToServicingPlatform.Carrier.SourceProductName=dbo.Product.SourceProductName
*/

/*
LineageOptions=NoScan;
Used for COL integration
*/

CREATE PROCEDURE APIv1.GetPlacementsReadyToSendToServicingPlatform (
    @dataSourceInstanceID INT
)
AS
SET NOCOUNT ON;

DECLARE
    @placementOpportunityType  NVARCHAR(50) = N'Renewal' -- Need to identify if the placement is a renewal or not
  , @placementStatus           NVARCHAR(50) = N'Complete' -- Only interested in completed placements
  , @AcceptedOutcomeStatus     NVARCHAR(50) = N'Accepted' -- Only include "accepted" carriers
  , @policyStatus              NVARCHAR(50) = N'Live'
  , @lapsedPolicyStatus        NVARCHAR(50) = N'Lapsed'
  , @placementCompletionStatus NVARCHAR(50) = N'ReadyToSendToServicingPlatform';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

CREATE TABLE #Placement (
    PlacementId                BIGINT
  , BrokingPlatformPlacementId NVARCHAR(15)
  , InceptionDate              DATETIME
  , ExpiryDate                 DATETIME
  , BusinessTypeId             INT
  , ClientId                   INT
  , NewClientId                INT
  , LocalClientId              NVARCHAR(50)
  , ExpiredLeadCarrierId       NVARCHAR(20)
  , ExpiredPolicyNumber        NVARCHAR(50)
  , Comments                   NVARCHAR(MAX)
);

CREATE TABLE #Carrier (
    PlacementId         BIGINT
  , IsLead              BIT
  , IsIncumbent         BIT
  , CarrierId           NVARCHAR(20)
  , CarrierName         NVARCHAR(200)
  , Premium             DECIMAL(19, 4)
  , PremiumCurrencyCode NVARCHAR(6)
  , CommissionRate      DECIMAL(21, 6)
  , CommissionValue     DECIMAL(19, 4)
  , OfferedPercentage   DECIMAL(21, 6)
  , ProductId           NVARCHAR(50)
  , SourceProductName   NVARCHAR(200)
);

-- Get Placements
INSERT
    #Placement
    (
        PlacementId
      , BrokingPlatformPlacementId
      , InceptionDate
      , ExpiryDate
      , BusinessTypeId
      , Comments
    )
SELECT DISTINCT
       p.PlacementId
     , BrokingPlatformPlacementId = CAST(p.PlacementSystemId AS NVARCHAR(100))
     , p.InceptionDate
     , p.ExpiryDate
     , BusinessTypeId = CASE WHEN pot.OpportunityType = @placementOpportunityType
                                 THEN 2
                             ELSE 1 END
     , Comments = COALESCE(p.InstructionDetails, p.Comments)
FROM
    dbo.Placement p
    INNER JOIN ref.PlacementStatus ps
        ON ps.PlacementStatusId = p.PlacementStatusId
           AND ps.PlacementStatus = @placementStatus

    LEFT JOIN ref.OpportunityType pot
        ON pot.OpportunityTypeId = p.PlacementOpportunityTypeId
           AND pot.IsDeprecated = 0

    INNER JOIN dbo.PlacementCompletionStatus pcs
        ON pcs.Id = p.PlacementCompletionStatusId
           AND pcs.Name = @placementCompletionStatus
WHERE
    p.IsDeleted = 0
    AND p.ServicingPlatformId = @dataSourceInstanceID
    AND p.DataSourceInstanceId = 50366 --BP
ORDER BY
    p.PlacementId

-- Get Client IDs
;

WITH CTE AS (
    SELECT
        cp.PlacementId
      , cp.ClientId
      , cp.LocalClientId
      , p.PartyId
      , p.PartyKey
      , ClientOrder = ROW_NUMBER() OVER (PARTITION BY cp.PlacementId ORDER BY ppr.IsPrimaryParty DESC, ppr.OnPlacement DESC, p.PartyName)
    FROM
        #Placement cp
        INNER JOIN dbo.PlacementPartyRole ppr
            ON ppr.PlacementId = cp.PlacementId
               AND ppr.IsDeleted = 0

        INNER JOIN ref.PartyRole pr
            ON pr.PartyRoleId = ppr.PartyRoleId
               AND pr.IsDeprecated = 0
               AND pr.GlobalPartyRoleId = 100 -- Client

        INNER JOIN dbo.Party p
            ON p.PartyId = ppr.PartyId
)
UPDATE CTE
SET
    CTE.ClientId = CTE.PartyId
  , CTE.LocalClientId = CTE.PartyKey
WHERE
    CTE.ClientOrder = 1;

-- Get New Client IDs
UPDATE cp
SET cp.NewClientId = ppr.NewClientId
FROM
    #Placement cp
    INNER JOIN dbo.PlacementPartyRole ppr
        ON ppr.PlacementId = cp.PlacementId
           AND ppr.IsDeleted = 0

    INNER JOIN dbo.NewClient nc
        ON ppr.NewClientId = nc.NewClientId
           AND nc.IsDeleted = 0;

-- Get Carriers
INSERT
    #Carrier
    (
        PlacementId
      , IsLead
      , IsIncumbent
      , CarrierId
      , CarrierName
      , Premium
      , PremiumCurrencyCode
      , CommissionRate
      , CommissionValue
      , ProductId
      , SourceProductName
      , OfferedPercentage
    )
SELECT
    p.PlacementId
  , IsLead = CASE WHEN (COUNT(mi.PartyKey) OVER (PARTITION BY mi.PlacementId)) = 1
                      THEN 1
                  ELSE ISNULL(mi.QuotedToLead, 0) END --> If there is a single accepted carrier on a Brazil or CRB will be marked as lead
  , mi.IsIncumbent
  , CarrierId = mi.PartyKey
  , CarrierName = mi.PartyName
  , Premium = ISNULL(mi.Premium, 0)
  , pcur.CurrencyAlphaCode
  , CommissionRate = ISNULL(mi.CommissionRate, 0)
  , CommissionValue = ISNULL(mi.Premium * mi.CommissionRate / 100, 0)
  , ProductId = pr.ProductKey
  , pr.SourceProductName
  , OfferedPercentage = ISNULL(mi.OfferedLineRate, 0)
FROM
    #Placement p
    INNER JOIN APIv1.PlacementMarketInteractions mi
        ON mi.PlacementId = p.PlacementId
           AND mi.OutcomeStatus = @AcceptedOutcomeStatus

    LEFT JOIN Reference.Currency pcur
        ON pcur.CurrencyId = mi.PremiumCurrencyId

    LEFT JOIN dbo.Product pr
        ON pr.ProductId = (mi.ProductId % 1000000)
           AND pr.SourcedFromBrokingPlatform = 0;

-- Get Expired Policy Details
UPDATE
    cp
SET
    cp.ExpiredPolicyNumber = C.ExpiredPolicyNumber
  , cp.ExpiredLeadCarrierId = C.PartyKey
FROM
    #Placement cp
    JOIN (
        SELECT
            cp.PlacementId
          , ExpiredPolicyNumber = py.PolicyReference
          , pty.PartyKey
          , PolicyOrder = ROW_NUMBER() OVER (PARTITION BY cp.PlacementId ORDER BY pcd.IsLead DESC, cup.NetPremiumtoUWUSD DESC)
        FROM
            #Placement cp
            INNER JOIN dbo.Placement p
                ON p.PlacementId = cp.PlacementId

            INNER JOIN dbo.PlacementPolicy pp
                ON pp.PlacementId = p.PlacementId
                   AND pp.IsDeleted = 0
                   AND pp.PlacementPolicyRelationshipTypeId = 2 --Expiring

            INNER JOIN dbo.Policy py
                ON pp.PolicyId = py.PolicyId

            INNER JOIN PAS.RefPolicyStatus rps
                ON rps.RefPolicyStatusId = py.RefPolicyStatusId
                   AND rps.RefPolicyStatus IN (
                           @policyStatus, @lapsedPolicyStatus
                       )

            LEFT JOIN dbo.PolicyCarrierDetails pcd
                ON pcd.PolicyId = py.PolicyId
                   AND pcd.IsDeleted = 0

            LEFT JOIN dbo.ClientUnderwriterPremium cup
                ON cup.PolicyId = pcd.PolicyId
                   AND cup.UWId = pcd.PartyId
                   AND cup.IsDeleted = 0

            LEFT JOIN dbo.Party pty
                ON pty.PartyId = pcd.PartyId
    ) C
        ON C.PlacementId = cp.PlacementId
           AND C.PolicyOrder = 1;

-- Return Data
SELECT
    PlacementID = PlacementId
  , BrokingPlatformPlacementID = BrokingPlatformPlacementId
  , InceptionDate
  , ExpiryDate
  , BusinessTypeID = BusinessTypeId
  , ClientID = ClientId
  , NewClientID = NewClientId
  , LocalClientID = LocalClientId
  , ExpiredLeadCarrierID = ExpiredLeadCarrierId
  , ExpiredPolicyNumber
  , Comments
FROM
    #Placement
ORDER BY
    PlacementID;

SELECT
    PlacementID = PlacementId
  , IsLead
  , IsIncumbent
  , CarrierID = CarrierId
  , CarrierName
  , Premium
  , PremiumCurrencyCode
  , CommissionRate
  , CommissionValue
  , OfferedPercentage
  , ProductID = ProductId
  , SourceProductName
FROM
    #Carrier
ORDER BY
    PlacementID ASC
  , CarrierID ASC;

DECLARE @RecordCount INT;

SELECT @RecordCount = COUNT(1)
FROM
    #Placement;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @RecordCount
  , NULL
  , NULL
  , NULL
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SET NOCOUNT OFF;