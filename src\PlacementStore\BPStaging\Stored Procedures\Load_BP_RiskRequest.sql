/*
Lineage
BP.RiskRequest.Id=BPStaging.RiskRequest.Id
BP.RiskRequest.RiskDefinitionElementId=BPStaging.RiskRequest.RiskDefinitionElementId
BP.RiskRequest.ProductId=BPStaging.RiskRequest.ProductId
BP.RiskRequest.RiskRequestTypeId=BPStaging.RiskRequest.RiskRequestTypeId
BP.RiskRequest.ParentId=BPStaging.RiskRequest.ParentId
BP.RiskRequest.RiskPolicyFormId=BPStaging.RiskRequest.RiskPolicyFormId
BP.RiskRequest.YoyComparison=BPStaging.RiskRequest.YoyComparison
BP.RiskRequest.AddlModelingReqd=BPStaging.RiskRequest.AddlModelingReqd
BP.RiskRequest.RiskPriorityLevelId=BPStaging.RiskRequest.RiskPriorityLevelId
BP.RiskRequest.CurrencyTypeId=BPStaging.RiskRequest.CurrencyTypeId
BP.RiskRequest.Comments=BPStaging.RiskRequest.Comments
BP.RiskRequest.RequestSubmitted=BPStaging.RiskRequest.RequestSubmitted
BP.RiskRequest.CreatedByUserId=BPStaging.RiskRequest.CreatedByUserId
BP.RiskRequest.PlacementId=BPStaging.RiskRequest.PlacementId
BP.RiskRequest.SourceUpdatedDate=BPStaging.RiskRequest.ValidTo
BP.RiskRequest.SourceUpdatedDate=BPStaging.RiskRequest.ValidFrom
BP.RiskRequest.IsDeleted=BPStaging.RiskRequest.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_RiskRequest
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(255) = 'BP.RiskRequest';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE BP.RiskRequest t
    USING (
        SELECT
            Id
          , RiskDefinitionElementId
          , ProductId
          , RiskRequestTypeId
          , ParentId
          , RiskPolicyFormId
          , YoyComparison
          , AddlModelingReqd
          , RiskPriorityLevelId
          , CurrencyTypeId
          , Comments
          , RequestSubmitted
          , CreatedByUserId
          , PlacementId
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            Id
          , RiskDefinitionElementId
          , ProductId
          , RiskRequestTypeId
          , ParentId
          , RiskPolicyFormId
          , YoyComparison
          , AddlModelingReqd
          , RiskPriorityLevelId
          , CurrencyTypeId
          , Comments
          , RequestSubmitted
          , CreatedByUserId
          , PlacementId
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.RiskRequest
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) s
    ON t.Id = s.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , RiskDefinitionElementId
               , ProductId
               , RiskRequestTypeId
               , ParentId
               , RiskPolicyFormId
               , YoyComparison
               , AddlModelingReqd
               , RiskPriorityLevelId
               , CurrencyTypeId
               , Comments
               , RequestSubmitted
               , CreatedByUserId
               , PlacementId
               , SourceUpdatedDate
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.Id
                   , s.RiskDefinitionElementId
                   , s.ProductId
                   , s.RiskRequestTypeId
                   , s.ParentId
                   , s.RiskPolicyFormId
                   , s.YoyComparison
                   , s.AddlModelingReqd
                   , s.RiskPriorityLevelId
                   , s.CurrencyTypeId
                   , s.Comments
                   , s.RequestSubmitted
                   , s.CreatedByUserId
                   , s.PlacementId
                   , s.SourceUpdatedDate
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.Id
                               , s.RiskDefinitionElementId
                               , s.ProductId
                               , s.RiskRequestTypeId
                               , s.ParentId
                               , s.RiskPolicyFormId
                               , s.YoyComparison
                               , s.AddlModelingReqd
                               , s.RiskPriorityLevelId
                               , s.CurrencyTypeId
                               , s.Comments
                               , s.RequestSubmitted
                               , s.CreatedByUserId
                               , s.PlacementId
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.Id
                               , t.RiskDefinitionElementId
                               , t.ProductId
                               , t.RiskRequestTypeId
                               , t.ParentId
                               , t.RiskPolicyFormId
                               , t.YoyComparison
                               , t.AddlModelingReqd
                               , t.RiskPriorityLevelId
                               , t.CurrencyTypeId
                               , t.Comments
                               , t.RequestSubmitted
                               , t.CreatedByUserId
                               , t.PlacementId
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.Id = s.Id
               , t.RiskDefinitionElementId = s.RiskDefinitionElementId
               , t.ProductId = s.ProductId
               , t.RiskRequestTypeId = s.RiskRequestTypeId
               , t.ParentId = s.ParentId
               , t.RiskPolicyFormId = s.RiskPolicyFormId
               , t.YoyComparison = s.YoyComparison
               , t.AddlModelingReqd = s.AddlModelingReqd
               , t.RiskPriorityLevelId = s.RiskPriorityLevelId
               , t.CurrencyTypeId = s.CurrencyTypeId
               , t.Comments = s.Comments
               , t.RequestSubmitted = s.RequestSubmitted
               , t.CreatedByUserId = s.CreatedByUserId
               , t.PlacementId = s.PlacementId
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.IsDeleted = s.IsDeleted

    /* Supporting incremental so no delete of any kind */
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = CONCAT(N'Merge ', @TargetTable);

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;