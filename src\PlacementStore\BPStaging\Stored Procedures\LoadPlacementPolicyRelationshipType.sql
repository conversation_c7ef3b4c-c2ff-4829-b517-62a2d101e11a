/*
Lineage
dbo.PlacementPolicyRelationshipType.PlacementPolicyRelationshipTypeId=BPStaging.PolicyType.Id
dbo.PlacementPolicyRelationshipType.PlacementPolicyRelationshipType=BPStaging.PolicyType.Text
*/
/* 
    Takes Broking Platform PolicyType and stores is in PlacementPolicyRelationship.
    It isn't a policy type, it is the type of the relationship between a Placement and a Policy 
*/
CREATE PROCEDURE BPStaging.LoadPlacementPolicyRelationshipType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementPolicyRelationshipType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE INTO dbo.PlacementPolicyRelationshipType T
    USING (
        SELECT
            PlacementPolicyRelationshipTypeId = Id
          , PlacementPolicyRelationshipType = Text
          , IsDeprecated = CAST(0 AS BIT)
        FROM
            BPStaging.PolicyType
    ) S
    ON S.PlacementPolicyRelationshipTypeId = T.PlacementPolicyRelationshipTypeId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 PlacementPolicyRelationshipTypeId
               , PlacementPolicyRelationshipType
               , IsDeprecated
               , UpdatedDate
             )
             VALUES
                 (
                     S.PlacementPolicyRelationshipTypeId
                   , S.PlacementPolicyRelationshipType
                   , S.IsDeprecated
                   , GETUTCDATE()
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.PlacementPolicyRelationshipType
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.PlacementPolicyRelationshipType
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.PlacementPolicyRelationshipType = S.PlacementPolicyRelationshipType
               , T.IsDeprecated = S.IsDeprecated
               , T.UpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.UpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
GO