/*
Lineage
CarrierID=dbo.Carrier.PSCarrierId
DataSourceInstanceId=dbo.Carrier.DataSourceInstanceId
CarrierName=dbo.CarrierAlias.FriendlyName
CarrierName=dbo.Carrier.CarrierEntityName
Geography=dbo.Carrier.Geography
EmployeeCount=Reference.Party.EmployeeCount
IsIndividual=Reference.Party.IsIndividual
OperatingRevenue=Reference.Party.OperatingRevenueUSD
CarrierNode=dbo.Carrier.PSCarrierId
RoleDescription=dbo.Carrier.PartyRoleKey
CreatedUTCDate=dbo.Carrier.LastUpdatedUTCDate
CreatedUTCDate=dbo.Carrier.CreatedUTCDate
LastUpdatedUTCDate=dbo.Carrier.LastUpdatedUTCDate
GlobalName=dbo.Carrier.CarrierEntityName
GlobalName=Reference.Party.PartyName
FatcaRated=dbo.Carrier.FatcaRated
TobaRated=dbo.Carrier.TobaRated
CarrierStatusId=dbo.CarrierStatus.CarrierStatusId
ApprovalStatus=dbo.Carrier.ApprovalStatus
CompCode=dbo.Carrier.CarrierEntityCompCode
ActiveFlag=dbo.Carrier.CarrierGroupId
ActiveFlag=dbo.Carrier.IsActive
ActiveFlag=dbo.Carrier.ShowInMosaic
ActiveFlag=dbo.Carrier.CarrierType
PartyCode=dbo.Carrier.PartyCode
Level1Carrier=dbo.Carrier.CarrierEntityName
Level1Carrier=dbo.Carrier.CarrierGroupName
*/
/* 
   This is used a lot. 
   It should not be used for new requirements. Use CarrierHierarchyExtended instead.
   If you are changing code that uses it see if you can migrate to CarrierHierarchyExtended.
*/
CREATE VIEW APIv1.CarrierHierarchy
AS
SELECT
    CarrierID = ch.CarrierId
  , ch.DataSourceInstanceId
  , ch.CarrierName
  , ch.Geography
  , ch.EmployeeCount
  , ch.IsIndividual
  , ch.OperatingRevenue
  , ch.LevelNum
  , ch.CarrierNode
  , ch.RoleDescription
  , ch.CreatedUTCDate
  , ch.LastUpdatedUTCDate
  , ch.GlobalName
  , ch.FatcaRated
  , ch.TobaRated
  , cs.CarrierStatusId
  , ch.ApprovalStatus
  , ch.CompCode
  , ch.ActiveFlag
  , ch.PartyCode
  , ch.Level1Carrier
FROM (
    -- Signet Carrier Group 
    SELECT DISTINCT
           CarrierId = c.PSCarrierId
         , DataSourceInstanceId = 50276 -- SIGNET
         , CarrierName = ISNULL(ca.FriendlyName, c.CarrierEntityName)
         , Geography = NULL
         , EmployeeCount = NULL
         , IsIndividual = 0
         , OperatingRevenue = NULL
         , LevelNum = 1
         , CarrierNode = CAST('/' + CAST(c.PSCarrierId AS VARCHAR(30)) + '/' AS HIERARCHYID)
         , RoleDescription = 'CARRIERGROUP'
         , CreatedUTCDate = c.LastUpdatedUTCDate
         , LastUpdatedUTCDate = c.LastUpdatedUTCDate
         , GlobalName = c.CarrierEntityName
         , FatcaRated = 0
         , TobaRated = 0
         , ApprovalStatus = NULL
         , CompCode = c.CarrierEntityCompCode
         , ActiveFlag = CASE WHEN car.CarrierGroupId IS NULL -- If no carriers use this group
                                 THEN 'N'
                             WHEN c.IsActive = 0
                                 THEN 'N'
                             ELSE 'Y' END
         , PartyCode = NULL
         , Level1Carrier = c.CarrierEntityName
    FROM
        dbo.Carrier c
        LEFT JOIN (SELECT DISTINCT CarrierGroupId FROM dbo.Carrier) car
            ON c.SourceCarrierId = car.CarrierGroupId

        LEFT JOIN dbo.CarrierAlias ca
            ON c.PSCarrierId = ca.CarrierId
               AND ca.IsDeleted = 0
    WHERE
        c.CarrierTypeId = 6
    UNION ALL

    -- Signet Carrier entity
    SELECT DISTINCT
           CarrierId = c.PSCarrierId
         , DataSourceInstanceId = 50276 -- SIGNET
         , CarrierName = ISNULL(ca.FriendlyName, c.CarrierEntityName)
         , Geography = NULL
         , EmployeeCount = NULL
         , IsIndividual = 0
         , OperatingRevenue = NULL
         , LevelNum = 2
         , CarrierNode = CAST(CASE WHEN ISNULL(cg.PSCarrierId, 0) > 0
                                       THEN '/' + CAST(cg.PSCarrierId AS VARCHAR(30)) + '/'
                                   ELSE '/' END + CAST(c.PSCarrierId AS VARCHAR(30)) + '/' AS HIERARCHYID)
         , RoleDescription = 'CARRIER'
         , CreatedUTCDate = MAX(c.LastUpdatedUTCDate)
         , LastUpdatedUTCDate = MAX(c.LastUpdatedUTCDate)
         , GlobalName = c.CarrierEntityName
         , c.FatcaRated
         , c.TobaRated
         , ApprovalStatus = c.ApprovalStatus
         , CompCode = c.CarrierEntityCompCode
         , ActiveFlag = CASE WHEN c.IsActive = 0
                                 THEN 'N'
                             WHEN c.ShowInMosaic = 0
                                  AND c.CarrierType <> 'Lloyd''s Syndicate'
                                 THEN 'N' -- PBI 131962
                             ELSE 'Y' END
         , PartyCode = NULL
         , Level1Carrier = ISNULL(c.CarrierGroupName, c.CarrierEntityName)
    FROM
        dbo.Carrier c
        LEFT JOIN dbo.CarrierAlias ca
            ON c.PSCarrierId = ca.CarrierId
               AND ca.IsDeleted = 0

        LEFT JOIN dbo.Carrier cg
            ON cg.CarrierTypeId = 6
               AND cg.SourceCarrierId = c.CarrierGroupId -- Carrier Group
    WHERE
        c.CarrierTypeId = 1 -- Signet
    GROUP BY
        cg.PSCarrierId
      , c.CarrierGroupName
      , c.PSCarrierId
      , ISNULL(ca.FriendlyName, c.CarrierEntityName)
      , c.CarrierEntityName
      , c.FatcaRated
      , c.TobaRated
      , c.ApprovalStatus
      , c.CarrierEntityCompCode
      , c.IsActive
      , c.ShowInMosaic
      , c.CarrierType
    UNION ALL

    -- Party 
    SELECT DISTINCT
           CarrierId = p.PSCarrierId
         , DataSourceInstanceId = p.DataSourceInstanceId
         , CarrierName = p.CarrierEntityName
         , Geography = p.Geography
         , rp.EmployeeCount
         , rp.IsIndividual
         , OperatingRevenue = rp.OperatingRevenueUSD
         , LevelNum = 3
         , CarrierNode = CAST(CASE WHEN ISNULL(cg.PSCarrierId, 0) > 0
                                       THEN '/' + CAST(cg.PSCarrierId AS VARCHAR(30)) + '/'
                                   ELSE '/' END + CASE WHEN ISNULL(c.PSCarrierId, 0) > 0
                                                           THEN CAST(c.PSCarrierId AS VARCHAR(30)) + '/'
                                                       ELSE '' END + CAST(p.PSCarrierId AS VARCHAR(30)) + '/' AS HIERARCHYID)
         , RoleDescription = p.PartyRoleKey
         , p.CreatedUTCDate
         , p.LastUpdatedUTCDate
         , GlobalName = rp.PartyName
         , p.FatcaRated
         , p.TobaRated
         , p.ApprovalStatus
         , CompCode = p.CarrierEntityCompCode
         , ActiveFlag = CASE WHEN p.IsActive = 0
                                 THEN 'N'
                             ELSE 'Y' END
         , p.PartyCode
         , Level1Carrier = COALESCE(cg.CarrierEntityName, c.CarrierEntityName, p.CarrierEntityName)
    FROM
        dbo.Carrier p
        LEFT JOIN dbo.Carrier c
            ON c.CarrierId = p.ParentId
               AND c.CarrierTypeId = 1 -- Signet although the ID should be enough

        LEFT JOIN Reference.Party rp
            ON rp.PartyId = p.GlobalPartyId

        LEFT JOIN dbo.Carrier cg
            ON cg.CarrierTypeId = 6
               AND cg.SourceCarrierId = p.CarrierGroupId -- Carrier Group
    WHERE
        p.CarrierTypeId = 5 -- ServicingPlatforms 
    UNION ALL

    -- Insurer Branch Carrier 
    SELECT DISTINCT
           CarrierId = p.PSCarrierId
         , DataSourceInstanceId = p.DataSourceInstanceId
         , CarrierName = p.CarrierEntityName
         , Geography = p.Geography
         , rp.EmployeeCount
         , rp.IsIndividual
         , OperatingRevenue = rp.OperatingRevenueUSD
         , LevelNum = 4
         , CarrierNode = CAST(CASE WHEN ISNULL(cg.PSCarrierId, 0) > 0
                                       THEN '/' + CAST(cg.PSCarrierId AS VARCHAR(30)) + '/'
                                   ELSE '/' END + CASE WHEN ISNULL(c.PSCarrierId, 0) > 0
                                                           THEN CAST(c.PSCarrierId AS VARCHAR(30)) + '/'
                                                       ELSE '' END + CAST(pp.PSCarrierId AS VARCHAR(30)) + '/'
                              + CAST(p.PSCarrierId AS VARCHAR(30)) + '/' AS HIERARCHYID)
         , RoleDescription = p.PartyRoleKey
         , p.CreatedUTCDate
         , p.LastUpdatedUTCDate
         , GlobalName = rp.PartyName
         , p.FatcaRated
         , p.TobaRated
         , p.ApprovalStatus
         , CompCode = p.CarrierEntityCompCode
         , ActiveFlag = CASE WHEN p.IsActive = 0
                                 THEN 'N'
                             ELSE 'Y' END
         , p.PartyCode
         , Level1Carrier = COALESCE(cg.CarrierEntityName, c.CarrierEntityName, p.CarrierEntityName)
    FROM
        dbo.Carrier p
        LEFT JOIN dbo.Carrier pp
            ON pp.CarrierId = p.ParentId
               AND pp.CarrierTypeId = 5 -- Looking at the Carrier at the next level to get the parent

        LEFT JOIN dbo.Carrier c
            ON c.CarrierId = pp.ParentId
               AND c.CarrierTypeId = 1 -- Signet although the ID should be enough

        LEFT JOIN Reference.Party rp
            ON rp.PartyId = pp.GlobalPartyId

        LEFT JOIN dbo.Carrier cg
            ON cg.CarrierTypeId = 6
               AND cg.SourceCarrierId = pp.CarrierGroupId -- Carrier Group
    WHERE
        p.CarrierTypeId = 8 -- Insurer Branch Carrier
) ch
     LEFT JOIN dbo.CarrierStatus cs
         ON CASE WHEN ch.ApprovalStatus = 'Deactivated'
                     THEN 'Not Approved'
                 WHEN ch.ApprovalStatus = 'HOLD - NO NEW OR RENEWALS'
                     THEN 'Restricted'
                 WHEN ch.ApprovalStatus = 'International'
                     THEN 'Approved'
                 WHEN ch.ApprovalStatus = 'Global Approval'
                     THEN 'Approved'
                 WHEN ch.ApprovalStatus = 'Not Approved'
                     THEN 'Not Approved'
                 WHEN ch.ApprovalStatus = 'Approved Willis assoc. entity'
                     THEN 'Approved'
                 WHEN ch.ApprovalStatus = 'Approved WTW associated entity'
                     THEN 'Approved'
                 WHEN ch.ApprovalStatus = 'Restricted'
                     THEN 'Restricted'
                 WHEN ch.ApprovalStatus = 'xxxxxxxxxxxxxxx'
                     THEN 'Not Approved'
                 WHEN ch.ApprovalStatus = 'Undecided'
                     THEN 'Not Approved'
                 WHEN ch.ApprovalStatus = 'See Qualifier'
                     THEN 'Not Approved' END = cs.CarrierStatus
WHERE
    ch.CarrierName IS NOT NULL
    AND ISNULL(ch.PartyCode, '') NOT LIKE ('HL%');