/*
Lineage
dbo.PlacementExtension.PlacementExtensionId=BPStaging.PlacementExtension.id
dbo.PlacementExtension.PlacementSystemId=BPStaging.PlacementExtension.PlacementId
dbo.PlacementExtension.PlacementId=dbo.Placement.PlacementId
dbo.PlacementExtension.KeyCode=BPStaging.PlacementExtension.ExternalCode
dbo.PlacementExtension.KeyCodeDescription=BPStaging.PlacementExtension.ExternalCodeDescription
dbo.PlacementExtension.ValueId=BPStaging.PlacementExtension.ValueId
*/
CREATE PROCEDURE BPStaging.LoadPlacementExtension
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementExtension';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.PlacementExtension
        );

IF @CheckCount <> 0
BEGIN TRY
    -- New Records Insert
    INSERT INTO
        dbo.PlacementExtension
        (
            PlacementExtensionId
          , PlacementSystemId
          , PlacementId
          , KeyCode
          , KeyCodeDescription
          , ValueId
          , UpdatedDateTime
          , CreatedDateTime
          , IsDeleted
        )
    SELECT
        PE.id
      , PE.PlacementId
      , p.PlacementId
      , PE.ExternalCode
      , PE.ExternalCodeDescription
      , PE.ValueId
      , GETUTCDATE()
      , GETUTCDATE()
      , 0
    FROM
        BPStaging.PlacementExtension PE
        INNER JOIN dbo.Placement p
            ON p.PlacementSystemId = PE.PlacementId
               AND p.DataSourceInstanceId = 50366
    WHERE
        NOT EXISTS (
        SELECT 1 FROM dbo.PlacementExtension DPE WHERE PE.id = DPE.PlacementExtensionId
    );

    SELECT @InsertedCount = @@ROWCOUNT;

    -- Update Existing Records
    UPDATE target
    SET
        target.PlacementExtensionId = PE.id
      , target.PlacementSystemId = PE.PlacementId
      , target.PlacementId = P.PlacementId
      , target.KeyCode = PE.ExternalCode
      , target.KeyCodeDescription = PE.ExternalCodeDescription
      , target.ValueId = PE.ValueId
      , target.UpdatedDateTime = GETUTCDATE()
      , target.IsDeleted = 0
    FROM
        dbo.PlacementExtension target
        INNER JOIN BPStaging.PlacementExtension PE
            ON PE.id = target.PlacementExtensionId

        INNER JOIN dbo.Placement P
            ON P.PlacementSystemId = PE.PlacementId
               AND P.DataSourceInstanceId = 50366
    WHERE
        NOT EXISTS (
        SELECT
            target.PlacementExtensionId
          , target.PlacementSystemId
          , target.PlacementId
          , target.KeyCode
          , target.KeyCodeDescription
          , target.ValueId
          , ISNULL(target.IsDeleted, -999)
        INTERSECT
        SELECT
            PE.id
          , PE.PlacementId
          , P.PlacementId
          , PE.ExternalCode
          , PE.ExternalCodeDescription
          , PE.ValueId
          , 0
    );

    SELECT @UpdatedCount = @@ROWCOUNT;

    -- Logical Delete - Records do not feature in BPStaging.PlacementExtension
    UPDATE target
    SET
        target.IsDeleted = 1
      , target.UpdatedDateTime = GETUTCDATE()
    FROM
        dbo.PlacementExtension target
    WHERE
        NOT EXISTS (
        SELECT 1 FROM BPStaging.PlacementExtension PE WHERE PE.id = target.PlacementExtensionId
    )
        AND target.IsDeleted = 0;

    SELECT @DeletedCount = @@ROWCOUNT;

    -- Rejected - Records that do not have an associated Placement on BP
    SELECT @RejectedCount = COUNT(*)
    FROM
        BPStaging.PlacementExtension PE
    WHERE
        NOT EXISTS (
        SELECT 1 FROM dbo.Placement PL WHERE PL.PlacementSystemId = PE.PlacementId AND PL.DataSourceInstanceId = 50366
    );
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    SELECT @ErrorMessage;

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
