﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.ods.Views;

[ExcludeFromCodeCoverage]
[ExpectedColumn(columnName: "PolicyStatusId", columnType: "int", nullable: false)]
[ExpectedColumn(columnName: "DataSourceInstanceId", columnType: "int", nullable: false)]
[ExpectedColumn(columnName: "PolicyStatusKey", columnType: "nvarchar", nullable: false, length: 100)]
[ExpectedColumn(columnName: "PolicyStatus", columnType: "nvarchar", nullable: false, length: 200)]
[ExpectedColumn(columnName: "PACTPolicyStatusId", columnType: "int", nullable: true)]
[ExpectedColumn(columnName: "RefPolicyStatusId", columnType: "int", nullable: true)]
[ExpectedColumn(columnName: "ETLCreatedDate", columnType: "datetime2", nullable: false, scale: 7)]
[ExpectedColumn(columnName: "ETLUpdatedDate", columnType: "datetime2", nullable: false, scale: 7)]
[ExpectedColumn(columnName: "SourceUpdatedDate", columnType: "datetime2", nullable: true, scale: 7)]
[ExpectedColumn(columnName: "IsDeprecated", columnType: "bit", nullable: false)]
public class vw_ref_PolicyStatusTests : PlacementStoreLockedDefinitionViewTestBase
{
    [Fact]
    public override void DoesNotNeedMuchToReturnARowTest()
    {
        dynamic PSRecord = CreateRow(
            tableName: "PAS.PolicyStatus",
            values: new
            {
                PASPolicyStatusId = 1,
                PolicyStatusKey = "ABC"
            });

        dynamic result = GetResultRow(tableName: ViewName);
        Assert.NotNull(result);
        Assert.Equal(expected: PSRecord.PolicyStatusKey, actual: result.PolicyStatusKey);
        Assert.Equal(expected: false, actual: result.IsDeprecated);
    }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="fixture"></param>
    public vw_ref_PolicyStatusTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
    }
}