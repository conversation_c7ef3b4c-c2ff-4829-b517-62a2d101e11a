/*
Lineage
PS.ClientProposalCoverage.Id=BPStaging.ClientProposalCoverage.Id
PS.ClientProposalCoverage.ClientProposalId=BPStaging.ClientProposalCoverage.ClientProposalId
PS.ClientProposalCoverage.CoverageWording=BPStaging.ClientProposalCoverage.CoverageWording
PS.ClientProposalCoverage.DisplayIndex=BPStaging.ClientProposalCoverage.DisplayIndex
PS.ClientProposalCoverage.SlideId=BPStaging.ClientProposalCoverage.SlideId
PS.ClientProposalCoverage.IsInclude=BPStaging.ClientProposalCoverage.IsInclude
PS.ClientProposalCoverage.IsMandatory=BPStaging.ClientProposalCoverage.IsMandatory
PS.ClientProposalCoverage.SourceUpdatedDate=BPStaging.ClientProposalCoverage.ValidTo
PS.ClientProposalCoverage.SourceUpdatedDate=BPStaging.ClientProposalCoverage.ValidFrom
PS.ClientProposalCoverage.IsDeleted=BPStaging.ClientProposalCoverage.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_ClientProposalCoverage
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ClientProposalCoverage';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ClientProposalCoverage)
BEGIN TRY
    MERGE PS.ClientProposalCoverage T
    USING (
        SELECT
            Id
          , ClientProposalId
          , CoverageWording
          , DisplayIndex
          , SlideId
          , IsInclude
          , IsMandatory
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , ClientProposalId
          , CoverageWording
          , DisplayIndex
          , SlideId
          , IsInclude
          , IsMandatory
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ClientProposalCoverage
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , ClientProposalId
               , CoverageWording
               , DisplayIndex
               , SlideId
               , IsInclude
               , IsMandatory
               , SourceUpdatedDate
               , DataSourceInstanceId
               , IsDeleted
             )
             VALUES
                 (
                     S.Id
                   , S.ClientProposalId
                   , S.CoverageWording
                   , S.DisplayIndex
                   , S.SlideId
                   , S.IsInclude
                   , S.IsMandatory
                   , S.SourceUpdatedDate
                   , S.DataSourceInstanceId
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.ClientProposalId
      , T.CoverageWording
      , T.DisplayIndex
      , T.SlideId
      , T.IsInclude
      , T.IsMandatory
      , T.SourceUpdatedDate
      , T.DataSourceInstanceId
      , T.IsDeleted
    INTERSECT
    SELECT
        S.ClientProposalId
      , S.CoverageWording
      , S.DisplayIndex
      , S.SlideId
      , S.IsInclude
      , S.IsMandatory
      , S.SourceUpdatedDate
      , S.DataSourceInstanceId
      , S.IsDeleted
)
        THEN UPDATE SET
                 T.ClientProposalId = S.ClientProposalId
               , T.CoverageWording = S.CoverageWording
               , T.DisplayIndex = S.DisplayIndex
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.IsDeleted = S.IsDeleted
               , T.SlideId = S.SlideId
               , T.IsInclude = S.IsInclude
               , T.IsMandatory = S.IsMandatory
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);