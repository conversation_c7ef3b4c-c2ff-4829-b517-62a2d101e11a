﻿namespace PsDb.Tests.Rules.Views;

public class PoliciesAndClientsTests : PlacementStoreTestBase
{
    [Fact]
    public void ReturnsNoDataTest()
    {
        dynamic row = GetResultRow(tableName: "Rules.PoliciesAndClients");
        Assert.Null(row);
    }

    [Fact]
    public void ReturnsNoDeletedDataTest()
    {
        dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            IsDeleted = true
        });

        dynamic row = GetResultRow(tableName: "Rules.PoliciesAndClients");
        Assert.Null(row);
    }

    [Fact]
    public void ReturnsMinimalDataTest()
    {
        dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            IsDeleted = false
        });

        dynamic row = GetResultRow(tableName: "Rules.PoliciesAndClients");
        Assert.NotNull(row);
    }



    [Theory]
    [InlineData((int)DataSourceInstance.Eclipse, "Eclipse", "1693358247", "Authorised", "2024-01-01 00:00:00", "2024-12-31 00:00:00", "Direct", "Complex", "Chicago", "Team", "Aspen", 1600026, 1029)]
    [InlineData((int)DataSourceInstance.Eclipse, "Eclipse", "2002363270", "Authorised", "2019-12-03 00:00:00", "2026-12-02 00:00:00", "Direct", "Non-Compex", "M&A (Transactional)", "Team", "Pontegadea UK Ltd", 1123015, -3)]
    [InlineData((int)DataSourceInstance.EpicUS, "Epic US", "2007604898", "NTU", "2020-01-01 00:00:00", "2021-01-01 00:00:00.0000000", "Reinsurance", "Non-Compex", "F P - North America", "Team", "Pontegadea UK Ltd", 1123015, null)]

    public void ReturnsDataTest(int dataSourceInstanceId, string dataSourceName, string policyKey, string policyStatus, DateTime inceptionDate, DateTime expiryDate, string insuranceType, string policyType, string organisation, string organisationRole, string partyName, int globalPartyId, int? ruleId)
    {
        dynamic dataSourceInstanceRecord, policyRecord, policyStatusRecord, insuranceTypeRecord, policyTypeRecord, pasOrganisationRecord, organisationRoleRecord
            , apiv1OrganisationHierarchyRecord, policySectionStatusRecord, productRecord, refProductClassRecord, refProductLineRecord, refProductRecord
            , partyRecord, partyRelationshipRecord, globalPartyRecord, refPartyRoleRecord, placementPolicyRecord;

        SetUpData(dataSourceInstanceId, dataSourceName, policyKey, policyStatus, inceptionDate, expiryDate, insuranceType, policyType, organisation, organisationRole, partyName, globalPartyId, ruleId
            , out dataSourceInstanceRecord, out policyRecord, out policyStatusRecord, out insuranceTypeRecord, out policyTypeRecord, out pasOrganisationRecord, out organisationRoleRecord
            , out apiv1OrganisationHierarchyRecord, out policySectionStatusRecord, out productRecord, out refProductClassRecord, out refProductLineRecord, out refProductRecord
            , out partyRecord, out partyRelationshipRecord, out globalPartyRecord, out refPartyRoleRecord, out placementPolicyRecord);

        dynamic row = GetResultRow(tableName: "Rules.PoliciesAndClients");
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: policyRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: policyRecord.PolicyStatusKey, actual: row.PolicyStatusKey);
        Assert.Equal(expected: policyStatusRecord.PolicyStatus, actual: row.PolicyStatus);
        Assert.Equal(expected: policyStatusRecord.PolicyStatusKey, actual: row.PolicyStatusKey);
        Assert.Equal(expected: policyRecord.RefPolicyStatusId, actual: row.RefPolicyStatusId);
        Assert.Equal(expected: policyRecord.InceptionDate, actual: row.InceptionDate);
        Assert.Equal(expected: policyRecord.ExpiryDate, actual: row.ExpiryDate);
        Assert.Equal(expected: policyRecord.InsuranceTypeId, actual: row.InsuranceTypeId);
        Assert.Equal(expected: insuranceTypeRecord.InsuranceType, actual: row.InsuranceType);
        Assert.Equal(expected: insuranceTypeRecord.InsuranceTypeKey, actual: row.InsuranceTypeKey);
        Assert.Equal(expected: policyRecord.RenewedFromPolicyId, actual: row.RenewedFromPolicyId);
        Assert.Equal(expected: policyRecord.PolicyTypeKey, actual: row.PolicyTypeKey);
        Assert.Equal(expected: policyTypeRecord.PolicyType, actual: row.PolicyType);
        Assert.Equal(expected: policyTypeRecord.PolicyTypeKey, actual: row.PolicyTypeKey);
        Assert.Equal(expected: pasOrganisationRecord.Organisation, actual: row.Organisation);
        Assert.Equal(expected: pasOrganisationRecord.OrganisationKey, actual: row.OrganisationKey);
        Assert.Equal(expected: apiv1OrganisationHierarchyRecord.OrgLevel1, actual: row.OrgLevel1);
        Assert.Equal(expected: apiv1OrganisationHierarchyRecord.OrgLevel2, actual: row.OrgLevel2);
        Assert.Equal(expected: apiv1OrganisationHierarchyRecord.OrgLevel3, actual: row.OrgLevel3);
        Assert.Equal(expected: apiv1OrganisationHierarchyRecord.OrgLevel4, actual: row.OrgLevel4);
        Assert.Equal(expected: apiv1OrganisationHierarchyRecord.OrgLevel5, actual: row.OrgLevel5);
        Assert.Equal(expected: apiv1OrganisationHierarchyRecord.OrgLevel6, actual: row.OrgLevel6);
        Assert.Equal(expected: organisationRoleRecord.OrganisationRole, actual: row.WillisOrganisationRole);
        Assert.Equal(expected: organisationRoleRecord.OrganisationRoleKey, actual: row.WillisOrganisationRoleKey);
        Assert.Equal(expected: policySectionStatusRecord.PolicySectionStatusKey, actual: row.PolicySectionStatusKey);
        Assert.Equal(expected: refProductClassRecord.ProductClassId, actual: row.GlobalProductClassID);
        Assert.Equal(expected: refProductClassRecord.ProductClassName, actual: row.ProductClassName);
        Assert.Equal(expected: refProductLineRecord.ProductLineId, actual: row.GlobalProductLineID);
        Assert.Equal(expected: refProductLineRecord.ProductLineName, actual: row.ProductLineName);
        Assert.Equal(expected: productRecord.ProductClass, actual: row.ProductClass);
        Assert.Equal(expected: DBNull.Value, actual: row.IsPrimaryProduct);
        Assert.Equal(expected: productRecord.ProductLine, actual: row.ProductLine);
        Assert.Equal(expected: productRecord.ProductKey, actual: row.ProductKey);
        Assert.Equal(expected: productRecord.SourceProductName, actual: row.SourceProductName);
        Assert.Equal(expected: partyRecord.PartyId, actual: row.PartyId);
        Assert.Equal(expected: partyRecord.GlobalPartyId, actual: row.GlobalPartyId);
        Assert.Equal(expected: partyRecord.PartyKey, actual: row.PartyKey);
        Assert.Equal(expected: partyRecord.PartyName, actual: row.PartyName);
        Assert.Equal(expected: partyRelationshipRecord.IsPrimaryParty, actual: row.IsPrimaryParty);
        Assert.Equal(expected: globalPartyRecord.PartyName, actual: row.ReferencePartyName);
        Assert.Equal(expected: globalPartyRecord.GlobalUltimateOwnerPartyId, actual: row.GlobalUltimateOwnerPartyId);
        Assert.Equal(expected: refPartyRoleRecord.PartyRole, actual: row.PartyRole);
        Assert.Equal(expected: refPartyRoleRecord.PartyRoleId, actual: row.PartyRoleId);
        Assert.Equal(expected: refPartyRoleRecord.PartyRoleKey, actual: row.PartyRoleKey);
        Assert.Equal(expected: refPartyRoleRecord.GlobalPartyRoleId, actual: row.GlobalPartyRoleId);
        Assert.Equal(expected: dataSourceInstanceRecord.DataSourceInstanceName, actual: row.DataSourceName);
        Assert.Equal(expected: placementPolicyRecord.PlacementId, actual: row.PlacementId);
        if(ruleId != null) { Assert.Equal(expected: policyRecord.RuleId, actual: row.RuleId); }
    }

    private void SetUpData(int dataSourceInstanceId, string dataSourceName, string policyKey, string policyStatus, DateTime inceptionDate, DateTime expiryDate, string insuranceType, string policyType, string organisation, string organisationRole, string partyName, int globalPartyId, int? ruleId
        , out dynamic dataSourceInstanceRecord, out dynamic policyRecord, out dynamic policyStatusRecord, out dynamic insuranceTypeRecord, out dynamic policyTypeRecord, out dynamic pasOrganisationRecord, out dynamic organisationRoleRecord, out dynamic apiv1OrganisationHierarchyRecord
        , out dynamic policySectionStatusRecord, out dynamic productRecord, out dynamic refProductClassRecord, out dynamic refProductLineRecord, out dynamic refProductRecord, out dynamic partyRecord, out dynamic partyRelationshipRecord
        , out dynamic globalPartyRecord, out dynamic refPartyRoleRecord, out dynamic placementPolicyRecord)
    {
        dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new
        {
            DataSourceInstanceId = dataSourceInstanceId,
            DataSourceInstanceName = dataSourceName,
            IsDeleted = false
        });

        policyStatusRecord = CreateRow(tableName: "PAS.PolicyStatus", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyStatusKey = "KEY|" + policyStatus,
            PolicyStatus = policyStatus,
            RefPolicyStatusID = 1,
            IsDeleted = false
        });

        policySectionStatusRecord = CreateRow(tableName: "PAS.PolicySectionStatus", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicySectionStatusKey = "KEY|" + policyStatus,
            PolicySectionStatus = policyStatus,
            IsDeleted = false
        });

        insuranceTypeRecord = CreateRow(tableName: "ref.InsuranceType", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            InsuranceTypeKey = "KEY|" + insuranceType,
            InsuranceType = insuranceType,
            IsDeprecated = false
        });

        policyTypeRecord = CreateRow(tableName: "PAS.PolicyType", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyTypeKey = "KEY|" + policyType,
            PolicyType = policyType,
            IsDeleted = false
        });

        policyRecord = CreateRow(tableName: "dbo.Policy", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyKey = policyKey,
            InceptionDate = inceptionDate,
            ExpiryDate = expiryDate,
            PolicyStatusKey = policyStatusRecord.PolicyStatusKey,
            RefPolicyStatusId = policyStatusRecord.RefPolicyStatusID,
            InsuranceTypeId = insuranceTypeRecord.InsuranceTypeId,
            RenewedFromPolicyId = 1,
            PolicyTypeKey = policyTypeRecord.PolicyTypeKey,
            PolicyDescription = "Policy for " + policyKey,
            RuleId = ruleId != null ? ruleId : null,
            IsDeleted = false
        });

        dynamic policySectionRecord = CreateRow(tableName: "dbo.PolicySection", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicySectionKey = policyKey,
            PolicyId = policyRecord.PolicyId,
            PolicySectionStatusKey = policySectionStatusRecord.PolicySectionStatusKey,
            IsDeleted = false
        });

        placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PolicyId = policyRecord.PolicyId,
            PlacementPolicyRelationshipTypeId = 1,
            IsDeleted = false
        });

        pasOrganisationRecord = CreateRow(tableName: "PAS.Organisation", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            OrganisationKey = "KEY|" + organisation,
            Organisation = organisation,
            IsDeleted = false
        });

        dynamic psOrganisationRecord = CreateRow(tableName: "PS.Organisation", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            OrganisationKey = pasOrganisationRecord.OrganisationKey,
            IsDeleted = false
        });

        organisationRoleRecord = CreateRow(tableName: "PAS.OrganisationRole", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            OrganisationRoleKey = "KEY|" + organisationRole,
            OrganisationRole = organisationRole,
            IsDeleted = false
        });

        dynamic psOrganisationRoleRecord = CreateRow(tableName: "PS.OrganisationRole", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            OrganisationRoleKey = "KEY|" + organisationRole,
            IsDeleted = false
        });

        dynamic policyOrganisationRecord = CreateRow(tableName: "dbo.PolicyOrganisation", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyId = policyRecord.PolicyId,
            OrganisationId = psOrganisationRecord.OrganisationSK,
            OrganisationRoleId = psOrganisationRoleRecord.OrganisationRoleSK,
            IsDeleted = false
        });

        apiv1OrganisationHierarchyRecord = CreateRow(tableName: "APIv1.OrganisationHierarchyTable", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            OrganisationId = psOrganisationRecord.OrganisationSK,
            OrgLevel1 = "OrgLevel1",
            OrgLevel2 = "OrgLevel2",
            OrgLevel3 = "OrgLevel3",
            OrgLevel4 = "OrgLevel4",
            OrgLevel5 = "OrgLevel5",
            OrgLevel6 = "OrgLevel6"
        });

        refProductClassRecord = CreateRow(tableName: "Reference.ProductClass", values: new
        {
            ProductClassId = 100000,
            ProductClassName = "RefClass",
            IsDeleted = false
        });

        refProductLineRecord = CreateRow(tableName: "Reference.ProductLine", values: new
        {
            ProductLineId = 200000,
            ProductLineName = "RefLine",
            ProductClassId = refProductClassRecord.ProductClassId,
            IsDeleted = false
        });

        refProductRecord = CreateRow(tableName: "Reference.Product", values: new
        {
            ProductId = 300000,
            ProductName = "RefProduct",
            ProductLineId = refProductLineRecord.ProductLineId,
            IsDeleted = false
        });

        productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            ProductKey = "KEY|A",
            ProductLine = "Line",
            ProductClass = "Class",
            SourceProductName = "SourceProductName",
            ReferenceProductId = refProductRecord.ProductId,
            IsDeleted = false
        });

        dynamic policySectionProductRecord = CreateRow(tableName: "dbo.PolicySectionProduct", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicySectionId = policySectionRecord.PolicySectionId,
            ProductId = productRecord.ProductId,
            IsDeleted = false
        });

        partyRecord = CreateRow(tableName: "dbo.Party", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PartyKey = "KEY|" + partyName,
            PartyName = partyName,
            GlobalPartyId = globalPartyId,
            IsDeleted = false
        });

        globalPartyRecord = CreateRow(tableName: "Reference.Party", values: new
        {
            PartyId = partyRecord.GlobalPartyId,
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PartyName = "Global" + partyName,
            GlobalUltimateOwnerPartyId = globalPartyId + 1,
            IsDeleted = false
        });

        refPartyRoleRecord = CreateRow(tableName: "ref.PartyRole", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PartyRoleKey = "KEY|CLIENT",
            PartyRole = "Client",
            GlobalPartyRoleId = 100,
            IsDeprecated = false
        });

        partyRelationshipRecord = CreateRow(tableName: "dbo.PolicyPartyRelationship", values: new
        {
            DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            PolicyId = policyRecord.PolicyId,
            PartyId = partyRecord.PartyId,
            PartyRoleId = refPartyRoleRecord.PartyRoleId,
            IsPrimaryParty = true,
            IsDeleted = false
        });
    }

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public PoliciesAndClientsTests(DatabaseFixture fixture) : base(fixture)
    {
    }

    #endregion
}
