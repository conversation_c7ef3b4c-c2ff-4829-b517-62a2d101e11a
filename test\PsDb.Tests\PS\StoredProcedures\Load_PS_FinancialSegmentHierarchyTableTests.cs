﻿using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.PS.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_PS_FinancialSegmentHierarchyTableTests : PlacementStoreTestBase
{
    [Fact]
    public void NoStageDataTest()
    {
        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PS.Load_PS_FinancialSegmentHierarchyTable");

        dynamic row = GetResultRow(tableName: "PS.FinancialSegmentHierarchyTable");
        Assert.Null(row);

    }

    [Fact]
    public void CreateFinancialSegmentHierarchyTest()
    {
        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance",
           values: new
           {
               DataSourceInstanceId = (int)DataSourceInstance.EpicUS
           });
        dynamic stagedFinancialSegmentRecord = CreateRow(tableName: "ref.FinancialSegment", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.EpicUS,
            FinancialSegmentId = 1,
            PASFinancialSegmentId = 11,
            FinancialStructureId = 111,
            FinancialSegment = "seg1",
            ETLUpdatedDate = DateTime.Now
        });
        dynamic stagedFinancialStructureAttributeRecord = CreateRow(tableName: "ref.FinancialStructureAttribute", values: new
        {
            FinancialStructureId = 111,
            DataSourceInstanceId = (int)DataSourceInstance.EpicUS,
            PACTFinancialStructureId = 11,
            EpicUniqAgency = 1111,
            EpicAgencyCode = 1,
            EpicAgencyName = "AG",
            EpicUniqBranch = 111,
            EpicBranchCode = 1,
            EpicUniqDepartment = -1,
            EpicUniqProfitCenter = -1,
            ETLUpdatedDate = DateTime.Now
        });
        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PS.Load_PS_FinancialSegmentHierarchyTable");

        dynamic row = GetResultRow(tableName: "PS.FinancialSegmentHierarchyTable", whereClause: $@"PASFinancialSegmentId = 11");
        Assert.NotNull(row);
        Assert.Equal(expected: "Branch", actual: row.SegRole);
        Assert.Equal(expected: stagedFinancialSegmentRecord.FinancialSegment, actual: row.SegLevel1);
    }

    [Fact]
    public void CreateFinancialSegmentHierarchyEclipseTest()
    {
        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance",
           values: new
           {
               DataSourceInstanceId = (int)DataSourceInstance.Eclipse
           });
        dynamic stagedFinancialSegmentRecord = CreateRow(tableName: "ref.FinancialSegment", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            FinancialSegmentId = 1,
            PASFinancialSegmentId = 11,
            FinancialSegment = "Team1",
            Level1 = "LE1",
            Level2 = "BU1",
            Level3 = "Fun1",
            Level4 = "Dep1",
            Level5 = "Team1",
            ETLUpdatedDate = DateTime.Now
        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PS.Load_PS_FinancialSegmentHierarchyTable");

        dynamic row = GetResultRow(tableName: "PS.FinancialSegmentHierarchyTable", whereClause: $@"FinancialSegmentId = 1");
        Assert.NotNull(row);
        Assert.Equal(expected: "Team", actual: row.SegRole);
        Assert.Equal(expected: stagedFinancialSegmentRecord.FinancialSegment, actual: row.SegLevel5);
        Assert.Equal(expected: stagedFinancialSegmentRecord.FinancialSegmentId + 3000000, actual: row.Level3SegId);
    }

    [Fact]
    public void UpdateFinancialSegmentHierarchyEclipseTest()
    {
        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance",
           values: new
           {
               DataSourceInstanceId = (int)DataSourceInstance.Eclipse
           });
        dynamic stagedFinancialSegmentRecord = CreateRow(tableName: "ref.FinancialSegment", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            FinancialSegmentId = 1,
            PASFinancialSegmentId = 11,
            FinancialSegment = "Team1",
            Level1 = "LE1",
            Level2 = "BU1",
            Level3 = "Fun2",
            Level4 = "Dep1",
            Level5 = "Team1",
            ETLUpdatedDate = DateTime.Now
        });
        dynamic stagedFinancialSegmentHierarchyRecord = CreateRow(tableName: "PS.FinancialSegmentHierarchyTable", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            FinancialSegmentId = 1,
            PASFinancialSegmentId = 11,
            FinancialSegment = "Team1",
            SegLevel3 = "Fun1",
            SegLevel4 = "Dep1",
            SegLevel5 = "Team1"
        });
        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PS.Load_PS_FinancialSegmentHierarchyTable");

        dynamic row = GetResultRow(tableName: "PS.FinancialSegmentHierarchyTable", whereClause: $@"FinancialSegmentId = 1");
        Assert.NotNull(row);
        Assert.Equal(expected: "Team", actual: row.SegRole);
        Assert.Equal(expected: stagedFinancialSegmentRecord.Level3, actual: row.SegLevel3);
        Assert.Equal(expected: stagedFinancialSegmentRecord.FinancialSegmentId + 3000000, actual: row.Level3SegId);
    }
    [Fact]
    public void DeprecateFinancialSegmentHierarchyEclipseTest()
    {
        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance",
           values: new
           {
               DataSourceInstanceId = (int)DataSourceInstance.Eclipse
           });
        dynamic stagedFinancialSegmentRecord = CreateRow(tableName: "ref.FinancialSegment", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            FinancialSegmentId = 2,
            PASFinancialSegmentId = 11,
            FinancialSegment = "Team1",
            Level1 = "LE1",
            Level2 = "BU1",
            Level3 = "Fun2",
            Level4 = "Dep1",
            Level5 = "Team1",
            ETLUpdatedDate = DateTime.Now
        });
        dynamic stagedFinancialSegmentHierarchyRecord = CreateRow(tableName: "PS.FinancialSegmentHierarchyTable", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            FinancialSegmentId = 1,
            PASFinancialSegmentId = 11,
            FinancialSegment = "Team1",
            SegLevel3 = "Fun1",
            SegLevel4 = "Dep1",
            SegLevel5 = "Team1"
        });
        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PS.Load_PS_FinancialSegmentHierarchyTable");

        dynamic row = GetResultRow(tableName: "PS.FinancialSegmentHierarchyTable", whereClause: $@"FinancialSegmentId = 1");
        Assert.NotNull(row);
        Assert.Equal(expected: true, actual: row.IsDeprecated);
    }
    public Load_PS_FinancialSegmentHierarchyTableTests(DatabaseFixture fixture) : base(fixture)
    {

    }

}
