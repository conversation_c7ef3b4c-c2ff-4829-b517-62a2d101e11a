/*
Lineage
PS.NegotiationSpecification.NegotiationSpecificationKey=BP.SubmissionContainerRequestedCoverageElement.Id
PS.NegotiationSpecification.NegotiationId=PS.Negotiation.NegotiationId
PS.NegotiationSpecification.SpecificationId=PS.Specification.SpecificationId
PS.NegotiationSpecification.IsDeleted=BP.SubmissionContainerRequestedCoverageElement.IsDeleted
PS.NegotiationSpecification.SourceUpdatedDate=BP.SubmissionContainerRequestedCoverageElement.ETLUpdatedDate
*/
CREATE PROCEDURE BPStaging.Load_PS_NegotiationSpecification
    @Success BIT OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.NegotiationSpecification';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #tempNegotiationSpecification;

    SELECT
        NegotiationSpecificationKey = CONCAT(N'SUBCRCE|', scrce.Id)
      , n.NegotiationId
      , s.SpecificationId
      , IsDeleted = scrce.IsDeleted
      , SourceUpdatedDate = scrce.ETLUpdatedDate
    INTO #tempNegotiationSpecification
    FROM
        BP.SubmissionContainerRequestedCoverageElement scrce
        INNER JOIN PS.Negotiation n
            ON n.NegotiationKey = CONCAT(N'SUBC|', scrce.SubmissionContainerId)

        INNER JOIN PS.Specification s
            ON s.SpecificationKey = CONCAT(N'RCE|', scrce.RequestedCoverageElementId);

    MERGE PS.NegotiationSpecification T
    USING (
        SELECT
            DataSourceInstanceId = 50366
          , NegotiationSpecificationKey
          , NegotiationId
          , SpecificationId
          , IsDeleted
          , SourceUpdatedDate
        FROM
            #tempNegotiationSpecification
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.NegotiationSpecificationKey = S.NegotiationSpecificationKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , NegotiationSpecificationKey
               , NegotiationId
               , SpecificationId
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.NegotiationSpecificationKey
                   , S.NegotiationId
                   , S.SpecificationId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeleted
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT S.NegotiationId, S.SpecificationId, S.IsDeleted INTERSECT SELECT T.NegotiationId, T.SpecificationId, T.IsDeleted
                         )
        THEN UPDATE SET
                 T.NegotiationId = S.NegotiationId
               , T.SpecificationId = S.SpecificationId
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;