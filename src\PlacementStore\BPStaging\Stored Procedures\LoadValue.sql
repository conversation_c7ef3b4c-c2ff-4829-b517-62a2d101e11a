/*
Lineage
dbo.Value.ValueId=BPStaging.Value.Id
dbo.Value.ValueTypeId=BPStaging.Value.ValueTypeId
dbo.Value.CurrencyTypeId=BPStaging.Value.CurrencyTypeId
dbo.Value.NumericValue=BPStaging.Value.NumericValue
dbo.Value.TextValue=BPStaging.Value.TextValue
dbo.Value.ValueTypeLookupValueId=BPStaging.Value.ValueTypeLookupValueId
dbo.Value.DateValue=BPStaging.Value.DateValue
dbo.Value.ComplexValue=BPStaging.Value.ComplexValue
dbo.Value.BooleanValue=BPStaging.Value.BooleanValue
*/
CREATE PROCEDURE BPStaging.LoadValue
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.Value';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.Value)
BEGIN TRY
    MERGE INTO dbo.Value T
    USING (
        SELECT
            ValueId = Id
          , ValueTypeId
          , CurrencyTypeId
          , WeightTypeId
          , NumericValue
          , TextValue
          , ValueTypeLookupValueId
          , DateValue
          , ComplexValue
          , BooleanValue
          , DataSourceInstanceId = 50366
          , IsDeleted = 0
        FROM
            BPStaging.Value
    ) S
    ON S.ValueId = T.ValueId
    WHEN NOT MATCHED
        THEN INSERT (
                 ValueId
               , ValueTypeId
               , CurrencyTypeId
               , NumericValue
               , TextValue
               , ValueTypeLookupValueId
               , DateValue
               , CreatedUTCDate
               , LastUpdatedUTCDate
               , ComplexValue
               , BooleanValue
               , IsDeleted
               , DataSourceInstanceId
             )
             VALUES
                 (
                     S.ValueId
                   , S.ValueTypeId
                   , S.CurrencyTypeId
                   , S.NumericValue
                   , S.TextValue
                   , S.ValueTypeLookupValueId
                   , S.DateValue
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.ComplexValue
                   , S.BooleanValue
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.ValueTypeId
      , S.CurrencyTypeId
      , S.NumericValue
      , S.TextValue
      , S.ValueTypeLookupValueId
      , S.DateValue
      , S.ComplexValue
      , S.BooleanValue
      , S.IsDeleted
      , S.DataSourceInstanceId
    INTERSECT
    SELECT
        T.ValueTypeId
      , T.CurrencyTypeId
      , T.NumericValue
      , T.TextValue
      , T.ValueTypeLookupValueId
      , T.DateValue
      , T.ComplexValue
      , T.BooleanValue
      , T.IsDeleted
      , T.DataSourceInstanceId
)
        THEN UPDATE SET
                 T.ValueTypeId = S.ValueTypeId
               , T.CurrencyTypeId = S.CurrencyTypeId
               , T.NumericValue = S.NumericValue
               , T.TextValue = S.TextValue
               , T.ValueTypeLookupValueId = S.ValueTypeLookupValueId
               , T.DateValue = S.DateValue
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.ComplexValue = S.ComplexValue
               , T.BooleanValue = S.BooleanValue
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
