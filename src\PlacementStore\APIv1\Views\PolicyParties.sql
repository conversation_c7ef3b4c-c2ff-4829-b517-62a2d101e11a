/*
Lineage
PolicyId=dbo.Policy.PolicyId
GlobalPartyRoleID=ref.PartyRole.GlobalPartyRoleId
IsPrimaryParty=dbo.PolicyPartyRelationship.IsPrimaryParty
PartyId=dbo.Party.PartyId
PartyKey=dbo.Party.PartyKey
PartyName=dbo.Party.PartyName
BusinessKey=dbo.Party.BusinessKey
*/
CREATE VIEW APIv1.PolicyParties
AS

SELECT
    P.PolicyId
  , GlobalPartyRoleID = PRL.GlobalPartyRoleId
  , PPR.IsPrimaryParty
  , PY.PartyId
  , PY.PartyKey
  , PY.PartyName
  , PY.BusinessKey
FROM
    dbo.Policy P
    JOIN dbo.PolicyPartyRelationship PPR
        ON PPR.PolicyId = P.PolicyId
           AND PPR.IsDeleted = 0

    JOIN ref.PartyRole PRL
        ON PRL.PartyRoleId = PPR.PartyRoleId
           AND PRL.IsDeprecated = 0

    JOIN dbo.Party PY
        ON PY.PartyId = PPR.PartyId
           AND PY.IsDeleted = 0;