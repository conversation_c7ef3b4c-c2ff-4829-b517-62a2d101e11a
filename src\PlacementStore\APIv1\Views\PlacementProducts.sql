/*
Lineage
PlacementId=dbo.PlacementStructure.PlacementId
PlacementId=dbo.Specification.PlacementId
PlacementId=dbo.Placement.PlacementId
DataSourceInstanceID=dbo.Placement.ServicingPlatformId
ProductId=dbo.PlacementProduct.ProductId
ProductId=dbo.Specification.ProductId
ProductId=PS.ElementTagSummary.ElementTagTypeId
ProductName=rpt.ProductHierarchy.ProductName
ProductName=PS.ElementTagSummary.ElementTagType
ETLUpdatedDate=dbo.PlacementProduct.LastUpdatedUTCDate
ETLUpdatedDate=dbo.PlacementStructure.ETLUpdatedDate
ETLUpdatedDate=dbo.Placement.LastUpdatedUTCDate
ETLUpdatedDate=dbo.Specification.LastUpdatedUTCDate
IsDeprecated=rpt.ProductHierarchy.IsDeprecated
*/
CREATE VIEW APIv1.PlacementProducts
AS

SELECT
    st.PlacementId
  , DataSourceInstanceID = p.ServicingPlatformId
  , pp.ProductId
  , ph.ProductName
  , ETLUpdatedDate = (
        SELECT MAX(v) FROM (VALUES (pp.LastUpdatedUTCDate), (st.ETLUpdatedDate), (p.LastUpdatedUTCDate)) value (v)
    )
  , ph.IsDeprecated
FROM
    dbo.PlacementProduct pp
    INNER JOIN dbo.PlacementStructure st
        ON pp.PlacementStructureId = st.PlacementStructureId
           AND st.IsDeleted = 0

    INNER JOIN rpt.ProductHierarchy ph
        ON pp.ProductId = ph.ProductId

    INNER JOIN dbo.Placement p
        ON p.PlacementId = st.PlacementId
WHERE
    pp.IsDeleted = 0
    AND pp.CreatedUser <> 'PACTImport'
UNION ALL
SELECT
    sp.PlacementId
  , DataSourceInstanceID = p.ServicingPlatformId
  , sp.ProductId
  , ph.ProductName
  , ETLUpdatedDate = (
        SELECT MAX(v) FROM (VALUES (sp.LastUpdatedUTCDate), (p.LastUpdatedUTCDate)) value (v)
    )
  , ph.IsDeprecated
FROM
    dbo.Specification sp
    INNER JOIN rpt.ProductHierarchy ph
        ON sp.ProductId = ph.ProductId

    INNER JOIN dbo.Placement p
        ON p.PlacementId = sp.PlacementId
WHERE
    sp.IsDeleted = 0
UNION ALL
SELECT
    pl.PlacementId
  , DataSourceInstanceID = pl.ServicingPlatformId
  , ProductId = (8000000 + tag.ElementTagTypeId)
  , ProductName = tag.ElementTagType
  , ETLUpdatedDate = (
        SELECT MAX(v) FROM (VALUES (pl.LastUpdatedUTCDate)) value (v)
    )
  , IsDeprecated = CAST(0 AS BIT)
FROM
    dbo.Placement pl
    INNER JOIN dbo.PlacementRiskDefinitionElement rd
        ON pl.PlacementId = rd.PlacementId

    INNER JOIN PS.ElementTagSummary tag
        ON tag.ElementId = rd.ElementId
           AND tag.ElementTagGroupKey = 'lineOfBusiness'
           AND tag.ElementTypeGroupRowNumber = 1;
