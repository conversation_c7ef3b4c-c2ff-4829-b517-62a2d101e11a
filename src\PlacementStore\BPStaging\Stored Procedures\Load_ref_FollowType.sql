/*
Lineage
ref.FollowType.FollowTypeKey=BPStaging.FollowType.LabelTranslationKey
ref.FollowType.FollowType=BPStaging.FollowType.Text
ref.FollowType.SourceUpdatedDate=BPStaging.FollowType.ValidFrom
ref.FollowType.IsDeprecated=BPStaging.FollowType.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_FollowType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.FollowType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.FollowType T
    USING (
        SELECT
            DataSourceInstanceId = 50366
          , FollowTypeKey = R.LabelTranslationKey
          , FollowType = R.Text
          , SourceUpdatedDate = COALESCE(R.ValidFrom, GETUTCDATE())
          , IsDeprecated = COALESCE(R.IsDeprecated, 0)
        FROM
            BPStaging.FollowType R
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.FollowTypeKey = S.FollowTypeKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , FollowTypeKey
               , FollowType
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.FollowTypeKey
                   , S.FollowType
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.FollowTypeKey
                               , T.FollowType
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.FollowTypeKey
                               , S.FollowType
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.FollowTypeKey = S.FollowTypeKey
               , T.FollowType = S.FollowType
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);