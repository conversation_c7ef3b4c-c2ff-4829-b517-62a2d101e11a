/*
Lineage
PlacementSystemId=dbo.Placement.PlacementSystemId
InceptionDate=dbo.Placement.InceptionDate
ExpiryDate=dbo.Placement.ExpiryDate
CreatedUTCDate=dbo.Placement.CreatedUTCDate
LastUpdatedUTCDate=dbo.Placement.LastUpdatedUTCDate
DataSourceInstanceId=dbo.Placement.ServicingPlatformId
PlacementStatusId=dbo.Placement.PlacementStatusId
*/
CREATE VIEW APIv1.PlacementUpdates
AS
--only attributes that will be updated by placement store are inception, expiry and status and renewed from placementid
--rest of updates will be mirroring
SELECT
    PlacementSystemId = CAST(pl.PlacementSystemId AS NVARCHAR(100))
  , pl.InceptionDate
  , pl.ExpiryDate
  , CreatedUTCDate = CAST(pl.CreatedUTCDate AS DATETIME2)
  , LastUpdatedUTCDate = CAST(pl.LastUpdatedUTCDate AS DATETIME2)
  , DataSourceInstanceId = pl.ServicingPlatformId
  , pl.PlacementStatusId
FROM
    dbo.Placement pl
WHERE
    pl.IsDeleted = 0 --not deleted 
    AND pl.PlacementSystemId IS NOT NULL --on placement platform 
    AND pl.PolicyTriggeredUpdate = 1
    AND ISNULL(pl.CancellationReasonId, 0) <> 9 --exclude cancelled migrated cases from update
    AND pl.DataSourceInstanceId = 50366; --BP