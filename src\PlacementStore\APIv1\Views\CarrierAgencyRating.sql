/*
Lineage
CarrierID=PS.CarrierHierarchyExtended.CarrierId
AgencyId=dbo.CarrierAgencyRating.AgencyId
RatingId=dbo.CarrierAgencyRating.RatingId
CarrierRatingOutlook_ID=dbo.CarrierAgencyRating.CarrierRatingOutlook_Id
DateOfRating=dbo.CarrierAgencyRating.DateOfRating
CreatedUTCDate=dbo.CarrierAgencyRating.ETLCreatedDate
LastUpdatedUTCDate=dbo.CarrierAgencyRating.ETLUpdatedDate
FinancialSizeId=dbo.CarrierAgencyRating.FinancialSizeId
FinancialSize=dbo.CarrierAgencyRating.FinancialSize
FinancialSizeDesc=dbo.CarrierAgencyRating.FinancialSizeDesc
*/
CREATE VIEW APIv1.CarrierAgencyRating
AS
SELECT
    CarrierID = CH.CarrierId
  , CAR.AgencyId
  , CAR.RatingId
  , CarrierRatingOutlook_ID = CAR.CarrierRatingOutlook_Id
  , CAR.DateOfRating
  , CreatedUTCDate = CAR.ETLCreatedDate
  , LastUpdatedUTCDate = CAR.ETLUpdatedDate
  , CAR.FinancialSizeId
  , CAR.FinancialSize
  , CAR.FinancialSizeDesc
FROM
    dbo.CarrierAgencyRating CAR
    INNER JOIN dbo.Carrier C
        ON CAR.CarrierId = C.CarrierId

    INNER JOIN PS.CarrierHierarchyExtended CH
        ON C.PSCarrierId = CH.CarrierId

    LEFT JOIN dbo.CarrierRatingOutlook OU
        ON CAR.CarrierRatingOutlook_Id = OU.CarrierRatingOutlookId
WHERE
    ISNULL(CAR.CarrierRatingOutlook_Id, 0) = ISNULL(OU.CarrierRatingOutlookId, 0) --To ignore invalid CAR.CarrierRatingOutlook_ID of 9
UNION ALL
SELECT
    CarrierID = CH.CarrierId
  , CAR.AgencyId
  , CAR.RatingId
  , CarrierRatingOutlook_ID = CAR.CarrierRatingOutlook_Id
  , CAR.DateOfRating
  , CreatedUTCDate = CAR.ETLCreatedDate
  , LastUpdatedUTCDate = CAR.ETLUpdatedDate
  , CAR.FinancialSizeId
  , CAR.FinancialSize
  , CAR.FinancialSizeDesc
FROM
    PS.CarrierHierarchyExtended CH
    INNER JOIN dbo.Carrier C
        ON C.PSCarrierId = CH.CarrierEntityId

    INNER JOIN dbo.CarrierAgencyRating CAR
        ON CAR.CarrierId = C.CarrierId

    LEFT JOIN dbo.CarrierRatingOutlook OU
        ON CAR.CarrierRatingOutlook_Id = OU.CarrierRatingOutlookId
WHERE
    CH.DataSourceInstanceId <> 50276
    AND ISNULL(CAR.CarrierRatingOutlook_Id, 0) = ISNULL(OU.CarrierRatingOutlookId, 0); --To ignore invalid CAR.CarrierRatingOutlook_ID of 9
