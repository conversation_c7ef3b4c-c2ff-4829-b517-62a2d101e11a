CREATE TABLE BP.ContractRiskCode (
    Id                INT          NOT NULL
  , ContractId        INT          NOT NULL
  , RiskCodeId        INT          NOT NULL
  , SourceUpdatedDate DATETIME2(7) NOT NULL
  , ETLCreatedDate    DATETIME2(7) NOT NULL
  , ETLUpdatedDate    DATETIME2(7) NOT NULL
  , IsDeleted         BIT          NOT NULL
        DEFAULT (0)
  , CONSTRAINT PK_BP_ContractRiskCode
        PRIMARY KEY
        (
            Id
        )
  , CONSTRAINT FK_BP_ContractRiskCode_ref_RiskCode
        FOREIGN KEY
        (
            RiskCodeId
        )
        REFERENCES ref.RiskCode
        (
            RiskCodeId
        )
);