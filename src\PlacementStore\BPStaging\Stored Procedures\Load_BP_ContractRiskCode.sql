/*
Lineage
BP.ContractRiskCode.Id=BPStaging.ContractRiskCode.Id
BP.ContractRiskCode.ContractId=BPStaging.ContractRiskCode.ContractId
BP.ContractRiskCode.RiskCodeId=BPStaging.ContractRiskCode.RiskCodeId
BP.ContractRiskCode.SourceUpdatedDate=BPStaging.ContractRiskCode.ValidTo
BP.ContractRiskCode.SourceUpdatedDate=BPStaging.ContractRiskCode.ValidFrom
BP.ContractRiskCode.IsDeleted=BPStaging.ContractRiskCode.ValidTo
*/
---------------------------------------------------------------------------------------

CREATE PROCEDURE BPStaging.Load_BP_ContractRiskCode
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.ContractRiskCode';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE BP.ContractRiskCode t
    USING (
        SELECT
            r.Id
          , r.ContractId
          , r.RiskCodeId
          , SourceUpdatedDate = IIF(YEAR(r.ValidTo) < 9999, r.ValidTo, r.ValidFrom)
          , IsDeleted = IIF(YEAR(r.ValidTo) < 9999, 1, 0)
        FROM (
        SELECT
            Id
          , ContractId
          , RiskCodeId
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ContractRiskCode
    ) r
        WHERE
            r.RowNo = 1
    ) s
    ON t.Id = s.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , ContractId
               , RiskCodeId
               , SourceUpdatedDate
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.Id
                   , s.ContractId
                   , s.RiskCodeId
                   , s.SourceUpdatedDate
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 t.Id
                               , t.ContractId
                               , t.RiskCodeId
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                             INTERSECT
                             SELECT
                                 s.Id
                               , s.ContractId
                               , s.RiskCodeId
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                         )
        THEN UPDATE SET
                 t.Id = s.Id
               , t.ContractId = s.ContractId
               , t.RiskCodeId = s.RiskCodeId
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.IsDeleted = s.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
