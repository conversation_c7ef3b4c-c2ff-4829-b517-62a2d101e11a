/*
Lineage
*/
/*
   LineageOptions=NoScan,NoValue;
   No Lineage will be added as this is being used for correcting the data.
*/
CREATE PROCEDURE BPStaging.UpdatePlacementDatesAndStatus
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @InceptionDateUpdateCount INT;
DECLARE @ExpiryDateUpdateCount INT;
DECLARE @StatusUpdateCount INT;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

/* Set initial counts to 0 */
SET @InceptionDateUpdateCount = 0;
SET @ExpiryDateUpdateCount = 0;
SET @StatusUpdateCount = 0;

BEGIN TRY
    DROP TABLE IF EXISTS #TeamsToExcludeFromUpdates;

    CREATE TABLE #TeamsToExcludeFromUpdates (
        FullPath NVARCHAR(255) NOT NULL PRIMARY KEY
    );

    INSERT INTO
        #TeamsToExcludeFromUpdates
        (
            FullPath
        )
    SELECT FullPath
    FROM
        ref.Team
    WHERE
        (
        FullPath LIKE 'CRB > Italy%'
        OR FullPath LIKE 'CRB > Spain%'
        OR FullPath LIKE 'CRB > Portugal%'
        OR FullPath LIKE 'CRB > The Netherlands%'
        OR FullPath LIKE 'CRB > Norway%'
        OR FullPath LIKE 'CRB > Sweden%'
        OR FullPath LIKE 'CRB > Finland%'
        OR FullPath LIKE 'CRB > Germany%'
        OR FullPath LIKE 'CRB > France%' -- Adding France as although there is a policy feed, it is not being updated so should be ignored.
    )
    ORDER BY
        FullPath;

    /*-------------------------------------------------------------------------------------------------------------------------------

    ***********************************************************************************************************

    Update placements where policy inception / expiry date / status changed where active on Broking Platform.

    ***********************************************************************************************************

    Updates should only be applied to placements in flight, if a placement is completed, cancelled or merged it should be ignored
    The current policy(s) should be used to drive updates, where it is available and is in an active state. If the current policy
    does not exist or is in the pre-live "Placement" state it should be ignored.

    When using the expiring policy to drive the update assume the Placement has the same period as the expiring policy.

    Where there are more than 1 expiring / current policy being considered, if they conflict do not apply an update (we dont know
    which is right).

    If current policy is NTU'd revert to expiring (it may have been extended).

    Exceptions:
    Brazil only want Auto Cancellation, and do not want updates to Inception / Expiry
    eGlobal (generic) if the expiring policy is lapsed then cancel the placement (they Lapse ahead of renewal)
    China dont want Inception date updates
    GB Inspace dont want Inception / Expiry Date updates
    MTA Placements - Product Backlog Item 257211 AC04
    Italy, Spain, Portugal, The Netherlands, Norway, Sweden, Finland and Germany do not want placement dates modified by attached policies.

    Current Policy Based Date Updates
    Any active policies set Inception / Expiry (no update if conflicts between these)

    Expiring Policy Based Date Uodates
    Lapsed / Cancelled - if all policies are lapsed / Cancelled use existing Placement dates
    NTU policies (if all policies are NTU use expiring Policy)

    Status Updates
    When all current policies are cancelled / NTU then Cancelled
    When all expriring policies are cancelled and no active current policies then cancelled

*/

    -------------------------------------------------------------------------------------------------------------------------------
    DECLARE @PlacementStatusId_NotStarted INT =
            (
                SELECT PlacementStatusId FROM ref.PlacementStatus WHERE PlacementStatusKey = '1' AND DataSourceInstanceId = 50366
            );
    DECLARE @PlacementStatusId_InProgress INT =
            (
                SELECT PlacementStatusId FROM ref.PlacementStatus WHERE PlacementStatusKey = '2' AND DataSourceInstanceId = 50366
            );
    DECLARE @PlacementStatusId_Complete INT =
            (
                SELECT PlacementStatusId FROM ref.PlacementStatus WHERE PlacementStatusKey = '3' AND DataSourceInstanceId = 50366
            );
    DECLARE @PlacementStatusId_Merged INT =
            (
                SELECT PlacementStatusId FROM ref.PlacementStatus WHERE PlacementStatusKey = '4' AND DataSourceInstanceId = 50366
            );
    DECLARE @PlacementStatusId_Validating INT =
            (
                SELECT PlacementStatusId FROM ref.PlacementStatus WHERE PlacementStatusKey = '5' AND DataSourceInstanceId = 50366
            );
    DECLARE @PlacementStatusId_Cancelled INT =
            (
                SELECT PlacementStatusId FROM ref.PlacementStatus WHERE PlacementStatusKey = '6' AND DataSourceInstanceId = 50366
            );

    DROP TABLE IF EXISTS #PlacementsWithCurrentPolicy;

    SELECT
        pl.PlacementId
      , pl.DataSourceInstanceId
      , ServicingPlatformDataSourceId = ds.DataSourceId
      , pl.ServicingPlatformId
      , pl.InceptionDate
      , pl.ExpiryDate
      , pl.PlacementStatusId
      , plst.PlacementStatus
      , pl.CancellationReasonId
      , po.PolicyId
      , po.PolicyReference
      , PolicyInceptionDate = po.InceptionDate
      , PolicyExpiryDate = po.ExpiryDate
      , PolicyRenewalDate = po.RenewalDate
      , PolicyDataSourceInstanceId = po.DataSourceInstanceId
      , ps.PolicyStatusKey
      , ps.PolicyStatus
      , RefPolicyStatusId = rps.RefPolicyStatusId
      , rps.RefPolicyStatus
      , TeamFullPath = t.FullPath
    INTO #PlacementsWithCurrentPolicy
    FROM
        dbo.Placement pl
        INNER JOIN dbo.PlacementPolicy pp
            ON pp.PlacementId = pl.PlacementId
               AND pp.PlacementPolicyRelationshipTypeId = 1 --> Get Current Policy
               AND pp.IsDeleted = 0

        INNER JOIN dbo.Policy po
            ON po.PolicyId = pp.PolicyId

        LEFT JOIN PAS.PolicyStatus ps
            ON ps.PolicyStatusKey = po.PolicyStatusKey
               AND ps.DataSourceInstanceId = po.DataSourceInstanceId

        LEFT JOIN PAS.RefPolicyStatus rps
            ON rps.RefPolicyStatusId = po.RefPolicyStatusId

        INNER JOIN ref.PlacementStatus plst
            ON pl.PlacementStatusId = plst.PlacementStatusId

        LEFT JOIN Reference.DataSourceInstance ds
            ON pl.ServicingPlatformId = ds.DataSourceInstanceId -- To get DataSourceId

        LEFT JOIN dbo.PlacementTeams pt
            ON pl.PlacementId = pt.PlacementId
               AND pt.IsDeleted = 0

        LEFT JOIN ref.Team t -- To allow team to be used later to prevent some operations.
            ON pt.TeamId = t.TeamId
    WHERE
        pl.PlacementSystemId IS NOT NULL --only update placements on placement platform
        AND pl.DataSourceInstanceId = 50366
        AND plst.PlacementStatusId IN (
                @PlacementStatusId_NotStarted, @PlacementStatusId_InProgress
            )
        AND ISNULL(pl.CancellationReasonId, 0) NOT IN (
                8, 9
            ) --Not Deprecated / Migrated
        AND pl.MTACreatedFromPlacementId IS NULL -- Exclude MTAs
        AND NOT EXISTS (
        SELECT 1 FROM PS.PlacementDatesExclusion pde WHERE pde.PlacementId = pl.PlacementId AND pde.IsActive = 1
    );

    --SELECT * FROM #PlacementsWithCurrentPolicy
    --SELECT DISTINCT PlacementId FROM #PlacementsWithCurrentPolicy
    DROP TABLE IF EXISTS #CurrentPolicyGroup;

    SELECT
        pl.PlacementId
      , pl.ServicingPlatformId
      , pl.ServicingPlatformDataSourceId
      , pl.InceptionDate
      , pl.ExpiryDate
      , pl.PlacementStatusId
      , pl.PlacementStatus
      , pl.PolicyInceptionDate
      , pl.PolicyExpiryDate
      , pl.RefPolicyStatusId
      , pl.RefPolicyStatus
      , pl.TeamFullPath
      , GroupPolicyCount = COUNT(pl.PolicyId)
      , ActivePolicy = CASE WHEN pl.RefPolicyStatus IN (
                                'Live', 'Lapsed'
                            )
                                THEN 1 --Live / Expired = Active
                            ELSE 0 END
      , StatusRank = CASE WHEN pl.RefPolicyStatus IN (
                              'Live'
                          )
                              THEN 1 --Live
                          WHEN pl.RefPolicyStatus IN (
                              'Lapsed'
                          )
                              THEN 2 --Expired
                          WHEN pl.RefPolicyStatus IN (
                              'Placement'
                          )
                              THEN 3 --Pre-Live
                          WHEN pl.RefPolicyStatus IN (
                              'Cancelled'
                          )
                              THEN 4 --Cancelled
                          WHEN pl.RefPolicyStatus IN (
                              'Not Taken Up (NTU)'
                          )
                              THEN 5 --NTU
                          ELSE 6 --Other / Unknown
                     END
    INTO #CurrentPolicyGroup
    FROM
        #PlacementsWithCurrentPolicy pl
    GROUP BY
        pl.PlacementId
      , pl.ServicingPlatformId
      , pl.ServicingPlatformDataSourceId
      , pl.InceptionDate
      , pl.ExpiryDate
      , pl.PlacementStatusId
      , pl.PlacementStatus
      , pl.PolicyInceptionDate
      , pl.PolicyExpiryDate
      , pl.RefPolicyStatusId
      , pl.RefPolicyStatus
      , pl.TeamFullPath;

    --SELECT * FROM #CurrentPolicyGroup WHERE [RefPolicyStatus] NOT IN ('Not Taken Up (NTU)', 'Live', 'Lapsed', 'Cancelled', 'Placement') --NTU, Live, Lapsed or Cancelled
    --SELECT * FROM #CurrentPolicyGroup WHERE StatusRank = 4
    DROP TABLE IF EXISTS #RankedCurrentPolicyGroup;

    SELECT
        cp.PlacementId
      , cp.ServicingPlatformId
      , cp.InceptionDate
      , cp.ExpiryDate
      , cp.PlacementStatusId
      , cp.PlacementStatus
      , cp.PolicyInceptionDate
      , cp.PolicyExpiryDate
      , cp.RefPolicyStatusId
      , cp.RefPolicyStatus
      , cp.TeamFullPath
      , cp.GroupPolicyCount
      , cp.StatusRank
      , dc.CurrentPolicyCount
      , ActiveCurrentPolicyCount = ISNULL(ap.ActiveCurrentPolicyCount, 0)
      , ActiveRefPolicyStatusDistinctCount = ISNULL(ap.ActiveRefPolicyStatusDistinctCount, 0)
      , ActiveStatusRankDistinctCount = ISNULL(ap.ActiveStatusRankDistinctCount, 0)
      , ActiveInceptionDateDistinctCount = ISNULL(ap.ActiveInceptionDateDistinctCount, 0)
      , ActiveExpiryDateDistinctCount = ISNULL(ap.ActiveExpiryDateDistinctCount, 0)
      , ROW_NO = ROW_NUMBER() OVER (PARTITION BY cp.PlacementId ORDER BY cp.StatusRank, cp.PolicyInceptionDate)
      , StatusDenseRank = DENSE_RANK() OVER (PARTITION BY cp.PlacementId ORDER BY cp.StatusRank, cp.PolicyInceptionDate)
    INTO #RankedCurrentPolicyGroup
    FROM
        #CurrentPolicyGroup cp
        INNER JOIN (
            SELECT
                PlacementId
              , CurrentPolicyCount = SUM(GroupPolicyCount)
              , RefPolicyStatusDistinctCount = COUNT(DISTINCT RefPolicyStatusId)
              , StatusRankDistinctCount = COUNT(DISTINCT StatusRank)
              , InceptionDateDistinctCount = COUNT(DISTINCT PolicyInceptionDate)
              , ExpiryDateDistinctCount = COUNT(DISTINCT PolicyExpiryDate)
            FROM
                #CurrentPolicyGroup
            GROUP BY
                PlacementId
        ) dc
            ON dc.PlacementId = cp.PlacementId

        LEFT JOIN (
            SELECT
                PlacementId
              , ActiveCurrentPolicyCount = SUM(GroupPolicyCount)
              , ActiveRefPolicyStatusDistinctCount = COUNT(DISTINCT RefPolicyStatusId)
              , ActiveStatusRankDistinctCount = COUNT(DISTINCT StatusRank)
              , ActiveInceptionDateDistinctCount = COUNT(DISTINCT PolicyInceptionDate)
              , ActiveExpiryDateDistinctCount = COUNT(DISTINCT PolicyExpiryDate)
            FROM
                #CurrentPolicyGroup
            WHERE
                ActivePolicy = 1
            GROUP BY
                PlacementId
        ) ap
            ON ap.PlacementId = cp.PlacementId;

    --SELECT * FROM #RankedCurrentPolicyGroup WHERE ActiveInceptionDateDistinctCount = 1 AND ROW_NO = 1
    --SELECT * FROM #RankedCurrentPolicyGroup WHERE ROW_NO <> StatusDenseRank
    --SELECT * FROM #RankedCurrentPolicyGroup WHERE InceptionDate <> PolicyInceptionDate AND ActiveInceptionDateDistinctCount = 1
    --SELECT * FROM #RankedCurrentPolicyGroup WHERE StatusRank = 4

    /* InceptionDate Updates */
    UPDATE pl
    SET
        InceptionDate = rpg.PolicyInceptionDate
      , LastUpdatedUTCDate = GETUTCDATE()
      , LastUpdatedUser = 'PACTImport'
      , PolicyTriggeredUpdate = 1
    FROM
        dbo.Placement pl
        INNER JOIN #RankedCurrentPolicyGroup rpg
            ON rpg.PlacementId = pl.PlacementId
               AND rpg.ActiveInceptionDateDistinctCount = 1 --All Active Policies have the same InceptionDate
               AND rpg.ROW_NO = 1 --Uses policies of the preferred status
               AND rpg.PolicyInceptionDate <> rpg.InceptionDate --Where the InceptionDate needs to be updated
               AND rpg.ServicingPlatformId NOT IN (
                       50003 /* Brazil */
                   ) --Brazil dont want InceptionDate updates
               AND NOT EXISTS (
                           SELECT * FROM #TeamsToExcludeFromUpdates tte WHERE tte.FullPath = rpg.TeamFullPath
                       ); -- Neither do these teams

    SELECT @InceptionDateUpdateCount = @InceptionDateUpdateCount + @@ROWCOUNT;

    /* ExpiryDate Updates */
    UPDATE pl
    SET
        ExpiryDate = rpg.PolicyExpiryDate
      , LastUpdatedUTCDate = GETUTCDATE()
      , LastUpdatedUser = 'PACTImport'
      , PolicyTriggeredUpdate = 1
    FROM
        dbo.Placement pl
        INNER JOIN #RankedCurrentPolicyGroup rpg
            ON rpg.PlacementId = pl.PlacementId
               AND rpg.ActiveExpiryDateDistinctCount = 1 --All Active Policies have the same InceptionDate
               AND rpg.ROW_NO = 1 --Uses policies of the preferred status
               AND rpg.PolicyExpiryDate <> rpg.ExpiryDate --Where the ExpiryDate needs to be updated
               AND rpg.ServicingPlatformId NOT IN (
                       50003 /* Brazil */
                   ) --Brazil dont want ExpiryDate updates
               AND NOT EXISTS (
                           SELECT * FROM #TeamsToExcludeFromUpdates tte WHERE tte.FullPath = rpg.TeamFullPath
                       ); -- Neither do these teams

    SELECT @ExpiryDateUpdateCount = @ExpiryDateUpdateCount + @@ROWCOUNT;

    DROP TABLE IF EXISTS #CurrentCancellationGroup;

    SELECT
        PlacementId
      , ServicingPlatformId
      , PlacementStatusId
      , PlacementStatus
      , RefPolicyStatusId
      , RefPolicyStatus
      , GroupPolicyCount = SUM(GroupPolicyCount)
      , StatusRank
      , CurrentPolicyCount
    INTO #CurrentCancellationGroup
    FROM
        #RankedCurrentPolicyGroup
    WHERE
        ActiveCurrentPolicyCount = 0
        AND RefPolicyStatus IN (
                'Cancelled', 'Not Taken Up (NTU)'
            )
    GROUP BY
        PlacementId
      , ServicingPlatformId
      , PlacementStatusId
      , PlacementStatus
      , RefPolicyStatusId
      , RefPolicyStatus
      , StatusRank
      , CurrentPolicyCount;

    /* Placements to Cancel */
    UPDATE
        pl
    SET
        PlacementStatusId = @PlacementStatusId_Cancelled
      , LastUpdatedUTCDate = GETUTCDATE()
      , LastUpdatedUser = 'PACTImport'
      , PolicyTriggeredUpdate = 1
    FROM
        dbo.Placement pl
        INNER JOIN #CurrentCancellationGroup pcg
            ON pcg.PlacementId = pl.PlacementId
               AND pcg.GroupPolicyCount = pcg.CurrentPolicyCount --All Current Policies are Cancelled
               AND ISNULL(pcg.PlacementStatusId, @PlacementStatusId_NotStarted) <> @PlacementStatusId_Cancelled --Placement is not yet Cancelled
               /* Placements to Cancel for GB Marine only When all Placements current policies are all NTU's and it is 60 day past the inception date of the placement*/
               AND NOT EXISTS (
                           SELECT '*'
                           FROM
                               dbo.PlacementTeams pt
                               INNER JOIN ods.vw_ref_Team reft
                                   ON reft.TeamId = pt.TeamId
                           WHERE
                               pt.PlacementId = pl.PlacementId
                               AND DATEDIFF(DAY, ISNULL(pl.InceptionDate, '1900-01-01'), GETDATE()) <= 60
                               AND reft.FullPath LIKE 'CRB > GB > MARINE%'
                       );

    SELECT @StatusUpdateCount = @StatusUpdateCount + @@ROWCOUNT;

    /* Placements to Cancel for GB Marine  When all Placements current policies are all NTU's and it is 60 day past the inception date of the placement*/
    UPDATE
        pl
    SET
        pl.PlacementStatusId = @PlacementStatusId_Cancelled
      , pl.LastUpdatedUTCDate = GETUTCDATE()
      , pl.LastUpdatedUser = 'PACTImport'
      , pl.PolicyTriggeredUpdate = 1
    FROM
        dbo.Placement pl
        INNER JOIN dbo.PlacementTeams pt
            ON pt.PlacementId = pl.PlacementId

        INNER JOIN ods.vw_ref_Team reft
            ON reft.TeamId = pt.TeamId

        INNER JOIN dbo.PlacementPolicy pp
            ON pl.PlacementId = pp.PlacementId

        INNER JOIN dbo.Policy p
            ON pp.PolicyId = p.PolicyId
    WHERE
        ISNULL(pl.PlacementStatusId, @PlacementStatusId_NotStarted) NOT IN (
            @PlacementStatusId_Cancelled, @PlacementStatusId_Complete, @PlacementStatusId_Merged
        ) --Placement is not yet Cancelled
        AND (
            SELECT COUNT(plp.PolicyId)
            FROM
                dbo.PlacementPolicy plp
            WHERE
                plp.PlacementId = pl.PlacementId
                AND plp.PlacementPolicyRelationshipTypeId = 1
        ) = (
            SELECT COUNT(plp1.PolicyId)
            FROM
                dbo.PlacementPolicy plp1
                INNER JOIN dbo.Policy pol
                    ON plp1.PolicyId = pol.PolicyId
            WHERE
                plp1.PlacementId = pl.PlacementId
                AND plp1.PlacementPolicyRelationshipTypeId = 1
                AND pol.RefPolicyStatusId = 101
        ) -->All Current Policies are NTU
        AND pl.MTACreatedFromPlacementId IS NULL
        AND DATEDIFF(DAY, ISNULL(pl.InceptionDate, '1900-01-01'), GETDATE()) >= 60 --> 60 day past the inception date of the placement.
        AND reft.FullPath LIKE 'CRB > GB > MARINE%'
        AND NOT EXISTS (
        SELECT 1 FROM PS.PlacementDatesExclusion pde WHERE pde.PlacementId = pl.PlacementId AND pde.IsActive = 1
    );

    SELECT @StatusUpdateCount = @StatusUpdateCount + @@ROWCOUNT;

    /* Tidy up temp tables */
    --DROP TABLE IF EXISTS #PlacementsWithCurrentPolicy
    DROP TABLE IF EXISTS #CurrentPolicyGroup;
    DROP TABLE IF EXISTS #RankedCurrentPolicyGroup;
    DROP TABLE IF EXISTS #CurrentCancellationGroup;
    DROP TABLE IF EXISTS #PlacementsWithExpiringPolicyOnly;

    SELECT
        pl.PlacementId
      , pl.DataSourceInstanceId
      , ServicingPlatformDataSourceId = ds.DataSourceId
      , pl.ServicingPlatformId
      , pl.InceptionDate
      , pl.ExpiryDate
      , pl.PlacementStatusId
      , plst.PlacementStatus
      , pl.CancellationReasonId
      , po.PolicyId
      , po.PolicyReference
      -- Decide how to produce the Inception Date.
      /* If you change this code you also need to change it in  [APIv1].[Placements] */
      , CalculatedPlacementInceptionDate = CASE WHEN ds.DataSourceId IN (
                                                    50001
                                                ) -- eGlobals
                                                     AND pl.ServicingPlatformId NOT IN (
                                                    50006, 50010 -- but no China
                                                )
                                                     AND po.RenewalDate IS NOT NULL
                                                    THEN po.RenewalDate
                                                WHEN (
                                                    pl.ServicingPlatformId IN (
                                                    50001, 50003, 50044, 50354, 50045, 50029, 50010, 50041
                                                )   -- Epic US, Col (Brazil), VisualSeg (Spain), Epic Canada, WIBS, ASYS, SegElevia or eGlobal Netherlands, Portugal
                                                    -- Brazil don't want Inception Date updates so although this is set, this is ignored.
                                                    OR (
                                                        pl.ServicingPlatformId = 50000
                                                        AND pl.BrokingRegionId = 1
                                                        AND pl.BrokingSubSegmentId = 24
                                                    )
                                                ) --Global FAC NA
                                                    THEN ISNULL(po.ExpiryDate, pl.ExpiryDate)
                                                WHEN pl.ServicingPlatformId IN (
                                                    50006
                                                ) -- China
                                                    THEN pl.InceptionDate
                                                WHEN pt.TeamId = 3000261 -- GB Inspace leave as Placement dates
                                                    THEN pl.InceptionDate
                                                WHEN po.ExpiryDate >= '2999-12-31' --Max range of DATETIME2 is 9999-12-31 and we have policies that have this as an expiry date, anything in the next millennium we should leave as entered in BP
                                                    THEN pl.InceptionDate
                                                ELSE DATEADD(DD, 1, ISNULL(po.ExpiryDate, pl.ExpiryDate)) END
      -- Decide how to produce the Expiry date.
      /* If you change this code you also need to change it in  [APIv1].[Placements] */
      , CalculatedPlacementExpiryDate = CASE WHEN pt.TeamId = 3000261 -- Inspace leave as Placement dates
                                                 THEN pl.ExpiryDate
                                             WHEN po.ExpiryDate >= '2999-12-31' --Max range of DATETIME2 is 9999-12-31 and we have policies that have this as an expiry date, anything in the next millennium we should leave as entered in BP
                                                 THEN pl.ExpiryDate
                                             WHEN DATEDIFF(DD, po.InceptionDate, po.ExpiryDate) IN (
                                                 364, 365, 366
                                             )
                                                 THEN DATEADD(YY, 1, po.ExpiryDate) --annual policy
                                             WHEN DATEDIFF(DD, po.InceptionDate, po.ExpiryDate) IN (
                                                 729, 730, 731
                                             )
                                                 THEN DATEADD(YY, 2, po.ExpiryDate) --biannual policy
                                             ELSE
                                                 DATEADD(
                                                     DD, DATEDIFF(DD, po.InceptionDate, po.ExpiryDate), po.ExpiryDate
                                                 ) -- Use previous policy duration
                                        END
      , PolicyInceptionDate = po.InceptionDate
      , PolicyExpiryDate = po.ExpiryDate
      , PolicyRenewalDate = po.RenewalDate
      , PolicyDataSourceInstanceId = po.DataSourceInstanceId
      , PolicyStatusKey = ps.PolicyStatusKey
      , ps.PolicyStatus
      , RefPolicyStatusId = rps.RefPolicyStatusId
      , rps.RefPolicyStatus
      , TeamFullPath = t.FullPath
    INTO #PlacementsWithExpiringPolicyOnly
    FROM
        dbo.Placement pl
        INNER JOIN dbo.PlacementPolicy pp
            ON pp.PlacementId = pl.PlacementId
               AND pp.PlacementPolicyRelationshipTypeId = 2 --> Get Expiring Policy
               AND pp.IsDeleted = 0

        INNER JOIN dbo.Policy po
            ON po.PolicyId = pp.PolicyId

        LEFT JOIN PAS.PolicyStatus ps
            ON ps.PolicyStatusKey = po.PolicyStatusKey
               AND ps.DataSourceInstanceId = po.DataSourceInstanceId

        LEFT JOIN PAS.RefPolicyStatus rps
            /* Get from the PolicyStatus to avoid PACT auto Lapsing */
            ON rps.RefPolicyStatusId = ps.RefPolicyStatusId

        INNER JOIN ref.PlacementStatus plst
            ON pl.PlacementStatusId = plst.PlacementStatusId

        LEFT JOIN dbo.PlacementTeams pt
            ON pl.PlacementId = pt.PlacementId
               AND pt.IsDeleted = 0

        LEFT JOIN ref.Team t -- To allow team to be used later to prevent some operations.
            ON pt.TeamId = t.TeamId

        LEFT JOIN Reference.DataSourceInstance ds
            ON pl.ServicingPlatformId = ds.DataSourceInstanceId -- To get DataSourceId
    WHERE
        pl.PlacementSystemId IS NOT NULL --only update placements on placement platform
        AND pl.DataSourceInstanceId = 50366
        AND pl.PlacementStatusId IN (
                @PlacementStatusId_NotStarted, @PlacementStatusId_InProgress
            )
        AND ISNULL(pl.CancellationReasonId, 0) NOT IN (
                8, 9
            ) --Not Deprecated / Migrated
        AND NOT EXISTS (
        SELECT PCP.PlacementId FROM #PlacementsWithCurrentPolicy PCP WHERE PCP.PlacementId = pl.PlacementId
    )
        AND NOT EXISTS (
        SELECT 1 FROM PS.PlacementDatesExclusion pde WHERE pde.PlacementId = pl.PlacementId AND pde.IsActive = 1
    );

    --SELECT * FROM #PlacementsWithExpiringPolicyOnly
    --SELECT DISTINCT PlacementId FROM #PlacementsWithExpiringPolicyOnly
    DROP TABLE IF EXISTS #ExpiringPolicyGroup;

    SELECT
        pl.PlacementId
      , pl.ServicingPlatformId
      , pl.ServicingPlatformDataSourceId
      , pl.InceptionDate
      , pl.ExpiryDate
      , pl.PlacementStatusId
      , pl.PlacementStatus
      , pl.CalculatedPlacementInceptionDate
      , pl.CalculatedPlacementExpiryDate
      , pl.RefPolicyStatusId
      , pl.RefPolicyStatus
      , pl.TeamFullPath
      , GroupPolicyCount = COUNT(pl.PolicyId)
      , ActivePolicy = CASE WHEN pl.RefPolicyStatus IN (
                                'Live'
                            )
                                THEN 1 --Live = Active
                            WHEN pl.RefPolicyStatus IN (
                                'Lapsed'
                            )
                                 AND pl.ServicingPlatformDataSourceId NOT IN (
                                50001
                            ) --eGlobal
                                THEN 1 --Treat Lapsed as Live for all but eGlobal
                            ELSE 0 END
      , StatusRank = CASE WHEN pl.RefPolicyStatus IN (
                              'Live'
                          )
                              THEN 1 --Live
                          WHEN pl.RefPolicyStatus IN (
                              'Lapsed'
                          )
                              THEN 2 --Expired
                          WHEN pl.RefPolicyStatus IN (
                              'Placement'
                          )
                              THEN 3 --Pre-Live
                          WHEN pl.RefPolicyStatus IN (
                              'Cancelled'
                          )
                              THEN 4 --Cancelled
                          WHEN pl.RefPolicyStatus IN (
                              'Not Taken Up (NTU)'
                          )
                              THEN 5 --NTU
                          ELSE 6 --Other / Unknown
                     END
    INTO #ExpiringPolicyGroup
    FROM
        #PlacementsWithExpiringPolicyOnly pl
    GROUP BY
        pl.PlacementId
      , pl.ServicingPlatformId
      , pl.ServicingPlatformDataSourceId
      , pl.InceptionDate
      , pl.ExpiryDate
      , pl.PlacementStatusId
      , pl.PlacementStatus
      , pl.CalculatedPlacementInceptionDate
      , pl.CalculatedPlacementExpiryDate
      , pl.RefPolicyStatusId
      , pl.RefPolicyStatus
      , pl.TeamFullPath;

    --SELECT * FROM #ExpiringPolicyGroup ORDER BY PlacementId
    DROP TABLE IF EXISTS #RankedExpiringPolicyGroup;

    SELECT
        cp.PlacementId
      , cp.ServicingPlatformId
      , cp.ServicingPlatformDataSourceId
      , cp.InceptionDate
      , cp.ExpiryDate
      , cp.PlacementStatusId
      , cp.PlacementStatus
      , cp.CalculatedPlacementInceptionDate
      , cp.CalculatedPlacementExpiryDate
      , cp.RefPolicyStatusId
      , cp.RefPolicyStatus
      , cp.TeamFullPath
      , cp.GroupPolicyCount
      , cp.StatusRank
      , dc.ExpiringPolicyCount
      , ActiveExpiringPolicyCount = ISNULL(ap.ActiveExpiringPolicyCount, 0)
      , ActiveRefPolicyStatusDistinctCount = ISNULL(ap.ActiveRefPolicyStatusDistinctCount, 0)
      , ActiveStatusRankDistinctCount = ISNULL(ap.ActiveStatusRankDistinctCount, 0)
      , ActiveInceptionDateDistinctCount = ISNULL(ap.ActiveInceptionDateDistinctCount, 0)
      , ActiveExpiryDateDistinctCount = ISNULL(ap.ActiveExpiryDateDistinctCount, 0)
      , ROW_NO = ROW_NUMBER() OVER (PARTITION BY cp.PlacementId ORDER BY cp.StatusRank, cp.CalculatedPlacementInceptionDate)
      , StatusDenseRank = DENSE_RANK() OVER (PARTITION BY cp.PlacementId ORDER BY cp.StatusRank, cp.CalculatedPlacementInceptionDate)
    INTO #RankedExpiringPolicyGroup
    FROM
        #ExpiringPolicyGroup cp
        INNER JOIN (
            SELECT
                PlacementId
              , ExpiringPolicyCount = SUM(GroupPolicyCount)
              , RefPolicyStatusDistinctCount = COUNT(DISTINCT RefPolicyStatusId)
              , StatusRankDistinctCount = COUNT(DISTINCT StatusRank)
              , InceptionDateDistinctCount = COUNT(DISTINCT CalculatedPlacementInceptionDate)
              , ExpiryDateDistinctCount = COUNT(DISTINCT CalculatedPlacementExpiryDate)
            FROM
                #ExpiringPolicyGroup
            GROUP BY
                PlacementId
        ) dc
            ON dc.PlacementId = cp.PlacementId

        LEFT JOIN (
            SELECT
                PlacementId
              , ActiveExpiringPolicyCount = SUM(GroupPolicyCount)
              , ActiveRefPolicyStatusDistinctCount = COUNT(DISTINCT RefPolicyStatusId)
              , ActiveStatusRankDistinctCount = COUNT(DISTINCT StatusRank)
              , ActiveInceptionDateDistinctCount = COUNT(DISTINCT CalculatedPlacementInceptionDate)
              , ActiveExpiryDateDistinctCount = COUNT(DISTINCT CalculatedPlacementExpiryDate)
            FROM
                #ExpiringPolicyGroup
            WHERE
                ActivePolicy = 1
            GROUP BY
                PlacementId
        ) ap
            ON ap.PlacementId = cp.PlacementId;

    --SELECT * FROM #RankedExpiringPolicyGroup

    /* InceptionDate Updates */
    UPDATE
        pl
    SET
        InceptionDate = rpg.CalculatedPlacementInceptionDate
      , LastUpdatedUTCDate = GETUTCDATE()
      , LastUpdatedUser = 'PACTImport'
      , PolicyTriggeredUpdate = 1
    FROM
        dbo.Placement pl
        INNER JOIN #RankedExpiringPolicyGroup rpg
            ON rpg.PlacementId = pl.PlacementId
               AND rpg.ActiveInceptionDateDistinctCount = 1 --All Active Policies have the same InceptionDate
               AND rpg.ROW_NO = 1 --Uses policies of the preferred status
               AND rpg.CalculatedPlacementInceptionDate <> rpg.InceptionDate --Where the InceptionDate needs to be updated
               AND rpg.ServicingPlatformId NOT IN (
                       50003
                   ) --Brazil dont want InceptionDate updates
               AND NOT EXISTS (
                           SELECT * FROM #TeamsToExcludeFromUpdates tte WHERE tte.FullPath = rpg.TeamFullPath
                       ); -- Neither do these teams

    SELECT @InceptionDateUpdateCount = @InceptionDateUpdateCount + @@ROWCOUNT;

    /* ExpiryDate Updates */
    UPDATE pl
    SET
        ExpiryDate = rpg.CalculatedPlacementExpiryDate
      , LastUpdatedUTCDate = GETUTCDATE()
      , LastUpdatedUser = 'PACTImport'
      , PolicyTriggeredUpdate = 1
    FROM
        dbo.Placement pl
        INNER JOIN #RankedExpiringPolicyGroup rpg
            ON rpg.PlacementId = pl.PlacementId
               AND rpg.ActiveExpiryDateDistinctCount = 1 --All Active Policies have the same InceptionDate
               AND rpg.ROW_NO = 1 --Uses policies of the preferred status
               AND rpg.CalculatedPlacementExpiryDate <> rpg.ExpiryDate --Where the ExpiryDate needs to be updated
               AND rpg.ServicingPlatformId NOT IN (
                       50003
                   ) --Brazil dont want ExpiryDate updates
               AND NOT EXISTS (
                           SELECT * FROM #TeamsToExcludeFromUpdates tte WHERE tte.FullPath = rpg.TeamFullPath
                       ); -- Neither do these teams

    SELECT @ExpiryDateUpdateCount = @ExpiryDateUpdateCount + @@ROWCOUNT;

    DROP TABLE IF EXISTS #ExpiringCancellationGroup;

    SELECT
        PlacementId
      , ServicingPlatformId
      , PlacementStatusId
      , PlacementStatus
      , RefPolicyStatusId
      , RefPolicyStatus
      , GroupPolicyCount = SUM(GroupPolicyCount)
      , StatusRank
      , ExpiringPolicyCount
    INTO #ExpiringCancellationGroup
    FROM
        #RankedExpiringPolicyGroup
    WHERE
        ActiveExpiringPolicyCount = 0
        AND (
            RefPolicyStatus IN (
                'Cancelled', 'Not Taken Up (NTU)'
            )
            OR (
                RefPolicyStatus IN (
                    'Lapsed'
                )
                AND ServicingPlatformDataSourceId IN (
                        50001
                    )
            )
        )
    GROUP BY
        PlacementId
      , ServicingPlatformId
      , PlacementStatusId
      , PlacementStatus
      , RefPolicyStatusId
      , RefPolicyStatus
      , StatusRank
      , ExpiringPolicyCount;

    /* Placements to Cancel */
    UPDATE
        pl
    SET
        PlacementStatusId = @PlacementStatusId_Cancelled
      , LastUpdatedUTCDate = GETUTCDATE()
      , LastUpdatedUser = 'PACTImport'
      , PolicyTriggeredUpdate = 1
    FROM
        dbo.Placement pl
        INNER JOIN #ExpiringCancellationGroup pcg
            ON pcg.PlacementId = pl.PlacementId
               AND pcg.GroupPolicyCount = pcg.ExpiringPolicyCount --All Expiring Policies are Cancelled (or Lapsed for eGlobal)
               AND ISNULL(pcg.PlacementStatusId, @PlacementStatusId_NotStarted) <> @PlacementStatusId_Cancelled --Placement is not yet Cancelled
    ;

    SELECT @StatusUpdateCount = @StatusUpdateCount + @@ROWCOUNT;

    /* Tidy up temp tables */
    DROP TABLE IF EXISTS #PlacementsWithCurrentPolicy;
    DROP TABLE IF EXISTS #PlacementsWithExpiringPolicyOnly;
    DROP TABLE IF EXISTS #ExpiringPolicyGroup;
    DROP TABLE IF EXISTS #RankedExpiringPolicyGroup;
    DROP TABLE IF EXISTS #ExpiringCancellationGroup;

    SELECT @UpdatedCount = @InceptionDateUpdateCount + @ExpiryDateUpdateCount + @StatusUpdateCount;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

-- Let's log all the counts we can. Updates are only carried out if the values are different.
SET @Action =
    N'Update [dbo].[Placement] InceptionDate updates - ' + CONVERT(NVARCHAR(10), ISNULL(@InceptionDateUpdateCount, 0))
    + N', ExpiryDate updates - ' + CONVERT(NVARCHAR(10), ISNULL(@ExpiryDateUpdateCount, 0)) + N', Status updates - '
    + CONVERT(NVARCHAR(10), ISNULL(@StatusUpdateCount, 0)) + N'.';

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN;
