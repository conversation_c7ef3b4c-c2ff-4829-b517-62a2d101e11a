/*
Lineage
rpt.ContractTimeline.Action=BPStaging.ContractTimeline.EventType
rpt.ContractTimeline.ModifiedById=BPStaging.ContractTimeline.ModifiedById
rpt.ContractTimeline.TableId=BPStaging.ContractTimeline.TableId
rpt.ContractTimeline.TableKey=BPStaging.ContractTimeline.TableKey
rpt.ContractTimeline.UserId=dbo.PlacementSystemUser.UserId
rpt.ContractTimeline.UserId=BPStaging.ContractTimeline.UserId
rpt.ContractTimeline.ModifiedDate=BPStaging.ContractTimeline.DateOfEvent
rpt.ContractTimeline.ModifiedBy=BPStaging.ContractTimeline.UserPrincipalName
rpt.ContractTimeline.ModifiedBy=BPStaging.ContractTimeline.UserId
rpt.ContractTimeline.PlacementId=dbo.Placement.PlacementId
rpt.ContractTimeline.ContractId=rpt.Contract.ContractId
rpt.ContractTimeline.Description=BPStaging.ContractTimeline.Description
rpt.ContractTimeline.Detail=BPStaging.ContractTimeline.Detail
rpt.ContractTimeline.GroupingSection=BPStaging.ContractTimeline.GroupingSection
rpt.ContractTimeline.GroupingSectionType=BPStaging.ContractTimeline.GroupingSectionType
rpt.ContractTimeline.PlacementSystemId=BPStaging.ContractTimeline.PlacementId
*/
CREATE PROCEDURE BPStaging.Load_rpt_ContractTimeline
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'rpt.ContractTimeline';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.ContractTimeline
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE rpt.ContractTimeline T
    USING (
        SELECT
            Action
          , ModifiedById
          , TableId
          , TableKey
          , ContractId
          , DataSourceInstanceId
          , ModifiedDate
          , ModifiedBy
          , PlacementId
          , Description
          , Detail
          , GroupingSection
          , GroupingSectionType
          , PlacementSystemId
          , UserId
        FROM (
        SELECT
            Action = CT.EventType
          , CT.ModifiedById
          , CT.TableId
          , CT.TableKey
          , C.ContractId
          , DataSourceInstanceId = 50366 --Broking Platform
          , UserId = CASE WHEN CT.UserPrincipalName LIKE 'crbbro-bkp-%-em20-fa%'
                              THEN (
                              SELECT UserId FROM dbo.PlacementSystemUser WHERE UserPrincipalName = 'system'
                          )
                          ELSE CT.UserId END
          , ModifiedDate = CT.DateOfEvent
          , ModifiedBy = CASE WHEN CT.UserPrincipalName LIKE 'crbbro-bkp-%-em20-fa%'
                                  THEN 'system'
                              WHEN CT.UserId IS NULL
                                  THEN 'Unknown'
                              ELSE ISNULL(CT.UserPrincipalName, 'Unknown') END
          , PL.PlacementId
          , CT.Description
          , CT.Detail
          , CT.GroupingSection
          , CT.GroupingSectionType
          , PlacementSystemId = CT.PlacementId
          , ROW_NO = ROW_NUMBER() OVER (PARTITION BY ModifiedById, EventType ORDER BY DateOfEvent DESC)
        FROM
            BPStaging.ContractTimeline CT
            INNER JOIN dbo.Placement PL
                ON PL.PlacementSystemId = CT.PlacementId
                   AND PL.DataSourceInstanceId = 50366

            LEFT JOIN rpt.Contract C
                ON CAST(SUBSTRING(C.ContractKey
                                , 0
                                , CASE WHEN CHARINDEX('|', C.ContractKey) = 0
                                           THEN LEN(C.ContractKey) + 1
                                       ELSE CHARINDEX('|', C.ContractKey) END
                        ) AS INT) = CT.ContractId
                   AND C.ContractType = 2
                   AND C.IsDeleted = 0
    ) CT
        WHERE
            CT.ROW_NO = 1
    ) S
    ON S.ModifiedById = T.ModifiedById
       AND S.Action = T.Action
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 Action
               , ModifiedById
               , TableId
               , TableKey
               , DataSourceInstanceId
               , UserId
               , ModifiedDate
               , ModifiedBy
               , PlacementId
               , ContractId
               , Description
               , Detail
               , GroupingSection
               , GroupingSectionType
               , PlacementSystemId
             )
             VALUES
                 (
                     S.Action
                   , S.ModifiedById
                   , S.TableId
                   , S.TableKey
                   , S.DataSourceInstanceId
                   , S.UserId
                   , S.ModifiedDate
                   , S.ModifiedBy
                   , S.PlacementId
                   , S.ContractId
                   , S.Description
                   , S.Detail
                   , S.GroupingSection
                   , S.GroupingSectionType
                   , S.PlacementSystemId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.Action
      , S.ModifiedById
      , S.TableId
      , S.TableKey
      , S.DataSourceInstanceId
      , S.UserId
      , S.ModifiedDate
      , S.ModifiedBy
      , S.PlacementId
      , S.ContractId
      , S.Description
      , S.Detail
      , S.GroupingSection
      , S.GroupingSectionType
      , S.PlacementSystemId
    INTERSECT
    SELECT
        T.Action
      , T.ModifiedById
      , T.TableId
      , T.TableKey
      , T.DataSourceInstanceId
      , T.UserId
      , T.ModifiedDate
      , T.ModifiedBy
      , T.PlacementId
      , T.ContractId
      , T.Description
      , T.Detail
      , T.GroupingSection
      , T.GroupingSectionType
      , T.PlacementSystemId
)
        THEN UPDATE SET
                 T.Action = S.Action
               , T.TableId = S.TableId
               , T.TableKey = S.TableKey
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.UserId = S.UserId
               , T.ModifiedDate = S.ModifiedDate
               , T.ModifiedBy = S.ModifiedBy
               , T.PlacementId = S.PlacementId
               , T.ContractId = S.ContractId
               , T.Description = S.Description
               , T.Detail = S.Detail
               , T.GroupingSection = S.GroupingSection
               , T.GroupingSectionType = S.GroupingSectionType
               , T.PlacementSystemId = S.PlacementSystemId
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    --> Remove all audit data older than 3 years. PBI-174578
    DELETE
    CT
    FROM
        rpt.ContractTimeline CT
        INNER JOIN dbo.Placement PL
            ON CT.PlacementId = PL.PlacementId
    WHERE
        PL.InceptionDate < DATEADD(YY, -3, GETDATE());

    SELECT @DeletedCount = @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN;