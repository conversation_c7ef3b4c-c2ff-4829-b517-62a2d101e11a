/*
Lineage
PartyCheckResultId=Reference.PartyCheckResultHistory.PartyCheckResultId
GlobalPartyId=Reference.PartyCheckResultHistory.PartyId
CheckTypeName=Reference.CheckType.CheckTypeName
CheckTypeCode=Reference.CheckType.CheckTypeCode
CheckTypeCategory=Reference.CheckType.CheckTypeCategory
PartyCheckStatusName=Reference.AuthorizationStatus.AuthorizationStatusName
PartyVerificationStatusName=Reference.PartyVerificationStatus.PartyVerificationStatusName
CheckStatusDate=Reference.PartyCheckResultHistory.CheckStatusDate
LastUpdatedTime=Reference.PartyCheckResultHistory.LastUpdatedTime
LastUpdatedBy=Reference.PartyCheckResultHistory.LastUpdatedBy
ETLUpdatedDate=Reference.PartyCheckResultHistory.ETLUpdatedDate
ETLUpdatedDate=Reference.Party.ETLUpdatedDate
ETLUpdatedDate=Reference.CheckType.ETLUpdatedDate
ETLUpdatedDate=Reference.PartyVerificationStatus.ETLUpdatedDate
ETLUpdatedDate=Reference.AuthorizationStatus.ETLUpdatedDate
IsDeleted=Reference.PartyCheckResultHistory.IsDeleted
IsDeleted=Reference.Party.IsDeleted
IsDeleted=Reference.CheckType.IsDeleted
IsDeleted=Reference.PartyVerificationStatus.IsDeleted
IsDeleted=Reference.AuthorizationStatus.IsDeleted
*/
CREATE VIEW APIv1.PartyCheckResult
AS
WITH pcr AS (
    SELECT
        pcr.PartyCheckResultId
      , GlobalPartyId = pcr.PartyId
      , ct.CheckTypeName
      , ct.CheckTypeCode
      , ct.CheckTypeCategory
      , PartyCheckStatusName = pcs.AuthorizationStatusName
      , pvs.PartyVerificationStatusName
      , pcr.CheckStatusDate
      , pcr.LastUpdatedTime
      , pcr.LastUpdatedBy
      , ETLUpdatedDate = GREATEST(
                             pcr.ETLUpdatedDate
                           , p.ETLUpdatedDate
                           , ct.ETLUpdatedDate
                           , pvs.ETLUpdatedDate
                           , pcs.ETLUpdatedDate
                         )
      , IsDeleted = GREATEST(pcr.IsDeleted, p.IsDeleted, ct.IsDeleted, pvs.IsDeleted, pcs.IsDeleted)
      , Offset = ROW_NUMBER() OVER (PARTITION BY pcr.PartyCheckResultId ORDER BY pcr.ETLUpdatedDate DESC)
    FROM
        Reference.PartyCheckResultHistory pcr
        JOIN Reference.Party p
            ON p.PartyId = pcr.PartyId

        JOIN Reference.CheckType ct
            ON pcr.CheckTypeId = ct.CheckTypeId

        JOIN Reference.PartyVerificationStatus pvs
            ON pcr.PartyVerificationStatusId = pvs.PartyVerificationStatusId

        JOIN Reference.AuthorizationStatus pcs
            ON pcr.PartyCheckStatusId = pcs.AuthorizationStatusId
    WHERE
        pcr.EffectiveToDate = '9999-12-31'
)
SELECT
    pcr.PartyCheckResultId
  , pcr.GlobalPartyId
  , pcr.CheckTypeName
  , pcr.CheckTypeCode
  , pcr.CheckTypeCategory
  , pcr.PartyCheckStatusName
  , pcr.PartyVerificationStatusName
  , pcr.CheckStatusDate
  , pcr.LastUpdatedTime
  , pcr.LastUpdatedBy
  , pcr.ETLUpdatedDate
  , pcr.IsDeleted
FROM
    pcr
WHERE
    pcr.Offset = 1;