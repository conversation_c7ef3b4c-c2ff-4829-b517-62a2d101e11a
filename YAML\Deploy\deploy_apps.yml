parameters:
  configOnly: false
  reporting: false
  replicaCount: 1
  reportingOperation: 'scale_up' # Possible values: 'scale_up', 'scale_down'
  enableReplica: false
  download: ''
  appName: 'Placement Store'
  compareBranch: trunk
  variableGroupName: ''
  variableGroupNameShared: ''
  appId: ''
  billingCode: ''
  microservice: ''
  envName: ''
  regionName: ''
  regionNameDR: ''
  azureServiceConnection: ''
  agentPoolName: ''
  sqlDatabases: { }
  webApps: { }
  funcApps: { }

  virtualNetworkResourceGroupName: ''
  virtualNetworkName: ''
  appSubnetNamesCSV: ''
  appIdentitiesCSV: ''

  virtualNetworkResourceGroupNameDR: ''
  virtualNetworkNameDR: ''
  appSubnetNamesCSVDR: ''
  appIdentitiesCSVDR: ''

  dataFactory: {}
  dataFactoryName: ''
  dataFactoryResourceGroupName: ''

jobs:
- ${{ if eq(parameters.configOnly,'true') }}:
  - ${{ each sqlDatabase in parameters.sqlDatabases }}:
    - ${{ if eq(sqlDatabase.project, 'PlacementStoreDb.Build') }}:
      - deployment: ${{parameters.envName}}_config
        displayName: 'Database config'
        variables:
        - group: ${{parameters.variableGroupName}}
        - group: ${{parameters.variableGroupNameShared}}
        pool:
          name: ${{parameters.agentPoolName}}
          demands: AZP_REGION -equals ${{parameters.regionName}}
        environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
        strategy:
          runOnce:
            deploy:
              steps:
              - checkout: templates
                fetchDepth: 1    # shallow
                fetchTags: false # dont fetch tags
              - download: none
              - template: Deploy/database-script.yml@templates
                parameters:
                  appId: ${{parameters.appId}}
                  billingCode: ${{parameters.billingCode}}
                  microservice: ${{parameters.microservice}}
                  azureServiceConnection: ${{parameters.azureServiceConnection}}                    # Azure DevOps Service Connection for target Azure subscription
                  envName: ${{parameters.envName}}                                                  # Environment name
                  download: ${{parameters.download}}                                                # Download pipeline
                  artefactsPath: $(Pipeline.Workspace)                                              # Path to artefacts
                  sqlDatabase: ${{sqlDatabase}}                                                     # Sql Database
                  scriptArtefactName: PostDeployScript                                              # Artefact containing script
                  scriptFilename: postdeploy.sql                                                    # Script to run

- ${{ elseif eq(parameters.reporting,'true') }}:
    # Scale up or down
  - template: deploy_reporting_scale_sync.yml
    parameters:
      envName: ${{parameters.envName}}
      microservice: ${{parameters.microservice}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      download: ${{parameters.download}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      ssas: ${{parameters.ssas}}
      replicaCount: ${{parameters.replicaCount}}
      operation: ${{parameters.reportingOperation}}
      enableReplica: ${{parameters.enableReplica}}
    # Deploy reporting models
  - template: deploy_reporting_models.yml
    parameters:
      envName: ${{parameters.envName}}
      microservice: ${{parameters.microservice}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      download: ${{parameters.download}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      ssas: ${{parameters.ssas}}
      dependsOn:
      - ${{parameters.envName}}_reports_scale_sync
- ${{ else }}:
  # Trim Deployment History
  - deployment: ${{parameters.envName}}_${{parameters.regionName}}_deployhistory
    displayName: 'Clean Deploy History ${{parameters.regionName}}'
    variables:
    - group: ${{parameters.variableGroupName}}
    - group: ${{parameters.variableGroupNameShared}}
    pool:
      name: ${{parameters.agentPoolName}}
      demands: AZP_REGION -equals ${{parameters.regionName}}
    environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: templates
            fetchDepth: 1    # shallow
            fetchTags: false # dont fetch tags
          - download: none
          - template: Deploy/app-deployhistory.yml@templates
            parameters:
              appId: ${{parameters.appId}}
              billingCode: ${{parameters.billingCode}}
              microservice: ${{parameters.microservice}}
              azureServiceConnection: ${{parameters.azureServiceConnection}}   # Azure DevOps Service Connection for target Azure subscription
              envName: ${{parameters.envName}}                                 # Environment name
              regionName: ${{parameters.regionName}}                           # Region Name

  # Trim Deployment History - DR
  - ${{ if ne(parameters.regionNameDR, '') }}:
    - deployment: ${{parameters.envName}}_${{parameters.regionNameDR}}_deployhistory
      displayName: 'Clean Deploy History ${{parameters.regionNameDR}}'
      variables:
      - group: ${{parameters.variableGroupName}}
      - group: ${{parameters.variableGroupNameShared}}
      pool:
        name: ${{parameters.agentPoolName}}
        demands: AZP_REGION -equals ${{parameters.regionName}}
      environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: templates
              fetchDepth: 1    # shallow
              fetchTags: false # dont fetch tags
            - download: none
            - template: Deploy/app-deployhistory.yml@templates
              parameters:
                appId: ${{parameters.appId}}
                billingCode: ${{parameters.billingCode}}
                microservice: ${{parameters.microservice}}
                azureServiceConnection: ${{parameters.azureServiceConnection}}   # Azure DevOps Service Connection for target Azure subscription
                envName: ${{parameters.envName}}                                 # Environment name
                regionName: ${{parameters.regionNameDR}}                         # Region Name

  # Deploy database(s)
  - ${{ each sqlDatabase in parameters.sqlDatabases }}:
    - deployment: ${{parameters.envName}}_${{sqlDatabase.shortName}}_db
      displayName: 'Database: ${{sqlDatabase.shortName}}'
      variables:
      - group: ${{parameters.variableGroupName}}
      - group: ${{parameters.variableGroupNameShared}}
      pool:
        name: ${{parameters.agentPoolName}}
        demands: AZP_REGION -equals ${{parameters.regionName}}
      timeoutInMinutes: 180   # 3 hours
      environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: templates
              fetchDepth: 1    # shallow
              fetchTags: false # dont fetch tags
            - template: Deploy/app-deploydacpac.yml@templates
              parameters:
                download: ${{parameters.download}}
                appId: ${{parameters.appId}}
                billingCode: ${{parameters.billingCode}}
                microservice: ${{parameters.microservice}}
                azureServiceConnection: ${{parameters.azureServiceConnection}}                    # Azure DevOps Service Connection for target Azure subscription
                envName: ${{parameters.envName}}                                                  # Environment
                regionName: ${{parameters.regionName}}                                            # Region
                regionNameDR: ${{parameters.regionNameDR}}                                        # Region DR
                artefactsPath: $(Pipeline.Workspace)                                              # Path to artefacts
                sqlDatabase: ${{sqlDatabase}}                                                     # Deploy Sql Database
                virtualNetworkResourceGroupName: ${{parameters.virtualNetworkResourceGroupName}}  # Resource Group containing Virtual Network
                virtualNetworkName: ${{parameters.virtualNetworkName}}                            # Virtual Network
                subnetNamesCSV: ${{parameters.appSubnetNamesCSV}}                                 # CSV of Subnet names
                appIdentitiesCSV: ${{parameters.appIdentitiesCSV}}                                # CSV of Web / Func App identities

                virtualNetworkResourceGroupNameDR: ${{parameters.virtualNetworkResourceGroupNameDR}} # Resource Group containing Virtual Network DR
                virtualNetworkNameDR: ${{parameters.virtualNetworkNameDR}}                           # Virtual Network DR
                subnetNamesCSVDR: ${{parameters.appSubnetNamesCSVDR}}                                # CSV of Subnet names DR
                appIdentitiesCSVDR: ${{parameters.appIdentitiesCSVDR}}                               # CSV of Web / Func App identities DR

  # Deploy data factory
  - ${{ if in(parameters.envName, 'ci', 'dev','qa','uat','iat','prod') }}:
    - deployment: ${{parameters.envName}}_adf
      displayName: 'ADF Deployment'
      pool:
        name: ${{parameters.agentPoolName}}
        demands: AZP_REGION -equals ${{parameters.regionName}}
      environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
      strategy:
          runOnce:
            deploy:
              steps:
              - checkout: self
                fetchDepth: 1    # shallow
                fetchTags: false # dont fetch tags
              - template: deploy_adf.yml
                parameters:
                  download: ${{parameters.download}}
                  dataFactory: ${{parameters.dataFactory}}
                  dataFactoryName: ${{parameters.dataFactoryName}}
                  dataFactoryResourceGroupName: ${{parameters.dataFactoryResourceGroupName}}
                  azureServiceConnection: ${{parameters.azureServiceConnection}}

  # Deploy webApp(s)
  - ${{ each app in parameters.webApps }}:
    - deployment: ${{parameters.envName}}_${{parameters.regionName}}_wa
      displayName: 'Web App ${{parameters.regionName}}'
      variables:
      - group: ${{parameters.variableGroupName}}
      - group: ${{parameters.variableGroupNameShared}}
      pool:
        name: ${{parameters.agentPoolName}}
        demands: AZP_REGION -equals ${{parameters.regionName}}
      environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: templates
              fetchDepth: 1    # shallow
              fetchTags: false # dont fetch tags
            - template: Deploy/app-deployweb.yml@templates
              parameters:
                download: ${{parameters.download}}
                appId: ${{parameters.appId}}
                billingCode: ${{parameters.billingCode}}
                microservice: ${{parameters.microservice}}
                azureServiceConnection: ${{parameters.azureServiceConnection}}                    # Azure DevOps Service Connection for target Azure subscription
                envName: ${{parameters.envName}}                                                  # Environment name
                regionName: ${{parameters.regionName}}                                            # Region Name
                app: ${{app}}                                                                     # Deploy Web App

  # Deploy webApp(s) - DR
  - ${{ if ne(parameters.regionNameDR, '') }}:
    - ${{ each app in parameters.webApps }}:
      - deployment: ${{parameters.envName}}_${{parameters.regionNameDR}}_wa
        displayName: 'Web App ${{parameters.regionNameDR}}'
        variables:
        - group: ${{parameters.variableGroupName}}
        - group: ${{parameters.variableGroupNameShared}}
        pool:
          name: ${{parameters.agentPoolName}}
          demands: AZP_REGION -equals ${{parameters.regionName}}
        environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
        strategy:
          runOnce:
            deploy:
              steps:
              - checkout: templates
                fetchDepth: 1    # shallow
                fetchTags: false # dont fetch tags
              - template: Deploy/app-deployweb.yml@templates
                parameters:
                  download: ${{parameters.download}}
                  appId: ${{parameters.appId}}
                  billingCode: ${{parameters.billingCode}}
                  microservice: ${{parameters.microservice}}
                  azureServiceConnection: ${{parameters.azureServiceConnection}}                    # Azure DevOps Service Connection for target Azure subscription
                  envName: ${{parameters.envName}}                                                  # Environment name
                  regionName: ${{parameters.regionNameDR}}                                          # Region Name
                  app: ${{app}}                                                                     # Deploy Web App

  # Deploy funcApp(s)
  - ${{ each app in parameters.funcApps }}:
    - deployment: ${{parameters.envName}}_${{parameters.regionName}}_fa
      displayName: 'Func App ${{parameters.regionName}}'
      variables:
      - group: ${{parameters.variableGroupName}}
      - group: ${{parameters.variableGroupNameShared}}
      pool:
        name: ${{parameters.agentPoolName}}
        demands: AZP_REGION -equals ${{parameters.regionName}}
      environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: templates
              fetchDepth: 1    # shallow
              fetchTags: false # dont fetch tags
            - template: Deploy/app-deployfunc.yml@templates
              parameters:
                download: ${{parameters.download}}
                appId: ${{parameters.appId}}
                billingCode: ${{parameters.billingCode}}
                microservice: ${{parameters.microservice}}
                azureServiceConnection: ${{parameters.azureServiceConnection}}                    # Azure DevOps Service Connection for target Azure subscription
                envName: ${{parameters.envName}}                                                  # Environment name
                regionName: ${{parameters.regionName}}                                            # Region Name
                app: ${{app}}                                                                     # Deploy Func App

  # Deploy funcApp(s) - DR
  - ${{ if ne(parameters.regionNameDR, '') }}:
    - ${{ each app in parameters.funcApps }}:
      - deployment: ${{parameters.envName}}_${{parameters.regionNameDR}}_fa
        displayName: 'Func App ${{parameters.regionNameDR}}'
        variables:
        - group: ${{parameters.variableGroupName}}
        - group: ${{parameters.variableGroupNameShared}}
        pool:
          name: ${{parameters.agentPoolName}}
          demands: AZP_REGION -equals ${{parameters.regionName}}
        environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
        strategy:
          runOnce:
            deploy:
              steps:
              - checkout: templates
                fetchDepth: 1    # shallow
                fetchTags: false # dont fetch tags
              - template: Deploy/app-deployfunc.yml@templates
                parameters:
                  download: ${{parameters.download}}
                  appId: ${{parameters.appId}}
                  billingCode: ${{parameters.billingCode}}
                  microservice: ${{parameters.microservice}}
                  azureServiceConnection: ${{parameters.azureServiceConnection}}                  # Azure DevOps Service Connection for target Azure subscription
                  envName: ${{parameters.envName}}                                                # Environment name
                  regionName: ${{parameters.regionNameDR}}                                        # Region Name
                  app: ${{app}}                                                                   # Deploy Func App

  # Swap webApps(s)
  - ${{ each app in parameters.webApps }}:
    - deployment: ${{parameters.envName}}_${{parameters.regionName}}_swap_wa
      displayName: 'Swap Web ${{parameters.regionName}}'
      dependsOn:
      - ${{parameters.envName}}_ps_db
      - ${{parameters.envName}}_${{parameters.regionName}}_wa
      - ${{parameters.envName}}_${{parameters.regionName}}_fa
      condition: |
        and
        (
          eq(dependencies.${{parameters.envName}}_ps_db.result, 'Succeeded'),
          eq(dependencies.${{parameters.envName}}_${{parameters.regionName}}_wa.result, 'Succeeded'),
          eq(dependencies.${{parameters.envName}}_${{parameters.regionName}}_fa.result, 'Succeeded')
        )
      variables:
      - group: ${{parameters.variableGroupName}}
      - group: ${{parameters.variableGroupNameShared}}
      pool:
        name: ${{parameters.agentPoolName}}
        demands: AZP_REGION -equals ${{parameters.regionName}}
      environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: templates
              fetchDepth: 1    # shallow
              fetchTags: false # dont fetch tags
            - download: none
            - template: Deploy/app-swapweb.yml@templates
              parameters:
                appId: ${{parameters.appId}}
                billingCode: ${{parameters.billingCode}}
                microservice: ${{parameters.microservice}}
                azureServiceConnection: ${{parameters.azureServiceConnection}}                    # Azure DevOps Service Connection for target Azure subscription
                envName: ${{parameters.envName}}                                                  # Environment name
                regionName: ${{parameters.regionName}}                                            # Region Name
                app: ${{app}}                                                                     # Deploy Func App

  # Swap webApps(s) - DR
  - ${{ if ne(parameters.regionNameDR, '') }}:
    - ${{ each app in parameters.webApps }}:
      - deployment: ${{parameters.envName}}_${{parameters.regionNameDR}}_swap_wa
        displayName: 'Swap Web ${{parameters.regionNameDR}}'
        dependsOn:
        - ${{parameters.envName}}_ps_db
        - ${{parameters.envName}}_${{parameters.regionNameDR}}_wa
        - ${{parameters.envName}}_${{parameters.regionNameDR}}_fa
        condition: |
          and
          (
            eq(dependencies.${{parameters.envName}}_ps_db.result, 'Succeeded'),
            eq(dependencies.${{parameters.envName}}_${{parameters.regionNameDR}}_wa.result, 'Succeeded'),
            eq(dependencies.${{parameters.envName}}_${{parameters.regionNameDR}}_fa.result, 'Succeeded')
          )
        variables:
        - group: ${{parameters.variableGroupName}}
        - group: ${{parameters.variableGroupNameShared}}
        pool:
          name: ${{parameters.agentPoolName}}
          demands: AZP_REGION -equals ${{parameters.regionName}}
        environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
        strategy:
          runOnce:
            deploy:
              steps:
              - checkout: templates
                fetchDepth: 1    # shallow
                fetchTags: false # dont fetch tags
              - download: none
              - template: Deploy/app-swapweb.yml@templates
                parameters:
                  appId: ${{parameters.appId}}
                  billingCode: ${{parameters.billingCode}}
                  microservice: ${{parameters.microservice}}
                  azureServiceConnection: ${{parameters.azureServiceConnection}}                  # Azure DevOps Service Connection for target Azure subscription
                  envName: ${{parameters.envName}}                                                # Environment name
                  regionName: ${{parameters.regionNameDR}}                                        # Region Name
                  app: ${{app}}                                                                   # Deploy Func App

  # Swap funcApps(s)
  - ${{ each app in parameters.funcApps }}:
    - deployment: ${{parameters.envName}}_${{parameters.regionName}}_swap_fa
      displayName: 'Swap Func ${{parameters.regionName}}'
      dependsOn:
      - ${{parameters.envName}}_ps_db
      - ${{parameters.envName}}_${{parameters.regionName}}_wa
      - ${{parameters.envName}}_${{parameters.regionName}}_fa
      condition: |
        and
        (
          eq(dependencies.${{parameters.envName}}_ps_db.result, 'Succeeded'),
          eq(dependencies.${{parameters.envName}}_${{parameters.regionName}}_wa.result, 'Succeeded'),
          eq(dependencies.${{parameters.envName}}_${{parameters.regionName}}_fa.result, 'Succeeded')
        )
      variables:
      - group: ${{parameters.variableGroupName}}
      - group: ${{parameters.variableGroupNameShared}}
      pool:
        name: ${{parameters.agentPoolName}}
        demands: AZP_REGION -equals ${{parameters.regionName}}
      environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: templates
              fetchDepth: 1    # shallow
              fetchTags: false # dont fetch tags
            - download: none
            - template: Deploy/app-swapfunc.yml@templates
              parameters:
                appId: ${{parameters.appId}}
                billingCode: ${{parameters.billingCode}}
                microservice: ${{parameters.microservice}}
                azureServiceConnection: ${{parameters.azureServiceConnection}}                    # Azure DevOps Service Connection for target Azure subscription
                envName: ${{parameters.envName}}                                                  # Environment name
                regionName: ${{parameters.regionName}}                                            # Region Name
                app: ${{app}}                                                                     # Deploy Func App

  # Swap funcApps(s) - DR
  - ${{ if ne(parameters.regionNameDR, '') }}:
    - ${{ each app in parameters.funcApps }}:
      - deployment: ${{parameters.envName}}_${{parameters.regionNameDR}}_swap_fa
        displayName: 'Swap Func ${{parameters.regionNameDR}}'
        dependsOn:
        - ${{parameters.envName}}_ps_db
        - ${{parameters.envName}}_${{parameters.regionNameDR}}_wa
        - ${{parameters.envName}}_${{parameters.regionNameDR}}_fa
        condition: |
          and
          (
            eq(dependencies.${{parameters.envName}}_ps_db.result, 'Succeeded'),
            eq(dependencies.${{parameters.envName}}_${{parameters.regionNameDR}}_wa.result, 'Succeeded'),
            eq(dependencies.${{parameters.envName}}_${{parameters.regionNameDR}}_fa.result, 'Succeeded')
          )
        variables:
        - group: ${{parameters.variableGroupName}}
        - group: ${{parameters.variableGroupNameShared}}
        pool:
          name: ${{parameters.agentPoolName}}
          demands: AZP_REGION -equals ${{parameters.regionName}}
        environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
        strategy:
          runOnce:
            deploy:
              steps:
              - checkout: templates
                fetchDepth: 1    # shallow
                fetchTags: false # dont fetch tags
              - download: none
              - template: Deploy/app-swapfunc.yml@templates
                parameters:
                  appId: ${{parameters.appId}}
                  billingCode: ${{parameters.billingCode}}
                  microservice: ${{parameters.microservice}}
                  azureServiceConnection: ${{parameters.azureServiceConnection}}                  # Azure DevOps Service Connection for target Azure subscription
                  envName: ${{parameters.envName}}                                                # Environment name
                  regionName: ${{parameters.regionNameDR}}                                        # Region Name
                  app: ${{app}}                                                                   # Deploy Func App
                  enableIfHealthy: false                                                          # Disable function methods in DR site

# Complete
- ${{ if eq(variables['Build.SourceBranch'], format('refs/heads/{0}',parameters.compareBranch)) }}:
  - job: ${{parameters.envName}}_complete
    displayName: 'Deployment Complete'
    dependsOn:
    - ${{parameters.envName}}_${{parameters.regionName}}_swap_wa
    - ${{parameters.envName}}_${{parameters.regionName}}_swap_fa
    condition: |
      and
      (
        eq(dependencies.${{parameters.envName}}_${{parameters.regionName}}_swap_wa.result, 'Succeeded'),
        eq(dependencies.${{parameters.envName}}_${{parameters.regionName}}_swap_fa.result, 'Succeeded')
      )
    variables:
    - name: SYSTEM_ACCESSTOKEN
      value: $(System.AccessToken)
    - group: ${{parameters.variableGroupName}}
    - group: ${{parameters.variableGroupNameShared}}
    pool:
      name: ${{parameters.agentPoolName}}
      demands: AZP_REGION -equals ${{parameters.regionName}}
    steps:
    - checkout: templates
      fetchDepth: 1    # shallow
      fetchTags: false # dont fetch tags
    - task: AzurePowerShell@5
      displayName: 'PoSh: Send Teams Message'
      inputs:
        pwsh: true
        azureSubscription: ${{parameters.azureServiceConnection}}
        ScriptType: 'FilePath'
        ScriptPath: '$(Build.SourcesDirectory)/PoSh/Send-TeamsMessage.ps1'
        ScriptArguments: '-appName "${{parameters.appName}}" -teamsWebhookUrl $(MSTeamsReleaseChannel) -envName ${{parameters.envName}} -token $(SYSTEM_ACCESSTOKEN)'
        azurePowerShellVersion: 'LatestVersion'
        errorActionPreference:  'SilentlyContinue' # Continue on error
        FailOnStandardError : false                # Continue on error
