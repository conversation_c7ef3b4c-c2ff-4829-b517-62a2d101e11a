/*
Lineage
dbo.PlacementListener.PlacementSystemStatusId=dbo.PlacementSystemStatus.PlacementSystemStatusId
*/
CREATE PROCEDURE APIv1.UpdateSinglePlacementSystemStatus (
    @PlacementId           BIGINT
  , @PlacementSystemId     NVARCHAR(100)
  , @PlacementSystemStatus NVARCHAR(200)
  , @MarkAsSent            BIT
)
AS
DECLARE @UpdatedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementListener';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

/* Note PlacementId is actually PlacementListenerId */
UPDATE PL
SET
    PlacementSystemStatusId = ISNULL(PSS.PlacementSystemStatusId, PL.PlacementSystemStatusId)
  , PlacementSystemId = ISNULL(@PlacementSystemId, PL.PlacementSystemId)
  , IsReadyToSend = CASE WHEN @MarkAsSent = 1
                             THEN 0
                         ELSE PL.IsReadyToSend END
  , LastUpdatedUser = @SprocName
FROM
    dbo.PlacementListener PL
    LEFT JOIN dbo.PlacementSystemStatus PSS
        ON PSS.PlacementSystemStatus = @PlacementSystemStatus
WHERE
    PL.PlacementListenerId = @PlacementId
    AND PL.IsDeleted = 0;

SELECT @UpdatedCount = @@ROWCOUNT;

SET @Action = N'Update ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , NULL
  , @UpdatedCount
  , NULL
  , NULL
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;
