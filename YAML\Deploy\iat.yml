﻿parameters:
  configOnly: false
  reporting: false
  download: current
  variableGroupName: crbbro-ps-preprod
  variableGroupNameShared: crbbro-bkt-preprod
  microservice: ps
  appId: 002757
  billingCode: 'Placement Store'
  envName: iat
  regionName: em20
  regionNameDR: em21
  azureServiceConnection: AKS-CRBBRO-UAT
  aksIdentityName: aks-u-em20-identity
  agentPoolName: Private-CRB-Linux-AKS-U
  resourceGroupRoleAssignment:
  - roleName: Owner                                                         # Azure role
    members:
    - memberName: R-AZC-CRBBRO-PREPROD-RGRP-READER                          # Cloud Group
  - roleName: 'Storage Blob Data Contributor'                               # Storage Blob Data Contributor
    members:
    - memberName: crbbro-ps-##envLetter##-##regionName##-adf                # Data Factory
      type: ServicePrincipal
  - roleName: 'Storage Blob Data Reader'                                    # Storage Account Reader
    members:
    - memberName: R-AZC-CRBBRO-PREPROD-RGRP-READER                          # Cloud Group
      type: Group
  storageAccount:
    create: true                                                            # Create Storage Account
    fileShares:
    - name: datasource-cam-analytics
    rbacAssignments:
    - roleName: 'Storage File Data SMB Share Reader'                        # Azure role
      members:
      - memberName: R-AZC-CRBBRO-PREPROD-RGRP-READER                        # Cloud Group
        type: Group
  appServicePlans:
  - name: crbbro-ps-##envLetter##-##regionName##-asp
    shortName: dedicated
    sku: S1
    kind: app                                                               # app or linux
  keyVault:                                                                 # Create KeyVault
    create: true
    vaultAccess:                                                            # Access list for KeyVault
      - displayName: aks-u-em20-identity
        type: servicePrincipal
        permissions:                                                        # Permissions to enable on the KeyVault
          keys: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore'
          secrets: 'Get,List,Set,Delete,Recover,Backup,Restore'
          certificates: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore,ManageContacts,ManageIssuers,GetIssuers,ListIssuers,SetIssuers,DeleteIssuers'
      - displayName: crbbro-ps-##envLetter##-em20-adf
        type: ServicePrincipal
        permissions:                                                        # Permissions to enable on the KeyVault
          secrets: 'Get,List'                                               
      - displayName: R-AZC-CRBBRO-PREPROD-RGRP-CONTRIBUTOR                  # Cloud Group
        type: Group                                                         
        permissions:                                                        # Permissions to enable on the KeyVault
          secrets: 'Get,List'                                               
  vaultSecrets:                                                             # Secrets to automatically generate in KeyVault (if not already present)
  - name: ServiceAccountPassword--SVC-PSPACT-I                              # Database connection
    type: value                                                             # Secret type
    value: $(ServiceAccountPassword-SVC-PSPACT-I)
  virtualNetwork:
    resourceGroupName1: crbbro-bkt-##envLetter##-##regionName##-rgrp
    virtualNetworkName1: crbbro-bkt-##envLetter##-##regionName##-vnet
    resourceGroupName2: ''
    virtualNetworkName2: ''
  webApps:
  - project: PsWeb                                                          # Name of project containing Web application
    dotnetVersion: '6.0'                                                    # DotNet Framework Version
    appServicePlan: crbbro-ps-##envLetter##-##regionName##-asp              # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##envLetter##-##regionName##-subn1                # Subnet name to connect Web application
    clientAffinityEnabled: false
    use32BitWorkerProcess: false
    autoHealEnabled: true
    artifactName: WebApp                                                    # Name of published artefact from build stage
    healthCheckPath: '/api/HealthCheck'                                     # Path of health check api to call to check health during deployment
    appSettingsName: webAppSettings                                         # Artefact name with common app settings
    overrideAppSettings:                                                    # Environment specific overrides for app.settings
    - name: AzureAd__ClientId
      value: 35ae11d4-0d36-40af-9316-7ef58a1628d0
    - name: AzureAd__Scope
      value: api://35ae11d4-0d36-40af-9316-7ef58a1628d0/.default
    - name: PlacementStore__Mappings__0__ApplicationId
      value: 96e9be59-82c1-4d85-8705-9615e2914e87                           # PSA.Client.DevTest
    - name: PlacementStore__Mappings__0__SharedKeys__0
      value: D1C69217-47C9-490D-A7D2-5C0B6BB7066C                           # 50000
    - name: PlacementStore__Mappings__1__ApplicationId
      value: bcd61622-981e-4f97-8689-10cfcd4896f1                           # PSA.Client.COL-PowerApps
    - name: PlacementStore__Mappings__1__SharedKeys__0
      value: F4560FAD-00BC-431D-97D3-9E0705FB46C6                           # 50003
    - name: PlacementStore__Mappings__2__ApplicationId
      value: b94fd0ce-008f-448a-90fc-c576f5937dec                           # PSA.Client.Eclipse-PowerApps
    - name: PlacementStore__Mappings__2__SharedKeys__0
      value: D1C69217-47C9-490D-A7D2-5C0B6BB7066C                           # 50000
    - name: PlacementStore__Mappings__3__ApplicationId
      value: 3b1cf3b6-5f9f-4fca-931a-2ae26227fe25                           # PSA.Client.Broking-net-PowerApps
    - name: PlacementStore__Mappings__3__SharedKeys__0
      value: 477D97FB-BF43-4F1D-8923-43A6D684F6B2                           # 50358
    - name: PlacementStore__Mappings__4__ApplicationId
      value: c40ce2e4-dbd5-4e77-8db2-5ecf2a3af0d0                           # PSA.Client.Epic-US-PowerApps
    - name: PlacementStore__Mappings__4__SharedKeys__0
      value: 4CD4A178-6C96-4073-BE07-81F2A0855AB1                           # 50001
    - name: PlacementStore__Mappings__5__ApplicationId
      value: 7cca3417-7096-4cb2-b4cb-dcb94b9749aa                           # PSA.Client.CPT
    - name: PlacementStore__Mappings__5__SharedKeys__0
      value: 4CD4A178-6C96-4073-BE07-81F2A0855AB1                           # 50001
    - name: PlacementStore__Mappings__6__ApplicationId
      value: 7575f1d9-9b73-4af8-8793-a2cce1e23504                           # PSA.Client.CGW
    - name: PlacementStore__Mappings__7__ApplicationId
      value: 8d521895-919c-4fa4-a801-3d66bf7f2465                           # PSA.Client.Eglobal-Australia-PowerApps
    - name: PlacementStore__Mappings__7__SharedKeys__0
      value: 0DDB0B09-E632-430F-9CB8-42D71DC4141B                           # 50004
    - name: PlacementStore__Mappings__8__ApplicationId
      value: 7a3750ed-3d72-40cc-b924-177b47ceea80                           # PSA.Client.BPA
    - name: PlacementStore__Mappings__9__ApplicationId
      value: 8a870c9e-7e9d-4f00-a177-6e92adda4191                           # PSA.Client.WIBS (Italy)
    - name: PlacementStore__Mappings__9__SharedKeys__0
      value: 11B1E79D-96F1-4DFB-BA25-88BE30F77436                           # 50045
    - name: PlacementStore__Mappings__10__ApplicationId
      value: 9ce4dd24-2568-45a9-9437-4f0728df8ba5                           # PSA.Client.ASYS-Germany
    - name: PlacementStore__Mappings__10__SharedKeys__0
      value: 016BFED3-F568-4BAC-974F-A6F76509AC7A                           # 50029
    - name: PlacementStore__Mappings__11__ApplicationId
      value: 939bdebf-fcad-4879-b084-1084cd769b77                           # PSA.Client.Gras-Savoye-EGS (France)
    - name: PlacementStore__Mappings__11__SharedKeys__0
      value: BD4EEA26-4D78-4EB1-B0C1-C31E9512D4C5                           # 50364
    - name: PlacementStore__Mappings__12__ApplicationId
      value: 9d3284b5-3de8-4295-8723-09249b279d41                           # PSA.Client.VisualSeg-Spain
    - name: PlacementStore__Mappings__12__SharedKeys__0
      value: 956E008C-0154-45DC-8AD9-3A2F50AFF4B7                           # 50044
    - name: PlacementStore__Mappings__13__ApplicationId
      value: e30ed984-9565-4232-9778-0bf8e307a34b                           # PSA.Client.eGlobal-Netherlands
    - name: PlacementStore__Mappings__13__SharedKeys__0
      value: 74048E3C-464B-4CC7-A9EE-8CC8D4D49FBA                           # 50010
    - name: PlacementStore__Mappings__14__ApplicationId
      value: 5c559541-08ed-4ffa-beb8-212231bfa9b7                           # PSA.Client.eGlobal-HongKong
    - name: PlacementStore__Mappings__14__SharedKeys__0
      value: CA536DB5-ECEC-4E50-B742-5B27E44F51CF                           # 50007
    - name: PlacementStore__Mappings__15__ApplicationId
      value: ec64c6d4-b8e4-4005-a498-7b9564572a24                           # PSA.Client.BisCore
    - name: PlacementStore__Mappings__15__SharedKeys__0
      value: B333245C-D5AE-47EE-B529-0B7EC238BC1A                           # 50500
    - name: PlacementStore__Mappings__16__ApplicationId
      value: f7dcb086-0acd-4ebf-b911-a389daad2379                           # PSA.Client.eGlobal-South-Africa
    - name: PlacementStore__Mappings__16__SharedKeys__0
      value: 18C94857-C0CB-4A8C-AF88-CD98DE987730                           # 50015
    - name: PlacementStore__Mappings__17__ApplicationId
      value: e0de8fca-064a-4d8a-8d4f-080d69121fca                           # PSA.Client.Reference-Data
    - name: PlacementStore__Mappings__17__SharedKeys__0
      value: 78CE4793-4469-4613-BC16-1B6C5ADB78D8                           # 50355
    - name: PlacementStore__Mappings__18__ApplicationId
      value: a73bb145-0552-4f75-9b90-40901aab8647                           # PSA.Client.Carrier-Gateway
    - name: PlacementStore__Mappings__19__ApplicationId
      value: 1c65bbf2-1625-485b-b6de-057db41e71a7                           # PSA.Client.SegElevia-Portugal
    - name: PlacementStore__Mappings__19__SharedKeys__0
      value: 7CD5D949-EE5C-4866-8FAD-A5E625F5EF49                           # 50041
    - name: PlacementStore__Mappings__20__ApplicationId
      value: 3669797f-2f5e-465f-a9b9-9468cc194272                           # PSA.Client.eGlobal-China
    - name: PlacementStore__Mappings__20__SharedKeys__0
      value: 328905E0-BC8F-4729-BB20-B2D97B1FC3DF                           # 50006
  funcApps:
  - project: PsFunc                                                         # Name of project containing Function application
    dotnetVersion: 'v8.0'                                                   # DotNet Framework Version
    artifactName: FuncApp                                                   # Name of published artefact from build stage
    appServicePlan: crbbro-ps-##envLetter##-##regionName##-asp              # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##envLetter##-##regionName##-subn1                # Subnet name to connect Web application
    healthCheckPath: '/api/HealthCheck'                                     # Path of Http Trigger Function to call to check health during deployment
    disabledFunctions:                                                      # Functions that should remain disabled
    - name: HealthCheckExt                                                  # Extended healthcheck for manual run only
    - name: PlacementCompleted                                              # CRB EventHub integration not supported in feature branch
    - name: PlacementEvents                                                 # PlacementEvents supported in feature branch
    - name: RefreshAnalysisServer                                           # RefreshAnalysisServer supported in feature branch
    appSettingsName: funcAppSettings                                        # Artefact name with common app settings
    overrideAppSettings:                                                    # Environment specific overrides for app.settings
    - name: ConnectionStrings__BKPDatabase
      value: "Server=crbbro-dvo-u-em20-sql.database.windows.net;Database=crbbro-bkp-m-em20-db;Authentication=Active Directory Default"
  sqlDatabases:
  - project: PlacementStoreDb.Build                                         # Project containing database
    name: crbbro-ps-##envLetter##-##regionName##-db
    shortName: ps
    useManagedIdentity: true                                                # Using Microsoft.Data.SqlClient instead of System.Data.SqlClient to allow automatic token authentication
    vaultSecretName: ConnectionStrings--Database
    source:
      resourceGroupName: crbbro-dvo-p-em20-rgrp
      sqlServerName: crbbro-dvo-p-em20-sql
      databaseName: crbbro-ps-p-em20-db
      elasticPoolName: crbbro-dvo-p-em-epool-ps
      sleepAfterDropSecs: 30
      azureServiceConnection: WTW-CRBBRO-PROD
    dest:                                                                   # SQL Server / Elastic Pool which will host the database (to manage Azure costs)
      resourceGroupName: crbbro-dvo-u-em20-rgrp
      sqlServerName: crbbro-dvo-u-em20-sql
      elasticPoolName: crbbro-dvo-u-em20-epool-ps
      scaleSource:                                                          # Apply scale up settings from this source to destination
        azureServiceConnection: WTW-CRBBRO-PROD
        sqlServerName: crbbro-dvo-p-em20-sql
        elasticPoolName: crbbro-dvo-p-em-epool-ps
        resourceGroupName: crbbro-dvo-p-em20-rgrp
    artifactName: PlacementStore.Database                                   # Name of published artefact from build stage
    roleMembers:
    - roleName: ETL_Role                                                    # Database role
      members:
      - memberName: crbbro-ps-##envLetter##-##regionName##-fa               # PS Func App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-fa/slots/staging # PS Staging Func App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-adf              # PS ADF
        type: ServicePrincipal
      - memberName: <EMAIL>               # PS Service Account
        type: User
      - memberName: <EMAIL>                     # PS Service Account
        type: User
    - roleName: PlacementStoreAPIConsumerRole                                 # Database role
      members:
      - memberName: crbbro-ps-##envLetter##-##regionName##-wa               # PS Web App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-wa/slots/staging # PS Staging Web App
        type: ServicePrincipal
  dataFactory:
    rbacAssignments:                                                        # RBAC assignments for Data Factory
    - roleName: 'Data Factory ReadOnly'                                     # Azure role
      members:
      - memberName: R-AZC-CRBBRO-DATAENGINEER                               # Cloud Group
        type: Group
    - roleName: 'Data Factory Contributor'                                  # Azure role
      members:
      - memberName: crbbro-ps-##envLetter##-##regionName##-adf              # Data Factory
        type: servicePrincipal
    create: true
    subscriptionId: f388d80c-59db-4f8f-b31f-bdd5d3195716
    artefacts: PS.ADF
    psKeyVaultUrl: 'https://crbbro-ps-i-em20-kv.vault.azure.net/'
    selfHostedIntegrationRuntimeResourceId: '/subscriptions/f388d80c-59db-4f8f-b31f-bdd5d3195716/resourcegroups/crbbro-bkt-u-em20-rgrp/providers/Microsoft.DataFactory/factories/crbbro-bkt-u-em20-adf/integrationruntimes/SharedSelfHostedIntegrationRuntime'
    mpeSqlServerResourceId: '/subscriptions/f388d80c-59db-4f8f-b31f-bdd5d3195716/resourceGroups/crbbro-dvo-u-em20-rgrp/providers/Microsoft.Sql/servers/crbbro-dvo-u-em20-sql'  # SQL Server Managed Private End point
    mpePactServerResourceId: '/subscriptions/92ae2673-1f32-4529-aeb0-197f7b14d0f7/resourceGroups/PAS-U-EM22-RGRP/providers/Microsoft.Synapse/workspaces/pas-u-em22-saws'       # PACT Server Managed Private End point
    mpeWillisReferenceServerResourceId: '/subscriptions/103aaa2d-b0cd-43f8-86df-a12b6f9340b6/resourceGroups/DPSERD-SQL-EM20-I-RGRP/providers/Microsoft.Sql/managedInstances/e20-eds-erdmd1i'        # Willis Reference Server Managed Private End point
    mpeCrbDataLakeResourceId: '/subscriptions/5c432ba7-a72c-4510-93aa-166f8fc865fe/resourceGroups/CRB-DATA-T-NA20-RGRP/providers/Microsoft.Storage/storageAccounts/crbdatatna20sa'        # CRB data lake Managed Private End point
    triggers:
    - Name : 'Every3Hours'
      enabled: false
    - Name: 'Every24Hours'
      enabled: false
    - Name: 'SetPactLoad'
      enabled: false
stages:
- stage: ${{parameters.envName}}
  dependsOn:
  - start
  jobs:
  - deployment: approve
    displayName: 'Approve ${{parameters.envName}}'
    pool: server
    environment: y-deploy-crbbro-${{parameters.microservice}}-${{parameters.envName}}

  - template: deploy_infra.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      resourceGroupRoleAssignment: ${{parameters.resourceGroupRoleAssignment}}
      virtualNetwork: ${{parameters.virtualNetwork}}
      storageAccount: ${{parameters.storageAccount}}
      keyVault: ${{parameters.keyVault}}
      vaultSecrets: ${{parameters.vaultSecrets}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      appServicePlans: ${{parameters.appServicePlans}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      dataFactory: ${{parameters.dataFactory}}

- stage: ${{parameters.envName}}_apps
  ${{ if or(eq(parameters.configOnly,'true'),eq(parameters.reporting,'true')) }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
  ${{ else }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
    - build_apps
  variables:
  - name: virtualNetworkResourceGroupName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkResourceGroupName'] ]
  - name: virtualNetworkName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkName'] ]
  - name: appSubnetNamesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['appSubnets.appSubnetNamesCSV'] ]
  - name: appIdentitiesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['identities.appIdentitiesCSV'] ]
  - name: dataFactoryName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_dedicated.outputs['dataFactory.dataFactoryName'] ]
  - name: dataFactoryresourceGroupName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_dedicated.outputs['dataFactory.resourceGroupName'] ]

  jobs:
  - template: deploy_apps.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      etl: ${{parameters.etl}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      virtualNetworkResourceGroupName: $(virtualNetworkResourceGroupName)
      virtualNetworkName: $(virtualNetworkName)
      appSubnetNamesCSV: $(appSubnetNamesCSV)
      appIdentitiesCSV: $(appIdentitiesCSV)
      dataFactory: ${{parameters.dataFactory}}
      dataFactoryName: $(dataFactoryName)
      dataFactoryResourceGroupName: $(dataFactoryResourceGroupName)

- stage: ${{parameters.envName}}_scale_down
  condition: succeededOrFailed()
  dependsOn: ${{parameters.envName}}_apps
  jobs:
  - deployment: approve
    displayName: 'Approve ${{parameters.envName}}'
    pool: server
    environment: y-deploy-crbbro-${{parameters.microservice}}-${{parameters.envName}}
  - template: scale_down.yml
    parameters:
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      sqlDatabases: ${{parameters.sqlDatabases}}