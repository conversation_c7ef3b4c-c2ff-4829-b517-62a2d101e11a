/*
Lineage
PS.PlacementRuleValidation.PlacementRuleValidationId=BPStaging.PlacementRuleValidation.Id
PS.PlacementRuleValidation.PlacementId=dbo.Placement.PlacementId
PS.PlacementRuleValidation.ActionValidationRuleId=PS.ActionValidationRule.ActionValidationRuleId
*/
CREATE PROCEDURE BPStaging.Load_PS_PlacementRuleValidation
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.PlacementRuleValidation';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.PlacementRuleValidation T
    USING (
        SELECT
            PlacementRuleValidationId = prv.Id
          , pl.PlacementId
          , avr.ActionValidationRuleId
          , DataSourceInstanceId = 50366
          , IsDeleted = 0
        FROM
            BPStaging.PlacementRuleValidation prv
            INNER JOIN dbo.Placement pl
                ON pl.PlacementSystemId = prv.PlacementId
                   AND pl.DataSourceInstanceId = 50366

            LEFT OUTER JOIN PS.ActionValidationRule avr
                ON avr.ActionValidationRuleKey = CONCAT('AVR|', prv.ActionValidationRuleId)
                   AND avr.DataSourceInstanceId = 50366
    ) S
    ON T.PlacementRuleValidationId = S.PlacementRuleValidationId
    WHEN NOT MATCHED
        THEN INSERT (
                 PlacementRuleValidationId
               , PlacementId
               , ActionValidationRuleId
               , DataSourceInstanceId
               , IsDeleted
             )
             VALUES
                 (
                     S.PlacementRuleValidationId
                   , S.PlacementId
                   , S.ActionValidationRuleId
                   , S.DataSourceInstanceId
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.PlacementRuleValidationId
                               , T.PlacementId
                               , T.ActionValidationRuleId
                               , T.DataSourceInstanceId
                               , T.IsDeleted
                             INTERSECT
                             SELECT
                                 S.PlacementRuleValidationId
                               , S.PlacementId
                               , S.ActionValidationRuleId
                               , S.DataSourceInstanceId
                               , S.IsDeleted
                         )
        THEN UPDATE SET
                 T.PlacementId = S.PlacementId
               , T.ActionValidationRuleId = S.ActionValidationRuleId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);