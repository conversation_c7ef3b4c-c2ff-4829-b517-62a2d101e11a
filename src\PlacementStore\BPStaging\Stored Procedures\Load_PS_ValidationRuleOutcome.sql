/*
Lineage
PS.ValidationRuleOutcome.ValidationRuleOutcomeId=BPStaging.ValidationRuleOutcome.Id
PS.ValidationRuleOutcome.PlacementRuleValidationId=BPStaging.ValidationRuleOutcome.PlacementRuleValidationId
PS.ValidationRuleOutcome.JustificationReasonId=BPStaging.ValidationRuleOutcome.JustificationReasonId
PS.ValidationRuleOutcome.ApprovalDate=BPStaging.ValidationRuleOutcome.ApprovalDate
PS.ValidationRuleOutcome.SourceUpdatedDate=BPStaging.ValidationRuleOutcome.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_PS_ValidationRuleOutcome
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ValidationRuleOutcome';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.ValidationRuleOutcome T
    USING (
        SELECT
            ValidationRuleOutcomeId = Id
          , PlacementRuleValidationId
          , JustificationReasonId
          , ApprovalDate
          , DataSourceInstanceId = 50366
          , IsDeleted = 0
          , SourceUpdatedDate = ValidFrom
        FROM
            BPStaging.ValidationRuleOutcome
    ) S
    ON T.ValidationRuleOutcomeId = S.ValidationRuleOutcomeId
    WHEN NOT MATCHED
        THEN INSERT (
                 ValidationRuleOutcomeId
               , PlacementRuleValidationId
               , JustificationReasonId
               , ApprovalDate
               , DataSourceInstanceId
               , IsDeleted
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.ValidationRuleOutcomeId
                   , S.PlacementRuleValidationId
                   , S.JustificationReasonId
                   , S.ApprovalDate
                   , S.DataSourceInstanceId
                   , S.IsDeleted
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.ValidationRuleOutcomeId
                               , T.PlacementRuleValidationId
                               , T.JustificationReasonId
                               , T.DataSourceInstanceId
                               , T.ApprovalDate
                               , T.IsDeleted
                               , T.SourceUpdatedDate
                             INTERSECT
                             SELECT
                                 S.ValidationRuleOutcomeId
                               , S.PlacementRuleValidationId
                               , S.JustificationReasonId
                               , S.DataSourceInstanceId
                               , S.ApprovalDate
                               , S.IsDeleted
                               , S.SourceUpdatedDate
                         )
        THEN UPDATE SET
                 T.PlacementRuleValidationId = S.PlacementRuleValidationId
               , T.JustificationReasonId = S.JustificationReasonId
               , T.ApprovalDate = S.ApprovalDate
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);