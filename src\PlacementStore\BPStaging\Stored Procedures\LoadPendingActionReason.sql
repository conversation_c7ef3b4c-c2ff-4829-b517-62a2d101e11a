/*
Lineage
ref.PendingActionReason.PendingActionReasonKey=BPStaging.PendingActionReason.Id
ref.PendingActionReason.PendingActionReason=BPStaging.PendingActionReason.Text
ref.PendingActionReason.SourceUpdatedDate=BPStaging.PendingActionReason.ValidFrom
ref.PendingActionReason.IsDeprecated=BPStaging.PendingActionReason.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadPendingActionReason
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.PendingActionReason';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.PendingActionReason T
    USING (
        SELECT
            PendingActionReasonId = Id
          , DataSourceInstanceId = 50366
          , PendingActionReasonKey = Id
          , PendingActionReason = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.PendingActionReason
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.PendingActionReasonKey = S.PendingActionReasonKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , PendingActionReasonKey
               , PendingActionReason
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.PendingActionReasonKey
                   , S.PendingActionReason
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.PendingActionReasonKey
                               , T.PendingActionReason
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.PendingActionReasonKey
                               , S.PendingActionReason
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PendingActionReasonKey = S.PendingActionReasonKey
               , T.PendingActionReason = S.PendingActionReason
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);