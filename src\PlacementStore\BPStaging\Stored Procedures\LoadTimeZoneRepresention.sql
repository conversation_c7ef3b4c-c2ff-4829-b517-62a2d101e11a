/*
Lineage
ref.TimeZoneRepresention.TimeZoneRepresentionId=BPStaging.TimeZoneRepresention.Id
ref.TimeZoneRepresention.TimeZoneRepresentionKey=BPStaging.TimeZoneRepresention.LabelTranslationKey
ref.TimeZoneRepresention.TimeZoneRepresention=BPStaging.TimeZoneRepresention.Text
ref.TimeZoneRepresention.SourceUpdatedDate=BPStaging.TimeZoneRepresention.ValidFrom
ref.TimeZoneRepresention.IsDeprecated=BPStaging.TimeZoneRepresention.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadTimeZoneRepresention
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.TimeZoneRepresention';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.TimeZoneRepresention T
    USING (
        SELECT
            TimeZoneRepresentionId = Id
          , DataSourceInstanceId = 50366
          , TimeZoneRepresentionKey = LabelTranslationKey
          , TimeZoneRepresention = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.TimeZoneRepresention
    ) S
    ON T.TimeZoneRepresentionId = S.TimeZoneRepresentionId
    WHEN NOT MATCHED
        THEN INSERT (
                 TimeZoneRepresentionId
               , DataSourceInstanceId
               , TimeZoneRepresentionKey
               , TimeZoneRepresention
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.TimeZoneRepresentionId
                   , S.DataSourceInstanceId
                   , S.TimeZoneRepresentionKey
                   , S.TimeZoneRepresention
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.TimeZoneRepresentionId
                               , T.DataSourceInstanceId
                               , T.TimeZoneRepresentionKey
                               , T.TimeZoneRepresention
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.TimeZoneRepresentionId
                               , S.DataSourceInstanceId
                               , S.TimeZoneRepresentionKey
                               , S.TimeZoneRepresention
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.TimeZoneRepresentionId = S.TimeZoneRepresentionId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.TimeZoneRepresentionKey = S.TimeZoneRepresentionKey
               , T.TimeZoneRepresention = S.TimeZoneRepresention
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
