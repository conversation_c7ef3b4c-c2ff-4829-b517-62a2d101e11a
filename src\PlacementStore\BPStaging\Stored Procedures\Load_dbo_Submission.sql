/*
Lineage
dbo.Submission.SubmissionId=BP.Submission.Id
dbo.Submission.SubmissionContainerId=BP.Submission.SubmissionContainerId
dbo.Submission.SubmissionSent=BP.Submission.Sent
dbo.Submission.UserId=BP.Submission.UserId
dbo.Submission.MessageHtml=BP.Submission.MessageHtml
dbo.Submission.NegotiationId=PS.Negotiation.NegotiationId
*/
CREATE PROCEDURE BPStaging.Load_dbo_Submission
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.Submission';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BP.Submission
        );

BEGIN TRY
    IF @CheckCount <> 0
    BEGIN
        MERGE dbo.Submission T
        USING (
            SELECT
                SubmissionId = sub.Id
              , sub.SubmissionContainerId
              , SubmissionSent = sub.Sent
              , DataSourceInstanceId = 50366
              , sub.UserId
              , sub.MessageHtml
              , n.NegotiationId
            FROM
                BP.Submission sub
                INNER JOIN PS.Negotiation n
                    ON n.NegotiationKey = CONCAT('SUBC|', sub.SubmissionContainerId)
                       AND n.DataSourceInstanceId = 50366
        ) S
        ON S.SubmissionId = T.SubmissionId
        WHEN NOT MATCHED BY TARGET
            THEN INSERT (
                     SubmissionId
                   , SubmissionContainerId
                   , SubmissionSent
                   , DataSourceInstanceId
                   , UserId
                   , MessageHtml
                   , NegotiationId
                 )
                 VALUES
                     (
                         S.SubmissionId
                       , S.SubmissionContainerId
                       , S.SubmissionSent
                       , S.DataSourceInstanceId
                       , S.UserId
                       , S.MessageHtml
                       , S.NegotiationId
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT
            S.SubmissionContainerId
          , S.SubmissionSent
          , S.DataSourceInstanceId
          , S.UserId
          , S.MessageHtml
          , S.NegotiationId
        INTERSECT
        SELECT
            T.SubmissionContainerId
          , T.SubmissionSent
          , T.DataSourceInstanceId
          , T.UserId
          , T.MessageHtml
          , T.NegotiationId
    )
            THEN UPDATE SET
                     T.SubmissionContainerId = S.SubmissionContainerId
                   , T.SubmissionSent = S.SubmissionSent
                   , T.DataSourceInstanceId = S.DataSourceInstanceId
                   , T.UserId = S.UserId
                   , T.MessageHtml = S.MessageHtml
                   , T.NegotiationId = S.NegotiationId
        WHEN NOT MATCHED BY SOURCE
            THEN DELETE
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;