/*
Lineage
PlacementId=dbo.Placement.PlacementId
TeamID=dbo.PlacementTeams.TeamId
TeamID=ref.Team.TeamId
LastUpdatedUTCDate=dbo.PlacementListener.ValidFrom
LastUpdatedUTCDate=dbo.Placement.LastUpdatedUTCDate
LastUpdatedUTCDate=dbo.Placement.CreatedUTCDate
RenewalProcessStartDate=dbo.PlacementListener.RenewalProcessStartDate
ExpiryDate=dbo.Policy.ExpiryDate
PlacementDataSourceInstanceID=dbo.PlacementListener.PlacementDataSourceInstanceId
TeamName=ref.Team.TeamName
IsReadyToSend=dbo.PlacementListener.IsReadyToSend
*/
CREATE VIEW APIv1.RenewalPlacementTeam
AS

WITH PlacementPolicyTeam AS (
    SELECT DISTINCT
           PL.PlacementId
         , PlacementSystemId = CASE WHEN PL.DataSourceInstanceId = 50366
                                        THEN CAST(PL.PlacementSystemId AS NVARCHAR(100))
                                    ELSE NULL END
         , PL.ServicingPlatformId
         , PPPO.PolicyId
         , PPPO.PolicyReference
         , PolicyExpiryDate = PPPO.ExpiryDate
         , ExpiringPlacementTeamId = PT.TeamId
         , ExpiringPlacementTeamName = T.TeamName
         , PolicyRuleTeamId = COALESCE(PPRT.TeamId, RT.TeamId)
         , PolicyRuleTeamName = COALESCE(PPRT.TeamName, RT.TeamName)
         , RuleOrder = COALESCE(PLIR.[Order], PPR.[Order], R.[Order])
         , TeamId = CASE WHEN R.UsePlacementValueYr2 = 1
                              OR PLIR.UsePlacementValueYr2 = 1
                             THEN COALESCE(PT.TeamId, RT.TeamId) --Prefer previous placement
                         ELSE COALESCE(PPRT.TeamId, RT.TeamId, PT.TeamId) --Prefer policy rule 
                    END
         , TeamName = CASE WHEN R.UsePlacementValueYr2 = 1
                                OR PLIR.UsePlacementValueYr2 = 1
                               THEN COALESCE(T.TeamName, RT.TeamName)
                           ELSE COALESCE(PPRT.TeamName, RT.TeamName, T.TeamName) END
         , LastUpdatedUTCDate = (
               SELECT CAST(MAX(v) AS DATETIME2)
               FROM (
                   VALUES (
                       PLI.ValidFrom
                   )
                        , (
                       PL.LastUpdatedUTCDate
                   )
                        , (
                       PL.CreatedUTCDate
                   )
               ) value (v)
           )
         , PLI.RenewalProcessStartDate
         , PlacementExpiryDate = PL.ExpiryDate
         , PLI.PlacementDataSourceInstanceId
         , TeamMatch = CASE WHEN COALESCE(PPRT.TeamId, RT.TeamId) = PT.TeamId
                                THEN 1
                            ELSE 0 END
         , PLI.IsReadyToSend
    FROM
        dbo.Placement PL
        LEFT JOIN dbo.Placement RTPL
            ON RTPL.RenewedFromPlacementId = PL.PlacementId

        INNER JOIN dbo.PlacementListener PLI
            ON PLI.PlacementId = PL.PlacementId
               AND PLI.PlacementDataSourceInstanceId = 50366
               AND PLI.IsDeleted = 0

        LEFT JOIN PactConfig.[Rule] PLIR --Placement Listener Rule
            ON PLIR.RuleId = PLI.ListenerRuleId
               AND PLIR.IsDeleted = 0

        LEFT JOIN dbo.PlacementTeams PT
            ON PT.PlacementId = PL.PlacementId
               AND PT.IsDeleted = 0

        LEFT JOIN ref.Team T
            ON T.TeamId = PT.TeamId

        LEFT JOIN dbo.PlacementPolicy PP
            ON PP.PlacementId = PL.PlacementId
               AND PP.PlacementPolicyRelationshipTypeId = 1 --Current
               AND PP.IsDeleted = 0

        LEFT JOIN dbo.Policy PPPO
            ON PPPO.PolicyId = PP.PolicyId

        LEFT JOIN PactConfig.[Rule] PPR
            ON PPR.RuleId = PPPO.RuleId
               AND PPR.IsDeleted = 0

        LEFT JOIN ref.Team PPRT
            ON PPRT.TeamId = PPR.TeamId
               AND PPRT.IsDeprecated = 0

        LEFT JOIN PactConfig.[Rule] R
            ON R.RuleId = PPPO.RuleId

        LEFT JOIN ref.Team RT
            ON RT.TeamId = R.TeamId
    WHERE
        PL.IsDeleted = 0
)
SELECT
    PlacementId
  , TeamID = TeamId
  , LastUpdatedUTCDate
  , RenewalProcessStartDate
  , ExpiryDate = CalcPlacementExpiryDate
  , PlacementDataSourceInstanceID = PlacementDataSourceInstanceId
  , TeamName
  , IsReadyToSend
FROM (
    SELECT
        PPT.PlacementId
      , PPT.TeamId
      , PPT.TeamName
      , PPT.LastUpdatedUTCDate
      , PPT.RenewalProcessStartDate
      , PPT.PlacementDataSourceInstanceId
      , PPT.IsReadyToSend
      , PE.CalcPlacementExpiryDate
      , ROW_NO = ROW_NUMBER() OVER (PARTITION BY PPT.PlacementId
                                    ORDER BY
                                        PPT.TeamMatch DESC
                                      , TC.TeamCount DESC
                                      , PPT.PolicyExpiryDate ASC
                                      , PPT.RuleOrder ASC
                              )
    FROM
        PlacementPolicyTeam PPT
        LEFT JOIN (
            SELECT
                PlacementId
              , CalcPlacementExpiryDate = MIN(PolicyExpiryDate)
            FROM
                PlacementPolicyTeam
            GROUP BY
                PlacementId
        ) PE
            ON PE.PlacementId = PPT.PlacementId

        LEFT JOIN (SELECT PlacementId, TeamId, TeamCount = COUNT(*) FROM PlacementPolicyTeam GROUP BY PlacementId, TeamId) TC
            ON TC.PlacementId = PPT.PlacementId
               AND TC.TeamId = PPT.TeamId
    WHERE
        PPT.TeamId IS NOT NULL
) A
WHERE
    ROW_NO = 1;
