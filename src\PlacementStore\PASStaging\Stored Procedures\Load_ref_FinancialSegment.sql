/*
Lineage
ref.FinancialSegment.DataSourceInstanceId=PASStaging.rpt_vwFinancialSegment.DataSourceInstanceId
ref.FinancialSegment.FinancialSegmentKey=PASStaging.rpt_vwFinancialSegment.FinancialSegmentKey
ref.FinancialSegment.FinancialSegment=PASStaging.rpt_vwFinancialSegment.FinancialSegment
ref.FinancialSegment.SourceQuery=PASStaging.rpt_vwFinancialSegment.SourceQuery
ref.FinancialSegment.SourceKey=PASStaging.rpt_vwFinancialSegment.SourceKey
ref.FinancialSegment.GlobalFinancialSegmentId=PASStaging.rpt_vwFinancialSegment.GlobalFinancialSegmentId
ref.FinancialSegment.FinancialSegmentCode=PASStaging.rpt_vwFinancialSegment.FinancialSegmentCode
ref.FinancialSegment.FinancialSegmentDescription=PASStaging.rpt_vwFinancialSegment.FinancialSegmentDescription
ref.FinancialSegment.Level1=PASStaging.rpt_vwFinancialSegment.Level1
ref.FinancialSegment.Level2=PASStaging.rpt_vwFinancialSegment.Level2
ref.FinancialSegment.Level3=PASStaging.rpt_vwFinancialSegment.Level3
ref.FinancialSegment.Level4=PASStaging.rpt_vwFinancialSegment.Level4
ref.FinancialSegment.Level5=PASStaging.rpt_vwFinancialSegment.Level5
ref.FinancialSegment.IsDeprecated=PASStaging.rpt_vwFinancialSegment.IsDeleted
ref.FinancialSegment.PASFinancialSegmentId=PASStaging.rpt_vwFinancialSegment.FinancialSegmentId
ref.FinancialSegment.FinancialStructureId=ref.FinancialStructureAttribute.FinancialStructureId
ref.FinancialSegment.SourceUpdatedDate=PASStaging.rpt_vwFinancialSegment.ETLUpdatedDate
*/

CREATE PROCEDURE PASStaging.Load_ref_FinancialSegment
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.FinancialSegment';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    -- Only process is something staged.
    IF (EXISTS (SELECT 1 FROM PASStaging.rpt_vwFinancialSegment))
    BEGIN
        MERGE ref.FinancialSegment t
        USING (
            SELECT
                DataSourceInstanceId = fs.DataSourceInstanceId
              , fs.FinancialSegmentKey
              , fs.FinancialSegment
              , fs.SourceQuery
              , fs.SourceKey
              , fs.GlobalFinancialSegmentId
              , fs.FinancialSegmentCode
              , fs.FinancialSegmentDescription
              , fs.Level1
              , fs.Level2
              , fs.Level3
              , fs.Level4
              , fs.Level5
              , fs.IsDeleted
              , PASFinancialSegmentId = fs.FinancialSegmentId
              , FinancialStructureId = fsa.FinancialStructureId
              , SourceUpdatedDate = fs.ETLUpdatedDate
            FROM
                PASStaging.rpt_vwFinancialSegment fs
                LEFT JOIN ref.FinancialStructureAttribute fsa
                    ON fs.FinancialSegmentId = fsa.PACTFinancialStructureId
        ) s
        ON t.PASFinancialSegmentId = s.PASFinancialSegmentId
        WHEN NOT MATCHED
            THEN INSERT (
                     DataSourceInstanceId
                   , FinancialSegmentKey
                   , FinancialSegment
                   , SourceQuery
                   , SourceKey
                   , GlobalFinancialSegmentId
                   , FinancialSegmentCode
                   , FinancialSegmentDescription
                   , Level1
                   , Level2
                   , Level3
                   , Level4
                   , Level5
                   , ETLCreatedDate
                   , ETLUpdatedDate
                   , IsDeprecated
                   , PASFinancialSegmentId
                   , FinancialStructureId
                   , SourceUpdatedDate
                 )
                 VALUES
                     (
                         s.DataSourceInstanceId
                       , s.FinancialSegmentKey
                       , s.FinancialSegment
                       , s.SourceQuery
                       , s.SourceKey
                       , s.GlobalFinancialSegmentId
                       , s.FinancialSegmentCode
                       , s.FinancialSegmentDescription
                       , s.Level1
                       , s.Level2
                       , s.Level3
                       , s.Level4
                       , s.Level5
                       , GETUTCDATE()
                       , GETUTCDATE()
                       , s.IsDeleted
                       , s.PASFinancialSegmentId
                       , s.FinancialStructureId
                       , s.SourceUpdatedDate
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT
            s.DataSourceInstanceId
          , s.FinancialSegmentKey
          , s.FinancialSegment
          , s.SourceQuery
          , s.SourceKey
          , s.GlobalFinancialSegmentId
          , s.FinancialSegmentCode
          , s.FinancialSegmentDescription
          , s.Level1
          , s.Level2
          , s.Level3
          , s.Level4
          , s.Level5
          , s.IsDeleted
          , s.PASFinancialSegmentId
          , s.FinancialStructureId
        INTERSECT
        SELECT
            t.DataSourceInstanceId
          , t.FinancialSegmentKey
          , t.FinancialSegment
          , t.SourceQuery
          , t.SourceKey
          , t.GlobalFinancialSegmentId
          , t.FinancialSegmentCode
          , t.FinancialSegmentDescription
          , t.Level1
          , t.Level2
          , t.Level3
          , t.Level4
          , t.Level5
          , t.IsDeprecated
          , t.PASFinancialSegmentId
          , t.FinancialStructureId
    )
            THEN UPDATE SET
                     t.SourceUpdatedDate = s.SourceUpdatedDate
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.DataSourceInstanceId = s.DataSourceInstanceId
                   , t.FinancialSegmentKey = s.FinancialSegmentKey
                   , t.FinancialSegment = s.FinancialSegment
                   , t.SourceQuery = s.SourceQuery
                   , t.SourceKey = s.SourceKey
                   , t.GlobalFinancialSegmentId = s.GlobalFinancialSegmentId
                   , t.FinancialSegmentCode = s.FinancialSegmentCode
                   , t.FinancialSegmentDescription = s.FinancialSegmentDescription
                   , t.Level1 = s.Level1
                   , t.Level2 = s.Level2
                   , t.Level3 = s.Level3
                   , t.Level4 = s.Level4
                   , t.Level5 = s.Level5
                   , t.IsDeprecated = s.IsDeleted
                   , t.PASFinancialSegmentId = s.PASFinancialSegmentId
                   , t.FinancialStructureId = s.FinancialStructureId
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;