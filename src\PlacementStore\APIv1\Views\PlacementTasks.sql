/*
Lineage
PlacementId=dbo.Placement.PlacementId
PlacementSystemId=dbo.Placement.PlacementSystemId
TaskId=dbo.PlacementTasks.TaskId
RiskRequestId=dbo.PlacementTasks.RiskRequestId
TaskType=dbo.PlacementTasks.TaskType
TaskStatus=dbo.PlacementTasks.TaskStatus
LastUpdatedUTCDate=dbo.PlacementTasks.LastUpdatedUTCDate
*/
-- This view is being used as part of the property Enrichment Request process in BP, and we use it to pull in ServiceHub Task Id's
-- Confirmed by <PERSON> on 21/06/2024

CREATE VIEW APIv1.PlacementTasks
AS
SELECT
    p.PlacementId
  , PlacementSystemId = CAST(p.PlacementSystemId AS NVARCHAR(100))
  , pt.TaskId
  , pt.RiskRequestId
  , pt.TaskType
  , pt.TaskStatus
  , pt.LastUpdatedUTCDate
FROM
    dbo.PlacementTasks pt
    INNER JOIN dbo.Placement p
        ON pt.PlacementId = p.PlacementId
           AND p.DataSourceInstanceId = 50366;