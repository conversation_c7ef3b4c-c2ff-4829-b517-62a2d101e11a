PRINT 'Running population script for reference\adf\ProcessInterdependency';

/*
This query generates the definition from the existing data in the database.

SELECT
    v = CONCAT(
            N'        ('
            , CHAR(13)
            , CHAR(10)
            , N'            '''
            , pmain.Name
            , ''''
            , CHAR(13)
            , CHAR(10)
            , '          , '''
            , MAX(pind.InterdependencyType)
            , ''''
            , CHAR(13)
            , CHAR(10)
            , '          , ''["'
            , STRING_AGG(CAST(pdepend.Name AS NVARCHAR(MAX)), CONCAT('"', CHAR(13), CHAR(10), '             ,"')) WITHIN GROUP(ORDER BY
                                                                                                                                    pdepend.Name ASC)
            , '"]''),'
        )
FROM
    ADF.ProcessInterdependency pind
    INNER JOIN ADF.Process pmain
        ON pmain.ProcessId = pind.ProcessId
            AND pmain.IsDeleted = 0

    INNER JOIN ADF.Process pdepend
        ON pdepend.ProcessId = pind.PrerequisiteProcessId
            AND pdepend.IsDeleted = 0
WHERE
    pind.IsDeleted = 0
GROUP BY
    pmain.Name
ORDER BY
    pmain.Name ASC;
*/
DROP TABLE IF EXISTS #Depends;

CREATE TABLE #Depends (
    ProcessName         NVARCHAR(100) NOT NULL
  , InterdependencyType NVARCHAR(100) NOT NULL
  , DependsOnJsonArray  NVARCHAR(MAX) NOT NULL
);

/* To prevent multiple entries for the same Process. */
CREATE UNIQUE INDEX IXU_#Depends
ON #Depends
    (
    ProcessName
    );

/* Please keep the list in alphabetical list of ProcessName */
INSERT INTO #Depends
    (ProcessName, InterdependencyType, DependsOnJsonArray)
VALUES
    ('APIv1.PopulateAPIv1PartyAttributesTable', 'LoadAll Query dbo.PartyAttribute', '["COLStaging.Load_dbo_PartyAttribute"
            ,"COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('AzureProcessSSAS-Carrier', 'SSAS Model Load', '["APIv1.PopulateAPIv1PartyAttributesTable"
            ,"BPStaging.Load_dbo_SubmissionDistribution"
            ,"BPStaging.Load_dbo_SubmissionMarketDocuments"
            ,"BPStaging.Load_PS_ClientProposalPlacement"
            ,"BPStaging.Load_PS_ClientProposalQuoteComparison"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ElementTag"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_QuoteComparisonQuote"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.Load_PS_SubmissionPortalSummary"
            ,"BPStaging.Load_ref_MarketKind"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.LoadBrokingSubSegment"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPartyRole"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementProduct"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.LoadPlacementStructure"
            ,"BPStaging.LoadPlacementSystemAudit"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.LoadRiskLocation"
            ,"BPStaging.LoadScope"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.LoadSubmissionPortalDetails"
            ,"BPStaging.LoadTeamMember"
            ,"CMSStaging.Load_ref_Industry"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"PACTStaging.LoadFacilitySection"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PS.LoadCarrierHierarchyExtended"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoExchangeRate"
            ,"ReferenceStaging.MergeIntoGeographyGroup"
            ,"ReferenceStaging.MergeIntoGeographyGroupingScheme"
            ,"ReferenceStaging.MergeIntoGeographyGroupMembership"
            ,"ReferenceStaging.MergeIntoIndustry"
            ,"ReferenceStaging.MergeIntoIndustrySector"
            ,"ReferenceStaging.MergeIntoIndustrySubSector"
            ,"ReferenceStaging.MergeIntoISIC4Industry"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"
            ,"ReferenceStaging.MergeIntoSIC87Industry"
            ,"rpt.Load_rpt_Contract"
            ,"rpt.Load_rpt_Market"
            ,"rpt.Load_rpt_MarketActivity"
            ,"rpt.Load_rpt_PlacementSecurity"
            ,"rpt.Load_rpt_SubmissionDistribution"
            ,"rpt.Load_rpt_UserSecurity"
            ,"rpt.LoadPlacementPrimaryRole"
            ,"rpt.LoadProductHierarchy"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"
            ,"SignetStaging.LoadCarrierAgencyRating"
            ,"SignetStaging.LoadCarrierRating"
            ,"SignetStaging.LoadCarrierRatingOutlook"
            ,"SignetStaging.LoadRatingAgency"]')
  , ('AzureProcessSSAS-General', 'SSAS Model Load', '["APIv1.PopulateAPIv1PartyAttributesTable"
            ,"BPStaging.Load_dbo_SubmissionDistribution"
            ,"BPStaging.Load_dbo_SubmissionMarketDocuments"
            ,"BPStaging.Load_PS_ClientProposalPlacement"
            ,"BPStaging.Load_PS_ClientProposalQuoteComparison"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.Load_PS_ElementTag"
            ,"BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_NegotiationMarketContract"
            ,"BPStaging.Load_PS_QuoteComparisonQuote"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.Load_PS_SubmissionPortalSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.Load_ref_MarketKind"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.Load_rpt_ContractTimeline"
            ,"BPStaging.LoadBrokingSubSegment"
            ,"BPStaging.LoadCarrierResponse"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadContractSection"
            ,"BPStaging.LoadContractSectionBasis"
            ,"BPStaging.LoadContractSectionBasisType"
            ,"BPStaging.LoadContractSectionRiskCode"
            ,"BPStaging.LoadCoverage"
            ,"BPStaging.LoadCoverageGroup"
            ,"BPStaging.LoadCoverageGroupSection"
            ,"BPStaging.LoadCoverageType"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadEndorsementStatus"
            ,"BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadLegalEntity"
            ,"BPStaging.LoadOutcomeStatus"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPartyRole"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementProduct"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.LoadPlacementStructure"
            ,"BPStaging.LoadPlacementSystemAudit"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.LoadRiskCode"
            ,"BPStaging.LoadRiskLocation"
            ,"BPStaging.LoadScope"
            ,"BPStaging.LoadSelectedReason"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.LoadSubmissionCoverageGroup"
            ,"BPStaging.LoadSubmissionPortalDetails"
            ,"BPStaging.LoadValue"
            ,"CAMStaging.Load_ref_PanelMember"
            ,"CMSStaging.Load_ref_Industry"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_PartyAttribute"
            ,"COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.LoadFacilitySection"
            ,"PACTStaging.MergeClientUnderwriterPremiumIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"PAS.Load_APIv1_OrganisationHierarchyTable"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PS.LoadCarrierHierarchyExtended"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoExchangeRate"
            ,"ReferenceStaging.MergeIntoGeographyGroup"
            ,"ReferenceStaging.MergeIntoGeographyGroupingScheme"
            ,"ReferenceStaging.MergeIntoGeographyGroupMembership"
            ,"ReferenceStaging.MergeIntoIndustry"
            ,"ReferenceStaging.MergeIntoIndustrySector"
            ,"ReferenceStaging.MergeIntoIndustrySubSector"
            ,"ReferenceStaging.MergeIntoISIC4Industry"
            ,"ReferenceStaging.MergeIntoLanguage"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"
            ,"ReferenceStaging.MergeIntoSIC87Industry"
            ,"rpt.Load_rpt_CarrierMatching"
            ,"rpt.Load_rpt_Contract"
            ,"rpt.Load_rpt_ContractElementAttribute"
            ,"rpt.Load_rpt_ContractEndorsementLimit"
            ,"rpt.Load_rpt_ContractLimit"
            ,"rpt.Load_rpt_ContractVersionLimit"
            ,"rpt.Load_rpt_Market"
            ,"rpt.Load_rpt_MarketActivity"
            ,"rpt.Load_rpt_MarketInteractionResponse"
            ,"rpt.Load_rpt_MarketResponseElementAttribute"
            ,"rpt.Load_rpt_Organisation"
            ,"rpt.Load_rpt_PolicyAudit"
            ,"rpt.Load_rpt_PlacementExposure"
            ,"rpt.Load_rpt_PlacementSecurity"
            ,"rpt.Load_rpt_ProgrammeContractLimit"
            ,"rpt.Load_rpt_RiskAttribute"
            ,"rpt.Load_rpt_SubmissionDistribution"
            ,"rpt.Load_rpt_SubmissionMarketSelection"
            ,"rpt.Load_rpt_UserSecurity"
            ,"rpt.LoadContractAuditUser"
            ,"rpt.LoadContractDocument"
            ,"rpt.LoadPlacementProductSummary"
            ,"rpt.LoadProductHierarchy"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"
            ,"SignetStaging.LoadCarrierAgencyRating"
            ,"SignetStaging.LoadCarrierRating"
            ,"SignetStaging.LoadCarrierRatingOutlook"
            ,"SignetStaging.LoadRatingAgency"]')
  , ('AzureProcessSSAS-Usage', 'SSAS Model Load', '["APIv1.PopulateAPIv1PartyAttributesTable"
            ,"BPStaging.Load_dbo_SubmissionDistribution"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.Load_rpt_ContractTimeline"
            ,"BPStaging.LoadBrokingSubSegment"
            ,"BPStaging.LoadLegalEntity"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPartyRole"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.LoadRiskLocation"
            ,"BPStaging.LoadScope"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.LoadTeamMember"
            ,"BPStaging.LoadUserScope"
            ,"CMSStaging.Load_ref_Industry"
            ,"COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PS.LoadElementTimeline"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoExchangeRate"
            ,"ReferenceStaging.MergeIntoGeographyGroup"
            ,"ReferenceStaging.MergeIntoGeographyGroupingScheme"
            ,"ReferenceStaging.MergeIntoGeographyGroupMembership"
            ,"ReferenceStaging.MergeIntoIndustry"
            ,"ReferenceStaging.MergeIntoIndustrySector"
            ,"ReferenceStaging.MergeIntoIndustrySubSector"
            ,"ReferenceStaging.MergeIntoISIC4Industry"
            ,"ReferenceStaging.MergeIntoLanguage"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeIntoSIC87Industry"
            ,"rpt.Load_rpt_Contract"
            ,"rpt.Load_rpt_Market"
            ,"rpt.Load_rpt_MarketInteractionResponse"
            ,"rpt.Load_rpt_PlacementSecurity"
            ,"rpt.Load_rpt_SubmissionMarketSelection"
            ,"rpt.Load_rpt_UserSecurity"
            ,"rpt.LoadContractAuditUser"
            ,"rpt.LoadPlacementPrimaryRole"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('BP Load Event FMATemp.AdditionalDataItem', 'Event for BP Load Event FMATemp.AdditionalDataItem', '["FMATemp.StageAdditionalDataItem"]')
  , ('BP Load Event FMATemp.AppPlacementPolicy', 'Event for BP Load Event FMATemp.AppPlacementPolicy', '["FMATemp.StageAppPlacementPolicy"]')
  , ('BP Load Event FMATemp.Insured', 'Event for BP Load Event FMATemp.Insured', '["FMATemp.StageInsured"]')
  , ('BP Load Event FMATemp.Placement', 'Event for BP Load Event FMATemp.Placement', '["FMATemp.StagePlacement"]')
  , ('BP Load Event FMATemp.PlacementCarrier', 'Event for BP Load Event FMATemp.PlacementCarrier', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StagePlacementCarrier"]')
  , ('BP Load Event FMATemp.PlacementPolicy', 'Event for BP Load Event FMATemp.PlacementPolicy', '["FMATemp.StagePlacementPolicy"]')
  , ('BP Load Event FMATemp.PlacementProducts', 'Event for BP Load Event FMATemp.PlacementProducts', '["FMATemp.StagePlacementProducts"]')
  , ('BP Load Event FMATemp.PlacementTeams', 'Event for BP Load Event FMATemp.PlacementTeams', '["FMATemp.StagePlacementTeams"]')
  , ('BP Load Event FMATemp.PlacementUpdates', 'Event for BP Load Event FMATemp.PlacementUpdates', '["FMATemp.StagePlacementUpdates"]')
  , ('BP Load Event FMATemp.PlacementWorkers', 'Event for BP Load Event FMATemp.PlacementWorkers', '["FMATemp.StagePlacementWorkers"]')
  , ('BP Load Event FMATemp.Policy', 'Event for BP Load Event FMATemp.Policy', '["FMATemp.StagePolicy"]')
  , ('BP Load Event FMATemp.PolicyCarrier', 'Event for BP Load Event FMATemp.PolicyCarrier', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StagePolicyCarrier"]')
  , ('BP Load Event FMATemp.PolicyProduct', 'Event for BP Load Event FMATemp.PolicyProduct', '["FMATemp.StagePolicyProduct"]')
  , ('BP Load Event FMATemp.staging_vw_Agency', 'Event for BP Load Event FMATemp.staging_vw_Agency', '["FMATemp.StageAgency"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierAdditionalData', 'Event for BP Load Event FMATemp.staging_vw_CarrierAdditionalData', '["FMATemp.StageCarrierAdditionalData"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierAgency', 'Event for BP Load Event FMATemp.staging_vw_CarrierAgency', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageCarrierAgency"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierAgencyRating', 'Event for BP Load Event FMATemp.staging_vw_CarrierAgencyRating', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageCarrierAgencyRating"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierExtended', 'Event for BP Load Event FMATemp.staging_vw_CarrierExtended', '["FMATemp.StageCarrierExtended"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierQIRating', 'Event for BP Load Event FMATemp.staging_vw_CarrierQIRating', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageCarrierQIRating"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierRating', 'Event for BP Load Event FMATemp.staging_vw_CarrierRating', '["FMATemp.StageCarrierRating"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierRatingOutlookDefinition', 'Event for BP Load Event FMATemp.staging_vw_CarrierRatingOutlookDefinition', '["FMATemp.StageCarrierRatingOutlookDefinition"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierRelationship', 'Event for BP Load Event FMATemp.staging_vw_CarrierRelationship', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageCarrierRelationship"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierRestriction', 'Event for BP Load Event FMATemp.staging_vw_CarrierRestriction', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageCarrierRestriction"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierRestrictionDefinition', 'Event for BP Load Event FMATemp.staging_vw_CarrierRestrictionDefinition', '["FMATemp.StageCarrierRestrictionDefinition"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierStatus', 'Event for BP Load Event FMATemp.staging_vw_CarrierStatus', '["FMATemp.StageCarrierStatus"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierStatusOverride', 'Event for BP Load Event FMATemp.staging_vw_CarrierStatusOverride', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageCarrierStatusOverride"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierTOBA', 'Event for BP Load Event FMATemp.staging_vw_CarrierTOBA', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageCarrierTOBA"]')
  , ('BP Load Event FMATemp.staging_vw_CarrierType', 'Event for BP Load Event FMATemp.staging_vw_CarrierType', '["FMATemp.StageCarrierType"]')
  , ('BP Load Event FMATemp.staging_vw_Country', 'Event for BP Load Event FMATemp.staging_vw_Country', '["FMATemp.StageCountry"]')
  , ('BP Load Event FMATemp.staging_vw_CountrySubdivision', 'Event for BP Load Event FMATemp.staging_vw_CountrySubdivision', '["FMATemp.StageCountrySubdivision"]')
  , ('BP Load Event FMATemp.staging_vw_Currency', 'Event for BP Load Event FMATemp.staging_vw_Currency', '["FMATemp.StageCurrency"]')
  , ('BP Load Event FMATemp.staging_vw_Facility', 'Event for BP Load Event FMATemp.staging_vw_Facility', '["FMATemp.StageFacility"]')
  , ('BP Load Event FMATemp.staging_vw_FacilityCarrier', 'Event for BP Load Event FMATemp.staging_vw_FacilityCarrier', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageFacilityCarrier"]')
  , ('BP Load Event FMATemp.staging_vw_FacilitySection', 'Event for BP Load Event FMATemp.staging_vw_FacilitySection', '["FMATemp.StageFacilitySection"]')
  , ('BP Load Event FMATemp.staging_vw_FacilitySectionCarrier', 'Event for BP Load Event FMATemp.staging_vw_FacilitySectionCarrier', '["BP Load Event FMATemp.staging_vw_CarrierExtended"
            ,"FMATemp.StageFacilitySectionCarrier"]')
  , ('BP Load Event FMATemp.staging_vw_Industry', 'Event for BP Load Event FMATemp.staging_vw_Industry', '["FMATemp.StageIndustry"]')
  , ('BP Load Event FMATemp.staging_vw_Lookup', 'Event for BP Load Event FMATemp.staging_vw_Lookup', '["FMATemp.StageLookup"]')
  , ('BP Load Event FMATemp.staging_vw_LookupGroup', 'Event for BP Load Event FMATemp.staging_vw_LookupGroup', '["FMATemp.StageLookupGroup"]')
  , ('BP Load Event FMATemp.staging_vw_Office', 'Event for BP Load Event FMATemp.staging_vw_Office', '["FMATemp.StageOffice"]')
  , ('BP Load Event FMATemp.staging_vw_OfficeAddress', 'Event for BP Load Event FMATemp.staging_vw_OfficeAddress', '["FMATemp.StageOfficeAddress"]')
  , ('BP Load Event FMATemp.staging_vw_Party', 'Event for BP Load Event FMATemp.staging_vw_Party', '["FMATemp.StageParty"]')
  , ('BP Load Event FMATemp.staging_vw_PartyAddress', 'Event for BP Load Event FMATemp.staging_vw_PartyAddress', '["BP Load Event FMATemp.staging_vw_Party"
            ,"FMATemp.StagePartyAddress"]')
  , ('BP Load Event FMATemp.staging_vw_PartyCheckResult', 'Uses FMATemp.staging_vw_PartyCheckResult', '["FMATemp.StagePartyCheckResult"]')
  , ('BP Load Event FMATemp.staging_vw_PolicyStatus', 'Event for BP Load Event FMATemp.staging_vw_PolicyStatus', '["FMATemp.StagePolicyStatus"]')
  , ('BP Load Event FMATemp.staging_vw_Product', 'Event for BP Load Event FMATemp.staging_vw_Product', '["FMATemp.StageProduct"]')
  , ('BP Load Event FMATemp.staging_vw_ServicingPlatform', 'Event for BP Load Event FMATemp.staging_vw_ServicingPlatform', '["FMATemp.StageServicingPlatform"]')
  , ('BP Load Event FMATemp.staging_vw_Translation', 'Update language on Translation table', '["PS.UpdateTranslationLanguage"]')
  , ('BP Load Event FMATemp.staging_vw_User', 'Event for BP Load Event FMATemp.staging_vw_User', '["FMATemp.StageUser"]')
  , ('BPStaging.CreatePlacementListener', 'BPStaging.CreatePlacementListener', '["BPStaging.LoadPlacement"]')
  , ('BPStaging.Element', 'Ensure we have loaded linkedelements before copying elements', '["BPStaging.ElementCompositionLinkedElement"]')
  , ('BPStaging.ElementBranch', 'Ensure we have copied the linked element branches before pulling all the branches', '["BPStaging.ElementCompositionLinkedElementBranch"]')
  , ('BPStaging.Load_BP_AdjustmentResponseElement', 'Needs ref.FollowType', '["BPStaging.AdjustmentResponseElement"
            ,"BPStaging.Load_ref_FollowType"]')
  , ('BPStaging.Load_BP_AdjustmentResponseGroup', 'ExtractDefinition Table BPStaging.AdjustmentResponseGroup', '["BPStaging.AdjustmentResponseGroup"]')
  , ('BPStaging.Load_BP_AffirmationQuestion', 'ExtractDefinition Table BPStaging.AffirmationQuestion', '["BPStaging.AffirmationQuestion"]')
  , ('BPStaging.Load_BP_AffirmationQuestionSet', 'ExtractDefinition Table BPStaging.AffirmationQuestionSet', '["BPStaging.AffirmationQuestionSet"]')
  , ('BPStaging.Load_BP_AutoFollowMarketEligibilityRule', 'ExtractDefinition Table BPStaging.AutoFollowMarketEligibilityRule', '["BPStaging.AutoFollowMarketEligibilityRule"
    ,"BPStaging.Load_ref_EligibilityRule"]')
  , ('BPStaging.Load_BP_BoundPosition', 'ExtractDefinition Table BPStaging.BoundPosition', '["BPStaging.BoundPosition"
            ,"BPStaging.Load_ref_BoundPositionType"
            ,"BPStaging.LoadPlacement"]')
  , ('BPStaging.Load_BP_BoundPositionPlacementPolicy', 'ExtractDefinition Table BPStaging.BoundPositionPlacementPolicy', '["BPStaging.BoundPositionPlacementPolicy"
            ,"BPStaging.Load_BP_BoundPosition"
            ,"BPStaging.LoadPlacementPolicies"]')
  , ('BPStaging.Load_BP_ExpiringResponseElement', 'Needs ref.FollowType', '["BPStaging.ExpiringResponseElement"
            ,"BPStaging.Load_ref_FollowType"]')
  , ('BPStaging.Load_BP_ExpiringResponseGroup', 'ExtractDefinition Table BPStaging.ExpiringResponseGroup', '["BPStaging.ExpiringResponseGroup"]')
  , ('BPStaging.Load_BP_ExposureSummaryElement', 'ExtractDefinition Table BPStaging.ExposureSummaryElement', '["BPStaging.ExposureSummaryElement"]')
  , ('BPStaging.Load_BP_ExposureSummaryElementRiskDefinitionItem', 'ExtractDefinition Table BPStaging.ExposureSummaryElementRiskDefinitionItem', '["BPStaging.ExposureSummaryElementRiskDefinitionItem"]')
  , ('BPStaging.Load_BP_MarketResponseElement', 'ExtractDefinition Table BPStaging.MarketResponseElement', '["BPStaging.MarketResponseElement"]')
  , ('BPStaging.Load_BP_MarketResponseAffirmationQuestion', 'ExtractDefinition Table BPStaging.MarketResponseAffirmationQuestion', '["BPStaging.MarketResponseAffirmationQuestion"]')
  , ('BPStaging.Load_BP_MarketResponseAutoFollowEligibilityRule', 'Uses BPStaging.MarketResponseAutoFollowEligibilityRule', '["BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_ref_EligibilityRule"
            ,"BPStaging.MarketResponseAutoFollowEligibilityRule"]')
  , ('BPStaging.Load_BP_PlacementAffirmationQuestion', 'ExtractDefinition Table BPStaging.PlacementAffirmationQuestion', '["BPStaging.PlacementAffirmationQuestion"]')
  , ('BPStaging.Load_BP_PlacementExposureSummary', 'ExtractDefinition Table BPStaging.PlacementExposureSummary', '["BPStaging.PlacementExposureSummary"]')
  , ('BPStaging.Load_BP_PlacementExposureSummaryGroup', 'ExtractDefinition Table BPStaging.PlacementExposureSummaryGroup', '["BPStaging.PlacementExposureSummaryGroup"]')
  , ('BPStaging.Load_BP_PortalRejectionReason', 'Uses BPStaging.PortalRejectionReason', '["BPStaging.PortalRejectionReason"]')
  , ('BPStaging.Load_BP_PortalUserDocument', 'ExtractDefinition Table BPStaging.PortalUserDocument', '["BPStaging.PortalUserDocument"]')
  , ('BPStaging.Load_BP_RequestedCoverageElement', 'ExtractDefinition Table BPStaging.RequestedCoverageElement', '["BPStaging.RequestedCoverageElement"]')
  , ('BPStaging.Load_BP_ResponseManagementElement', 'ExtractDefinition Table BPStaging.ResponseManagementElement', '["BPStaging.ResponseManagementElement"]')
  , ('BPStaging.Load_BP_ResponseManagementElementRiskDefinitionElement', 'LoadAll Table BPStaging.ResponseManagementElementRiskDefinitionElement', '["BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.ResponseManagementElementRiskDefinitionElement"]')
  , ('BPStaging.Load_BP_RiskAndAnalyticsRunMapping', 'ExtractDefinition Table BPStaging.RiskAndAnalyticsRunMapping', '["BPStaging.RiskAndAnalyticsRunMapping"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_BP_RiskRequest', 'ExtractDefinition Table BPStaging.RiskRequest', '["BPStaging.RiskRequest"]')
  , ('BPStaging.Load_BP_Submission', 'ExtractDefinition Table BPStaging.Submission', '["BPStaging.Submission"]')
  , ('BPStaging.Load_BP_SubmissionContainerCarrier', 'ExtractDefinition Table BPStaging.SubmissionContainerCarrier', '["BPStaging.SubmissionContainerCarrier"]')
  , ('BPStaging.Load_BP_SubmissionContainerFacility', 'ExtractDefinition Table BPStaging.SubmissionContainerFacility', '["BPStaging.SubmissionContainerFacility"]')
  , ('BPStaging.Load_BP_SubmissionContainerMarket', 'ExtractDefinition Table BPStaging.SubmissionContainerMarket', '["BPStaging.SubmissionContainerMarket"]')
  , ('BPStaging.Load_BP_SubmissionContainerPanel', 'ExtractDefinition Table BPStaging.SubmissionContainerPanel', '["BPStaging.SubmissionContainerPanel"]')
  , ('BPStaging.Load_BP_SubmissionContainerPanelMember', 'ExtractDefinition Table BPStaging.SubmissionContainerPanelMember', '["BPStaging.SubmissionContainerPanelMember"]')
  , ('BPStaging.Load_BP_SubmissionContainerRequestedCoverageElement', 'ExtractDefinition Table BPStaging.SubmissionContainerRequestedCoverageElement', '["BPStaging.SubmissionContainerRequestedCoverageElement"]')
  , ('BPStaging.Load_BP_SubmissionContainerRiskDefinitionItem', 'ExtractDefinition Table BPStaging.SubmissionContainerRiskDefinitionItem', '["BPStaging.SubmissionContainerRiskDefinitionItem"]')
  , ('BPStaging.Load_BP_SubmissionContainerThirdPartyMarket', 'ExtractDefinition Table BPStaging.SubmissionContainerThirdPartyMarket', '["BPStaging.SubmissionContainerThirdPartyMarket"]')
  , ('BPStaging.Load_BP_SubmissionDocument', 'ExtractDefinition Table BPStaging.SubmissionDocument', '["BPStaging.SubmissionDocument"]')
  , ('BPStaging.Load_BP_SubmissionMarket', 'ExtractDefinition Table BPStaging.SubmissionMarket_v2', '["BPStaging.SubmissionMarket_v2"]')
  , ('BPStaging.Load_BP_SubmissionMarketRecipient', 'ExtractDefinition Table BPStaging.SubmissionMarketRecipient', '["BPStaging.Load_BP_PortalRejectionReason"
            ,"BPStaging.SubmissionMarketRecipient"]')
  , ('BPStaging.Load_BP_SubmissionPortalUser', 'ExtractDefinition Table BPStaging.SubmissionPortalUser', '["BPStaging.SubmissionPortalUser"]')
  , ('BPStaging.Load_dbo_ElementChangeSet', 'ExtractDefinition Table BPStaging.ElementChangeSet', '["BPStaging.ElementChangeSet"]')
  , ('BPStaging.Load_dbo_PlacementMarketValidation', 'FK dbo.Policy', '["BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.PlacementMarketValidation"
            ,"BPStaging.UpdatePlacement"
            ,"CAMStaging.Load_ref_PanelMember"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"]')
  , ('BPStaging.Load_dbo_Submission', 'ExtractDefinition Table dbo.Submission', '["BPStaging.Load_BP_Submission"
            ,"BPStaging.Load_PS_Negotiation"]')
  , ('BPStaging.Load_dbo_SubmissionDistribution', 'ExtractDefinition Table dbo.SubmissionDistribution', '["BPStaging.Load_BP_SubmissionMarket"
            ,"BPStaging.Load_dbo_Submission"
            ,"BPStaging.Load_PS_NegotiationMarket"]')
  , ('BPStaging.Load_dbo_SubmissionMarketDocuments', 'ExtractDefinition Table dbo.SubmissionDocumentSummary', '["BPStaging.Load_BP_SubmissionDocument"
            ,"BPStaging.Load_BP_PortalUserDocument"
            ,"BPStaging.Load_BP_SubmissionPortalUser"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_dbo_Submission"]')
  , ('BPStaging.Load_dbo_SubmissionRecipient', 'ExtractDefinition Table dbo.SubmissionRecipient', '["BPStaging.Load_BP_SubmissionMarketRecipient"
            ,"BPStaging.Load_dbo_SubmissionDistribution"]')
  , ('BPStaging.Load_PS_ActionValidationRule', 'Needs BPStaging.ActionValidationRule', '["BPStaging.ActionValidationRule"]')
  , ('BPStaging.Load_PS_AdditionalDataItem', 'Uses dbo.Placement', '["BPStaging.AdditionalDataItem"
            ,"BPStaging.Load_PS_LibAdditionalDataItem"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadValue"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_AppetiteNarrative', 'FK Reference.DataSourceInstance', '["BPStaging.AppetiteNarrative"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_AppetiteResponse', 'LoadAll Table PS.AppetiteResponse', '["BPStaging.Load_BP_SubmissionContainerMarket"
            ,"BPStaging.Load_BP_SubmissionContainerPanelMember"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_ref_AppetiteLevel"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_ClientProfile', 'LoadAll Table dbo.Placement', '["BPStaging.ClientProfile"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.PlacementPartyRelationship"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('BPStaging.Load_PS_ClientProposal', 'ExtractDefinition Table BPStaging.ClientProposal', '["BPStaging.ClientProposal"]')
  , ('BPStaging.Load_PS_ClientProposalCoverage', 'ExtractDefinition Table BPStaging.ClientProposalCoverage', '["BPStaging.ClientProposalCoverage"]')
  , ('BPStaging.Load_PS_ClientProposalCoverageNotice', 'ExtractDefinition Table BPStaging.ClientProposalCoverageNotice', '["BPStaging.ClientProposalCoverageNotice"]')
  , ('BPStaging.Load_PS_ClientProposalPlacement', 'ExtractDefinition Table BPStaging.ClientProposalPlacement', '["BPStaging.ClientProposalPlacement"]')
  , ('BPStaging.Load_PS_ClientProposalQuoteComparison', 'ExtractDefinition Table BPStaging.ClientProposalQuoteComparison', '["BPStaging.ClientProposalQuoteComparison"]')
  , ('BPStaging.Load_PS_Contract', 'ExtractDefinition Table dbo.Placement', '["BPStaging.Contract"
            ,"BPStaging.ContractRenewedFrom"
            ,"BPStaging.Load_PS_ContractDocumentBase"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_ContractAttribute', 'Dependency for Load_PS_ContractAttribute', '["BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"]')
  , ('BPStaging.Load_PS_ContractDocumentBase', 'FK PS.Contract', '["BPStaging.ContractDocumentBase"
            ,"BPStaging.LoadPlacement"]')
  , ('BPStaging.Load_PS_ContractDocumentVersion', 'ExtractDefinition Table BPStaging.ContractDocumentVersion', '["BPStaging.ContractDocumentVersion"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.LoadContractDocumentElement"]')
  , ('BPStaging.Load_PS_ContractEndorsement', 'Dependency for Load_PS_ContractEndorsement', '["BPStaging.ContractEndorsement"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.Load_PS_Contract"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_ContractEndorsementAttribute', 'Dependency for Load_PS_ContractEndorsementAttribute', '["BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"PS.Load_PS_ContractClauseAndCondition"]')
  , ('BPStaging.Load_PS_ContractProgrammeAttribute', 'Dependency for Load_PS_ContractProgrammeAttribute', '["BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.Load_PS_ContractDocumentBase"
            ,"BPStaging.Load_PS_ContractEndorsementAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"]')
  , ('BPStaging.Load_PS_ContractStatusHistory', 'Load PS.ContractStatusHistory table', '["BPStaging.Contract"
            ,"BPStaging.Load_PS_Contract"]')
  , ('BPStaging.Load_PS_ContractVersionAttribute', 'Dependency for Load_PS_ContractVersionAttribute', '["BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractProgrammeAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"]')
  , ('BPStaging.Load_PS_Element', 'FK ref.ElementType', '["BPStaging.Element"
            ,"BPStaging.Load_ref_ElementType"]')
  , ('BPStaging.Load_PS_ElementCompositionLinkedElement', 'Uses BPStaging.ElementCompositionLinkedElement', '["BPStaging.ElementCompositionLinkedElement"
            ,"BPStaging.Load_PS_Element"]')
  , ('BPStaging.Load_PS_ElementCompositionLinkedElementBranch', 'Uses BPStaging.ElementCompositionLinkedElementBranch', '["BPStaging.ElementCompositionLinkedElementBranch"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.Load_PS_ElementCompositionLinkedElement"
            ,"BPStaging.LoadElementBranch"]')
  , ('BPStaging.Load_PS_ElementTag', 'ExtractDefinition Table BPStaging.ElementTag', '["BPStaging.ElementTag"]')
  , ('BPStaging.Load_PS_ElementTagSummary', 'LoadAll Table ref.ElementTagType', '["BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"]')
  , ('BPStaging.Load_PS_ExposureElement', 'LoadAll Table PS.PlacementExposureGroup', '["BPStaging.Load_PS_PlacementExposureGroup"
            ,"BPStaging.Load_BP_ExposureSummaryElement"
            ,"BPStaging.Load_BP_PlacementExposureSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_ExposureElementAttribute', 'LoadAll Table PS.PlacementExposureGroup', '["BPStaging.Load_PS_ExposureElement"
            ,"BPStaging.Load_PS_PlacementExposureGroup"
            ,"BPStaging.Load_BP_ExposureSummaryElement"
            ,"BPStaging.Load_BP_PlacementExposureSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_LibAdditionalDataItem', 'Uses BPStaging.LibAdditionalDataItem', '["BPStaging.LibAdditionalDataItem"]')
  , ('BPStaging.Load_PS_MarketQuoteResponsePolicyRef', 'Uses ref.PolicyRefType', '["BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_ref_PolicyRefType"
            ,"BPStaging.MarketQuoteResponsePolicyRef"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_MarketResponse', 'LoadAll Table Reference.Currency', '["BPStaging.CarrierResponse"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_BP_AdjustmentResponseGroup"
            ,"BPStaging.Load_BP_ExpiringResponseGroup"
            ,"BPStaging.Load_ref_ProgramStructureType"
            ,"BPStaging.LoadDeclinationReason"
            ,"BPStaging.LoadOutcomeReason"
            ,"BPStaging.LoadOutcomeStatus"
            ,"BPStaging.LoadPendingActionReason"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadResponseType"
            ,"BPStaging.MarketResponse"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.LoadFacilitySection"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('BPStaging.Load_PS_MarketResponseBasis', 'LoadAll Table PS.RiskProfile', '["BPStaging.CarrierResponse"
            ,"BPStaging.Load_BP_ResponseManagementElementRiskDefinitionElement"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_BP_AdjustmentResponseElement"
            ,"BPStaging.Load_BP_ExpiringResponseElement"
            ,"BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.LoadContractSection"
            ,"BPStaging.LoadElementBranch"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.MarketResponseBasis"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.Load_PS_MarketResponseElement', 'LoadAll Table PS.MarketResponse', '["BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_BP_AdjustmentResponseElement"
            ,"BPStaging.Load_BP_ExpiringResponseElement"
            ,"BPStaging.Load_BP_MarketResponseElement"
            ,"BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_MarketResponseElementAttribute', 'LoadAll Table PS.MarketResponse', '["BPStaging.Load_BP_AdjustmentResponseElement"
            ,"BPStaging.Load_BP_ExpiringResponseElement"
            ,"BPStaging.Load_BP_MarketResponseElement"
            ,"BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_MarketResponseElement"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_Negotiation', 'LoadAll Table dbo.Placement', '["BPStaging.CarrierResponse"
            ,"BPStaging.Load_BP_AdjustmentResponseGroup"
            ,"BPStaging.Load_BP_ExpiringResponseGroup"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.MarketSelection"
            ,"BPStaging.SubmissionContainer"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_NegotiationContract', 'Load PS.NegotiationContract', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.SubmissionContainerContract"]')
  , ('BPStaging.Load_PS_NegotiationMarket', 'LoadAll Table PS.Negotiation', '["BPStaging.CarrierResponse"
            ,"BPStaging.Load_BP_AdjustmentResponseGroup"
            ,"BPStaging.Load_BP_ExpiringResponseGroup"
            ,"BPStaging.Load_BP_SubmissionContainerMarket"
            ,"BPStaging.Load_BP_SubmissionContainerCarrier"
            ,"BPStaging.Load_BP_SubmissionContainerFacility"
            ,"BPStaging.Load_BP_SubmissionContainerPanel"
            ,"BPStaging.Load_BP_SubmissionContainerPanelMember"
            ,"BPStaging.Load_BP_SubmissionContainerThirdPartyMarket"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_ref_MarketKind"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.MarketSelection"
            ,"BPStaging.UpdatePlacement"
            ,"CAMStaging.Load_ref_Panel"
            ,"CAMStaging.Load_ref_PanelMember"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.LoadFacilitySection"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('BPStaging.Load_PS_NegotiationRiskProfile', 'LoadAll Table PS.RiskStructure', '["BPStaging.Load_BP_SubmissionContainerRiskDefinitionItem"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.LoadCoverageGroup"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.LoadSubmissionCoverageGroup"
            ]')
  , ('BPStaging.Load_PS_NegotiationSpecification', 'LoadAll Table PS.Specification', '["BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_Specification"
            ,"BPStaging.Load_BP_SubmissionContainerRequestedCoverageElement"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_PlacementExposureGroup', 'LoadAll Table dbo.Placement', '["BPStaging.Load_BP_ExposureSummaryElement"
            ,"BPStaging.Load_BP_PlacementExposureSummary"
            ,"BPStaging.Load_BP_PlacementExposureSummaryGroup"
            ,"BPStaging.Load_ref_ExposurePeriod"
            ,"BPStaging.Load_ref_ExposureType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_PlacementRuleValidation', 'Needs PS.ActionValidationRule', '["BPStaging.Load_PS_ActionValidationRule"
            ,"BPStaging.PlacementRuleValidation"]')
  , ('BPStaging.Load_PS_PlacementServiceSatisfactionValidation', 'Needs PS.NegotiationMarket', '["BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.PlacementServiceSatisfactionValidation"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.Load_PS_PlacementStatusHistory', 'Load PS.PlacementStatusHistory table', '["BPStaging.LoadPlacement"
            ,"BPStaging.PlacementSystemAudit"
            ,"BPStaging.UpdatePlacementDatesAndStatus"]')
  , ('BPStaging.Load_PS_QuoteComparison', 'ExtractDefinition Table BPStaging.QuoteComparison', '["BPStaging.QuoteComparison"]')
  , ('BPStaging.Load_PS_QuoteComparisonQuote', 'ExtractDefinition Table BPStaging.QuoteComparisonQuote', '["BPStaging.QuoteComparisonQuote"]')
  , ('BPStaging.Load_PS_RiskProfile', 'LoadAll Table ref.OpportunityType', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.RiskStructure"
            ,"BPStaging.Specification"
            ,"BPStaging.UpdatePlacement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"]')
  , ('BPStaging.Load_PS_RiskProfilePlacementExposureGroup', 'LoadAll Table PS.RiskProfile', '["BPStaging.Load_PS_PlacementExposureGroup"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_BP_ExposureSummaryElement"
            ,"BPStaging.Load_BP_ExposureSummaryElementRiskDefinitionItem"
            ,"BPStaging.Load_BP_PlacementExposureSummary"]')
  , ('BPStaging.Load_PS_RiskStructure', 'Uses PS.SpecificationAttribute via PS.vwSpecificationLayerFlat', '["BPStaging.ElementType"
            ,"BPStaging.Layer"
            ,"BPStaging.Load_BP_SubmissionContainerCarrier"
            ,"BPStaging.Load_BP_SubmissionContainerFacility"
            ,"BPStaging.Load_BP_SubmissionContainerMarket"
            ,"BPStaging.Load_BP_SubmissionContainerPanel"
            ,"BPStaging.Load_BP_SubmissionContainerPanelMember"
            ,"BPStaging.Load_BP_SubmissionContainerThirdPartyMarket"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_PS_Specification"
            ,"BPStaging.Load_PS_SpecificationElement"
            ,"BPStaging.Load_PS_SpecificationElementAttribute"
            ,"BPStaging.Load_PS_Staging_RiskStructurePolicy"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.RiskStructure"
            ,"BPStaging.Specification"
            ,"BPStaging.SubmissionContainer"
            ,"CAMStaging.Panel"
            ,"CAMStaging.PanelMember"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.LoadFacilitySection"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_RiskStructureContract', 'LoadAll Table PS.RiskStructure', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.LoadContractRisk"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_RiskStructureMarketResponse', 'LoadAll Table PS.Specification', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.LoadFacilitySection"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_RiskStructurePolicy', 'LoadAll Table PS.RiskStructure', '["BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.Load_PS_Staging_RiskStructurePolicy"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_RiskStructureSpecification', 'Uses', '["BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.Load_PS_SpecificationElement"
            ,"BPStaging.Load_PS_SpecificationElementAttribute"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_ServiceSatisfactionIssueOutcome', 'Needs PS.ServiceSatisfactionIssueOutcome', '["BPStaging.Load_PS_PlacementServiceSatisfactionValidation"
            ,"BPStaging.ServiceSatisfactionIssueOutcome"]')
  , ('BPStaging.Load_PS_Specification', 'LoadAll Table PS.RiskProfile', '["BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_BP_RequestedCoverageElement"
            ,"BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_SpecificationElement', 'LoadAll Table PS.SpecificationElement', '["BPStaging.ElementTypeMetaTag"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.Load_PS_Specification"
            ,"BPStaging.Load_BP_RequestedCoverageElement"
            ,"BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.Load_dbo_ElementChangeSet"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.Load_ref_ElementTypeMetaTag"
            ,"BPStaging.Load_ref_MetaTag"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.MetaTag"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_SpecificationElementAttribute', 'LoadAll Table PS.Specification', '["BPStaging.ElementTypeMetaTag"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.Load_PS_Specification"
            ,"BPStaging.Load_PS_SpecificationElement"
            ,"BPStaging.Load_BP_RequestedCoverageElement"
            ,"BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.Load_dbo_ElementChangeSet"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.Load_ref_ElementTypeMetaTag"
            ,"BPStaging.Load_ref_MetaTag"
            ,"BPStaging.LoadElementAttribute"
            ,"BPStaging.LoadElementAttributeDelta"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.MetaTag"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_Staging_RiskStructurePolicy', 'Uses PS.ContractRisk', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.LoadContractPolicy"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadLegalEntity"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PAS.Load_APIv1_OrganisationHierarchyTable"]')
  , ('BPStaging.Load_PS_SubmissionPortalSummary', 'ExtractDefinition Table dbo.SubmissionPortalSummary', '["BPStaging.Load_BP_SubmissionPortalUser"
            ,"BPStaging.Load_dbo_Submission"
            ,"BPStaging.Load_PS_NegotiationMarket"]')
  , ('BPStaging.Load_PS_ValidationRuleOutcome', 'BPStaging.ValidationRuleOutcome used as source', '["BPStaging.ValidationRuleOutcome"]')
  , ('BPStaging.Load_ref_AppetiteLevel', 'ExtractDefinition Table BPStaging.AppetiteLevel', '["BPStaging.AppetiteLevel"]')
  , ('BPStaging.Load_ref_BoundPositionType', 'ExtractDefinition Table BPStaging.BoundPositionType', '["BPStaging.BoundPositionType"]')
  , ('BPStaging.Load_ref_ElementAttributeReferenceOption', 'Load the staging table', '["BPStaging.ElementAttributeReferenceOption"]')
  , ('BPStaging.Load_ref_EligibilityRule', 'ExtractDefinition Table BPStaging.EligibilityRule', '["BPStaging.EligibilityRule"]')
  , ('BPStaging.Load_ref_ExposurePeriod', 'ExtractDefinition Table BPStaging.ExposurePeriodGroup', '["BPStaging.ExposurePeriodGroup"]')
  , ('BPStaging.Load_ref_ExposureType', 'ExtractDefinition Table BPStaging.ExposureType', '["BPStaging.ExposureType"]')
  , ('BPStaging.Load_ref_FollowType', 'ExtractDefinition Table BPStaging.FollowType', '["BPStaging.FollowType"]')
  , ('BPStaging.Load_ref_Industry', 'LoadAll Table Reference.IndustrySubSector', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"CMSStaging.Load_ref_Industry"
            ,"CMSStaging.Load_ref_IndustryGroup"
            ,"ReferenceStaging.MergeIntoIndustry"
            ,"ReferenceStaging.MergeIntoIndustrySector"
            ,"ReferenceStaging.MergeIntoIndustrySubSector"]')
  , ('BPStaging.Load_ref_InsuredType', 'Uses BPStaging.InsuredType', '["BPStaging.InsuredType"]')
  , ('BPStaging.Load_ref_MTAType', 'Uses BPStaging.MTAType', '["BPStaging.MTAType"]')
  , ('BPStaging.Load_ref_PolicyRefType', 'Uses BPStaging.PolicyRefType', '["BPStaging.PolicyRefType"]')
  , ('BPStaging.Load_ref_PricingFactor', 'ExtractDefinition Table BPStaging.PricingFactor', '["BPStaging.PricingFactor"]')
  , ('BPStaging.Load_ref_RiskAndAnalyticsModelType', 'ExtractDefinition Table BPStaging.RiskAndAnalyticsModelType', '["BPStaging.RiskAndAnalyticsModelType"]')
  , ('BPStaging.Load_ref_Team', 'ExtractDefinition Table ref.Team', '["BPStaging.UserGroup"]')
  , ('BPStaging.Load_Reference_VerticalIndustry', 'Load the staging table', '["BPStaging.VerticalIndustry"]')
  , ('BPStaging.Load_rpt_ContractTimeline', 'Load_rpt_Master Table rpt.Contract', '["BPStaging.ContractTimeline"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"rpt.Load_rpt_Contract"]')
  , ('BPStaging.LoadAppraisalType', 'Uses BPStaging.AppraisalType', '["BPStaging.AppraisalType"]')
  , ('BPStaging.LoadBespokeHeadingReason', 'ExtractDefinition Table BPStaging.BespokeHeadingReason', '["BPStaging.BespokeHeadingReason"]')
  , ('BPStaging.LoadBrokingSegment', 'Uses BPStaging.BrokingSegment', '["BPStaging.BrokingSegment"]')
  , ('BPStaging.LoadBrokingSubSegment', 'ExtractDefinition Table BPStaging.BrokingSubSegment', '["BPStaging.BrokingSubSegment"]')
  , ('BPStaging.LoadCarrierResponse', 'Required for load', '["BPStaging.CarrierResponse"
            ,"BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadJustificationReasonType"
            ,"BPStaging.LoadMarketSelection"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadSelectedReason"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('BPStaging.LoadContractDocumentElement', 'ExtractDefinition Table dbo.DocumentElement', '["BPStaging.ContractDocumentElement"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.LoadDocumentElement"]')
  , ('BPStaging.LoadContractEndorsementDocumentElement', 'ExtractDefinition Table dbo.DocumentElement', '["BPStaging.ContractEndorsementDocumentElement"
            ,"BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadDocumentElement"]')
  , ('BPStaging.Load_BP_ContractEndorsementElementChangeSet', 'ExtractDefinition Table BPStaging.ContractEndorsementElementChangeSet', '["BPStaging.ContractEndorsementElementChangeSet"]')
  , ('BPStaging.LoadContractPolicy', 'ExtractDefinition Table PS.Contract', '["BPStaging.ContractPolicy"
            ,"BPStaging.Load_PS_Contract"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"]')
  , ('BPStaging.LoadContractRisk', 'ExtractDefinition Table PS.Contract', '["BPStaging.ContractRisk"
            ,"BPStaging.Load_PS_Contract"]')
  , ('BPStaging.Load_BP_ContractRiskCode', 'ExtractDefinition Table BPStaging.ContractRiskCode', '["BPStaging.ContractRiskCode"
        ,"BPStaging.LoadRiskCode"]')
  , ('BPStaging.LoadContractSection', 'ExtractDefinition Table Reference.Currency', '["BPStaging.ContractSection"
            ,"BPStaging.Load_PS_Contract"
            ,"ReferenceStaging.MergeIntoCurrency"]')
  , ('BPStaging.LoadContractSectionBasis', 'FK ref.ContractSectionBasisType', '["BPStaging.ContractSectionBasis"
            ,"BPStaging.LoadContractSection"
            ,"BPStaging.LoadContractSectionBasisType"]')
  , ('BPStaging.LoadContractSectionBasisType', 'ExtractDefinition Table BPStaging.ContractSectionBasisType', '["BPStaging.ContractSectionBasisType"]')
  , ('BPStaging.LoadContractSectionRiskCode', 'FK ref.RiskCode', '["BPStaging.ContractSectionRiskCode"
            ,"BPStaging.LoadContractSection"
            ,"BPStaging.LoadRiskCode"]')
  , ('BPStaging.LoadContractStatus', 'ExtractDefinition Table BPStaging.ContractStatus', '["BPStaging.ContractStatus"]')
  , ('BPStaging.LoadContractType', 'ExtractDefinition Table BPStaging.ContractType', '["BPStaging.ContractType"]')
  , ('BPStaging.LoadCoverage', 'AzurePolicyDataDownloadSpec.dtsx Table BPStaging.Coverage', '["BPStaging.Coverage"]')
  , ('BPStaging.LoadCoverageBasis', 'ExtractDefinition Table BPStaging.CoverageBasis', '["BPStaging.CoverageBasis"]')
  , ('BPStaging.LoadCoverageGroup', 'FK dbo.Specification', '["BPStaging.CoverageGroup"
            ,"BPStaging.LoadSpecification"]')
  , ('BPStaging.LoadCoverageGroupSection', 'FK dbo.CoverageGroup', '["BPStaging.CoverageGroupSection"
            ,"BPStaging.LoadCoverageGroup"]')
  , ('BPStaging.LoadCoverageType', 'AzurePolicyDataDownloadSpec.dtsx Table BPStaging.CoverageType', '["BPStaging.CoverageType"]')
  , ('BPStaging.LoadDeclinationReason', 'ExtractDefinition Table BPStaging.DeclinationReason', '["BPStaging.DeclinationReason"]')
  , ('BPStaging.LoadDeductibleBasis', 'ExtractDefinition Table BPStaging.DeductibleBasis', '["BPStaging.DeductibleBasis"]')
  , ('BPStaging.LoadDeltaType', 'ExtractDefinition Table BPStaging.DeltaType', '["BPStaging.DeltaType"]')
  , ('BPStaging.LoadDocumentElement', 'FK dbo.Element', '["BPStaging.DocumentElement"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.LoadElementCache"]')
  , ('BPStaging.LoadDocumentTemplateElement', 'ExtractDefinition Table BPStaging.DocumentTemplateElement', '["BPStaging.DocumentTemplateElement"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.LoadElementCache"]')
  , ('BPStaging.LoadElementAttribute', 'FK dbo.ElementAttributeType', '["BPStaging.ElementAttribute"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.LoadElementAttributeType"]')
  , ('BPStaging.LoadElementAttributeCache', 'FK dbo.ElementAttributeType', '["BPStaging.Load_dbo_ElementChangeSet"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.LoadElementAttribute"
            ,"BPStaging.LoadElementAttributeDelta"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementBranch"]')
  , ('BPStaging.LoadElementAttributeDelta', 'ExtractDefinition Table BPStaging.ElementAttributeDelta', '["BPStaging.ElementAttributeDelta"
            ,"BPStaging.Load_dbo_ElementChangeSet"]')
  , ('BPStaging.LoadElementAttributeType', 'ExtractDefinition Table BPStaging.ElementAttributeType', '["BPStaging.ElementAttributeType"]')
  , ('BPStaging.LoadElementBranch', 'ExtractDefinition Table BPStaging.ElementBranch', '["BPStaging.ElementBranch"]')
  , ('BPStaging.LoadElementCache', 'ExtractDefinition Table ref.ElementType', '["BPStaging.Load_dbo_ElementChangeSet"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementBranch"
            ,"BPStaging.LoadElementDelta"]')
  , ('BPStaging.LoadElementDelta', 'ExtractDefinition Table BPStaging.ElementDelta', '["BPStaging.ElementDelta"]')
  , ('BPStaging.Load_dbo_ElementTagCache', 'ExtractDefinition Table PS.ElementTag', '["BPStaging.Load_dbo_ElementChangeSet"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.Load_PS_ElementTag"
            ,"BPStaging.LoadElementBranch"
            ,"BPStaging.LoadElementTagDelta"]')
  , ('BPStaging.LoadElementTagDelta', 'ExtractDefinition Table BPStaging.ElementTagDelta', '["BPStaging.ElementTagDelta"]')
  , ('BPStaging.LoadElementTagGroup', 'ExtractDefinition Table BPStaging.ElementTagGroup', '["BPStaging.ElementTagGroup"]')
  , ('BPStaging.LoadElementTagType', 'FK ref.ElementTagGroup', '["BPStaging.ElementTagType"
            ,"BPStaging.LoadElementTagGroup"]')
  , ('BPStaging.Load_ref_ElementType', 'ExtractDefinition Table BPStaging.ElementType', '["BPStaging.ElementType"]')
  , ('BPStaging.Load_ref_ElementTypeMetaTag', 'ExtractDefinition Table BPStaging.ElementTypeMetaTag', '["BPStaging.ElementTypeMetaTag"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.Load_ref_MetaTag"]')
  , ('BPStaging.LoadEndorsementStatus', 'ExtractDefinition Table BPStaging.EndorsementStatus', '["BPStaging.EndorsementStatus"]')
  , ('BPStaging.LoadExtendedReportingPeriod', 'ExtractDefinition Table BPStaging.ExtendedReportingPeriod', '["BPStaging.ExtendedReportingPeriod"]')
  , ('BPStaging.LoadJustificationReason', 'ExtractDefinition Table BPStaging.JustificationReason', '["BPStaging.JustificationReason"]')
  , ('BPStaging.LoadJustificationReasonType', 'ExtractDefinition Table BPStaging.JustificationReasonType', '["BPStaging.JustificationReasonType"]')
  , ('BPStaging.LoadLayer', 'FK Reference.Currency', '["BPStaging.Layer"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementStructure"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"ReferenceStaging.MergeIntoCurrency"]')
  , ('BPStaging.LoadLayerType', 'Uses BPStaging.LayerType', '["BPStaging.LayerType"]')
  , ('BPStaging.LoadLegalEntity', 'From Heidi Table BPStaging.LegalEntity', '["BPStaging.LegalEntity"]')
  , ('BPStaging.LoadLibraryElement', 'ExtractDefinition Table BPStaging.LibraryElement', '["BPStaging.LibraryElement"]')
  , ('BPStaging.LoadLinePercentageBasis', 'ExtractDefinition Table BPStaging.LinePercentageBasis', '["BPStaging.LinePercentageBasis"]')
  , ('BPStaging.LoadMarketInteractionFacility', 'ExtractDefinition Table BPStaging.MarketInteractionFacility', '["BPStaging.MarketInteractionFacility"]')
  , ('BPStaging.Load_PS_MarketQuoteResponse', 'Uses BPStaging.MarketQuoteResponse', '["BPStaging.MarketQuoteResponseAcceptedDates"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_ref_FollowType"
            ,"BPStaging.LoadOutcomeReason"
            ,"BPStaging.LoadOutcomeStatus"
            ,"BPStaging.MarketQuoteResponse"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.Load_PS_MarketQuoteResponseHistoric', 'Uses BPStaging.Load_PS_MarketQuoteResponseHistoric', '["BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_BP_AdjustmentResponseElement"
            ,"BPStaging.Load_BP_AdjustmentResponseGroup"
            ,"BPStaging.Load_BP_ExpiringResponseElement"
            ,"BPStaging.Load_BP_ExpiringResponseGroup"
            ,"BPStaging.Load_BP_ResponseManagementElement"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementBranch"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"]')
  , ('BPStaging.Load_PS_MarketQuoteResponseLegacy', 'Uses BPStaging.MarketQuoteResponseLegacy', '["BPStaging.CarrierResponse"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.LoadPlacement"]')
  , ('BPStaging.LoadMarketResponseSecurity', 'ExtractDefinition Table dbo.MarketResponseSecurity', '["BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.MarketResponseSecurity"
            ,"PACTStaging.LoadFacilitySection"]')
  , ('BPStaging.LoadMarketSelection', 'FK dbo.SelectedReason', '["BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadJustificationReasonType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadSelectedReason"
            ,"BPStaging.MarketSelection"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('BPStaging.LoadMergedPlacements', 'ExtractDefinition Table BPStaging.MergedPlacements', '["BPStaging.LoadPlacement"
            ,"BPStaging.MergedPlacements"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.Load_ref_MetaTag', 'ExtractDefinition Table BPStaging.ElementTypeMetaTag', '["BPStaging.MetaTag"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.LoadNewClient', 'FK dbo.Party', '["BPStaging.NewClient"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('BPStaging.LoadOutcomeReason', 'ExtractDefinition Table ref.OutcomeStatus', '["BPStaging.LoadOutcomeStatus"
            ,"BPStaging.OutcomeReason"]')
  , ('BPStaging.LoadOutcomeStatus', 'ExtractDefinition Table BPStaging.OutcomeStatus', '["BPStaging.OutcomeStatus"]')
  , ('BPStaging.LoadParticipantFunction', 'ExtractDefinition Table BPStaging.ParticipantFunction', '["BPStaging.ParticipantFunction"]')
  , ('BPStaging.LoadPaymentPeriod', 'ExtractDefinition Table BPStaging.PaymentPeriod', '["BPStaging.PaymentPeriod"]')
  , ('BPStaging.LoadPendingActionReason', 'ExtractDefinition Table BPStaging.PendingActionReason', '["BPStaging.PendingActionReason"]')
  , ('BPStaging.LoadPlacement', 'Loading Placement', '["BPStaging.LoadAppraisalType"
            ,"BPStaging.LoadRiskLocation"
            ,"BPStaging.Load_ref_CancellationReason"
            ,"BPStaging.Load_ref_MTAType"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.Load_ref_Team"
            ,"BPStaging.Load_Reference_VerticalIndustry"
            ,"BPStaging.Placement"
            ,"CMSStaging.Load_ref_PlacementStatus"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_RefInsuranceType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('BPStaging.Load_ref_CancellationReason', 'Uses ref.CancellationReason', '["BPStaging.PlacementCancellationReason"]')
  , ('BPStaging.LoadPlacementExtension', 'AzurePolicyDataDownloadSpec.dtsx Table dbo.Placement', '["BPStaging.LoadPlacement"
            ,"BPStaging.PlacementExtension"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPlacementOpportunity', 'Wait for source to be staged BPStaging.OpportunityPlacement', '["BPStaging.LoadPlacement"
            ,"BPStaging.OpportunityPlacement"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.LoadPlacementPartyRole', 'ExtractDefinition Table BPStaging.PlacementPartyRelationship', '["BPStaging.LoadNewClient"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.PlacementPartyRelationship"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('BPStaging.LoadPlacementPolicies', 'FK dbo.PlacementPolicyRelationshipType', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicyRelationshipType"
            ,"BPStaging.PlacementPolicy"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"]')
  , ('BPStaging.LoadPlacementPolicyRelationshipType', 'ExtractDefinition Table BPStaging.PolicyType', '["BPStaging.PolicyType"]')
  , ('BPStaging.LoadPlacementPremium', 'ExtractDefinition Table dbo.Placement', '["BPStaging.LoadPlacement"
            ,"BPStaging.Placement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPlacementProduct', 'From Heidi Table dbo.PlacementStructure', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementStructure"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.RiskStructure"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"]')
  , ('BPStaging.LoadPlacementRiskDefinitionElement', 'ExtractDefinition Table dbo.Placement', '["BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.Placement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPlacementStatus', 'Uses BPStaging.PlacementStatus', '["BPStaging.PlacementStatus"]')
  , ('BPStaging.LoadPlacementStructure', 'ExtractDefinition Table dbo.Placement', '["BPStaging.LoadPlacement"
            ,"BPStaging.RiskStructure"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPlacementSystemAudit', 'FK dbo.PlacementSystemTable', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.PlacementSystemAudit"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPlacementsystemtable', 'ExtractDefinition Table BPStaging.PlacementSystemTable', '["BPStaging.PlacementSystemTable"]')
  , ('BPStaging.LoadPlacementSystemUser', 'FK Reference.Worker', '["BPStaging.PlacementSystemUser"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoWorker"
            ,"ReferenceStaging.MergeIntoWorkerAccount"]')
  , ('BPStaging.LoadPlacementTasks', 'ExtractDefinition Table dbo.Placement', '["BPStaging.LoadPlacement"
            ,"BPStaging.PlacementTasks"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPlacementTeamMember', 'FK ref.ServicingRole', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.PlacementTeamMember"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPlacementTeams', 'ExtractDefinition Table dbo.Placement', '["BPStaging.Load_ref_Team"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.PlacementTeams"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.LoadPremiumAdjustableIndicator', 'ExtractDefinition Table BPStaging.PremiumAdjustableIndicator', '["BPStaging.PremiumAdjustableIndicator"]')
  , ('BPStaging.LoadPremiumType', 'ExtractDefinition Table BPStaging.PremiumType', '["BPStaging.PremiumType"]')
  , ('BPStaging.LoadProductsFromElements', 'FK Reference.Product', '["BPStaging.ElementTagInclusionRule"
            ,"BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"ReferenceStaging.MergeIntoProduct"]')
  , ('BPStaging.Load_ref_MarketKind', 'ExtractDefinition Table BPStaging.MarketKind', '["BPStaging.MarketKind"]')
  , ('BPStaging.Load_ref_OpportunityType', 'ExtractDefinition Table BPStaging.OpportunityType', '["BPStaging.OpportunityType"]')
  , ('BPStaging.Load_ref_ProgramStructureType', 'ExtractDefinition Table BPStaging.ProgramStructureType', '["BPStaging.ProgramStructureType"]')
  , ('BPStaging.LoadRegion', 'Uses BPStaging.Region', '["BPStaging.Region"]')
  , ('BPStaging.LoadRegulatoryClientClassification', 'ExtractDefinition Table BPStaging.RegulatoryClientClassification', '["BPStaging.RegulatoryClientClassification"]')
  , ('BPStaging.LoadResponseType', 'ExtractDefinition Table BPStaging.ResponseType', '["BPStaging.ResponseType"]')
  , ('BPStaging.LoadRiskClassification', 'ExtractDefinition Table BPStaging.RiskClassification', '["BPStaging.RiskClassification"]')
  , ('BPStaging.LoadRiskCode', 'ExtractDefinition Table BPStaging.RiskCode', '["BPStaging.RiskCode"]')
  , ('BPStaging.LoadRiskLocation', 'ExtractDefinition Table BPStaging.RiskLocation', '["BPStaging.RiskLocation"]')
  , ('BPStaging.LoadScope', 'FK ref.PlacementStatus', '["BPStaging.LoadBrokingSubSegment"
            ,"BPStaging.Scope"
            ,"CMSStaging.Load_ref_PlacementStatus"]')
  , ('BPStaging.LoadSelectedReason', 'ExtractDefinition Table BPStaging.SelectedReason', '["BPStaging.SelectedReason"]')
  , ('BPStaging.LoadServiceLevel', 'Load depends on copy', '["BPStaging.ServiceLevel"]')
  , ('BPStaging.LoadServiceLevelIssue', 'Load depends on copy', '["BPStaging.ServiceLevelIssue"]')
  , ('BPStaging.LoadServicingRole', 'FK Reference.DataSourceInstance', '["BPStaging.Role"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('BPStaging.LoadSpecification', 'FK dbo.Element', '["BPStaging.Load_PS_Element"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.Specification"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('BPStaging.Load_PS_NegotiationMarketContract', 'ExtractDefinition Table BPStaging.SubmissionContainerMarketContract', '["BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.SubmissionContainerMarketContract"]')
  , ('BPStaging.LoadSubmissionCoverageGroup', 'FK dbo.CoverageGroup', '["BPStaging.LoadCoverageGroup"
            ,"BPStaging.SubmissionCoverageGroup"]')
  , ('BPStaging.LoadSubmissionPortalDetails', 'ExtractDefinition Table BPStaging.SubmissionPortalDetails', '["BPStaging.SubmissionPortalDetails"]')
  , ('BPStaging.LoadTaxCode', 'ExtractDefinition Table BPStaging.TaxCode', '["BPStaging.TaxCode"]')
  , ('BPStaging.LoadTaxRateBasis', 'ExtractDefinition Table BPStaging.TaxRateBasis', '["BPStaging.TaxRateBasis"]')
  , ('BPStaging.LoadTeamMember', 'FK ref.ServicingRole', '["BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.TeamMember"]')
  , ('BPStaging.LoadTerritory', 'ExtractDefinition Table BPStaging.Territory', '["BPStaging.Territory"]')
  , ('BPStaging.LoadTerritoryCountry', 'ExtractDefinition Table BPStaging.TerritoryCountry', '["BPStaging.TerritoryCountry"]')
  , ('BPStaging.LoadTimeZoneRepresention', 'ExtractDefinition Table BPStaging.TimeZoneRepresention', '["BPStaging.TimeZoneRepresention"]')
  , ('BPStaging.LoadUserScope', 'FK ref.ServicingRole', '["BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.UserScope"]')
  , ('BPStaging.LoadValue', 'AzurePolicyDataDownloadSpec.dtsx Table BPStaging.Value', '["BPStaging.Value"]')
  , ('BPStaging.LoadValueType', 'AzurePolicyDataDownloadSpec.dtsx Table BPStaging.ValueType', '["BPStaging.ValueType"]')
  , ('BPStaging.LoadValueTypeLookupValue', 'AzurePolicyDataDownloadSpec.dtsx Table BPStaging.ValueTypeLookupValue', '["BPStaging.ValueTypeLookupValue"]')
  , ('BPStaging.UpdateMarketSelectionIsLeadFlag', 'Market Interaction table load', '["BPStaging.LoadCarrierResponse"
            ,"BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadJustificationReasonType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadSelectedReason"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('BPStaging.UpdatePlacement', 'FK Reference.DataSourceInstance', '["BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.LoadRiskLocation"
            ,"CMSStaging.Load_ref_PlacementStatus"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_RefInsuranceType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('BPStaging.UpdatePlacementDatesAndStatus', 'Uses dbo.Placement', '["BPStaging.UpdatePlacement"]')
  , ('BPStaging.UpdatePlacementRiskDefinitionElement', 'ExtractDefinition Table dbo.ElementCache', '["BPStaging.LoadPlacementRiskDefinitionElement"]')
  , ('CAM PS.Carrier', 'Load PS.LoadCarrierHierarchyExtended table', '["PS.LoadCarrierHierarchyExtended"]')
  , ('CAM PS.ElementTagInclusionRuleLink', 'Load BPStaging.ElementTagInclusionRuleLink table', '["BPStaging.ElementTagInclusionRule"]')
  , ('CAM PS.Facility', 'Load ref.Facility table', '["PACTStaging.LoadFacility"]')
  , ('CAM PS.FacilitySection', 'Load ref.Facility table', '["CAM PS.Facility"
            ,"PACTStaging.LoadFacilitySection"]')
  , ('CAM PS.FacilitySectionCarrier', 'Load ref.Facility table', '["CAM PS.FacilitySection"
            ,"CAM PS.MergeFacilitySectionData"
            ,"PACTStaging.LoadFacilitySectionCarrier"]')
  , ('CAM PS.Lookup - BrokingRegion', 'Load PS.Lookup', '["BPStaging.LoadRegion"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - BrokingSegment', 'Load PS.Lookup', '["BPStaging.LoadBrokingSegment"
            ,"CAM PS.Lookup - BrokingRegion"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - BrokingSubSegment', 'Load PS.Lookup', '["BPStaging.LoadBrokingSubSegment"
            ,"CAM PS.Lookup - BrokingSegment"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - Geography', 'Load PS.Lookup', '["CAM PS.Lookup - Product"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoCountrySubdivision"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoGeographyGroup"
            ,"ReferenceStaging.MergeIntoGeographyGroupingScheme"
            ,"ReferenceStaging.MergeIntoGeographyGroupMembership"]')
  , ('CAM PS.Lookup - Industry', 'Load PS.Lookup', '["CAM PS.Lookup - BrokingSubSegment"
            ,"COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoIndustry"
            ,"ReferenceStaging.MergeIntoIndustrySector"
            ,"ReferenceStaging.MergeIntoIndustrySubSector"
            ,"ReferenceStaging.MergeIntoISIC4Industry"
            ,"ReferenceStaging.MergeIntoSIC87Industry"]')
  , ('CAM PS.Lookup - InsuranceType', 'Load PS.Lookup', '["CAM PS.Lookup - Industry"
            ,"PASStaging.Load_ref_RefInsuranceType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - Product', 'Load PS.Lookup', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadProductsFromElements"
            ,"CAM PS.Lookup - InsuranceType"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.MergeProductIntoProgrammeStore"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"]')
  , ('CAM PS.Lookup - RiskClassOfBusiness', 'Load PS.Lookup', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"CAM PS.Lookup - Geography"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - RiskCoverageType', 'Load PS.Lookup', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"CAM PS.Lookup - RiskTrigger"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - RiskIndustry', 'Load PS.Lookup', '["BPStaging.Load_ref_Industry"
            ,"CAM PS.Lookup - RiskCoverageType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - RiskProduct', 'Load PS.Lookup', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"CAM PS.Lookup - RiskIndustry"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.Lookup - RiskTrigger', 'Load PS.Lookup', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"CAM PS.Lookup - RiskClassOfBusiness"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('CAM PS.MergeData', 'Load PS.Carrier table', '["CAM PS.Carrier"]')
  , ('CAM PS.MergeFacilityData', 'Load PS.Facility table', '["CAM PS.Facility"]')
  , ('CAM PS.MergeFacilitySectionCarrierData', 'Load PS.FacilitySectionCarrier table', '["CAM PS.FacilitySectionCarrier"]')
  , ('CAM PS.MergeFacilitySectionData', 'Load PS.FacilitySection table', '["CAM PS.FacilitySection"
            ,"CAM PS.MergeFacilityData"]')
  , ('CAM PS.MergeLinkedElementData', 'Load PS.ElementTagInclusionRuleLink table', '["CAM PS.ElementTagInclusionRuleLink"]')
  , ('CAM PS.MergeLookups', 'Load PS.Lookup table', '["CAM PS.Lookup - RiskProduct"]')
  , ('CAM PS.MergeUserGroups', 'Load PS.UserGroups table', '["CAM PS.UserGroups"]')
  , ('CAMStaging.Load_ref_Panel', 'Load_CAM_Master Table CAMStaging.Panel', '["CAMStaging.Panel"]')
  , ('CAMStaging.Load_ref_PanelMember', 'Load_CAM_Master Table CAMStaging.PanelMemberCarrier', '["CAMStaging.Load_ref_Panel"
            ,"CAMStaging.PanelMember"
            ,"CAMStaging.PanelMemberCarrier"]')
  , ('CAMStaging.Load_ref_PanelMemberCarrier', 'Load_CAM_Master Table ref.PanelMember', '["CAMStaging.Load_ref_PanelMember"
            ,"CAMStaging.PanelMemberCarrier"]')
  , ('CAMStaging.Load_ref_PanelMemberContact', 'Load_CAM_Master Table ref.PanelMember', '["CAMStaging.Load_ref_PanelMember"
            ,"CAMStaging.PanelMemberContact"]')
  , ('CAMStaging.Load_ref_PanelMemberFacility', 'ExtractDefinition Table CAMStaging.PanelMemberFacility', '["CAMStaging.Load_ref_PanelMember"
            ,"CAMStaging.PanelMemberFacility"]')
  , ('CMAStaging.LoadCarrierQIData', 'CMAStaging.LoadCarrierQIData needs CMAStaging.LoadQITeamMapping', '["CMAStaging.ExecTable"
            ,"CMAStaging.LoadQITeamMapping"]')
  , ('CMAStaging.LoadQITeamMapping', 'CMAStaging.LoadQITeamMapping needs CMAStaging.QITeamMapping', '["CMAStaging.QITeamMapping"]')
  , ('CMSStaging.AppetiteResponse', 'CMSStaging Copy - Stream 2', '["CMSStaging.QuoteOutcome"]')
  , ('CMSStaging.DistributionChannel', 'CMSStaging Copy - Stream 3', '["CMSStaging.BUHierarchy"]')
  , ('CMSStaging.ExceptionType', 'CMSStaging Copy - Stream 4', '["CMSStaging.BusinessType"]')
  , ('CMSStaging.ExposureMeasure', 'CMSStaging Copy - Stream 5', '["CMSStaging.CompanyOwnership"]')
  , ('CMSStaging.GeographyGroupMembership', 'CMSStaging Copy - Stream 2', '["CMSStaging.Currency"]')
  , ('CMSStaging.Industry', 'CMSStaging Copy - Stream 3', '["CMSStaging.DistributionChannel"]')
  , ('CMSStaging.IndustryGroup', 'CMSStaging Copy - Stream 4', '["CMSStaging.ExceptionType"]')
  , ('CMSStaging.Layer', 'CMSStaging Copy - Stream 5', '["CMSStaging.ExposureMeasure"]')
  , ('CMSStaging.Load_dbo_Placement', 'LoadCMSData Table ref.PlacementStatus', '["BPStaging.LoadPlacement"
            ,"CMSStaging.Layer"
            ,"CMSStaging.Load_ref_PlacementStatus"
            ,"CMSStaging.Load_ref_Product"
            ,"CMSStaging.Policy"
            ,"CMSStaging.PolicyLayerLink"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_RefInsuranceType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('CMSStaging.Load_dbo_PlacementPolicy', 'LoadCMSData Table dbo.Policy', '["CMSStaging.Layer"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Policy"
            ,"CMSStaging.PolicyLayerLink"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"]')
  , ('CMSStaging.Load_ref_Industry', 'CMS Load Industry', '["CMSStaging.Industry"]')
  , ('CMSStaging.Load_ref_IndustryGroup', 'CMS Load Industry Group', '["CMSStaging.IndustryGroup"]')
  , ('CMSStaging.Load_ref_PlacementStatus', 'CMS Load Placement Status', '["CMSStaging.PlacementStatus"]')
  , ('CMSStaging.Load_ref_Product', 'FK Reference.Product', '["CMSStaging.Product"
            ,"CMSStaging.SubProduct"
            ,"ReferenceStaging.MergeIntoProduct"]')
  , ('CMSStaging.MarketingDecision', 'CMSStaging Copy - Stream 2', '["CMSStaging.GeographyGroupMembership"]')
  , ('CMSStaging.OutcomeReason', 'CMSStaging Copy - Stream 3', '["CMSStaging.Industry"]')
  , ('CMSStaging.Placement', 'CMSStaging Copy - Stream 4', '["CMSStaging.IndustryGroup"]')
  , ('CMSStaging.PlacementStatus', 'CMSStaging Copy - Stream 5', '["CMSStaging.Layer"]')
  , ('CMSStaging.Policy', 'CMSStaging Copy - Stream 2', '["CMSStaging.MarketingDecision"]')
  , ('CMSStaging.PolicyLayerLink', 'CMSStaging Copy - Stream 3', '["CMSStaging.OutcomeReason"]')
  , ('CMSStaging.PricingFactor', 'CMSStaging Copy - Stream 4', '["CMSStaging.Placement"]')
  , ('CMSStaging.Product', 'CMSStaging Copy - Stream 5', '["CMSStaging.PlacementStatus"]')
  , ('CMSStaging.QPF', 'CMSStaging Copy - Stream 1', '["CMSStaging.PlacementType"]')
  , ('CMSStaging.QuoteOutcome', 'CMSStaging Copy - Stream 2', '["CMSStaging.Policy"]')
  , ('CMSStaging.RenewabilityStatus', 'CMSStaging Copy - Stream 3', '["CMSStaging.PolicyLayerLink"]')
  , ('CMSStaging.RenewalStatus', 'CMSStaging Copy - Stream 4', '["CMSStaging.PricingFactor"]')
  , ('CMSStaging.RiskExposure', 'CMSStaging Copy - Stream 5', '["CMSStaging.Product"]')
  , ('CMSStaging.SubProduct', 'CMSStaging Copy - Stream 1', '["CMSStaging.QPF"]')
  , ('COLStaging.Load_dbo_PartyAttribute', 'LoadAll Table dbo.Party', '["COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"COLStaging.Tabela_ClientCaracs"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('COLStaging.Load_COLStaging_Product', 'ExtractDefinition Table COLStaging.CarrierProduct', '["COLStaging.CarrierProduct"]')
  , ('COLStaging.Load_dbo_Lookup', 'LoadAll Table COLStaging.Tabela_TiposCaracter', '["COLStaging.Load_dbo_LookupGroup"
            ,"COLStaging.Tabela_Caracterists"
            ,"COLStaging.Tabela_GruposHierarq"
            ,"COLStaging.Tabela_TiposCaracter"]')
  , ('COLStaging.Load_dbo_LookupGroup', 'LoadAll Table COLStaging.Tabela_TiposCaracter', '["COLStaging.Tabela_Caracterists"
            ,"COLStaging.Tabela_GruposHierarq"
            ,"COLStaging.Tabela_TiposCaracter"]')
  , ('COLStaging.Load_dbo_Product', 'FK Reference.Product', '["COLStaging.Load_COLStaging_Product"
            ,"PACTStaging.rpt_vwProduct"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"]')
  , ('COLStaging.Tabela_Caracterists', 'COLStaging.Tabela_Caracterists', '["COLStaging.CarrierProduct"]')
  , ('COLStaging.Tabela_ClientCaracs', 'COLStaging.Tabela_ClientCaracs', '["COLStaging.Tabela_Caracterists"]')
  , ('COLStaging.Tabela_Documentos', 'COLStaging.Tabela_Documentos', '["COLStaging.Tabela_ClientCaracs"]')
  , ('COLStaging.Tabela_GruposHierarq', 'COLStaging.Tabela_GruposHierarq', '["COLStaging.Tabela_Documentos"]')
  , ('COLStaging.Tabela_TiposCaracter', 'COLStaging.Tabela_TiposCaracter', '["COLStaging.Tabela_GruposHierarq"]')
  , ('COLStaging.Load_PS_ProductAttribute', 'Load PS ProductAttribute', '["COLStaging.Load_dbo_Product"]')
  , ('EPICStaging.MergeLineAgencyDefinedIntoPolicySectionAttribute', 'LoadAll Table Reference.DataSourceInstance', '["PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('ERD Mapping dbo.spMappingUpdateMDS_Br', 'Send Mappings to ERD', '["ERD Mapping ReferenceStaging.ServiceBusMDSInterimTable_Br"]')
  , ('ERD Mapping ReferenceStaging.ServiceBusMDSInterimTable_Br', 'Send Mappings to ERD', '["ERD Mapping ReferenceStaging.StageServiceBusMDSInterimTable_Br"]')
  , ('ERD Mapping ReferenceStaging.StageServiceBusMDSInterimTable_Br', 'Send Mappings to ERD', '["BPStaging.LoadProductsFromElements"
            ,"CMSStaging.Load_ref_Product"]')
  , ('ERD Mapping ReferenceStaging.UpdateSentForMappingDateOnProduct', 'Send Mappings to ERD', '["ERD Mapping dbo.spMappingUpdateMDS_Br"]')
  , ('FMATemp.StageAdditionalDataItem', 'PactConfig.ELTMetadata Query Reference.DataSourceInstance', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('FMATemp.StageAgency', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"SignetStaging.LoadRatingAgency"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageAppPlacementPolicy', 'PactConfig.ELTMetadata Table dbo.PlacementPolicyRelationshipType', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementPolicyRelationshipType"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"FMATemp ForceReload to BP"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"]')
  , ('FMATemp.StageCarrierAdditionalData', 'Table PAS.PartyAttribute', '["PASStaging.Load_PAS_PartyAttribute"]')
  , ('FMATemp.StageCarrierAgency', 'Wait for translation table to be cleared', '["Truncate FMATemp.TruncateTranslation"
            ,"WIBSStaging.Load_dbo_CarrierMapping"]')
    , ('FMATemp.StageCarrierAgencyRating', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierAgencyRating"
            ,"SignetStaging.LoadCarrierGroup"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageCarrierExtended', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"PS.LoadCarrierHierarchyExtended"]')
  , ('FMATemp.StageCarrierQIRating', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["CMAStaging.LoadCarrierQIData"
            ,"FMATemp.StageTranslation"]')
  , ('FMATemp.StageCarrierRating', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"SignetStaging.LoadCarrierRating"]')
  , ('FMATemp.StageCarrierRatingOutlookDefinition', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"SignetStaging.LoadCarrierRatingOutlook"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageCarrierRelationship', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.LoadCarrierHierarchyExtended"
            ,"PS.Load_dbo_Carrier"
            ,"PS.Load_dbo_CarrierMapping"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"WIBSStaging.Load_dbo_CarrierMapping"]')
    , ('FMATemp.StageCarrierRestriction', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierRestriction"]')
  , ('FMATemp.StageCarrierRestrictionDefinition', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"SignetStaging.LoadCarrierRestrictionDefinition"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageCarrierStatus', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"SignetStaging.LoadCarrierStatus"
            ,"Truncate FMATemp.TruncateTranslation"]')
    , ('FMATemp.StageCarrierStatusOverride', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierRestriction"
            ,"SignetStaging.LoadCarrierRestrictionDefinition"
            ,"SignetStaging.LoadCarrierStatus"]')
    , ('FMATemp.StageCarrierTOBA', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierTOBA"]')
  , ('FMATemp.StageCarrierType', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"]')
  , ('FMATemp.StageCountry', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageCountrySubdivision', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoCountrySubdivision"
            ,"ReferenceStaging.MergeIntoGeographyGroup"
            ,"ReferenceStaging.MergeIntoGeographyGroupingScheme"
            ,"ReferenceStaging.MergeIntoGeographyGroupMembership"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageCurrency', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageFacility', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"PACTStaging.LoadFacility"
            ,"PASStaging.Load_PAS_RefPolicyStatus"]')
  , ('FMATemp.StageFacilityCarrier', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageFacility"
            ,"FMATemp.StageTranslation"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.LoadFacilitySection"
            ,"PACTStaging.LoadFacilitySectionCarrier"
            ,"PASStaging.Load_PAS_RefPolicyStatus"]')
  , ('FMATemp.StageFacilitySection', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageFacility"
            ,"FMATemp.StageTranslation"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.LoadFacilitySection"
            ,"PASStaging.Load_PAS_RefPolicyStatus"]')
  , ('FMATemp.StageFacilitySectionCarrier', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageFacilitySection"
            ,"FMATemp.StageTranslation"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.LoadFacilitySection"
            ,"PACTStaging.LoadFacilitySectionCarrier"
            ,"PASStaging.Load_PAS_RefPolicyStatus"]')
  , ('FMATemp.StageIndustry', 'Wait for translation table to be cleared', '["CMSStaging.Load_ref_Industry"
            ,"CMSStaging.Load_ref_IndustryGroup"
            ,"COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"FMATemp.StageTranslation"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoIndustry"
            ,"ReferenceStaging.MergeIntoIndustrySector"
            ,"ReferenceStaging.MergeIntoIndustrySubSector"
            ,"ReferenceStaging.MergeIntoISIC4Industry"
            ,"ReferenceStaging.MergeIntoSIC87Industry"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageInsured', 'PactConfig.ELTMetadata Query Reference.PartyRole', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPartyRole"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StageLookup', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["COLStaging.Load_dbo_Lookup"
            ,"COLStaging.Load_dbo_LookupGroup"
            ,"FMATemp.StageTranslation"]')
  , ('FMATemp.StageLookupGroup', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"]')
  , ('FMATemp.StageOffice', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"]')
  , ('FMATemp.StageOfficeAddress', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"]')
  , ('FMATemp.StageParty', 'Uses APIv1.PartyHierarchy which needs ref.InsuredType', '["APIv1.PopulateAPIv1PartyAttributesTable"
            ,"BPStaging.Load_ref_InsuredType"
            ,"FMATemp.StageTranslation"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_PartyAttribute"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeIntoPartyRoleRelationship"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"]')
  , ('FMATemp.StagePartyAddress', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["APIv1.PopulateAPIv1PartyAttributesTable"
            ,"FMATemp.StageTranslation"
            ,"PACTStaging.MergeIntoPartyAddress"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_PartyAttribute"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoDboPartyAddress"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeIntoPartyRoleRelationship"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"]')
  , ('FMATemp.StagePartyCheckResult', 'Uses ReferenceStaging.rpt_vwPartyCheckResultHistory', '["ReferenceStaging.MergeIntoPartyCheckResultHistory"]')
  , ('FMATemp.StagePlacement', 'PactConfig.ELTMetadata Query Reference.DataSourceInstance', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadBrokingSubSegment"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"CMSStaging.Load_ref_PlacementStatus"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StagePlacementCarrier', 'PactConfig.ELTMetadata Query Reference.Party', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierStatus"]')
  , ('FMATemp.StagePlacementPolicy', 'PactConfig.ELTMetadata Table dbo.PlacementPolicyRelationshipType', '["BPStaging.LoadMergedPlacements"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementPolicyRelationshipType"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"FMATemp ForceReload to BP"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StagePlacementProducts', 'PactConfig.ELTMetadata Query PS.ElementTagSummary', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementProduct"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.LoadPlacementStructure"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.UpdatePlacement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StagePlacementTeams', 'PactConfig.ELTMetadata Query dbo.Policy', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StagePlacementUpdates', 'PactConfig.ELTMetadata Query dbo.Placement', '["BPStaging.LoadPlacement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StagePlacementWorkers', 'PactConfig.ELTMetadata Query Reference.WorkerAccount', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PASStaging.Load_PAS_ServicingRole"
            ,"PASStaging.Load_PAS_Worker"
            ,"ReferenceStaging.MergeIntoWorkerAccount"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StagePolicy', 'PactConfig.ELTMetadata Table ref.PartyRole', '["FMATemp ForceReload to BP"
            ,"PAS.Load_PS_Organisation"
            ,"PAS.Load_PS_OrganisationRelationship"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PAS.Load_APIv1_OrganisationHierarchyTable"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
    , ('FMATemp.StagePolicyCarrier', 'PactConfig.ELTMetadata Query Reference.Party', '["FMATemp ForceReload to BP"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarrierDetailsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierStatus"]')
  , ('FMATemp.StagePolicyProduct', 'PactConfig.ELTMetadata Table dbo.ProductAttribute', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionProductsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.MergeProductAttributeIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('FMATemp.StagePolicyStatus', 'Wait for translation table to be cleared', '["FMATemp.StageTranslation"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageProduct', 'Wait for translation table to be cleared', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadProductsFromElements"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"FMATemp.StageTranslation"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoLanguage"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageServicingPlatform', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"]')
  , ('FMATemp.StageTranslation', 'Wait for translation table to be cleared', '["BPStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_RefInsuranceType"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoISIC4Industry"
            ,"ReferenceStaging.MergeIntoLanguage"
            ,"ReferenceStaging.MergeIntoSIC87Industry"
            ,"SignetStaging.LoadCarrierRatingOutlook"
            ,"SignetStaging.LoadCarrierRestrictionDefinition"
            ,"Truncate FMATemp.TruncateTranslation"]')
  , ('FMATemp.StageUser', 'PactConfig.ELTMetadata Table FMATemp.staging_vw_Translation', '["FMATemp.StageTranslation"
            ,"ReferenceStaging.MergeIntoActiveDirectory"]')
  , ('MarineMar.CalculateContractEPI', 'MarineMar Table MarineMar.NegotiationMarket', '["MarineMar.Load_MarineMar_NegotiationMarket"
            ,"MarineMar.LoadMarketResponse"]')
  , ('MarineMar.Load_MarineMar_NegotiationMarket', 'MarineMar Table ref.LegalEntity', '["BPStaging.Load_dbo_PlacementMarketValidation"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_NegotiationContract"
            ,"BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadLegalEntity"
            ,"CAMStaging.Load_ref_Panel"
            ,"MarineMar.LoadPlacement"
            ,"MarineMar.LoadPolicy"
            ,"PACTStaging.LoadFacility"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('MarineMar.LoadContract', 'MarineMar Table Reference.Currency', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.LoadContractPolicy"
            ,"BPStaging.LoadContractStatus"
            ,"BPStaging.LoadLegalEntity"
            ,"MarineMar.LoadPlacement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"ReferenceStaging.MergeIntoCurrency"]')
  , ('MarineMar.LoadMarketResponse', 'MarineMar Table Reference.ExchangeRate', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.LoadDeclinationReason"
            ,"BPStaging.LoadOutcomeReason"
            ,"BPStaging.LoadOutcomeStatus"
            ,"BPStaging.LoadResponseType"
            ,"MarineMar.Load_MarineMar_NegotiationMarket"
            ,"PACTStaging.LoadFacilitySection"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoExchangeRate"]')
  , ('MarineMar.LoadPlacement', 'MarineMar Table ref.PlacementStatus', '["BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_ref_PlacementStatus"
            ,"PASStaging.Load_ref_OpportunityType"]')
  , ('MarineMar.LoadPlacementBroker', 'MarineMar Table ref.ServicingRole', '["BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadServicingRole"
            ,"MarineMar.LoadPlacement"]')
  , ('MarineMar.LoadPlacementClient', 'MarineMar Table ref.PartyRole', '["BPStaging.LoadPlacementPartyRole"
            ,"MarineMar.LoadPlacement"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('MarineMar.LoadPlacementClientExec', 'MarineMar Table ref.ServicingRole', '["BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadServicingRole"
            ,"MarineMar.LoadPlacement"]')
  , ('MarineMar.LoadPlacementInsured', 'MarineMar Table Reference.Country', '["BPStaging.LoadPlacementPartyRole"
            ,"MarineMar.LoadPlacement"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('MarineMar.LoadPlacementReinsured', 'MarineMar Table ref.PartyRole', '["BPStaging.LoadPlacementPartyRole"
            ,"MarineMar.LoadPlacement"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('MarineMar.LoadPolicy', 'MarineMar Table ref.LegalEntity', '["BPStaging.LoadLegalEntity"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"MarineMar.LoadContract"
            ,"MarineMar.LoadPlacement"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PAS.Load_APIv1_OrganisationHierarchyTable"]')
  , ('MarineMar.LoadPolicyAccountHandler', 'MarineMar Table MarineMar.Policy', '["MarineMar.LoadPolicy"
            ,"PACTStaging.MergePolicyWorkersIntoProgrammeStore"
            ,"PASStaging.Load_PAS_ServicingRole"
            ,"PASStaging.Load_PAS_Worker"]')
  , ('MarineMar.LoadRiskProfileClassOfBusiness', 'MarineMar Table PS.ElementTagSummary', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"MarineMar.LoadPlacement"]')
  , ('MarineMar.LoadRiskProfileCoverType', 'MarineMar Table PS.ElementTagSummary', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"MarineMar.LoadPlacement"]')
  , ('MarineMar.LoadRiskProfileIndustrySectorType', 'MarineMar Table PS.ElementTagSummary', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"MarineMar.LoadPlacement"]')
  , ('MarineMar.LoadRiskProfileLineOfBusiness', 'MarineMar Table PS.ElementTagSummary', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"MarineMar.LoadPlacement"]')
  , ('MarineMar.LoadRiskProfileNatureOfOperationInterest', 'MarineMar Table PS.ElementTagSummary', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"MarineMar.LoadPlacement"]')
  , ('MarineMar.LoadRiskProfileVesselType', 'MarineMar Table PS.ElementTagSummary', '["BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"MarineMar.LoadPlacement"]')
  , ('PACTStaging.Load_ref_FinancialStructureAttribute', 'ExtractDefinition Table PACTStaging.rpt_vwFinancialStructureAttribute', '["PACTStaging.rpt_vwFinancialStructureAttribute"]')
  , ('PACTStaging.LoadFacility', 'LoadAll Table ref.PartyRole', '["BPStaging.LoadLegalEntity"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyMarketIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PAS.Load_APIv1_OrganisationHierarchyTable"
            ,"PASStaging.Load_ref_PartyRole"
            ,"Eclipse.LoadUWGroupVersion"
            ,"Eclipse.LoadRole"
            ,"Eclipse.LoadPoolMember"]')
  , ('Eclipse.LoadRole', 'LoadAll Table EclipseStaging.Role', '["EclipseStaging.Role"]')
  , ('Eclipse.LoadUWGroupVersion', 'LoadAll Table Eclipse.UWGroupVersion', '["EclipseStaging.UWGroupVersion"]')
  , ('Eclipse.LoadPoolMember', 'LoadAll Table EclipseStaging.PoolMember', '["EclipseStaging.PoolMember"]')
  , ('PACTStaging.LoadFacilitySection', 'LoadAll Table ref.Facility', '["PACTStaging.LoadFacility"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"]')
  , ('PACTStaging.LoadFacilitySectionCarrier', 'LoadAll Table ref.PartyRole', '["PACTStaging.LoadFacilitySection"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarrierDetailsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.MergeClientUnderwriterPremiumIntoProgrammeStore', 'LoadAll Table PACTStaging.rpt_vwClientUnderwriterPremium', '["PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.rpt_vwClientUnderwriterPremium"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"]')
  , ('PACTStaging.MergeFacilityPoliciesIntoProgrammeStore', 'LoadAll Query ref.PartyRole', '["BPStaging.Load_ref_OpportunityType"
            ,"EclipseStaging.Policy"
            ,"EclipseStaging.PolicyBrokerOrder"
            ,"EclipseStaging.PolicyMarket"
            ,"EclipseStaging.Role"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.MergeIntoPartyAddress', 'ExtractDefinition Table Reference.Country', '["PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPartyAddress"
            ,"PASStaging.Load_PAS_Geography"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.MergePartyAttributestoParty', 'LoadAll Table dbo.PartyAttr', '["PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_PartyAttribute"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PACTStaging.MergePartyIntoProgrammeStore', 'LoadAll Query PACTStaging.rpt_vwParty', '["PACTStaging.rpt_vwParty"
            ,"PASStaging.Load_PAS_Geography"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PACTStaging.MergePoliciesIntoProgrammeStore', 'LoadAll Table PACTStaging.rpt_vwPolicy', '["BPStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_PAS_Currency"
            ,"PACTStaging.rpt_vwPolicy"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.Load_ref_FinancialGeography"
            ,"PASStaging.Load_ref_FinancialSegment"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"PASStaging.Load_ref_LegalEntity"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PACTStaging.MergePolicyAttributeIntoProgrammeStore', 'LoadAll Query Reference.DataSourceInstance', '["BPStaging.Load_ref_OpportunityType"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.rpt_vwAttribute"
            ,"PACTStaging.rpt_vwPolicyAttribute"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PASStaging.rpt_vwCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.MergePolicyCarrierDetailsIntoProgrammeStore', 'LoadAll Table dbo.PolicySection', '["EclipseStaging.Policy"
            ,"EclipseStaging.PolicyBrokerOrder"
            ,"EclipseStaging.PolicyMarket"
            ,"EclipseStaging.Role"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyMarketIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPolicyPartyRole"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_PAS_TransactionSummaryUSD"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.MergePolicyCarriersIntoProgrammeStore', 'LoadAll Table ref.PartyRole', '["EclipseStaging.Policy"
            ,"EclipseStaging.PolicyBrokerOrder"
            ,"EclipseStaging.PolicyMarket"
            ,"EclipseStaging.Role"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPolicyPartyRole"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PASStaging.Load_PAS_TransactionSummaryUSD"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.MergePolicyClientsIntoProgrammeStore', 'LoadAll Table Reference.DataSourceInstance', '["PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPolicyPartyRole"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.MergePolicyMarketIntoProgrammeStore', 'LoadAll Query EclipseStaging.Role', '["EclipseStaging.Policy"
            ,"EclipseStaging.PolicyBrokerOrder"
            ,"EclipseStaging.PolicyMarket"
            ,"EclipseStaging.Role"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_PAS_PolicyType"]')
  , ('PACTStaging.MergePolicyOrgsIntoProgrammeStore', 'LoadAll Table dbo.PolicyOrganisationRole', '["PAS.Load_PS_OrganisationRole"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPolicy"
            ,"PACTStaging.rpt_vwPolicyOrganisationRole"]')
  , ('PACTStaging.MergePolicySectionProductsIntoProgrammeStore', 'LoadAll Query PACTStaging.rpt_vwPolicySectionProduct', '["BPStaging.LoadProductsFromElements"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPolicySectionProduct"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"]')
  , ('PACTStaging.MergePolicySectionsIntoProgrammeStore', 'LoadAll Table PACTStaging.rpt_vwPolicySection', '["PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPolicySection"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PASStaging.Load_PAS_PolicySectionStatus"]')
  , ('PACTStaging.MergePolicyWorkersIntoProgrammeStore', 'LoadAll Query PACTStaging.rpt_vwPolicyServicingRole', '["PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.rpt_vwPolicyServicingRole"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_PAS_ServicingRole"]')
  , ('PACTStaging.MergeProductAttributeIntoProgrammeStore', 'LoadAll Query dbo.Product', '["COLStaging.CarrierProduct"
            ,"PACTStaging.MergeProductIntoProgrammeStore"
            ,"PACTStaging.rpt_vwProductAttribute"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PACTStaging.MergeProductIntoProgrammeStore', 'Serialised to avoid deadlocks', '["BPStaging.LoadProductsFromElements"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_COLStaging_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.rpt_vwProduct"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"]')
    , ('PACTStaging.rpt_vwPolicyAttribute', 'LoadAll Query PACTStaging.rpt_vwPolicy', '["PACTStaging.rpt_vwPolicy"]')
    , ('PACTStaging.UpdatePolicyTacitConsecutiveCount', 'Serialised to avoid deadlocks', '["BPStaging.Load_ref_OpportunityType"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PACTStaging.UpdatePolicyWithAttributes', 'LoadAll Query Reference.DataSourceInstance', '["BPStaging.Load_ref_OpportunityType"
            ,"PACTStaging.MergePolicyAttributeIntoProgrammeStore"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PACTStaging.UpdatePolicyWithCUP', 'LoadAll Table Reference.Currency', '["BPStaging.Load_ref_OpportunityType"
            ,"PACTStaging.MergeClientUnderwriterPremiumIntoProgrammeStore"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PACTStaging.UpdatePolicyWithEclipseAuthorizedDatePolicyAttribute', 'Load Policy Eclipse Authorized date into dbo.PolicyAtrribute', '["PACTStaging.MergePoliciesIntoProgrammeStore"]')
  , ('PACTStaging.UpdateProductWithAttributes', 'LoadAll Table Reference.DataSourceInstance', '["PACTStaging.MergeProductAttributeIntoProgrammeStore"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"]')
  , ('PACTStaging.UpdateProductWithParentProduct', 'Update parent product', '["PACTStaging.MergeProductIntoProgrammeStore"]')
  , ('PAS.Load_APIv1_OrganisationHierarchyTable', 'PAS Merge OrganisationHierarchyTable', '["PAS.Load_PS_Organisation"
        ,"PASStaging.Load_PAS_Organisation"
        ,"PASStaging.Load_PAS_OrganisationAttribute"]')
  , ('PAS.Load_PS_Organisation', 'PAS Merge PS.Organisation', '["PASStaging.Load_PAS_Organisation"]')
  , ('PAS.Load_PS_OrganisationAddress', 'PAS Merge PS.OrganisationAddress', '["PAS.Load_PS_Organisation"
            ,"PASStaging.Load_PAS_OrganisationAddress"]')
  , ('PAS.Load_PS_OrganisationRelationship', 'PAS Merge PS.OrganisationRelationship', '["PAS.Load_PS_Organisation"
            ,"PASStaging.Load_PAS_Organisation"
            ,"PASStaging.Load_PAS_OrganisationAttribute"]')
  , ('PAS.Load_PS_OrganisationRole', 'PAS Merge PS.OrganisationRole', '["PASStaging.Load_PAS_OrganisationRole"]')
  , ('PAS.Load_PS_Worker', 'PAS Merge PS.Worker', '["PASStaging.Load_PAS_Worker"]')
  , ('PASStaging.Load_dbo_Carrier', 'PASStaging Merge Carrier', '["PASStaging.rpt_vwCarrierHierarchy"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('PASStaging.Load_dbo_Carrier_GlobalParent', 'PASStaging Merge Carrier Global', '["PASStaging.rpt_vwCarrierHierarchy"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('PASStaging.Load_dbo_Carrier_OperatingCompany', 'PASStaging Merge Carrier OperatingCompany', '["PASStaging.rpt_vwCarrierHierarchy"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('PASStaging.Load_PAS_Currency', 'ExtractDefinition Table PASStaging.rpt_vwCurrency', '["PASStaging.rpt_vwCurrency"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_FinancialGeography', 'PASStaging Merge FinancialGeography', '["PASStaging.rpt_vwFinancialGeography"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_FinancialSegment', 'PASStaging Merge FinancialSegment', '["PASStaging.rpt_vwFinancialSegment"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_Geography', 'ExtractDefinition Table PASStaging.rpt_vwGeography', '["PASStaging.rpt_vwGeography"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_InsuranceType', 'FK PAS.RefInsuranceType', '["PASStaging.Load_PAS_RefInsuranceType"
            ,"PASStaging.rpt_vwInsuranceType"]')
  , ('PASStaging.Load_PAS_LegalEntity', 'PASStaging Merge LegalEntity', '["PASStaging.rpt_vwLegalEntity"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_OpportunityType', 'PASStaging Merge OpportunityType', '["PASStaging.rpt_vwOpportunityType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_Organisation', 'PASStaging Merge Orgs', '["PASStaging.rpt_vwOrganisation"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_OrganisationAddress', 'Create Organisation Address', '["PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_Organisation"
            ,"PASStaging.rpt_vwOrganisationAddress"]')
  , ('PASStaging.Load_PAS_OrganisationAttribute', 'PASStaging Merge Org Attributes', '["PASStaging.Load_PAS_Organisation"
            ,"PASStaging.rpt_vwOrganisationAttribute"]')
  , ('PASStaging.Load_PAS_OrganisationRole', 'ExtractDefinition Table PASStaging.rpt_vwOrganisationRole', '["PASStaging.rpt_vwOrganisationRole"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_Party', ' PASStaging Merge Party', '["PASStaging.rpt_vwParty"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_PartyAddress', 'ExtractDefinition Table Reference.Country', '["PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_Party"
            ,"PASStaging.rpt_vwPartyAddress"]')
  , ('PASStaging.Load_PAS_PartyAttribute', ' PASStaging Merge PartyAttribute', '["PASStaging.Load_PAS_Party"
            ,"PASStaging.rpt_vwPartyAttribute"]')
  , ('PASStaging.Load_PAS_PartyRole', ' PASStaging Merge PartyRole', '["PASStaging.rpt_vwPartyRole"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_Policy', ' PASStaging Merge Policy', '["PASStaging.Load_PAS_Currency"
            ,"PASStaging.Load_PAS_FinancialGeography"
            ,"PASStaging.Load_PAS_FinancialSegment"
            ,"PASStaging.Load_PAS_InsuranceType"
            ,"PASStaging.Load_PAS_LegalEntity"
            ,"PASStaging.Load_PAS_OpportunityType"
            ,"PASStaging.Load_PAS_Organisation"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.rpt_vwPolicy"]')
  , ('PASStaging.Load_PAS_PolicyAttribute', 'Create PAS PolicyAttribute', '["PASStaging.Load_PAS_Policy"
            ,"PASStaging.rpt_vwPolicyAttribute"]')
  , ('PASStaging.Load_PAS_PolicyOrganisationRole', 'Create PAS PolicyOrganisationRole', '["PASStaging.Load_PAS_Organisation"
            ,"PASStaging.Load_PAS_OrganisationRole"
            ,"PASStaging.Load_PAS_Policy"
            ,"PASStaging.rpt_vwPolicyOrganisationRole"]')
  , ('PASStaging.Load_PAS_PolicyPartyRole', 'Create PAS PolicyPartyRole', '["PASStaging.Load_PAS_Party"
            ,"PASStaging.Load_PAS_PartyRole"
            ,"PASStaging.Load_PAS_Policy"
            ,"PASStaging.rpt_vwPolicyPartyRole"]')
  , ('PASStaging.Load_PAS_PolicySection', 'Create PAS PolicySection', '["PASStaging.Load_PAS_Policy"
            ,"PASStaging.Load_PAS_PolicySectionStatus"
            ,"PASStaging.rpt_vwPolicySection"]')
  , ('PASStaging.Load_PAS_PolicySectionAttribute', 'Create PAS PolicySectionAttribute', '["PASStaging.Load_PAS_PolicySection"
            ,"PASStaging.rpt_vwPolicySectionAttribute"]')
  , ('PASStaging.Load_PAS_PolicySectionProduct', 'Create PAS PolicySectionProduct', '["PASStaging.Load_PAS_PolicySection"
            ,"PASStaging.Load_PAS_Product"
            ,"PASStaging.rpt_vwPolicySectionProduct"]')
  , ('PASStaging.Load_PAS_PolicySectionStatus', 'FK Reference.DataSourceInstance', '["PASStaging.rpt_vwPolicySectionStatus"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_PolicyStatus', 'FK Reference.DataSourceInstance', '["PASStaging.Load_PAS_RefPolicyStatus"
            ,"PASStaging.rpt_vwPolicyStatus"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_PolicyType', 'FK Reference.DataSourceInstance', '["PASStaging.rpt_vwPolicyType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_PolicyWorkerRole', 'Create PAS PolicyWorkerRole', '["PASStaging.Load_PAS_ServicingRole"
            ,"PASStaging.Load_PAS_Policy"
            ,"PASStaging.Load_PAS_Worker"
            ,"PASStaging.rpt_vwPolicyWorkerRole"]')
  , ('PASStaging.Load_PAS_Product', ' PASStaging Merge Product', '["PASStaging.rpt_vwProduct"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_ProductAttribute', 'Create PAS Product Attribute', '["PASStaging.Load_PAS_Product"
            ,"PASStaging.rpt_vwProductAttribute"]')
  , ('PASStaging.Load_PAS_RefInsuranceType', 'ExtractDefinition Table PASStaging.rpt_vwRefInsuranceType', '["PASStaging.rpt_vwRefInsuranceType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_RefPolicyStatus', 'ExtractDefinition Table PASStaging.rpt_vwRefPolicyStatus', '["PASStaging.rpt_vwRefPolicyStatus"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_ServicingRole', 'FK Reference.DataSourceInstance', '["PASStaging.rpt_vwServicingRole"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_PAS_TransactionSummaryUSD', 'LoadAll Table PASStaging.rpt_vwTransactionSummaryUSD', '["PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PASStaging.rpt_vwTransactionSummaryUSD"]')
  , ('PASStaging.Load_PAS_Worker', 'Create PAS Worker', '["PASStaging.rpt_vwWorker"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_ref_FinancialGeography', 'ExtractDefinition Table PASStaging.rpt_vwFinancialGeography', '["PASStaging.rpt_vwFinancialGeography"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_ref_FinancialSegment', 'ExtractDefinition Table PASStaging.rpt_vwFinancialSegment', '["PACTStaging.Load_ref_FinancialStructureAttribute"
            ,"PASStaging.rpt_vwFinancialSegment"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_ref_InsuranceType', 'FK ref.RefInsuranceType', '["PASStaging.rpt_vwInsuranceType"
            ,"PASStaging.Load_ref_RefInsuranceType"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('PASStaging.Load_ref_LegalEntity', 'ExtractDefinition Table PASStaging.rpt_vwLegalEntity', '["PASStaging.rpt_vwLegalEntity"]')
  , ('PASStaging.Load_ref_OpportunityType', 'ExtractDefinition Table PASStaging.rpt_vwOpportunityType', '["PASStaging.rpt_vwOpportunityType"]')
  , ('PASStaging.Load_ref_PartyRole', 'Uses PASStaging.rpt_vwPartyRole', '["PASStaging.rpt_vwPartyRole"]')
  , ('PASStaging.Load_ref_RefInsuranceType', 'ExtractDefinition Table PACTStaging.rpt_vwRefInsuranceType', '["PASStaging.rpt_vwRefInsuranceType"]')
  , ('PS.Load_dbo_Carrier', 'LoadAll Table Reference.Party', '["PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_PartyAttribute"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"]')
  , ('PS.Load_dbo_CarrierMapping', 'LoadAll Table dbo.CarrierMapping', '["PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('PS.Load_PS_ContractClauseAndCondition', 'Loading PS.ContractClauseAndCondition', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementTagGroup"]')
  , ('PS.Load_PS_ContractEndorsementClauseAndCondition', 'Loading PS.ContractEndorsementClauseAndCondition', '["BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.Load_PS_ContractEndorsementAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementTagGroup"]')
  , ('PS.Load_PS_ContractProgrammeClauseAndCondition', 'Loading PS.ContractProgrammeClauseAndCondition', '["BPStaging.Load_PS_ContractDocumentBase"
            ,"BPStaging.Load_PS_ContractProgrammeAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementTagGroup"]')
  , ('PS.Load_PS_ContractVersionClauseAndCondition', 'Loading PS.ContractVersionClauseAndCondition', '["BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractVersionAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementTagGroup"]')
  , ('PS.Load_PS_FinancialSegmentHierarchyTable', 'Create FinancialSegment Hierarchy', '["PACTStaging.Load_ref_FinancialStructureAttribute"
            ,"PASStaging.Load_ref_FinancialSegment"]')
    , ('PS.LoadCarrierHierarchyExtended', 'From Heidi Table Reference.Party', '["PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"PS.Load_dbo_CarrierMapping"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"
            ,"SignetStaging.ApplyCarrierDataMasking"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierStatus"
            ,"WIBSStaging.Load_dbo_CarrierMapping"]')
  , ('PS.LoadElementTimeline', 'Uses', '["BPStaging.Load_dbo_ElementChangeSet"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.Load_PS_ElementTag"
            ,"BPStaging.Load_PS_ExposureElement"
            ,"BPStaging.Load_PS_ExposureElementAttribute"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_MarketResponseElement"
            ,"BPStaging.Load_PS_MarketResponseElementAttribute"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_NegotiationSpecification"
            ,"BPStaging.Load_PS_PlacementExposureGroup"
            ,"BPStaging.Load_PS_Specification"
            ,"BPStaging.Load_PS_SpecificationElement"
            ,"BPStaging.Load_PS_SpecificationElementAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeDelta"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.LoadElementTagDelta"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.UpdatePlacement"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"]')
  , ('PS.LoadGlobalPartySecurity', 'LoadAll Table ref.PartyRole', '["BPStaging.LoadPlacementPartyRole"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PS.LoadPlacementSecurity"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('PS.LoadPlacementSecurity', 'LoadAll Table dbo.UserScope', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.LoadScope"
            ,"BPStaging.LoadTeamMember"
            ,"BPStaging.LoadUserScope"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('PS.UpdateTranslationLanguage', 'Event for BP Load Event FMATemp.staging_vw_Translation', '["FMATemp.StageAgency"
            ,"FMATemp.StageCarrierAgency"
            ,"FMATemp.StageCarrierAgencyRating"
            ,"FMATemp.StageCarrierRatingOutlookDefinition"
            ,"FMATemp.StageCarrierRestrictionDefinition"
            ,"FMATemp.StageCarrierStatus"
            ,"FMATemp.StageCountry"
            ,"FMATemp.StageCountrySubdivision"
            ,"FMATemp.StageCurrency"
            ,"FMATemp.StageIndustry"
            ,"FMATemp.StagePolicyStatus"
            ,"FMATemp.StageProduct"
            ,"FMATemp.StageTranslation"]')
  , ('ReferenceStaging.Load_Reference_ClientDetails', 'ExtractDefinition Table ReferenceStaging.rpt_vwClientDetails', '["ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"ReferenceStaging.rpt_vwClientDetails"]')
  , ('ReferenceStaging.MergeIntoActiveDirectory', 'ExtractDefinition Table ReferenceStaging.rpt_vwActiveDirectory', '["ReferenceStaging.rpt_vwActiveDirectory"]')
  , ('ReferenceStaging.MergeIntoAuthorizationStatus', 'Uses ReferenceStaging.rpt_vwAuthorizationStatus', '["ReferenceStaging.rpt_vwAuthorizationStatus"]')
  , ('ReferenceStaging.MergeIntoCheckType', 'Uses ReferenceStaging.rpt_vwCheckType', '["ReferenceStaging.rpt_vwCheckType"]')
  , ('ReferenceStaging.MergeIntoCity', 'FK Reference.Country', '["ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.rpt_vwCity"]')
  , ('ReferenceStaging.MergeIntoContactType', 'ExtractDefinition Table ReferenceStaging.wrk_vwContactType', '["ReferenceStaging.wrk_vwContactType"]')
  , ('ReferenceStaging.MergeIntoCountry', 'ExtractDefinition Table ReferenceStaging.rpt_vwCountry', '["ReferenceStaging.rpt_vwCountry"]')
  , ('ReferenceStaging.MergeIntoCountrySubdivision', 'ExtractDefinition Table ReferenceStaging.rpt_vwCountrySubdivision', '["ReferenceStaging.rpt_vwCountrySubdivision"]')
  , ('ReferenceStaging.MergeIntoCurrency', 'ExtractDefinition Table ReferenceStaging.rpt_vwCurrency', '["ReferenceStaging.rpt_vwCurrency"]')
  , ('ReferenceStaging.MergeIntoDataSource', 'Merge DataSource', '["ReferenceStaging.rpt_vwDataSource from WillisReference"]')
  , ('ReferenceStaging.MergeIntoDataSourceInstance', 'ExtractDefinition Table ReferenceStaging.rpt_vwDataSourceInstance', '["ReferenceStaging.MergeIntoDataSource"
            ,"ReferenceStaging.rpt_vwDataSourceInstance"]')
  , ('ReferenceStaging.MergeIntoDboPartyAddress', 'ExtractDefinition Table ReferenceStaging.rpt_vwClientDetails', '["PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergeIntoPartyAddress"
            ,"ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"ReferenceStaging.rpt_vwClientDetails"]')
  , ('ReferenceStaging.MergeIntoEmploymentStatus', 'ExtractDefinition Table ReferenceStaging.wrk_vwEmploymentStatus', '["ReferenceStaging.wrk_vwEmploymentStatus"]')
  , ('ReferenceStaging.MergeIntoExchangeRate', 'ExtractDefinition Table ReferenceStaging.rpt_vwExchangeRate', '["ReferenceStaging.rpt_vwExchangeRate"]')
  , ('ReferenceStaging.MergeIntoFinancialStructureMapping', 'ExtractDefinition Table ReferenceStaging.rpt_vwLegacyWTWFinancialStructureMapping', '["ReferenceStaging.rpt_vwLegacyWTWFinancialStructureMapping"]')
  , ('ReferenceStaging.MergeIntoGeographyGroup', 'FK Reference.GeographyGroupingScheme', '["ReferenceStaging.MergeIntoGeographyGroupingScheme"
            ,"ReferenceStaging.rpt_vwGeographyGroup"]')
  , ('ReferenceStaging.MergeIntoGeographyGroupingScheme', 'ExtractDefinition Table ReferenceStaging.rpt_vwGeographyGroupingScheme', '["ReferenceStaging.rpt_vwGeographyGroupingScheme"]')
  , ('ReferenceStaging.MergeIntoGeographyGroupMembership', 'FK Reference.GeographyGroup', '["ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.MergeIntoGeographyGroup"
            ,"ReferenceStaging.rpt_vwGeographyGroupMembership"]')
  , ('ReferenceStaging.MergeIntoIndustry', 'ExtractDefinition Table ReferenceStaging.rpt_vwWillisIndustry', '["ReferenceStaging.rpt_vwWillisIndustry"]')
  , ('ReferenceStaging.MergeIntoIndustrySector', 'FK Reference.Industry', '["ReferenceStaging.MergeIntoIndustry"
            ,"ReferenceStaging.rpt_vwWillisIndustrySector"]')
  , ('ReferenceStaging.MergeIntoIndustrySubSector', 'FK Reference.IndustrySector', '["ReferenceStaging.MergeIntoIndustrySector"
            ,"ReferenceStaging.rpt_vwWillisIndustrySubsector"]')
  , ('ReferenceStaging.MergeIntoISIC4Industry', 'ExtractDefinition Table ReferenceStaging.rpt_vwISIC4Industry', '["ReferenceStaging.rpt_vwISIC4Industry"]')
  , ('ReferenceStaging.MergeIntoLanguage', 'ExtractDefinition Table ReferenceStaging.rpt_vwLanguage', '["ReferenceStaging.rpt_vwLanguage"]')
  , ('ReferenceStaging.MergeIntoLegalEntity', 'ExtractDefinition Table ReferenceStaging.rpt_vwLegalEntity', '["ReferenceStaging.rpt_vwLegalEntity"]')
  , ('ReferenceStaging.MergeIntoOffice', 'ExtractDefinition Table ReferenceStaging.rpt_vwOffice', '["ReferenceStaging.rpt_vwOffice"]')
  , ('ReferenceStaging.MergeIntoParty', 'FK Reference.Segmentation', '["ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoIndustrySubSector"
            ,"ReferenceStaging.MergeIntoSegmentation"
            ,"ReferenceStaging.MergeIntoSIC87Industry"
            ,"ReferenceStaging.rpt_vwClientDetails"]')
  , ('ReferenceStaging.MergeIntoPartyCheckResultHistory', 'Uses ReferenceStaging.rpt_vwPartyCheckResultHistory', '["ReferenceStaging.MergeIntoAuthorizationStatus"
            ,"ReferenceStaging.MergeIntoCheckType"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeIntoPartyVerificationStatus"
            ,"ReferenceStaging.rpt_vwPartyCheckResultHistory"]')
  , ('ReferenceStaging.MergeIntoPartyExternalReference', 'Uses ReferenceStaging.rpt_vwPartyExternalReference', '["ReferenceStaging.rpt_vwPartyExternalReference"]')
  , ('ReferenceStaging.MergeIntoPartyRole', 'ExtractDefinition Table ReferenceStaging.rpt_vwPartyRole', '["ReferenceStaging.rpt_vwPartyRole"]')
  , ('ReferenceStaging.MergeIntoPartyRoleRelationship', 'FK Reference.PartyRole', '["ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.rpt_vwPartyRoleRelationship"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"]')
  , ('ReferenceStaging.MergeIntoPartyVerificationStatus', 'Uses ReferenceStaging.rpt_vwPartyVerificationStatus', '["ReferenceStaging.rpt_vwPartyVerificationStatus"]')
  , ('ReferenceStaging.MergeIntoProduct', 'FK Reference.ProductLine', '["ReferenceStaging.MergeIntoProductLine"
            ,"ReferenceStaging.rpt_vwProduct"]')
  , ('ReferenceStaging.MergeIntoProductClass', 'ExtractDefinition Table ReferenceStaging.rpt_vwProductClass', '["ReferenceStaging.rpt_vwProductClass"]')
  , ('ReferenceStaging.MergeIntoProductLine', 'FK Reference.ProductClass', '["ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.rpt_vwProductLine"]')
  , ('ReferenceStaging.MergeIntoProductMapping', 'ExtractDefinition Table Reference.ProductMapping', '["ReferenceStaging.rpt_vwProductMapping"]')
  , ('ReferenceStaging.MergeIntoSegmentation', 'ExtractDefinition Table ReferenceStaging.rpt_vwSegmentation', '["ReferenceStaging.rpt_vwSegmentation"]')
  , ('ReferenceStaging.MergeIntoSIC87Industry', 'ExtractDefinition Table ReferenceStaging.rpt_vwSIC87Industry', '["ReferenceStaging.rpt_vwSIC87Industry"]')
  , ('ReferenceStaging.MergeIntoWorker', 'FK Reference.Country', '["ReferenceStaging.MergeIntoCountry"
            ,"ReferenceStaging.wrk_vwWorker"]')
  , ('ReferenceStaging.MergeIntoWorkerAccount', 'ExtractDefinition Table ReferenceStaging.wrk_vwWorkerAccount', '["ReferenceStaging.MergeIntoWorker"
            ,"ReferenceStaging.wrk_vwWorkerAccount"]')
  , ('ReferenceStaging.MergeIntoWorkerContact', 'FK Reference.ContactType', '["ReferenceStaging.MergeIntoContactType"
            ,"ReferenceStaging.MergeIntoWorker"
            ,"ReferenceStaging.wrk_vwWorkerContact"]')
  , ('ReferenceStaging.MergeIntoWTWFinancialGeography', 'ExtractDefinition Table ReferenceStaging.rpt_vwWTWFinancialGeography', '["ReferenceStaging.rpt_vwWTWFinancialGeography"]')
  , ('ReferenceStaging.MergeIntoWTWFinancialGeographyAddress', 'ExtractDefinition Table ReferenceStaging.rpt_vwWTWFinancialGeographyAddress', '["ReferenceStaging.rpt_vwWTWFinancialGeographyAddress"]')
  , ('ReferenceStaging.MergeIntoWTWFinancialSegment', 'ExtractDefinition Table ReferenceStaging.rpt_vwWTWFinancialSegment', '["ReferenceStaging.rpt_vwWTWFinancialSegment"]')
  , ('ReferenceStaging.MergeIntoWTWLegalEntity', 'ExtractDefinition Table ReferenceStaging.rpt_vwWTWLegalEntity', '["ReferenceStaging.rpt_vwWTWLegalEntity"]')
  , ('ReferenceStaging.MergeProductMappingIntoProduct', 'Load Product Mappings (product table)', '["BPStaging.LoadPlacementProduct"
            ,"ReferenceStaging.MergeIntoProductMapping"]')
  , ('ReferenceStaging.MergeReferencePartyIntoDboParty', 'Serialised to avoid deadlocks', '["PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_PAS_Geography"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoParty"
            ,"ReferenceStaging.UpdateOperatingRevenueUSDOnParty"]')
  , ('ReferenceStaging.UpdateOperatingRevenueUSDOnParty', 'RefStage Updated Op Rev', '["ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoExchangeRate"
            ,"ReferenceStaging.MergeIntoParty"]')
  , ('rpt.Load_rpt_ContractElementAttribute', 'rpt Load ContractElementAttribute', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacement"
            ,"rpt.Load_rpt_Contract"]')
  , ('rpt.Load_rpt_ContractLimit', 'rpt Load ContractLimit', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadCoverageBasis"
            ,"BPStaging.LoadDeductibleBasis"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"rpt.Load_rpt_Contract"]')
  , ('rpt.Load_rpt_ProgrammeContractLimit', 'rpt Load Load_rpt_ProgrammeContractLimit', '["BPStaging.Load_PS_ContractDocumentBase"
            ,"BPStaging.Load_PS_ContractProgrammeAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadCoverageBasis"
            ,"BPStaging.LoadDeductibleBasis"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"rpt.Load_rpt_Contract"]')
  , ('rpt.Load_rpt_ContractEndorsementLimit', 'rpt Load Load_rpt_ContractEndorsementLimit', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.Load_PS_ContractEndorsementAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadCoverageBasis"
            ,"BPStaging.LoadDeductibleBasis"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"rpt.Load_rpt_Contract"]')
  , ('rpt.Load_rpt_ContractVersionLimit', 'rpt Load Load_rpt_ContractVersionLimit', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractVersionAttribute"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadCoverageBasis"
            ,"BPStaging.LoadDeductibleBasis"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"rpt.Load_rpt_Contract"]')
  , ('rpt.Load_rpt_MarketResponseElementAttribute', 'rpt Load MarketResponseElementAttribute', '["ReferenceStaging.MergeIntoExchangeRate"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"BPStaging.Load_ref_ElementAttributeReferenceOption"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadCoverageBasis"
            ,"BPStaging.Load_ref_ProgramStructureType"
            ,"BPStaging.Load_PS_MarketResponseElement"
            ,"BPStaging.Load_PS_MarketResponseElementAttribute"
            ,"BPStaging.Load_PS_MarketResponse"]')
  , ('rpt.Load_rpt_PlacementExposure', 'rpt Load PlacementExposure', '["BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.Load_PS_RiskProfile"]')
  , ('rpt.Load_rpt_SubmissionDistribution', 'Loads data around submission distributions', '["BPStaging.Load_BP_SubmissionMarketRecipient"
            ,"BPStaging.Load_BP_SubmissionMarket"
            ,"BPStaging.Load_BP_Submission"
            ,"BPStaging.Load_BP_SubmissionPortalUser"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_SubmissionPortalSummary"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadSubmissionPortalDetails"
            ,"PS.LoadCarrierHierarchyExtended"]')
  , ('rpt.LoadAuditUser', 'Load_rpt_Master Table dbo.PlacementSystemUser', '["BPStaging.LoadCarrierResponse"
            ,"BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadMarketSelection"
            ,"BPStaging.LoadOutcomeReason"
            ,"BPStaging.LoadOutcomeStatus"
            ,"BPStaging.LoadPlacementSystemAudit"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadSelectedReason"
            ,"BPStaging.UpdateMarketSelectionIsLeadFlag"]')
  , ('rpt.Load_rpt_CarrierMatching', 'CarrierMatching Table rpt.SubmissionMarketSelection', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarrierDetailsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"PASStaging.Load_ref_PartyRole"
            ,"rpt.Load_rpt_MarketInteractionResponse"
            ,"rpt.Load_rpt_SubmissionMarketSelection"]')
  , ('rpt.Load_rpt_Contract', 'Load_rpt_Master Table ref.EndorsementStatus', '["BPStaging.ContractRenewedFrom"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractDocumentBase"
            ,"BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.Load_PS_NegotiationMarketContract"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadContractPolicy"
            ,"BPStaging.LoadContractStatus"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadDocumentTemplateElement"
            ,"BPStaging.LoadElementBranch"
            ,"BPStaging.LoadEndorsementStatus"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.UpdatePlacement"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"BPStaging.Load_BP_ContractRiskCode"]')
  , ('rpt.LoadContractAuditUser', 'Load_rpt_Master Table rpt.ContractTimeline', '["BPStaging.Load_rpt_ContractTimeline"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('rpt.LoadContractDocument', 'Load_rpt_Master Table rpt.Contract', '["BPStaging.ContractDocumentBase"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.Load_PS_NegotiationMarketContract"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadContractEndorsementDocumentElement"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementBranch"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"rpt.Load_rpt_Contract"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('rpt.Load_rpt_Market', 'Load_rpt_Master Table ref.MarketKind', '["BPStaging.Load_ref_MarketKind"
            ,"BPStaging.LoadCarrierResponse"
            ,"BPStaging.LoadMarketResponseSecurity"
            ,"BPStaging.LoadMarketSelection"
            ,"BPStaging.UpdateMarketSelectionIsLeadFlag"
            ,"CAMStaging.Load_ref_Panel"
            ,"CAMStaging.Load_ref_PanelMember"
            ,"PACTStaging.LoadFacility"
            ,"PACTStaging.LoadFacilitySection"
            ,"PACTStaging.LoadFacilitySectionCarrier"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"PS.LoadCarrierHierarchyExtended"]')
  , ('rpt.Load_rpt_MarketActivity', 'Load_rpt_Master Table rpt.SubmissionMarketSelection', '["BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementPolicyRelationshipType"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"PACTStaging.MergeClientUnderwriterPremiumIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PS.LoadCarrierHierarchyExtended"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"rpt.Load_rpt_MarketInteractionResponse"
            ,"rpt.Load_rpt_SubmissionMarketSelection"]')
  , ('rpt.Load_rpt_MarketInteractionResponse', 'rpt Load MarketInteractionResponse', '["BPStaging.Load_BP_Submission"
            ,"BPStaging.Load_dbo_SubmissionDistribution"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponseBasis"
            ,"BPStaging.Load_PS_MarketQuoteResponseHistoric"
            ,"BPStaging.Load_PS_MarketQuoteResponseLegacy"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_ref_FollowType"
            ,"BPStaging.LoadCarrierResponse"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadDeclinationReason"
            ,"BPStaging.LoadLayerType"
            ,"BPStaging.LoadMarketResponseSecurity"
            ,"BPStaging.LoadOutcomeReason"
            ,"BPStaging.LoadOutcomeStatus"
            ,"BPStaging.LoadPendingActionReason"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementStatus"
            ,"BPStaging.LoadResponseType"
            ,"BPStaging.LoadSpecification"
            ,"PACTStaging.LoadFacilitySectionCarrier"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoExchangeRate"
            ,"rpt.LoadProductHierarchy"]')
  , ('rpt.Load_rpt_PlacementSecurity', 'Load_rpt_Master Table dbo.Scope', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.LoadScope"
            ,"BPStaging.UpdatePlacement"]')
  , ('rpt.Load_rpt_PolicyAudit', 'Load_rpt_Master Table Reference.PartyRole', '["BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.LoadBrokingSubSegment"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadContractPolicy"
            ,"BPStaging.LoadLegalEntity"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"COLStaging.Tabela_Documentos"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyCarriersIntoProgrammeStore"
            ,"PACTStaging.MergePolicyClientsIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionProductsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.MergeProductAttributeIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"PAS.Load_APIv1_OrganisationHierarchyTable"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PASStaging.Load_ref_RefInsuranceType"
            ,"ReferenceStaging.MergeIntoCurrency"
            ,"ReferenceStaging.MergeIntoExchangeRate"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"Rules.EnabledDataSources_Run_IncludeForRenewal"]')
  , ('rpt.Load_rpt_SubmissionMarketSelection', 'rpt Load_rpt_SubmissionMarketSelection', '["BPStaging.Load_dbo_PlacementMarketValidation"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_ref_MarketKind"
            ,"BPStaging.LoadCarrierResponse"
            ,"BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadJustificationReasonType"
            ,"BPStaging.LoadPlacement"
            ,"PACTStaging.LoadFacility"
            ,"PS.LoadCarrierHierarchyExtended"
            ,"PS.Load_dbo_Carrier"
            ,"rpt.Load_rpt_SubmissionDistribution"]')
  , ('rpt.Load_rpt_Organisation', 'Load a hierarchy of Policy Organisation', '["PAS.Load_PS_Organisation"
            ,"PASStaging.Load_PAS_OrganisationAttribute"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"rpt.Load_rpt_SubmissionMarketSelection"]')
  , ('rpt.Load_rpt_RiskAttribute', 'Load hierarchy of Placement RiskAttribute', '["BPStaging.Load_ref_ElementType"
            ,"BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"rpt.Load_rpt_SubmissionMarketSelection"]')
  , ('rpt.Load_rpt_UserSecurity', 'Load_rpt_Master Table dbo.UserScope', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadScope"
            ,"BPStaging.LoadTeamMember"
            ,"BPStaging.LoadUserScope"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"]')
  , ('rpt.LoadPlacementPrimaryRole', 'Load_rpt_Master Table Reference.PartyRole', '["BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementPartyRole"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_ref_PartyRole"
            ,"ReferenceStaging.MergeIntoPartyRole"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"]')
  , ('rpt.LoadPlacementProductSummary', 'Load_rpt_Master Table ref.ResponseType', '["BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_NegotiationRiskProfile"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.LoadResponseType"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.UpdatePlacement"
            ,"CMSStaging.Load_dbo_Placement"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.LoadFacilitySection"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"]')
  , ('rpt.LoadProductHierarchy', 'Load_rpt_Master Table Reference.ProductLine', '["BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadProductsFromElements"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"ReferenceStaging.MergeIntoDataSourceInstance"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"]')
  , ('Rules.ClearSegmentationRules', 'Rules.EnabledDataSources_Run_IncludeForRenewal', '["COLStaging.Load_dbo_Product"
            ,"EPICStaging.MergeLineAgencyDefinedIntoPolicySectionAttribute"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PASStaging.Load_PAS_Currency"
            ,"PACTStaging.MergeIntoPartyAddress"
            ,"PACTStaging.MergePolicyWorkersIntoProgrammeStore"
            ,"PACTStaging.MergeProductAttributeIntoProgrammeStore"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"PAS.Load_APIv1_OrganisationHierarchyTable"
            ,"PASStaging.Load_PAS_Geography"
            ,"PASStaging.Load_PAS_PolicySectionStatus"
            ,"PASStaging.Load_PAS_PolicyStatus"
            ,"PASStaging.Load_PAS_PolicyType"
            ,"PASStaging.Load_PAS_RefPolicyStatus"
            ,"PASStaging.Load_PAS_ServicingRole"
            ,"PASStaging.Load_PAS_TransactionSummaryUSD"
            ,"PASStaging.Load_PAS_Worker"
            ,"PASStaging.Load_ref_FinancialGeography"
            ,"PASStaging.Load_ref_FinancialSegment"
            ,"PASStaging.Load_ref_InsuranceType"
            ,"PASStaging.Load_ref_LegalEntity"
            ,"PASStaging.Load_ref_OpportunityType"
            ,"PASStaging.Load_ref_PartyRole"
            ,"PASStaging.Load_ref_RefInsuranceType"]')
  , ('Rules.EnabledDataSources_Run_IncludeForRenewal', 'Rule Processing', '["Rules.Run_PlacementRenewal"]')
  , ('Rules.Run_PlacementRenewal', 'Rule Processing', '["Rules.Run_SegmentationRules"]')
  , ('Rules.Run_SegmentationRules', 'Rule Processing', '["Rules.ClearSegmentationRules"]')
  , ('Send Data Refresh Message', 'Send Data Refresh Message', '["BPStaging.Load_BP_ContractEndorsementElementChangeSet"
            ,"BPStaging.Load_dbo_ElementChangeSet"
            ,"BPStaging.Load_dbo_ElementTagCache"
            ,"BPStaging.Load_dbo_PlacementMarketValidation"
            ,"BPStaging.Load_dbo_SubmissionDistribution"
            ,"BPStaging.Load_dbo_SubmissionMarketDocuments"
            ,"BPStaging.Load_PS_AppetiteNarrative"
            ,"BPStaging.Load_PS_AppetiteResponse"
            ,"BPStaging.Load_PS_ClientProfile"
            ,"BPStaging.Load_PS_ClientProposal"
            ,"BPStaging.Load_PS_ClientProposalCoverage"
            ,"BPStaging.Load_PS_ClientProposalCoverageNotice"
            ,"BPStaging.Load_PS_ClientProposalPlacement"
            ,"BPStaging.Load_PS_ClientProposalQuoteComparison"
            ,"BPStaging.Load_PS_Contract"
            ,"BPStaging.Load_PS_ContractDocumentVersion"
            ,"BPStaging.Load_PS_ContractEndorsement"
            ,"BPStaging.Load_PS_Element"
            ,"BPStaging.Load_PS_ElementTag"
            ,"BPStaging.Load_PS_ElementTagSummary"
            ,"BPStaging.Load_PS_ExposureElement"
            ,"BPStaging.Load_PS_ExposureElementAttribute"
            ,"BPStaging.Load_PS_MarketQuoteResponse"
            ,"BPStaging.Load_PS_MarketQuoteResponseHistoric"
            ,"BPStaging.Load_PS_MarketQuoteResponseLegacy"
            ,"BPStaging.Load_PS_MarketQuoteResponsePolicyRef"
            ,"BPStaging.Load_PS_MarketResponse"
            ,"BPStaging.Load_PS_MarketResponseBasis"
            ,"BPStaging.Load_PS_MarketResponseElement"
            ,"BPStaging.Load_PS_MarketResponseElementAttribute"
            ,"BPStaging.Load_PS_Negotiation"
            ,"BPStaging.Load_PS_NegotiationContract"
            ,"BPStaging.Load_PS_NegotiationMarket"
            ,"BPStaging.Load_PS_NegotiationMarketContract"
            ,"BPStaging.Load_PS_NegotiationRiskProfile"
            ,"BPStaging.Load_PS_NegotiationSpecification"
            ,"BPStaging.Load_PS_PlacementExposureGroup"
            ,"BPStaging.Load_PS_QuoteComparison"
            ,"BPStaging.Load_PS_QuoteComparisonQuote"
            ,"BPStaging.Load_PS_RiskProfile"
            ,"BPStaging.Load_PS_RiskProfilePlacementExposureGroup"
            ,"BPStaging.Load_PS_RiskStructure"
            ,"BPStaging.Load_PS_RiskStructureContract"
            ,"BPStaging.Load_PS_RiskStructureMarketResponse"
            ,"BPStaging.Load_PS_RiskStructurePolicy"
            ,"BPStaging.Load_PS_RiskStructureSpecification"
            ,"BPStaging.Load_PS_Specification"
            ,"BPStaging.Load_PS_SpecificationElement"
            ,"BPStaging.Load_PS_SpecificationElementAttribute"
            ,"BPStaging.Load_PS_Staging_RiskStructurePolicy"
            ,"BPStaging.Load_PS_SubmissionPortalSummary"
            ,"BPStaging.Load_ref_ElementType"
            ,"BPStaging.Load_ref_MarketKind"
            ,"BPStaging.Load_ref_OpportunityType"
            ,"BPStaging.Load_ref_PolicyRefType"
            ,"BPStaging.Load_ref_ProgramStructureType"
            ,"BPStaging.LoadBespokeHeadingReason"
            ,"BPStaging.LoadBrokingSubSegment"
            ,"BPStaging.LoadCarrierResponse"
            ,"BPStaging.LoadContractDocumentElement"
            ,"BPStaging.LoadContractEndorsementDocumentElement"
            ,"BPStaging.LoadContractPolicy"
            ,"BPStaging.LoadContractRisk"
            ,"BPStaging.LoadContractSection"
            ,"BPStaging.LoadContractSectionBasis"
            ,"BPStaging.LoadContractSectionBasisType"
            ,"BPStaging.LoadContractSectionRiskCode"
            ,"BPStaging.LoadContractStatus"
            ,"BPStaging.LoadContractType"
            ,"BPStaging.LoadCoverage"
            ,"BPStaging.LoadCoverageBasis"
            ,"BPStaging.LoadCoverageGroup"
            ,"BPStaging.LoadCoverageGroupSection"
            ,"BPStaging.LoadCoverageType"
            ,"BPStaging.LoadDeclinationReason"
            ,"BPStaging.LoadDeductibleBasis"
            ,"BPStaging.LoadDeltaType"
            ,"BPStaging.LoadDocumentElement"
            ,"BPStaging.LoadElementAttribute"
            ,"BPStaging.LoadElementAttributeCache"
            ,"BPStaging.LoadElementAttributeDelta"
            ,"BPStaging.LoadElementAttributeType"
            ,"BPStaging.LoadElementBranch"
            ,"BPStaging.LoadElementCache"
            ,"BPStaging.LoadElementDelta"
            ,"BPStaging.LoadElementTagDelta"
            ,"BPStaging.LoadElementTagGroup"
            ,"BPStaging.LoadElementTagType"
            ,"BPStaging.LoadEndorsementStatus"
            ,"BPStaging.LoadExtendedReportingPeriod"
            ,"BPStaging.LoadJustificationReason"
            ,"BPStaging.LoadJustificationReasonType"
            ,"BPStaging.LoadLayer"
            ,"BPStaging.LoadLegalEntity"
            ,"BPStaging.LoadLibraryElement"
            ,"BPStaging.LoadLinePercentageBasis"
            ,"BPStaging.LoadMarketInteractionFacility"
            ,"BPStaging.LoadMarketResponseSecurity"
            ,"BPStaging.LoadMarketSelection"
            ,"BPStaging.LoadMergedPlacements"
            ,"BPStaging.LoadNewClient"
            ,"BPStaging.LoadOutcomeReason"
            ,"BPStaging.LoadOutcomeStatus"
            ,"BPStaging.LoadParticipantFunction"
            ,"BPStaging.LoadPaymentPeriod"
            ,"BPStaging.LoadPendingActionReason"
            ,"BPStaging.LoadPlacement"
            ,"BPStaging.LoadPlacementExtension"
            ,"BPStaging.LoadPlacementOpportunity"
            ,"BPStaging.LoadPlacementPartyRole"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadPlacementPolicyRelationshipType"
            ,"BPStaging.LoadPlacementPremium"
            ,"BPStaging.LoadPlacementProduct"
            ,"BPStaging.LoadPlacementRiskDefinitionElement"
            ,"BPStaging.LoadPlacementStructure"
            ,"BPStaging.LoadPlacementSystemAudit"
            ,"BPStaging.LoadPlacementsystemtable"
            ,"BPStaging.LoadPlacementSystemUser"
            ,"BPStaging.LoadPlacementTasks"
            ,"BPStaging.LoadPlacementTeamMember"
            ,"BPStaging.LoadPlacementTeams"
            ,"BPStaging.LoadPremiumAdjustableIndicator"
            ,"BPStaging.LoadPremiumType"
            ,"BPStaging.LoadProductsFromElements"
            ,"BPStaging.LoadRegulatoryClientClassification"
            ,"BPStaging.LoadResponseType"
            ,"BPStaging.LoadRiskClassification"
            ,"BPStaging.LoadRiskCode"
            ,"BPStaging.LoadRiskLocation"
            ,"BPStaging.LoadScope"
            ,"BPStaging.LoadSelectedReason"
            ,"BPStaging.LoadServicingRole"
            ,"BPStaging.LoadSpecification"
            ,"BPStaging.LoadSubmissionCoverageGroup"
            ,"BPStaging.LoadSubmissionPortalDetails"
            ,"BPStaging.LoadTaxCode"
            ,"BPStaging.LoadTaxRateBasis"
            ,"BPStaging.LoadTeamMember"
            ,"BPStaging.LoadTerritory"
            ,"BPStaging.LoadTerritoryCountry"
            ,"BPStaging.LoadTimeZoneRepresention"
            ,"BPStaging.LoadUserScope"
            ,"BPStaging.LoadValue"
            ,"BPStaging.LoadValueType"
            ,"BPStaging.LoadValueTypeLookupValue"
            ,"BPStaging.UpdatePlacementRiskDefinitionElement"
            ,"PACTStaging.UpdatePolicyWithEclipseAuthorizedDatePolicyAttribute"
            ,"PS.Load_PS_ContractClauseAndCondition"
            ,"PS.Load_PS_ContractEndorsementClauseAndCondition"
            ,"PS.Load_PS_ContractProgrammeClauseAndCondition"
            ,"PS.Load_PS_ContractVersionClauseAndCondition"
            ,"Task.StartRun"]')
  , ('SHPACT.Carrier', 'Load PACT.Carrier table', '["SHStaging.Carrier_Ireland"
            ,"SHStaging.Carrier_Like"
            ,"SHStaging.CarrierAtrrole"
            ,"SHStaging.CarrierReferenceData"
            ,"SHStaging.CarrierSqk"
            ,"SHStaging.CarrierSqk_Brazil"
            ,"SHStaging.CarrierTransactionLedger"]')
  , ('SHPACT.Client', 'Load PACT.Client table', '["SHStaging.Client_Like"
            ,"SHStaging.Client_PartyRoleId"
            ,"SHStaging.ClientAtrrole"
            ,"SHStaging.ClientReference"
            ,"SHStaging.ClientSqk_Brazil"]')
  , ('SHStaging.ActiveDirectory', 'Wait for ActiveDirectory to be loaded', '["ReferenceStaging.MergeIntoActiveDirectory"]')
  , ('SHStaging.Carrier_Ireland', 'Load ETL.CarrierServiceHub table', '["SHStaging.CarrierSqk"]')
  , ('SHStaging.Carrier_Like', 'Load ETL.CarrierServiceHub table', '["SHStaging.CarrierSqk"]')
  , ('SHStaging.CarrierAtrrole', 'Load ETL.CarrierServiceHub table', '["SHStaging.CarrierSqk"]')
  , ('SHStaging.CarrierSqk_Brazil', 'Load ETL.CarrierServiceHub table', '["SHStaging.CarrierSqk"]')
  , ('SHStaging.CarrierTransactionLedger', 'Load ETL.CarrierServiceHub table', '["SHStaging.CarrierSqk"]')
  , ('SHStaging.CarrierReferenceData', 'Load ETL.CarrierServiceHub table', '["PS.LoadCarrierHierarchyExtended"]')
  , ('SHStaging.Client_Like', 'Load ETL.ClientServiceHub table', '["SHStaging.ClientSqk"]')
  , ('SHStaging.Client_PartyRoleId', 'Load ETL.ClientServiceHub table', '["SHStaging.ClientSqk"]')
  , ('SHStaging.ClientAtrrole', 'Load ETL.ClientServiceHub table', '["SHStaging.ClientSqk"]')
  , ('SHStaging.ClientReference', 'Load ETL.ClientServiceHub table', '["SHStaging.ClientSqk"]')
  , ('SHStaging.ClientSqk_Brazil', 'Load ETL.ClientServiceHub table', '["SHStaging.ClientSqk"]')
  , ('SHStaging.vw_ActiveDirectory', 'Load Reference.ActiveDirectory table', '["SHStaging.ActiveDirectory"]')
  , ('SHStaging.vw_DataSource', 'Wait for DataSource to be loaded', '["ReferenceStaging.MergeIntoDataSource"]')
  , ('SHStaging.vw_DataSourceInstance', 'Wait for DataSourceInstance to be loaded', '["ReferenceStaging.MergeIntoDataSourceInstance"]')
  , ('SHStaging.vw_WorkerAccount', 'Wait for WorkerAccount to be loaded', '["SHStaging.WorkerAccount"]')
  , ('SHStaging.WorkerAccount', 'Wait for WorkerAccount to be loaded', '["ReferenceStaging.MergeIntoWorkerAccount"]')
  , ('SignetStaging.ApplyCarrierDataMasking', 'Ensure we have loaded SignetCarriers before masking the data', '["SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"]')
  , ('SignetStaging.LoadApprovedCarrier', 'SP to load dbo.Carrier', '["ReferenceStaging.MergeIntoParty"
            ,"SignetStaging.AssociatedCarriersRIMS"
            ,"SignetStaging.LoadCarrier"]')
  , ('SignetStaging.LoadCarrier', 'Signet Query SignetStaging.TOBA', '["ReferenceStaging.MergeIntoDboPartyAddress"
            ,"SignetStaging.ApprovalStatus"
            ,"SignetStaging.ApprovalType"
            ,"SignetStaging.Carrier"
            ,"SignetStaging.CompanyType"
            ,"SignetStaging.FatcaReferences"
            ,"SignetStaging.GroupCode"
            ,"SignetStaging.TOBA"]')
    , ('SignetStaging.LoadCarrierAgencyRating', 'Signet Query SignetStaging.RatingAgencyData', '["PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierRating"
            ,"SignetStaging.LoadCarrierRatingOutlook"
            ,"SignetStaging.LoadRatingAgency"
            ,"SignetStaging.PropertyType"
            ,"SignetStaging.RatingAgencyData"]')
    , ('SignetStaging.LoadCarrierGroup', 'Signet Table SignetStaging.GroupCode', '["SignetStaging.GroupCode"]')
    , ('SignetStaging.LoadCarrierRating', 'Signet Query SignetStaging.PropertyType', '["SignetStaging.PropertyType"]')
    , ('SignetStaging.LoadCarrierRatingOutlook', 'Signet Query SignetStaging.PropertyType', '["SignetStaging.PropertyType"]')
    , ('SignetStaging.LoadCarrierRestriction', 'Signet Query SignetStaging.RestrictionCode', '["PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"ReferenceStaging.MergeIntoDboPartyAddress"
            ,"SignetStaging.Carrier"
            ,"SignetStaging.CarrierRestriction"
            ,"SignetStaging.GroupCode"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.LoadCarrierRestrictionDefinition"
            ,"SignetStaging.RestrictionCode"]')
    , ('SignetStaging.LoadCarrierRestrictionDefinition', 'Signet Query SignetStaging.RestrictionCode', '["SignetStaging.RestrictionCode"]')
    , ('SignetStaging.LoadCarrierStatus', 'Signet Query SignetStaging.ApprovalStatus', '["SignetStaging.ApprovalStatus"]')
    , ('SignetStaging.LoadCarrierTOBA', 'Signet Query SignetStaging.TOBA', '["PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_Carrier"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"SignetStaging.TOBA"]')
  , ('SignetStaging.LoadRatingAgency', 'Signet Query SignetStaging.RatingAgency', '["SignetStaging.RatingAgency"]')
  , ('Task.StartRun', 'PRP Task.StartRun - Preparing Tasks to process', '["BPStaging.CreatePlacementListener"
            ,"BPStaging.LoadPlacementPolicies"
            ,"BPStaging.LoadProductsFromElements"
            ,"CMSStaging.Load_dbo_PlacementPolicy"
            ,"CMSStaging.Load_ref_Product"
            ,"COLStaging.Load_dbo_Product"
            ,"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePoliciesIntoProgrammeStore"
            ,"PACTStaging.MergePolicyMarketIntoProgrammeStore"
            ,"PACTStaging.MergePolicyOrgsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionProductsIntoProgrammeStore"
            ,"PACTStaging.MergePolicySectionsIntoProgrammeStore"
            ,"PACTStaging.MergeProductIntoProgrammeStore"
            ,"PACTStaging.rpt_vwProductAttribute"
            ,"PACTStaging.UpdatePolicyTacitConsecutiveCount"
            ,"PACTStaging.UpdatePolicyWithAttributes"
            ,"PACTStaging.UpdatePolicyWithCUP"
            ,"PACTStaging.UpdateProductWithAttributes"
            ,"PACTStaging.UpdateProductWithParentProduct"
            ,"ReferenceStaging.MergeIntoProduct"
            ,"ReferenceStaging.MergeIntoProductClass"
            ,"ReferenceStaging.MergeIntoProductLine"]')
    , ('WIBSStaging.Load_dbo_CarrierMapping', 'LoadAll Table dbo.CarrierMapping', '["PACTStaging.MergePartyAttributestoParty"
            ,"PACTStaging.MergePartyIntoProgrammeStore"
            ,"PASStaging.Load_dbo_Carrier"
            ,"PASStaging.Load_dbo_Carrier_GlobalParent"
            ,"PASStaging.Load_dbo_Carrier_OperatingCompany"
            ,"PS.Load_dbo_CarrierMapping"
            ,"ReferenceStaging.MergeReferencePartyIntoDboParty"
            ,"SignetStaging.LoadCarrier"
            ,"SignetStaging.LoadCarrierGroup"
            ,"WIBSStaging.WIBS_PBANAMandCompPrd"]');

/* Check the JSON column is valid JSON */
IF EXISTS (SELECT * FROM #Depends WHERE ISJSON(DependsOnJsonArray) = 0)
BEGIN
    DECLARE c CURSOR LOCAL FOR
    SELECT ProcessName, InterdependencyType
    FROM
        #Depends
    WHERE
        ISJSON(DependsOnJsonArray) = 0;

    OPEN c;

    DECLARE @ProcessName NVARCHAR(100);
    DECLARE @InterdependencyType NVARCHAR(100);

    FETCH NEXT FROM c
    INTO @ProcessName, @InterdependencyType;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        RAISERROR('JSON Error: ProcessName:''%s''  (''%s'')', 0, 1, @ProcessName, @InterdependencyType) WITH NOWAIT;

        FETCH NEXT FROM c
        INTO @ProcessName, @InterdependencyType;
    END;

    RAISERROR('Error: JSON is not valid. JSON should be an array of strings only!', 15, 1) WITH NOWAIT;
END;

BEGIN TRY
    /* Convert into a Process and Dependent set of records. */
    DROP TABLE IF EXISTS #Dataset;

    SELECT Process = d.ProcessName, d.InterdependencyType, PrerequisteProcess = CAST(j.value AS NVARCHAR(100))
    INTO #Dataset
    FROM
        #Depends d
        CROSS APPLY OPENJSON(d.DependsOnJsonArray, '$') j;

    /* Added unique index to highlight duplicates quicker, and with names which is easier than seeing the IDs */
    CREATE UNIQUE INDEX IXU_#Dataset
    ON #Dataset
        (
        Process
      , PrerequisteProcess
        );

    /* These are LEFT JOINs so that an invalid name wont match and an error will occur when it tries to insert the record */
    MERGE ADF.ProcessInterdependency T
    USING
        (SELECT ProcessId = P.ProcessId, PrerequisiteProcessId = PP.ProcessId, D.InterdependencyType, IsDeleted = 0
         FROM
             #Dataset D
             LEFT JOIN ADF.Process P
                 ON D.Process = P.Name

             LEFT JOIN ADF.Process PP
                 ON D.PrerequisteProcess = PP.Name) S
    ON S.ProcessId = T.ProcessId
       AND S.PrerequisiteProcessId = T.PrerequisiteProcessId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT
                 (ProcessId, PrerequisiteProcessId, InterdependencyType)
             VALUES
                 (S.ProcessId, S.PrerequisiteProcessId, S.InterdependencyType)
    WHEN MATCHED AND NOT EXISTS
                             (SELECT T.InterdependencyType, T.IsDeleted INTERSECT SELECT S.InterdependencyType, S.IsDeleted)
        THEN UPDATE SET T.InterdependencyType = S.InterdependencyType, T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET T.IsDeleted = 1, T.ETLUpdatedDate = GETUTCDATE();
END TRY
BEGIN CATCH
    DECLARE @Type VARCHAR(500), @Detail VARCHAR(100);

    DECLARE c CURSOR LOCAL FOR
    SELECT 'Duplicate', '''' + d.Process + ''', ''' + d.PrerequisteProcess + ''''
    FROM
        #Dataset d
    GROUP BY
        d.Process, d.PrerequisteProcess
    HAVING
        COUNT(*) > 1
    UNION
    SELECT 'Missing Process', d.Process
    FROM
        #Dataset d
        LEFT JOIN ADF.Process p
            ON p.Name = d.Process
    WHERE
        p.ProcessId IS NULL
    UNION
    SELECT 'Missing Prerequisite Process', d.PrerequisteProcess
    FROM
        #Dataset d
        LEFT JOIN ADF.Process p
            ON p.Name = d.PrerequisteProcess
    WHERE
        p.ProcessId IS NULL
    ORDER BY
        2;

    OPEN c;

    FETCH NEXT FROM c
    INTO @Type, @Detail;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        RAISERROR('Error: %s - %s', 0, 1, @Type, @Detail) WITH NOWAIT;

        FETCH NEXT FROM c
        INTO @Type, @Detail;
    END;

    THROW;
END CATCH;
