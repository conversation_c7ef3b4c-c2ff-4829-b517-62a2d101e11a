﻿#------------------------------------------------------------------------------
# Sync Azure Analysis Services Models 
# Version: 1.0
#------------------------------------------------------------------------------

param(
    [string]$serverUrl,
    [string]$databaseName
)

# Validate required parameters
if ([string]::IsNullOrEmpty($serverUrl)) {
    Write-Error "ServerUrl parameter is required."
    exit 1
}

# Validate required parameters
if ([string]::IsNullOrEmpty($databaseName)) {
    Write-Error "DatabaseName parameter is required."
    exit 1
}
try {
    Write-Host "Parameters: -serverUrl $serverUrl -databaseName $databaseName"
    Write-Host "Synchronizing database '$databaseName' on server '$serverUrl'..."
    $syncResult = Sync-AzAnalysisServicesInstance -Instance $serverUrl -Database $databaseName -PassThru -ErrorAction Stop       
    if ($syncResult.SyncState -eq 'Completed') {
        Write-Host "Synchronization completed for database '$databaseName' on server '$serverUrl'"
    }
    else {
        Write-Error "Failed to synchronize database '$databaseName' on server '$serverUrl'"
    }
}
catch {
    Write-Error "Failed to Synchronize database '$databaseName' on server '$serverUrl'and the error is: $($_.Exception.Message)"
}
