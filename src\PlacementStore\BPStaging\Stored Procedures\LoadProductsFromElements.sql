/*
Lineage
dbo.Product.ProductKey=ref.ElementTagType.ElementTagTypeKey
dbo.Product.ProductKey=BPStaging.ElementTagInclusionRule.ElementTagTypeKey
dbo.Product.ProductKey=BPStaging.ElementTagInclusionRule.LinkedElementTagTypeKey
dbo.Product.ProductClass=ref.ElementTagType.ElementTagType
dbo.Product.ProductClass=BPStaging.ElementTagInclusionRule.Tag
dbo.Product.ProductLine=ref.ElementTagType.ElementTagType
dbo.Product.ProductLine=BPStaging.ElementTagInclusionRule.LinkedTag
dbo.Product.SourceLastUpdateDate=dbo.ElementCache.LastUpdatedUTCDate
dbo.Product.SourceLastUpdateDate=BPStaging.ElementTagInclusionRule.ETLUpdatedDate
*/
CREATE PROCEDURE BPStaging.LoadProductsFromElements
AS
/* 
        This stored procedure is run once the Elements are loaded to extract the Products contained
        in them.
        It also merges in the class and lines from the BPStaging.ElementTagInclusionRule as these are 
        also used and potential combinations.
        Note that the dbo.Product table already contains some products for 50366 so we have an additional
        BIT on the record SourcedFromBrokingPlatform to show these are from the Broking Platform so
        we can handle them seperately and also not send them to the BP with other records to the BP.
    */
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.Product';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @DataSourceInstanceId INT = 50366; /* Broking Platform */

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    ;
    WITH ElementTagAndType AS (
        SELECT
            et.ElementId
          , ett.ElementTagTypeId
          , ett.ElementTagTypeKey
          , Tag = ett.ElementTagType
          , ett.ElementTagGroupId
          , etg.ElementTagGroupKey
          , et.ElementBranchId
        FROM
            ref.ElementTagType ett
            INNER JOIN ref.ElementTagGroup etg
                ON etg.ElementTagGroupId = ett.ElementTagGroupId

            INNER JOIN dbo.ElementTagCache et
                ON et.ElementTagTypeId = ett.ElementTagTypeId
    )
    MERGE dbo.Product T
    USING (
        -- As this does a group by to remove duplicates I've not gone for the IsDeleted available on elemenent
        -- in case it doesnt apply to all. It currently runs quickly as is so going the brute force route.
        -- I do ignore the deleted elements."Deletion" will happen if the combination is no longer found
        -- in the target table.
        SELECT
            DataSourceInstanceId = @DataSourceInstanceId
          , x.SourcedFromBrokingPlatform
          , x.ProductKey
          , x.ProductClass
          , x.ProductLine
          , LastUpdatedUTCDate = MAX(x.LastUpdatedUTCDate)
        FROM (
        SELECT
            SourcedFromBrokingPlatform = CAST(1 AS BIT)
          , ProductKey = CONCAT(pc.ElementTagTypeKey, N'|', pl.ElementTagTypeKey)
          , ProductClass = pc.Tag
          , ProductLine = pl.Tag
          , LastUpdatedUTCDate = MAX(elem.LastUpdatedUTCDate)
        FROM
            ElementTagAndType pl
            JOIN dbo.ElementCache elem
                ON elem.ElementId = pl.ElementId
                   AND elem.ElementBranchId = pl.ElementBranchId

            LEFT JOIN ElementTagAndType pc
                ON pc.ElementId = elem.ElementId
                   AND pc.ElementBranchId = elem.ElementBranchId
                   AND pc.ElementTagGroupKey = 'classOfBusiness' -- To find LoB without a class.
        WHERE
            pl.ElementTagGroupKey = 'lineOfBusiness'
        GROUP BY
            pc.ElementTagTypeKey
          , pc.Tag
          , pl.ElementTagTypeKey
          , pl.Tag
        UNION ALL -- As we have a date on the lines it shouldn't make any difference if ALL or not.
        SELECT
            SourcedFromBrokingPlatform = CAST(1 AS BIT)
          , ProductKey = CONCAT(ElementTagTypeKey, N'|', LinkedElementTagTypeKey)
          , ProductClass = Tag
          , ProductLine = LinkedTag
          , LastUpdatedUTCDate = ETLUpdatedDate
        FROM
            BPStaging.ElementTagInclusionRule
        WHERE
            GroupKey = 'classOfBusiness'
            AND LinkedGroupKey = 'lineOfBusiness'
            AND SupportedLanguageId = 1 /* English only */
    ) x
        GROUP BY
            x.SourcedFromBrokingPlatform
          , x.ProductClass
          , x.ProductKey
          , x.ProductLine
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.SourcedFromBrokingPlatform = S.SourcedFromBrokingPlatform
       AND T.ProductKey = S.ProductKey

    /* New Products */
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 DataSourceInstanceId
               , ProductKey
               , ProductClass
               , ProductLine
               , SourcedFromBrokingPlatform
               , IsDeleted
               , CreatedUTCDate
               , LastUpdatedUTCDate
               , SourceLastUpdateDate
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.ProductKey
                   , S.ProductClass
                   , S.ProductLine
                   , S.SourcedFromBrokingPlatform
                   , 0
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.LastUpdatedUTCDate
                 )

    /* Updated Products */
    WHEN MATCHED AND NOT EXISTS (
                             SELECT S.ProductClass, S.ProductLine, 0 INTERSECT SELECT T.ProductClass, T.ProductLine, T.IsDeleted
                         )
        THEN UPDATE SET
                 T.ProductClass = S.ProductClass
               , T.ProductLine = S.ProductLine
               , T.IsDeleted = 0
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.SourceLastUpdateDate = S.LastUpdatedUTCDate

    /* Deleted Products */
    WHEN NOT MATCHED BY SOURCE AND T.DataSourceInstanceId = @DataSourceInstanceId
                                   AND T.SourcedFromBrokingPlatform = 1
                                   AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN;
