/*
Lineage
PS.RiskProfilePlacementExposureGroup.RiskProfilePlacementExposureGroupKey=BP.ExposureSummaryElementRiskDefinitionItem.Id
PS.RiskProfilePlacementExposureGroup.RiskProfilePlacementExposureGroupKey=BP.ExposureSummaryElementRiskDefinitionItem.ElementId
PS.RiskProfilePlacementExposureGroup.RiskProfilePlacementExposureGroupKey=BP.PlacementExposureSummary.Id
PS.RiskProfilePlacementExposureGroup.RiskProfileId=PS.RiskProfile.RiskProfileId
PS.RiskProfilePlacementExposureGroup.PlacementExposureGroupId=PS.PlacementExposureGroup.PlacementExposureGroupId
PS.RiskProfilePlacementExposureGroup.IsDeleted=BP.PlacementExposureSummary.IsDeleted
PS.RiskProfilePlacementExposureGroup.IsDeleted=BP.ExposureSummaryElement.IsDeleted
PS.RiskProfilePlacementExposureGroup.IsDeleted=BP.ExposureSummaryElementRiskDefinitionItem.IsDeleted
PS.RiskProfilePlacementExposureGroup.SourceUpdatedDate=BP.PlacementExposureSummary.ETLUpdatedDate
PS.RiskProfilePlacementExposureGroup.SourceUpdatedDate=BP.ExposureSummaryElement.ETLUpdatedDate
PS.RiskProfilePlacementExposureGroup.SourceUpdatedDate=BP.ExposureSummaryElementRiskDefinitionItem.ETLUpdatedDate
*/
CREATE PROCEDURE BPStaging.Load_PS_RiskProfilePlacementExposureGroup
    @Success BIT OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.RiskProfilePlacementExposureGroup';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #RiskProfilePlacementExposureGroup;

    SELECT
        DataSourceInstanceId = 50366
      , RiskProfilePlacementExposureGroupKey = CONCAT(N'ESERDI|', eserdi.Id, N'|', eserdi.ElementId, N'|', pes.Id)
      , rp.RiskProfileId
      , peg.PlacementExposureGroupId
      , IsDeleted = (pes.IsDeleted | ese.IsDeleted | eserdi.IsDeleted)
      , SourceUpdatedDate = (
            SELECT MAX(v) FROM (VALUES (pes.ETLUpdatedDate), (ese.ETLUpdatedDate), (eserdi.ETLUpdatedDate)) value (v)
        )
    INTO #RiskProfilePlacementExposureGroup
    FROM
        BP.PlacementExposureSummary pes
        INNER JOIN BP.ExposureSummaryElement ese
            ON ese.Id = pes.ExposureSummaryElementId

        INNER JOIN dbo.ElementBranch eb
            ON eb.ElementBranchId = pes.ElementBranchId
        --  AND eb.ClonedFromElementBranchId IS NULL --> Include only they haven't been cloned from last years placement.

        INNER JOIN BP.ExposureSummaryElementRiskDefinitionItem eserdi
            ON eserdi.ExposureSummaryElementId = ese.Id

        INNER JOIN PS.RiskProfile rp
            ON rp.RiskProfileKey = CONCAT(N'ELEM|', eserdi.ElementId)
               AND rp.DataSourceInstanceId = 50366

        INNER JOIN PS.PlacementExposureGroup peg
            ON peg.PlacementExposureGroupKey = CONCAT(N'PLEXPSUM|', pes.Id)
               AND peg.DataSourceInstanceId = 50366;

    MERGE PS.RiskProfilePlacementExposureGroup T
    USING (
        SELECT
            DataSourceInstanceId
          , RiskProfilePlacementExposureGroupKey
          , RiskProfileId
          , PlacementExposureGroupId
          , IsDeleted
          , SourceUpdatedDate
        FROM (
        SELECT
            DataSourceInstanceId
          , RiskProfilePlacementExposureGroupKey
          , RiskProfileId
          , PlacementExposureGroupId
          , IsDeleted
          , SourceUpdatedDate
          /* 
                        Did see a problem with duplicate records because it appears that you can delete a record in the BP 
                        and then create a new one with the same IDs. Seen for ExposureSummaryElementRiskDefinitionItem
                        but could potentially happen for any. This should handle them all here! 
                    */
          , RNum = ROW_NUMBER() OVER (PARTITION BY RiskProfileId, PlacementExposureGroupId ORDER BY IsDeleted ASC, SourceUpdatedDate DESC)
        FROM
            #RiskProfilePlacementExposureGroup
    ) inner_select
        WHERE
            RNum = 1
    ) S
    ON T.RiskProfileId = S.RiskProfileId
       AND T.PlacementExposureGroupId = S.PlacementExposureGroupId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , RiskProfilePlacementExposureGroupKey
               , RiskProfileId
               , PlacementExposureGroupId
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.RiskProfilePlacementExposureGroupKey
                   , S.RiskProfileId
                   , S.PlacementExposureGroupId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeleted
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.DataSourceInstanceId
                               , S.RiskProfilePlacementExposureGroupKey
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.DataSourceInstanceId
                               , T.RiskProfilePlacementExposureGroupKey
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RiskProfilePlacementExposureGroupKey = S.RiskProfilePlacementExposureGroupKey
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = CONCAT(N'Merge ', @TargetTable);

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;