CREATE TABLE rpt.PolicyAudit (
    DataSourceInstanceId     INT            NULL
  , PolicyId                 BIGINT         NOT NULL
  , PolicyReference          NVARCHAR(50)   NULL
  , PolicyDescription        NVARCHAR(500)  NULL
  , PolicyType               NVARCHAR(100)  NOT NULL
  , PolicyKey                NVARCHAR(101)  NULL
  , PolicyStatus             NVARCHAR(200)  NOT NULL
  , InceptionDate            DATETIME2(7)   NULL
  , InceptionDateKey         INT            NULL
  , OpportunityType          NVARCHAR(100)  NOT NULL
  , ExpiryDate               DATETIME2(7)   NULL
  , InsuranceType            NVARCHAR(250)  NOT NULL
  , IsDeleted                BIT            NOT NULL
  , CreatedUTCDate           DATE           NULL
  , CreationDateKey          INT            NULL
  , LastUpdatedUTCDate       DATETIME2(7)   NULL
  , IsFacility               BIT            NOT NULL
  , SumInsuredCurrency       NVARCHAR(3)    NULL
  , SumInsured               NUMERIC(18, 4) NULL
  , SumInsuredUSD            NUMERIC(38, 6) NULL
  , OrganisationId           INT            NULL
  , ExpiringPolicyId         BIGINT         NULL
  , ExpiringPolicyReference  NVARCHAR(50)   NULL
  , NewOrRenewal             NVARCHAR(7)    NOT NULL
  , ExpiringPolicyStatus     NVARCHAR(200)  NOT NULL
  , ExpiringPolicyKey        NVARCHAR(101)  NULL
  , RenewedToPolicyReference NVARCHAR(50)   NULL
  , RenewalPolicies          INT            NULL
  , SentToBP                 INT            NOT NULL
  , RuleId                   INT            NULL
  , InScope                  INT            NOT NULL
  , RuleName                 NVARCHAR(255)  NULL
  , RuleDescription          NVARCHAR(4000) NULL
  , LinkedToPlacement        INT            NOT NULL
  , RefPolicyStatus          NVARCHAR(250)  NULL
  , HasPlacement             INT            NOT NULL
  , CurrentPSPlacementId     BIGINT         NULL
  , CurrentBPPlacementId     NVARCHAR(100)  NULL
  , ExpiringPSPlacementId    BIGINT         NULL
  , ExpiringBPPlacementId    NVARCHAR(100)  NULL
  , BrokingSegmentName       NVARCHAR(100)  NOT NULL
  , BrokingRegionName        NVARCHAR(100)  NOT NULL
  , BrokingSubSegmentName    NVARCHAR(100)  NOT NULL
  , RenewalsEnabled          INT            NOT NULL
  , RenewalPlacement         INT            NOT NULL
  , RenewalSent              NVARCHAR(11)   NOT NULL
  , NewBusinessOnly          INT            NOT NULL
  , OutOfScope               INT            NOT NULL
  , IncomingGlobalBusiness   BIT            NULL
  , Backloaded               NVARCHAR(500)  NULL
  , ProductCode              NVARCHAR(MAX)  NULL
  , Proposta                 NVARCHAR(MAX)  NULL
  , IsMainPolicy             INT            NULL
  , COLRenewalDate           DATETIME2(7)   NULL
  , COLPolicyStatus          NVARCHAR(24)   NULL
  , TeamName                 NVARCHAR(200)  NOT NULL
  , COLCurrentPolicyStatus   NVARCHAR(36)   NULL
  , COLPlacementSystemId     NVARCHAR(100)  NULL
  , HasContract              BIT            NOT NULL
        DEFAULT 0
  , HasContractWording       BIT            NOT NULL
        DEFAULT 0
  , IsPrimaryParty           BIT            NOT NULL
        DEFAULT 0
  , GlobalPartyRole          NVARCHAR(100)  NULL
  , GlobalPartyRoleId        NVARCHAR(100)  NULL
  , PartyName                NVARCHAR(500)  NULL
  , BusinessKey              NVARCHAR(100)  NULL
  , GlobalPartyId            NVARCHAR(100)  NULL
  , CONSTRAINT PK_rpt_PolicyAudit
        PRIMARY KEY
        (
            PolicyId
        )
);
GO
