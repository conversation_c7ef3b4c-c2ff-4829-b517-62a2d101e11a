﻿using System.Reflection;

namespace PsDb.Tests.rpt.StoredProcedures;
public class Load_rpt_PolicyAuditTests : PlacementStoreTestBase
{
    [Fact]
    public void Load_rpt_PolicyAuditNoDataTest()
    {
        NoDataStoredProcedureTest(storedProcedureTestMethod: MethodBase.GetCurrentMethod());
    }

    [Fact]
    public void CheckCorrectParty()
    {
        DateTime dateTime = DateTime.Now;

        dynamic policyRecord = CreateRow(tableName: "dbo.Policy",
            values: new
            {
                PolicyId = 666,
                IsDeleted = 0,
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
                InceptionDate = DateTime.UtcNow,
                PactPolicyId = 1,
                SumInsured = 1000
            });


        // Create first party
        dynamic partyRecord1 = CreateRow(tableName: "dbo.Party", values: new
        {
            PartyId = 1000,
            PartyName = "WrongParty"
        });

        // Create first ref.partyRole
        dynamic refPartyRoleRecord1 = CreateRow(tableName: "ref.PartyRole", values: new
        {
            PartyRoleKey = "11",
            PartyRoleId = 11,
            GlobalPartyRoleId = (int)GlobalPartyRole.Insured //MUST be the same as refPartyRoleRecord2.GlobalPartyRoleId
        });

        // Create first policy party relationship
        dynamic policyPartyRelationship1 = CreateRow(tableName: "dbo.PolicyPartyRelationship", values: new
        {
            PolicyId = 666,
            PartyId = 1000,
            PartyRoleId = refPartyRoleRecord1.PartyRoleId,
            IsPrimaryParty = 1, //MUST be the same as policyPartyRelationship2.IsPrimaryParty
            ETLUpdatedDate = dateTime,
            IsDeleted = 0
        });

        // Create second party
        dynamic partyRecord2 = CreateRow(tableName: "dbo.Party", values: new
        {
            PartyId = 2000,
            PartyName = "RightParty"
        });

        // Create second ref.partyRole
        dynamic refPartyRoleRecord2 = CreateRow(tableName: "ref.PartyRole", values: new
        {
            PartyRoleId = 22,
            PartyRoleKey = "22",
            GlobalPartyRoleId = (int)GlobalPartyRole.Insured //MUST be the same as refPartyRoleRecord1.GlobalPartyRoleId
        });

        // Create second policy party relationship
        dynamic policyPartyRelationship2 = CreateRow(tableName: "dbo.PolicyPartyRelationship", values: new
        {
            PolicyId = 666,
            PartyId = 2000,
            PartyRoleId = refPartyRoleRecord2.PartyRoleId,
            IsPrimaryParty = 1, //MUST be the same as policyPartyRelationship1.IsPrimaryParty
            ETLUpdatedDate = dateTime,
            IsDeleted = 0
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_PolicyAudit");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_PolicyAudit", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PolicyAudit");
        Assert.NotNull(row);
        Assert.Equal(expected: partyRecord2.PartyName, actual: row.PartyName);
    }

    [Fact]
    public void CheckUsesRateOfExchangeTest()
    {
        dynamic currencyEurRecord = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "EUR",
            CurrencyId = 100248,
            CurrencyName = "Euro"
        });
        // Currency USD
        dynamic currencyUsdRecord = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "USD",
            CurrencyId = 100249,
            CurrencyName = "Dollars"
        });

        // Euro Exchange Rates
        CreateRow(tableName: "Reference.ExchangeRate", values: new
        {
            ExchangeRateId = 20,
            FromCurrencyId = currencyEurRecord.CurrencyId,
            ToCurrencyId = currencyUsdRecord.CurrencyId,
            EffectiveDate = "2023-10-31",
            ExchangeRateTypeId = (int)ExchangeRateType.AverageRate,
            IsDeleted = 0,
            ExchangeRate = 1.2
        });
        /* Should use the next one as latest */
        CreateRow(tableName: "Reference.ExchangeRate", values: new
        {
            ExchangeRateId = 30,
            FromCurrencyId = currencyEurRecord.CurrencyId,
            ToCurrencyId = currencyUsdRecord.CurrencyId,
            EffectiveDate = "2023-11-30",
            ExchangeRateTypeId = (int)ExchangeRateType.AverageRate,
            IsDeleted = 0,
            ExchangeRate = 1.3
        });
        CreateRow(tableName: "Reference.ExchangeRate", values: new
        {
            ExchangeRateId = 40,
            FromCurrencyId = currencyUsdRecord.CurrencyId,
            ToCurrencyId = currencyEurRecord.CurrencyId,
            EffectiveDate = "2024-02-29",
            ExchangeRateTypeId = (int)ExchangeRateType.AverageRate,
            IsDeleted = 0,
            ExchangeRate = 4.3
        });

        dynamic policyRecord = CreateRow(tableName: "dbo.Policy",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
                InceptionDate = DateTime.UtcNow,
                SumInsured = 1000,
                SumInsuredCurrencyId = currencyEurRecord.CurrencyId
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_PolicyAudit");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_PolicyAudit", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PolicyAudit");
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: policyRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: currencyEurRecord.CurrencyAlphaCode, actual: row.SumInsuredCurrency);
        Assert.Equal(expected: Convert.ToDecimal(1300), actual: Convert.ToDecimal(row.SumInsuredUSD));
    }
    [Fact]
    public void CheckUseOfPolicyOrgTest()
    {
        dynamic policyRecord = CreateRow(
    tableName: "dbo.Policy",
    values: new
    {
        PACTPolicyId = 13579,
        DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
        PolicyKey = "PolKey",
        PolicyReference = "polref",
        SourceUpdatedDate = DateTime.UtcNow,
        RefPolicyStatusId = 100,
        InceptionDate = DateTime.UtcNow,
    });
        dynamic organisationRecord = CreateRow(
           tableName: "PAS.Organisation",
           values: new
           {
               DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
               PASOrganisationId = 999,
               ParentKey = "OrgDeptKey",
               OrganisationKey = "OrgKey",
               Organisation = "TeamOrg"
           });
        dynamic psOrganisationRecord = CreateRow(
           tableName: "PS.Organisation",
           values: new
           {
               DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
               OrganisationKey = "OrgKey",
               OrganisationSK = 2
           });
        dynamic organisationDeptRecord = CreateRow(
            tableName: "PAS.Organisation",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
                PASOrganisationId = 1000,
                OrganisationKey = "OrgDeptKey",
                Organisation = "DeptOrg"
            });
        dynamic psOrganisationDeptRecord = CreateRow(
           tableName: "PS.Organisation",
           values: new
           {
               DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
               OrganisationKey = "OrgDeptKey",
               OrganisationSK = 1
           });
        dynamic organisationAttributeRecord = CreateRow(
            tableName: "PAS.OrganisationAttribute",
            values: new
            {
                OrganisationKey = organisationRecord.OrganisationKey,
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
                EclipseRole = "Team"
            });
        dynamic organisationDeptAttributeRecord = CreateRow(
          tableName: "PAS.OrganisationAttribute",
          values: new
          {
              OrganisationKey = organisationDeptRecord.OrganisationKey,
              DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
              EclipseRole = "Department"
          });
        dynamic pworRecord = CreateRow(
            tableName: "dbo.PolicyOrganisation",
            values: new
            {
                PolicyId = policyRecord.PolicyId,
                DataSourceInstanceId = organisationRecord.DataSourceInstanceId,
                OrganisationId = psOrganisationRecord.OrganisationSK,
                isDeleted = false
            });
        dynamic pworDeptRecord = CreateRow(
            tableName: "dbo.PolicyOrganisation",
            values: new
            {
                PolicyId = policyRecord.PolicyId,
                DataSourceInstanceId = organisationRecord.DataSourceInstanceId,
                OrganisationId = psOrganisationDeptRecord.OrganisationSK,
                isDeleted = false
            });
        dynamic organisationHierarchyTeamRecord = CreateRow(
            tableName: "APIv1.OrganisationHierarchyTable",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
                OrganisationId = psOrganisationRecord.OrganisationSK,
                OrganisationName = "EclipseOrg",
                Level1OrgId = 123
            });
        dynamic organisationHierarchyDeptRecord = CreateRow(
            tableName: "APIv1.OrganisationHierarchyTable",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
                OrganisationId = psOrganisationDeptRecord.OrganisationSK,
                OrganisationName = "EclipseDeptOrg",
                Level1OrgId = 123
            });
        dynamic OrganisationLegalEntity = CreateRow(
            tableName: "ref.OrganisationLegalEntity",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
                OrganisationId = 123,
                LegalEntityId = 111,
                IsDeprecated = false

            });
        dynamic LegalEntity = CreateRow(
            tableName: "ref.LegalEntity",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                LegalEntityKey = 111


            });
        dynamic PsContract = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                LegalEntityId = LegalEntity.LegalEntityKey,
                Reference = policyRecord.policyReference,
                IsExpiring = 0,
                IsDeleted = 0

            });



        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_PolicyAudit");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_PolicyAudit", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PolicyAudit");
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: policyRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: true, actual: row.HasContract);
        Assert.Equal(expected: false, actual: row.HasContractWording);
    }

    [Fact]
    public void CheckUseOfTeamTest()
    {
        dynamic policyRecord = CreateRow(
    tableName: "dbo.Policy",
    values: new
    {
        PACTPolicyId = 13579,
        DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
        PolicyKey = "PolKey",
        PolicyReference = "polref",
        SourceUpdatedDate = DateTime.UtcNow,
        RefPolicyStatusId = 100,
        InceptionDate = DateTime.UtcNow,
        RuleId = 1
    });
        dynamic TeamRecord = CreateRow(
           tableName: "ref.Team",
           values: new
           {
               TeamId = 3000255,
               TeamName = "EclipseGB_Aerospace",
               TeamKey = 1
           });




        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_PolicyAudit");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_PolicyAudit", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PolicyAudit");
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: policyRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: TeamRecord.TeamName, actual: row.TeamName);

    }

    [Fact]
    public void CheckExpiringPolicyTest()
    {
        dynamic pasPolicyStatusRecord = CreateRow(tableName: "PAS.PolicyStatus", values: new
        {
            PolicyStatusKey = "A",
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyStatus = "AUTHORISED",
            IsDeleted = false
        });

        dynamic pasExpiringPolicyStatusRecord = CreateRow(tableName: "PAS.PolicyStatus", values: new
        {
            PolicyStatusKey = "L",
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyStatus = "LIVE",
            IsDeleted = false
        });

        dynamic expiringPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
        {
            PACTPolicyId = 13578,
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyKey = "ExpPolKey",
            PolicyReference = "ExpPolRef",
            PolicyStatusKey = pasExpiringPolicyStatusRecord.PolicyStatusKey,
            SourceUpdatedDate = DateTime.UtcNow,
        });

        dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
        {
            PACTPolicyId = 13579,
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyKey = "PolKey",
            PolicyReference = "PolRef",
            PolicyStatusKey = pasPolicyStatusRecord.PolicyStatusKey,
            SourceUpdatedDate = DateTime.UtcNow,
            RefPolicyStatusId = 100,
            InceptionDate = DateTime.UtcNow,
            RenewedFromPolicyId = expiringPolicyRecord.PolicyId,
            RuleId = 1
        });


        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_PolicyAudit");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_PolicyAudit", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PolicyAudit");
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: policyRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: policyRecord.PolicyKey, actual: row.PolicyKey);
        Assert.Equal(expected: pasPolicyStatusRecord.PolicyStatus, actual: row.PolicyStatus);
        Assert.Equal(expected: expiringPolicyRecord.PolicyId, actual: row.ExpiringPolicyId);
        Assert.Equal(expected: expiringPolicyRecord.PolicyKey, actual: row.ExpiringPolicyKey);
        Assert.Equal(expected: expiringPolicyRecord.PolicyReference, actual: row.ExpiringPolicyReference);
        Assert.Equal(expected: pasExpiringPolicyStatusRecord.PolicyStatus, actual: row.ExpiringPolicyStatus);

    }
    /// <summary>
    /// Constructor - do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public Load_rpt_PolicyAuditTests(DatabaseFixture fixture) : base(fixture)
    {

    }
}
