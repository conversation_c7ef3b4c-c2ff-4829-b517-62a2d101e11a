/*
Lineage
PS.QuoteComparisonQuote.Id=BPStaging.QuoteComparisonQuote.Id
PS.QuoteComparisonQuote.QuoteComparisonId=BPStaging.QuoteComparisonQuote.QuoteComparisonId
PS.QuoteComparisonQuote.MarketResponseId=BPStaging.QuoteComparisonQuote.MarketResponseId
PS.QuoteComparisonQuote.ExpiringPolicyId=BPStaging.QuoteComparisonQuote.ExpiringPolicyId
PS.QuoteComparisonQuote.DisplayIndex=BPStaging.QuoteComparisonQuote.DisplayIndex
PS.QuoteComparisonQuote.Visible=BPStaging.QuoteComparisonQuote.Visible
PS.QuoteComparisonQuote.IncludeInProposal=BPStaging.QuoteComparisonQuote.IncludeInProposal
PS.QuoteComparisonQuote.Label=BPStaging.QuoteComparisonQuote.Label
PS.QuoteComparisonQuote.IsBound=BPStaging.QuoteComparisonQuote.IsBound
PS.QuoteComparisonQuote.SubmissionContainerId=BPStaging.QuoteComparisonQuote.SubmissionContainerId
PS.QuoteComparisonQuote.SourceUpdatedDate=BPStaging.QuoteComparisonQuote.ValidTo
PS.QuoteComparisonQuote.SourceUpdatedDate=BPStaging.QuoteComparisonQuote.ValidFrom
PS.QuoteComparisonQuote.IsDeleted=BPStaging.QuoteComparisonQuote.ValidTo
PS.QuoteComparisonQuote.MarketResponseKey=BPStaging.QuoteComparisonQuote.MarketResponseId
*/
CREATE PROCEDURE BPStaging.Load_PS_QuoteComparisonQuote
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.QuoteComparisonQuote';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.QuoteComparisonQuote)
BEGIN TRY
    MERGE PS.QuoteComparisonQuote T
    USING (
        SELECT
            Id
          , QuoteComparisonId
          , MarketResponseId
          , ExpiringPolicyId
          , DisplayIndex
          , Visible
          , IncludeInProposal
          , Label
          , IsBound
          , SubmissionContainerId
          , Inner_select.MarketResponseKey
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , QuoteComparisonId
          , MarketResponseId
          , ExpiringPolicyId
          , DisplayIndex
          , Visible
          , IncludeInProposal
          , Label
          , IsBound
          , SubmissionContainerId
          , ValidFrom
          , ValidTo
          , MarketResponseKey = CASE WHEN MarketResponseId IS NULL
                                         THEN NULL
                                     ELSE CONCAT('MKTRES|', MarketResponseId) END
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.QuoteComparisonQuote
    ) Inner_select
        WHERE
            Inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , QuoteComparisonId
               , MarketResponseId
               , ExpiringPolicyId
               , DisplayIndex
               , Visible
               , IncludeInProposal
               , Label
               , IsBound
               , SubmissionContainerId
               , SourceUpdatedDate
               , IsDeleted
               , DataSourceInstanceId
               , MarketResponseKey
             )
             VALUES
                 (
                     S.Id
                   , S.QuoteComparisonId
                   , S.MarketResponseId
                   , S.ExpiringPolicyId
                   , S.DisplayIndex
                   , S.Visible
                   , S.IncludeInProposal
                   , S.Label
                   , S.IsBound
                   , S.SubmissionContainerId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                   , S.MarketResponseKey
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.QuoteComparisonId
      , T.MarketResponseId
      , T.ExpiringPolicyId
      , T.DisplayIndex
      , T.Visible
      , T.IncludeInProposal
      , T.Label
      , T.IsBound
      , T.SubmissionContainerId
      , T.SourceUpdatedDate
      , T.IsDeleted
      , T.DataSourceInstanceId
      , T.MarketResponseKey
    INTERSECT
    SELECT
        S.QuoteComparisonId
      , S.MarketResponseId
      , S.ExpiringPolicyId
      , S.DisplayIndex
      , S.Visible
      , S.IncludeInProposal
      , S.Label
      , S.IsBound
      , S.SubmissionContainerId
      , S.SourceUpdatedDate
      , S.IsDeleted
      , S.DataSourceInstanceId
      , S.MarketResponseKey
)
        THEN UPDATE SET
                 T.QuoteComparisonId = S.QuoteComparisonId
               , T.MarketResponseId = S.MarketResponseId
               , T.ExpiringPolicyId = S.ExpiringPolicyId
               , T.DisplayIndex = S.DisplayIndex
               , T.Visible = S.Visible
               , T.IncludeInProposal = S.IncludeInProposal
               , T.Label = S.Label
               , T.IsBound = S.IsBound
               , T.SubmissionContainerId = S.SubmissionContainerId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.MarketResponseKey = S.MarketResponseKey
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);