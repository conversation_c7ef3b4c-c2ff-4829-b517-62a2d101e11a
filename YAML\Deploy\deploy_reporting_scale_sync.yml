parameters:
  envName: ''
  microservice: ''
  variableGroupName: ''
  variableGroupNameShared: ''
  azureServiceConnection: ''
  download: ''
  ssas: { }
  replicaCount: 1
  operation: 'scale_up' # Possible values: 'scale_up', 'scale_down'
  enableReplica: false
  agentPool: 'Private-VM-CRB-VS2022'

jobs:
  - deployment: ${{parameters.envName}}_reports_scale_sync
    ${{ if eq(parameters.operation, 'scale_up') }}:
      displayName: 'Analysis Server: Scale Up and Sync'
    ${{ if eq(parameters.operation, 'scale_down') }}:
      displayName: 'Analysis Server: Scale Down and Sync'
    variables:
    - group: ${{parameters.variableGroupName}}
    - group: ${{parameters.variableGroupNameShared}}
    pool:
      name: ${{parameters.agentPoolName}}
      demands: AZP_REGION -equals EM
    environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
            enabled: ${{parameters.enableReplica}}
            fetchDepth: 1    # shallow
            fetchTags: false # dont fetch tags
          - task: DeleteFiles@1
            displayName: 'Delete: PlacementStore.Reporting'
            enabled: ${{parameters.enableReplica}}
            inputs:
              sourceFolder: $(Pipeline.Workspace)/PlacementStore.Reporting
              contents: '**/*'
          - download: ${{parameters.download}}
            enabled: ${{parameters.enableReplica}}
            artifact: PlacementStore.Reporting
            displayName: 'Download: PlacementStore.Reporting'
          - task: AzurePowerShell@5
            enabled: ${{parameters.enableReplica}}
            ${{ if eq(parameters.operation, 'scale_up') }}:
              displayName: 'Scale up: Replica Count for Analysis Server'
            ${{ if eq(parameters.operation, 'scale_down') }}:
              displayName: 'Scale down: Replica Count for Analysis Server'
            inputs:
             azureSubscription: ${{parameters.azureServiceConnection}}
             pwsh: true
             azurePowerShellVersion: LatestVersion
             scriptType: 'FilePath'
             scriptPath: '$(Pipeline.Workspace)/PlacementStore.Reporting/ScaleUpDown-AnalysisServices-Replicas.ps1'
             scriptArguments: '-serverUrl ${{parameters.ssas.ssasServer}} -resourceGroupName ${{parameters.ssas.resourceGroupName}} -replicaCount ${{parameters.replicaCount}}'
             # Sync models
          - ${{ each model in parameters.ssas.models }}:
            - task: AzurePowerShell@5
              enabled: ${{parameters.enableReplica}}
              displayName: 'Sync: ${{model.name}} - Model'
              inputs:
               azureSubscription: ${{parameters.azureServiceConnection}}
               pwsh: true
               azurePowerShellVersion: LatestVersion
               scriptType: 'FilePath'
               scriptPath: '$(Pipeline.Workspace)/PlacementStore.Reporting/Sync-AnalysisServicesModels.ps1'
               scriptArguments: '-serverUrl ${{parameters.ssas.ssasServer}} -databaseName ${{model.databaseName}}'
