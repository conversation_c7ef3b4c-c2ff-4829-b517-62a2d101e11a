{"version": "2.0", "functionTimeout": "-1", "logging": {"logLevel": {"Azure": "Warning", "Function": "Warning", "Host.Triggers.DurableTask": "Information", "Worker.rpcWorkerProcess": "Warning"}, "applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 20, "excludedTypes": "Request;Exception"}}}, "extensions": {"serviceBus": {"messageHandlerOptions": {"autoComplete": true, "maxAutoRenewDuration": "02:00:00", "maxConcurrentCalls": 1}, "sessionHandlerOptions": {"autoComplete": true, "maxAutoRenewDuration": "02:00:00"}, "clientRetryOptions": {"mode": "exponential", "maxRetries": 3, "delay": "00:00:30", "maxDelay": "00:05:00"}}}, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:00:05", "maximumInterval": "00:00:30"}}