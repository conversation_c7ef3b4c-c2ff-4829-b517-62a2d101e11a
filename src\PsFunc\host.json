{"version": "2.0", "functionTimeout": "-1", "logging": {"logLevel": {"Azure": "Warning", "Function": "Warning", "Host.Triggers.DurableTask": "Information", "Worker.rpcWorkerProcess": "Warning", "Azure.Messaging.ServiceBus": "Information"}, "applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 20, "excludedTypes": "Request;Exception"}}}, "extensions": {"serviceBus": {"messageHandlerOptions": {"autoComplete": true, "maxAutoRenewDuration": "02:30:00", "maxConcurrentCalls": 1, "autoRenewTimeout": "00:10:00", "maxMessageWaitTimeout": "00:01:00"}, "sessionHandlerOptions": {"autoComplete": true, "maxAutoRenewDuration": "00:15:00", "maxConcurrentSessions": 1, "autoRenewTimeout": "00:05:00", "maxMessageWaitTimeout": "00:01:00"}, "clientRetryOptions": {"mode": "exponential", "maxRetries": 5, "delay": "00:00:30", "maxDelay": "00:05:00", "tryTimeout": "00:15:00"}, "transportType": "AmqpWebSockets"}}, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:02:00", "maximumInterval": "00:10:00"}, "healthMonitor": {"enabled": true, "healthCheckInterval": "00:00:30", "healthCheckWindow": "00:02:00", "healthCheckThreshold": 6, "counterThreshold": 0.8}}