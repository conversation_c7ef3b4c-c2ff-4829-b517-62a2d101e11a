{"version": "2.0", "functionTimeout": "-1", "logging": {"logLevel": {"Azure": "Warning", "Function": "Warning", "Host.Triggers.DurableTask": "Information", "Worker.rpcWorkerProcess": "Warning"}, "applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 20, "excludedTypes": "Request;Exception"}}}, "extensions": {"serviceBus": {"messageHandlerOptions": {"autoComplete": true, "maxAutoRenewDuration": "01:30:00", "maxConcurrentCalls": 1, "autoRenewTimeout": "00:05:00"}, "sessionHandlerOptions": {"autoComplete": true, "maxAutoRenewDuration": "01:30:00", "autoRenewTimeout": "00:05:00"}, "clientRetryOptions": {"mode": "exponential", "maxRetries": 5, "delay": "00:00:10", "maxDelay": "00:02:00", "tryTimeout": "00:10:00"}, "transportType": "AmqpWebSockets"}}, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:00:05", "maximumInterval": "00:00:30"}}