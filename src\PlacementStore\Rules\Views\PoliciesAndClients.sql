/*
Lineage
PolicyId=dbo.Policy.PolicyId
DataSourceInstanceId=dbo.Policy.DataSourceInstanceId
PolicyStatusId=dbo.Policy.PolicyStatusId
PolicyStatus=PAS.PolicyStatus.PolicyStatus
PolicyStatusKey=PAS.PolicyStatus.PolicyStatusKey
RefPolicyStatusID=dbo.Policy.RefPolicyStatusId
InceptionDate=dbo.Policy.InceptionDate
ExpiryDate=dbo.Policy.ExpiryDate
InsuranceTypeId=dbo.Policy.InsuranceTypeId
InsuranceType=ref.InsuranceType.InsuranceType
InsuranceTypeKey=ref.InsuranceType.InsuranceTypeKey
RefInsuranceTypeId=ref.InsuranceType.RefInsuranceTypeId
RenewedFromPolicyId=dbo.Policy.RenewedFromPolicyId
PolicyTypeId=dbo.Policy.PolicyTypeId
PolicyType=PAS.PolicyType.PolicyType
PolicyTypeKey=PAS.PolicyType.PolicyTypeKey
OrganisationId=PS.Organisation.OrganisationSK
Organisation=PAS.Organisation.Organisation
OrganisationKey=PAS.Organisation.OrganisationKey
OrgLevel1=APIv1.OrganisationHierarchyTable.OrgLevel1
OrgLevel2=APIv1.OrganisationHierarchyTable.OrgLevel2
OrgLevel3=APIv1.OrganisationHierarchyTable.OrgLevel3
OrgLevel4=APIv1.OrganisationHierarchyTable.OrgLevel4
OrgLevel5=APIv1.OrganisationHierarchyTable.OrgLevel5
OrgLevel6=APIv1.OrganisationHierarchyTable.OrgLevel6
OrgNode=APIv1.OrganisationHierarchyTable.OrgNode
WillisOrganisationRole=PAS.OrganisationRole.OrganisationRole
WillisOrganisationRoleKey=PAS.OrganisationRole.OrganisationRoleKey
PolicySectionStatusKey=PAS.PolicySectionStatus.PolicySectionStatusKey
GlobalProductClassID=Reference.ProductClass.ProductClassId
ProductClassName=Reference.ProductClass.ProductClassName
GlobalProductLineID=Reference.ProductLine.ProductLineId
ProductLineName=Reference.ProductLine.ProductLineName
ProductClass=dbo.Product.ProductClass
IsPrimaryProduct=dbo.PolicySectionProduct.IsPrimaryProduct
ProductLine=dbo.Product.ProductLine
ProductKey=dbo.Product.ProductKey
SourceProductName=dbo.Product.SourceProductName
PartyId=dbo.Party.PartyId
GlobalPartyId=dbo.Party.GlobalPartyId
PartyKey=dbo.Party.PartyKey
PartyName=dbo.Party.PartyName
IsPrimaryParty=dbo.PolicyPartyRelationship.IsPrimaryParty
ReferencePartyName=Reference.Party.PartyName
GlobalUltimateOwnerPartyId=Reference.Party.GlobalUltimateOwnerPartyId
PartyRole=ref.PartyRole.PartyRole
PartyRoleId=ref.PartyRole.PartyRoleId
PartyRoleKey=ref.PartyRole.PartyRoleKey
GlobalPartyRoleId=ref.PartyRole.GlobalPartyRoleId
PolicyDescription=dbo.Policy.PolicyDescription
DataSourceName=Reference.DataSourceInstance.DataSourceInstanceName
PlacementId=dbo.PlacementPolicy.PlacementId
RuleId=dbo.Policy.RuleId
*/

CREATE VIEW Rules.PoliciesAndClients
AS
-- Bug 137935 - Added IsDeleted check to key tables related to policy.
--              But do not check IsDeleted on reference data. If an undeleted record links to it then it presumably was valid when that link was made.
SELECT
    pol.PolicyId
  , pol.DataSourceInstanceId
  , pol.PolicyStatusId
  , pstat.PolicyStatus
  , pstat.PolicyStatusKey
  , RefPolicyStatusID = pol.RefPolicyStatusId
  , pol.InceptionDate
  , pol.ExpiryDate
  , pol.InsuranceTypeId
  , it.InsuranceType
  , it.InsuranceTypeKey
  , it.RefInsuranceTypeId
  , pol.RenewedFromPolicyId
  , pol.PolicyTypeId
  , pt.PolicyType
  , pt.PolicyTypeKey
  , OrganisationId = psorg.OrganisationSK
  , org.Organisation
  , org.OrganisationKey
  , orgh.OrgLevel1
  , orgh.OrgLevel2
  , orgh.OrgLevel3
  , orgh.OrgLevel4
  , orgh.OrgLevel5
  , orgh.OrgLevel6
  , orgh.OrgNode
  , WillisOrganisationRole = orl.OrganisationRole
  , WillisOrganisationRoleKey = orl.OrganisationRoleKey
  , pss.PolicySectionStatusKey
  , GlobalProductClassID = pc.ProductClassId
  , pc.ProductClassName
  , GlobalProductLineID = pl.ProductLineId
  , pl.ProductLineName
  , pr.ProductClass
  , psp.IsPrimaryProduct
  , pr.ProductLine
  , pr.ProductKey
  , pr.SourceProductName
  , PartyNumber = ROW_NUMBER() OVER (PARTITION BY
                                         pol.PolicyId
                                       , porg.OrganisationId
                                       , ps.PolicySectionId
                                       , pr.ProductId
                                       , ptyr.GlobalPartyRoleId
                                     ORDER BY
                                         pol.PolicyId
                                       , prr.IsPrimaryParty DESC
                                       , pty.PartyId
                               ) -- Used to find the first (Primary) Client
  , pty.PartyId
  , pty.GlobalPartyId
  , pty.PartyKey
  , pty.PartyName
  , prr.IsPrimaryParty
  , ReferencePartyName = rpty.PartyName
  , rpty.GlobalUltimateOwnerPartyId
  , ptyr.PartyRole
  , ptyr.PartyRoleId
  , ptyr.PartyRoleKey
  , ptyr.GlobalPartyRoleId
  , pol.PolicyDescription
  , DataSourceName = ISNULL(dsi.DataSourceInstanceName, 'None')
  , pp.PlacementId
  , pol.RuleId
FROM
    dbo.Policy pol WITH(NOLOCK)
    LEFT JOIN dbo.PlacementPolicy pp WITH(NOLOCK)
        ON pp.PolicyId = pol.PolicyId
           AND pp.DataSourceInstanceId = 50366 /* Broking Platform Only */
           AND pp.PlacementPolicyRelationshipTypeId = 1
           AND pp.IsDeleted = 0

    LEFT JOIN dbo.PolicyOrganisation porg WITH(NOLOCK)
        ON porg.PolicyId = pol.PolicyId
           AND porg.IsDeleted = 0

    LEFT JOIN PS.Organisation psorg WITH(NOLOCK)
        ON porg.OrganisationId = psorg.OrganisationSK

    LEFT JOIN PAS.Organisation org WITH(NOLOCK)
        ON psorg.OrganisationKey = org.OrganisationKey
           AND psorg.DataSourceInstanceId = org.DataSourceInstanceId
           AND org.IsDeleted = 0

    LEFT JOIN APIv1.OrganisationHierarchyTable orgh WITH(NOLOCK)
        ON orgh.OrganisationId = porg.OrganisationId

    LEFT JOIN PS.OrganisationRole psor WITH(NOLOCK)
        ON porg.OrganisationRoleId = psor.OrganisationRoleSK

    LEFT JOIN PAS.OrganisationRole orl WITH(NOLOCK)
        ON psor.OrganisationRoleKey = orl.OrganisationRoleKey
           AND psor.DataSourceInstanceId = orl.DataSourceInstanceId

    LEFT JOIN dbo.PolicySection ps WITH(NOLOCK)
        ON ps.PolicyId = pol.PolicyId
           AND ps.IsDeleted = 0

    LEFT JOIN PAS.PolicySectionStatus pss WITH(NOLOCK)
        ON pss.PolicySectionStatusKey = ps.PolicySectionStatusKey
            AND pss.DataSourceInstanceId = ps.DataSourceInstanceId

    LEFT JOIN dbo.PolicySectionProduct psp WITH(NOLOCK)
        ON psp.PolicySectionId = ps.PolicySectionId
           AND psp.IsDeleted = 0

    LEFT JOIN dbo.Product pr WITH(NOLOCK)
        ON pr.ProductId = psp.ProductId

    LEFT JOIN Reference.Product rp WITH(NOLOCK)
        ON rp.ProductId = pr.ReferenceProductId

    LEFT JOIN Reference.ProductLine pl WITH(NOLOCK)
        ON pl.ProductLineId = rp.ProductLineId

    LEFT JOIN Reference.ProductClass pc WITH(NOLOCK)
        ON pc.ProductClassId = pl.ProductClassId

    LEFT JOIN PAS.PolicyStatus pstat WITH(NOLOCK)
        ON pstat.PolicyStatusKey = pol.PolicyStatusKey
            AND pstat.DataSourceInstanceId = pol.DataSourceInstanceId

    LEFT JOIN ref.InsuranceType it WITH(NOLOCK)
        ON it.InsuranceTypeId = pol.InsuranceTypeId

    LEFT JOIN PAS.PolicyType pt WITH(NOLOCK)
        ON pt.PolicyTypeKey = pol.PolicyTypeKey
            AND pt.DataSourceInstanceId = pol.DataSourceInstanceId

    LEFT JOIN dbo.PolicyPartyRelationship prr WITH(NOLOCK)
        ON prr.PolicyId = pol.PolicyId
           AND prr.IsDeleted = 0

    LEFT JOIN ref.PartyRole ptyr WITH(NOLOCK)
        ON ptyr.PartyRoleId = prr.PartyRoleId

    LEFT JOIN dbo.Party pty WITH(NOLOCK)
        ON pty.PartyId = prr.PartyId

    LEFT JOIN Reference.Party rpty WITH(NOLOCK)
        ON rpty.PartyId = pty.GlobalPartyId

    LEFT JOIN Reference.DataSourceInstance dsi WITH(NOLOCK)
        ON dsi.DataSourceInstanceId = pol.DataSourceInstanceId
WHERE
    pol.IsDeleted = 0;