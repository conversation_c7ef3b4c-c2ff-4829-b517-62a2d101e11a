/*
Lineage
dbo.SubmissionDistribution.SubmissionMarketId=BP.SubmissionMarket.Id
dbo.SubmissionDistribution.SubmissionContainerMarketId=BP.SubmissionMarket.SubmissionContainerMarketId
dbo.SubmissionDistribution.SubmissionId=BP.SubmissionMarket.SubmissionId
*/
CREATE PROCEDURE BPStaging.Load_dbo_SubmissionDistribution
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.SubmissionDistribution';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BP.SubmissionMarket
        );

BEGIN TRY
    IF @CheckCount <> 0
    BEGIN
        MERGE dbo.SubmissionDistribution T
        USING (
            SELECT
                SubmissionMarketId = sm.Id
              , sm.SubmissionContainerMarketId
              , sm.SubmissionId
            FROM
                BP.SubmissionMarket sm
            WHERE
                EXISTS (
                SELECT * FROM dbo.Submission s WHERE s.SubmissionId = sm.SubmissionId
            )
                AND EXISTS (
                SELECT *
                FROM
                    PS.NegotiationMarket NM
                WHERE
                    NM.NegotiationMarketKey = CONCAT('SUBCONMKT|', sm.SubmissionContainerMarketId)
                    AND NM.DataSourceInstanceId = 50366
            )
        ) S
        ON S.SubmissionMarketId = T.SubmissionMarketId
        WHEN NOT MATCHED BY TARGET
            THEN INSERT (
                     SubmissionMarketId
                   , SubmissionContainerMarketId
                   , SubmissionId
                 )
                 VALUES
                     (
                         S.SubmissionMarketId
                       , S.SubmissionContainerMarketId
                       , S.SubmissionId
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT T.SubmissionContainerMarketId, T.SubmissionId INTERSECT SELECT S.SubmissionContainerMarketId, S.SubmissionId
    )
            THEN UPDATE SET
                     T.SubmissionContainerMarketId = S.SubmissionContainerMarketId
                   , T.SubmissionId = S.SubmissionId
        WHEN NOT MATCHED BY SOURCE
            THEN DELETE
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;