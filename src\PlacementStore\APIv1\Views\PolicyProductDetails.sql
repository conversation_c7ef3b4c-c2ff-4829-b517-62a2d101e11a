/*
Lineage
PolicyId=dbo.Policy.PolicyId
DataSourceInstanceId=dbo.Policy.DataSourceInstanceId
ProductClass=dbo.Product.ProductClass
ProductLine=dbo.Product.ProductLine
SourceProductName=dbo.Product.SourceProductName
ReferenceProductClassID=Reference.ProductClass.ProductClassId
ReferenceProductClassName=Reference.ProductClass.ProductClassName
ReferenceProductLineID=Reference.ProductLine.ProductLineId
ReferenceProductLineName=Reference.ProductLine.ProductLineName
ReferenceProductId=Reference.Product.ProductId
ReferenceProductName=Reference.Product.ProductName
*/
CREATE VIEW APIv1.PolicyProductDetails
AS
SELECT DISTINCT
       P.PolicyId
     , P.DataSourceInstanceId
     --PR.ProductID,
     , PR.ProductClass
     , PR.ProductLine
     , PR.SourceProductName
     , ReferenceProductClassID = RPC.ProductClassId
     , ReferenceProductClassName = RPC.ProductClassName
     , ReferenceProductLineID = RPL.ProductLineId
     , ReferenceProductLineName = RPL.ProductLineName
     , ReferenceProductId = RP.ProductId
     , ReferenceProductName = RP.ProductName
FROM
    dbo.Policy P
    INNER JOIN dbo.PolicySection PS
        ON P.PolicyId = PS.PolicyId
           AND P.IsDeleted = 0

    INNER JOIN dbo.PolicySectionProduct PSP
        ON PSP.PolicySectionId = PS.PolicySectionId
           AND PSP.IsDeleted = 0

    INNER JOIN dbo.Product PR
        ON PR.ProductId = PSP.ProductId
           AND PR.IsDeleted = 0

    LEFT JOIN Reference.Product RP
        ON RP.ProductId = PR.ReferenceProductId
           AND RP.IsDeleted = 0

    LEFT JOIN Reference.ProductLine RPL
        ON RPL.ProductLineId = RP.ProductLineId
           AND RPL.IsDeleted = 0

    LEFT JOIN Reference.ProductClass RPC
        ON RPC.ProductClassId = RPL.ProductClassId
           AND RPC.IsDeleted = 0;