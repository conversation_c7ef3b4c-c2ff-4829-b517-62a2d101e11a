/*
Lineage
dbo.TeamMember.TeamId=BPStaging.TeamMember.TeamId
dbo.TeamMember.UserId=BPStaging.TeamMember.UserId
dbo.TeamMember.RoleId=BPStaging.TeamMember.RoleId
*/
CREATE PROCEDURE BPStaging.LoadTeamMember
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.TeamMember';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.TeamMember T
    USING (SELECT DISTINCT DataSourceInstanceId = 50366, TeamId, UserId, RoleId FROM BPStaging.TeamMember) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.TeamId = S.TeamId
       AND T.UserId = S.UserId
       AND T.RoleId = S.RoleId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , TeamId
               , UserId
               , RoleId
               , LastUpdatedUTCDate
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.TeamId
                   , S.UserId
                   , S.RoleId
                   , GETUTCDATE()
                 )

    -- WHEN MATCHED -- Nothing to do as this is all the columns

    /* The original code truncated. If there is no match then we need to do the same */
    WHEN NOT MATCHED BY SOURCE
        THEN DELETE
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);