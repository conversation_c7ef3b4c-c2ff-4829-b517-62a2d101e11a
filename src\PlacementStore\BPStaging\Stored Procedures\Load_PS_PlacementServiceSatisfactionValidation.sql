/*
Lineage
PS.PlacementServiceSatisfactionValidation.PlacementServiceSatisfactionValidationKey=BPStaging.PlacementServiceSatisfactionValidation.Id
PS.PlacementServiceSatisfactionValidation.PlacementId=dbo.Placement.PlacementId
PS.PlacementServiceSatisfactionValidation.ActionValidationRuleId=PS.ActionValidationRule.ActionValidationRuleId
PS.PlacementServiceSatisfactionValidation.NegotiationId=PS.Negotiation.NegotiationId
PS.PlacementServiceSatisfactionValidation.NegotiationMarketId=PS.NegotiationMarket.NegotiationMarketId
PS.PlacementServiceSatisfactionValidation.CarrierId=BPStaging.PlacementServiceSatisfactionValidation.CarrierId
PS.PlacementServiceSatisfactionValidation.ServiceLevelId=BPStaging.PlacementServiceSatisfactionValidation.ServiceLevelId
PS.PlacementServiceSatisfactionValidation.Comments=BPStaging.PlacementServiceSatisfactionValidation.Comments
PS.PlacementServiceSatisfactionValidation.IsDeleted=PS.NegotiationMarket.Sent
*/
CREATE PROCEDURE BPStaging.Load_PS_PlacementServiceSatisfactionValidation
    @Success BIT = 1 OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.PlacementServiceSatisfactionValidation';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.PlacementServiceSatisfactionValidation T
    USING (
        SELECT
            PlacementServiceSatisfactionValidationKey = CONCAT('PSSV|', CAST(s.Id AS VARCHAR(11)))
          , p.PlacementId
          , avr.ActionValidationRuleId
          , n.NegotiationId
          , nm.NegotiationMarketId
          , s.CarrierId
          , s.ServiceLevelId
          , s.Comments
          , SourceUpdatedDate = s.ValidFrom
          , DataSourceInstanceId = 50366
          , IsDeleted = CASE WHEN nm.Sent = 0
                                 THEN 1
                             ELSE 0 END
        FROM
            BPStaging.PlacementServiceSatisfactionValidation s
            INNER JOIN dbo.Placement p
                ON p.PlacementSystemId = s.PlacementId
                   AND p.DataSourceInstanceId = 50366

            INNER JOIN PS.Negotiation n
                ON n.DataSourceInstanceId = 50366
                   AND n.NegotiationKey = CONCAT(N'SUBC|', CAST(s.SubmissionContainerId AS NVARCHAR(11)))

            INNER JOIN PS.NegotiationMarket nm
                ON nm.DataSourceInstanceId = 50366
                   AND nm.NegotiationMarketKey = CONCAT(
                                                     'SUBCONMKT|', CAST(s.SubmissionContainerMarketId AS VARCHAR(11))
                                                 )

            INNER JOIN PS.ActionValidationRule avr
                ON avr.ActionValidationRuleKey = CONCAT('AVR|', s.ActionValidationRuleId)
                   AND avr.DataSourceInstanceId = 50366
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.PlacementServiceSatisfactionValidationKey = S.PlacementServiceSatisfactionValidationKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , PlacementServiceSatisfactionValidationKey
               , PlacementId
               , ActionValidationRuleId
               , NegotiationId
               , NegotiationMarketId
               , CarrierId
               , ServiceLevelId
               , Comments
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.PlacementServiceSatisfactionValidationKey
                   , S.PlacementId
                   , S.ActionValidationRuleId
                   , S.NegotiationId
                   , S.NegotiationMarketId
                   , S.CarrierId
                   , S.ServiceLevelId
                   , S.Comments
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.PlacementId
                               , S.ActionValidationRuleId
                               , S.NegotiationId
                               , S.NegotiationMarketId
                               , S.CarrierId
                               , S.ServiceLevelId
                               , S.Comments
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.PlacementId
                               , T.ActionValidationRuleId
                               , T.NegotiationId
                               , T.NegotiationMarketId
                               , T.CarrierId
                               , T.ServiceLevelId
                               , T.Comments
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.PlacementId = S.PlacementId
               , T.ActionValidationRuleId = S.ActionValidationRuleId
               , T.NegotiationId = S.NegotiationId
               , T.NegotiationMarketId = S.NegotiationMarketId
               , T.CarrierId = S.CarrierId
               , T.ServiceLevelId = S.ServiceLevelId
               , T.Comments = S.Comments
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;