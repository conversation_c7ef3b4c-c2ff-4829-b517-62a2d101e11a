/*
Lineage
ref.ExposureType.ExposureTypeId=BPStaging.ExposureType.Id
ref.ExposureType.ExposureTypeKey=BPStaging.ExposureType.LabelTranslationKey
ref.ExposureType.ExposureType=BPStaging.ExposureType.Text
ref.ExposureType.SourceUpdatedDate=BPStaging.ExposureType.ValidFrom
ref.ExposureType.IsDeprecated=BPStaging.ExposureType.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_ExposureType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ExposureType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.ExposureType T
    USING (
        SELECT
            ExposureTypeId = Id
          , ExposureTypeKey = LabelTranslationKey
          , ExposureType = Text
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.ExposureType
    ) S
    ON T.ExposureTypeId = S.ExposureTypeId
    WHEN NOT MATCHED
        THEN INSERT (
                 ExposureTypeId
               , DataSourceInstanceId
               , ExposureTypeKey
               , ExposureType
               , SourceUpdatedDate
               , IsDeprecated
               , ETLCreatedDate
               , ETLUpdatedDate
             )
             VALUES
                 (
                     S.ExposureTypeId
                   , S.DataSourceInstanceId
                   , S.ExposureTypeKey
                   , S.ExposureType
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                   , GETUTCDATE()
                   , GETUTCDATE()
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.ExposureTypeKey
                               , T.ExposureType
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.ExposureTypeKey
                               , S.ExposureType
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ExposureTypeKey = S.ExposureTypeKey
               , T.ExposureType = S.ExposureType
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);