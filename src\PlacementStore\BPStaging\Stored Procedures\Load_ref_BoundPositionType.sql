/*
Lineage
ref.BoundPositionType.BoundPositionTypeKey=BPStaging.BoundPositionType.Id
ref.BoundPositionType.BoundPositionType=BPStaging.BoundPositionType.Text
ref.BoundPositionType.SourceUpdatedDate=BPStaging.BoundPositionType.ValidTo
ref.BoundPositionType.SourceUpdatedDate=BPStaging.BoundPositionType.ValidFrom
ref.BoundPositionType.IsDeprecated=BPStaging.BoundPositionType.IsDeprecated
ref.BoundPositionType.IsDeprecated=BPStaging.BoundPositionType.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_ref_BoundPositionType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.BoundPositionType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.BoundPositionType)
BEGIN TRY
    MERGE ref.BoundPositionType T
    USING (
        SELECT
            BoundPositionTypeKey = CAST(Id AS VARCHAR(10))
          , BoundPositionType = Text
          , IsDeprecated = IIF(YEAR(ValidTo) < 9999, 1, IsDeprecated)
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , Text
          , IsDeprecated
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.BoundPositionType
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.BoundPositionTypeKey = S.BoundPositionTypeKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 BoundPositionTypeKey
               , BoundPositionType
               , SourceUpdatedDate
               , DataSourceInstanceId
               , IsDeprecated
             )
             VALUES
                 (
                     S.BoundPositionTypeKey
                   , S.BoundPositionType
                   , S.SourceUpdatedDate
                   , S.DataSourceInstanceId
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.BoundPositionType
      , T.SourceUpdatedDate
      , T.DataSourceInstanceId
      , T.IsDeprecated
    INTERSECT
    SELECT
        S.BoundPositionType
      , S.SourceUpdatedDate
      , S.DataSourceInstanceId
      , S.IsDeprecated
)
        THEN UPDATE SET
                 T.BoundPositionType = S.BoundPositionType
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.IsDeprecated = S.IsDeprecated
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);