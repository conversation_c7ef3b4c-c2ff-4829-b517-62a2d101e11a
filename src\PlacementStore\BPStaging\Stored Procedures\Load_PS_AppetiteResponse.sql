/*
Lineage
PS.AppetiteResponse.DataSourceInstanceId=PS.NegotiationMarket.DataSourceInstanceId
PS.AppetiteResponse.NegotiationMarketId=PS.NegotiationMarket.NegotiationMarketId
PS.AppetiteResponse.AppetiteResponseKey=BP.SubmissionContainerPanelMember.AppetiteLevelId
PS.AppetiteResponse.AppetiteResponseKey=PS.NegotiationMarket.NegotiationMarketId
PS.AppetiteResponse.AppetiteLevelId=BP.SubmissionContainerPanelMember.AppetiteLevelId
PS.AppetiteResponse.SourceUpdatedDate=BP.SubmissionContainerMarket.SourceUpdatedDate
*/
CREATE PROCEDURE BPStaging.Load_PS_AppetiteResponse
    @Success BIT OUTPUT
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.AppetiteResponse';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.AppetiteResponse T
    USING (
        SELECT DISTINCT
               NM.DataSourceInstanceId
             , AppetiteResponseKey = CONCAT('APPRES|', ISNULL(scpm.AppetiteLevelId, -1), '|', NM.NegotiationMarketId)
             , NM.NegotiationMarketId
             , scpm.AppetiteLevelId
             , SourceUpdatedDate = scm.SourceUpdatedDate
             , IsDeleted = 0
        FROM
            BP.SubmissionContainerMarket scm
            LEFT JOIN BP.SubmissionContainerPanelMember scpm
                ON scm.Id = scpm.SubmissionContainerMarketId

            INNER JOIN PS.NegotiationMarket NM
                ON NM.NegotiationMarketKey = CONCAT('SUBCONMKT|', scm.Id)
    ) S
    ON T.NegotiationMarketId = S.NegotiationMarketId
       AND ISNULL(T.AppetiteLevelId, -1) = ISNULL(S.AppetiteLevelId, -1)
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , NegotiationMarketId
               , AppetiteResponseKey
               , AppetiteLevelId
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.NegotiationMarketId
                   , S.AppetiteResponseKey
                   , S.AppetiteLevelId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.NegotiationMarketId
                               , T.AppetiteResponseKey
                               , T.AppetiteLevelId
                               , T.IsDeleted
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.NegotiationMarketId
                               , S.AppetiteResponseKey
                               , S.AppetiteLevelId
                               , S.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.NegotiationMarketId = S.NegotiationMarketId
               , T.AppetiteResponseKey = S.AppetiteResponseKey
               , T.AppetiteLevelId = S.AppetiteLevelId
               , T.IsDeleted = S.IsDeleted
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
    SET @Success = 0;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);