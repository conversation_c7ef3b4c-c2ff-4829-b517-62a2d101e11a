using Microsoft.AnalysisServices.AdomdClient;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PsFunc.Helper;
using PsFunc.Model;
using System.Data;
using System.Diagnostics;
using System.Text;
using System.Xml.Linq;

using RefreshType = Microsoft.AnalysisServices.Tabular.RefreshType;
using Server = Microsoft.AnalysisServices.Server;


namespace PsFunc.Functions.ServiceBus
{
    public class RefreshAnalysisServer
    {

        private readonly IConfigurationRoot _config;
        private const string SsasTopicName = "psadf-to-psfuncapp-ssas-refresh-event";
        private const string SsasSubcriptionName = "refreshmodel";
        public readonly string? aasRegion;
        private readonly string? tenantId;
        private readonly string? aasServerName;
        private readonly ILogger<RefreshAnalysisServer> _logger;
        private readonly int TimeoutInSeconds;

        public RefreshAnalysisServer(IConfigurationRoot config, ILogger<RefreshAnalysisServer> log)
        {
            _logger = log;
            _config = config;
            aasServerName = config["AasServerName"];
            aasRegion = config["AasRegionName"];
            tenantId = config["AzureAd:TenantId"];
            TimeoutInSeconds = Convert.ToInt16(config["TimeoutInSeconds"]);
        }

        [Function("RefreshAnalysisServer")]
        public async Task Run([ServiceBusTrigger(SsasTopicName, SsasSubcriptionName, Connection = "ServiceBusConnection")] byte[] messageContent)
        {
            var tableName = string.Empty;
            var databaseName = string.Empty;
            var ProcessSessionId = string.Empty;
            var InstanceLogId = string.Empty;
            var partitionName = string.Empty;

            try
            {
                var body = Encoding.ASCII.GetString(messageContent);
                _logger.LogInformation("Message received from the service bus: {Body}", body);
                // Read the request body and parse it as JSON
                var requestData = System.Text.Json.JsonSerializer.Deserialize<SsasModel>(body);

                if(requestData != null)
                {
                    using var server = new Server();
                    ProcessSessionId = requestData.ProcessSessionId.ToString();
                    InstanceLogId = requestData.InstanceLogId.ToString();
                    server.Connect(this.GetConnectionString(requestData.DatabaseName, await TokenHelper.GetTokenAsync(tenantId, $"https://{aasRegion}.asazure.windows.net/.default")));
                    _logger.LogInformation("InstanceLogID: {InstanceLogId}; ProcessSessionId:{ProcessSessionId};Message:Token generation Successfull",
                        InstanceLogId, ProcessSessionId);
                    var m = server.Databases.GetByName(requestData.DatabaseName).Model;
                    _logger.LogInformation("InstanceLogID: {InstanceLogId}; ProcessSessionId:{ProcessSessionId};ModelName:{Server}({Database});Message:SSAS Server Connected",
                        InstanceLogId, ProcessSessionId, m.Server, m.Database);
                    var tabs = m.Tables.OrderBy(tabName => tabName.Name);
                    var iCount = 0;
                    _logger.LogInformation("InstanceLogID: {InstanceLogId}; ProcessSessionId:{ProcessSessionId};ModelName:{Server}({Database});RefreshType:{RefreshType}",
                        InstanceLogId, ProcessSessionId, m.Server, m.Database, requestData.RefreshType);

                    string[] partitionedTables = new string[]
                    {
                        "AuditDetails","AuditDetailsElement"
                    };

                    if(tabs != null)
                    {
                        foreach(var item in tabs)
                        {
                            Stopwatch stopwatch = new Stopwatch();
                            stopwatch.Start();
                            try
                            {
                                tableName = item.Name;
                                databaseName = m.Database.ToString();
                                iCount++;

                                if(requestData.RefreshType?.ToUpper() == "FULL")
                                {
                                    if(partitionedTables.Contains(tableName) && await checkAuditTableCount(InstanceLogId, ProcessSessionId, tableName, requestData.DatabaseName) > 0)
                                    {
                                        //Refresh Partition
                                        partitionName = "Y" + DateTime.UtcNow.Year + "-M" + DateTime.UtcNow.Month;
                                        item.Partitions[partitionName].RequestRefresh(RefreshType.Full);
                                        tableName = item.Name + "(" + partitionName + ")";
                                    }
                                    else
                                    {
                                        item.RequestRefresh(RefreshType.Full);
                                    }
                                }
                                else
                                {
                                    item.RequestRefresh(RefreshType.Automatic);

                                }
                                _logger.LogInformation("InstanceLogID:{InstanceLogId} ;ProcessSessionId:{ProcessSessionId};ModelName:{DatabaseName};tableName:{TableName};Message:Process Start({Count} of {TabsCount})",
                                    InstanceLogId, ProcessSessionId, databaseName, tableName, iCount, tabs.Count());
                                m.Model.SaveChanges();
                                stopwatch.Stop();
                                _logger.LogInformation("InstanceLogID:{InstanceLogId} ;ProcessSessionId:{ProcessSessionId};ModelName:{DatabaseName};tableName:{TableName};Message:Process End({Count} of {TabsCount});Status:Success;ProcessingTime:{ProcessingTime} ms",
                                    InstanceLogId, ProcessSessionId, databaseName, tableName, iCount, tabs.Count(), Math.Round(stopwatch.Elapsed.TotalMilliseconds));
                            }
                            catch(Exception ex)
                            {
                                // log the end event
                                // But then send to outer exception handler
                                // Since moving to Isolated Worker Hosting model, if we pass in the original exception into the logger, it will be logged but details missing in the Application Insights
                                stopwatch.Stop();
                                _logger.LogError(ex, "InstanceLogID:{InstanceLogId} ;ProcessSessionId:{ProcessSessionId};ModelName:{DatabaseName};tableName:{TableName};Message:Error Processing({Count} of {TabsCount});Status:Failed;ProcessingTime:{ProcessingTime} ms;ErrorMessage:{ErrorMessage}",
                                    InstanceLogId, ProcessSessionId, databaseName, tableName, iCount, tabs.Count(), Math.Round(stopwatch.Elapsed.TotalMilliseconds), ex.Message);

                                m.Model.UndoLocalChanges();
                            }
                        }
                    }
                    server.Disconnect();
                    _logger.LogInformation("InstanceLogID: {InstanceLogId}; ProcessSessionId:{ProcessSessionId};Database:{Server}({Database});Message:SSAS Server Disconnected",
                        InstanceLogId, ProcessSessionId, m.Server, m.Database);
                    _logger.LogInformation("InstanceLogID: {InstanceLogId}; ProcessSessionId:{ProcessSessionId};Database:{Server}({Database});Total Tables count:{TabsCount}",
                        InstanceLogId, ProcessSessionId, m.Server, m.Database, tabs?.Count());
                    _logger.LogInformation("InstanceLogID: {InstanceLogId}; ProcessSessionId:{ProcessSessionId};Database:{Server}({Database});Processed Tables count:{Count}",
                        InstanceLogId, ProcessSessionId, m.Server, m.Database, iCount);
                }

            }
            catch(Exception ex)
            {
                _logger.LogError(ex, "InstanceLogID:{InstanceLogId} ;ProcessSessionId:{ProcessSessionId};ModelName:{DatabaseName};Message:Error Processing for the model;ErrorMessage:{ErrorMessage}",
                        InstanceLogId, ProcessSessionId, databaseName, ex.Message);
                throw;
            }

        }

        private XElement ExceptionToXml(Exception ex)
        {
            var xml = new XElement("exception",
                new XElement("message", ex.Message),
                new XElement("stacktrace", ex.StackTrace),
                new XElement("innerException", ex.InnerException != null ? ExceptionToXml(ex.InnerException) : null));
            return xml;
        }
        private string GetConnectionString(string? databaseName, string token)
        {
            return $"Provider=MSOLAP;" +
                $"Data Source=asazure://{aasRegion}.asazure.windows.net/{aasServerName}:rw;" +
                $"Initial Catalog={databaseName};" +
                $"User ID=;" +
                $"Password={token};" +
                $"Persist Security Info=True;" +
                $"Impersonation Level=Impersonate";
        }

        private async Task<long> checkAuditTableCount(string? InstanceLogId, string? ProcessSessionId, string tableName, string? databaseName)
        {
            long rowCount = 0;
            try
            {
                string mdxQuery = $"SELECT DIMENSION_CAPTION AS TableName, DIMENSION_CARDINALITY AS RowCount FROM $system.MDSchema_Dimensions where DIMENSION_CAPTION = '{tableName}'";
                string strConn = this.GetConnectionString(databaseName, await TokenHelper.GetTokenAsync(tenantId, $"https://{aasRegion}.asazure.windows.net/.default"));
                using AdomdConnection conn = new AdomdConnection(strConn);
                conn.Open();
                using AdomdCommand cmd = new AdomdCommand(mdxQuery, conn);
                using AdomdDataAdapter da = new AdomdDataAdapter(cmd);
                using DataTable dt = new DataTable();
                da.Fill(dt);
                rowCount = Convert.ToInt64(dt.Rows[0]["RowCount"]);
                _logger.LogInformation("InstanceLogID:{InstanceLogId};ProcessSessionId:{ProcessSessionId};ModelName:{DatabaseName};tableName:{TableName};Partitioned table row count:{RowCount}",
                    InstanceLogId, ProcessSessionId, databaseName, tableName, rowCount);
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, "InstanceLogID:{InstanceLogId};ProcessSessionId:{ProcessSessionId};ModelName:{DatabaseName};tableName:{TableName};Message:Error checking AuditTableCount;ErrorMessage:{ErrorMessage}",
                    InstanceLogId, ProcessSessionId, databaseName, tableName, ex.Message);
                throw;
            }
            return rowCount;
        }



    }
}