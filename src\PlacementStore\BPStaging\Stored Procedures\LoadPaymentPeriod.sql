/*
Lineage
dbo.PaymentPeriod.PaymentPeriodId=BPStaging.PaymentPeriod.Id
dbo.PaymentPeriod.PaymentPeriod=BPStaging.PaymentPeriod.Text
*/
CREATE PROCEDURE BPStaging.LoadPaymentPeriod
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PaymentPeriod';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.PaymentPeriod T
    USING (SELECT PaymentPeriodId = Id, PaymentPeriod = Text, IsDeprecated = 0 FROM BPStaging.PaymentPeriod) S
    ON T.PaymentPeriodId = S.PaymentPeriodId
    WHEN NOT MATCHED
        THEN INSERT (
                 PaymentPeriodId
               , PaymentPeriod
               , IsDeprecated
               , LastUpdatedUTCDate
               , CreatedUTCDate
             )
             VALUES
                 (
                     S.PaymentPeriodId
                   , S.PaymentPeriod
                   , S.IsDeprecated
                   , GETUTCDATE()
                   , GETUTCDATE()
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT T.PaymentPeriod, T.IsDeprecated INTERSECT SELECT S.PaymentPeriod, S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.PaymentPeriod = S.PaymentPeriod
               , T.IsDeprecated = S.IsDeprecated
               , T.LastUpdatedUTCDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
