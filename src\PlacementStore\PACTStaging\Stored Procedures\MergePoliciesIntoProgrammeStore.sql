/*
Lineage
dbo.Policy.PACTPolicyId=PACTStaging.vwPolicy.PACTPolicyId
dbo.Policy.PolicyReference=PACTStaging.vwPolicy.PolicyReference
dbo.Policy.PolicyDescription=PACTStaging.vwPolicy.PolicyDescription
dbo.Policy.DataSourceInstanceId=PACTStaging.vwPolicy.DataSourceInstanceId
dbo.Policy.PolicyKey=PACTStaging.vwPolicy.PolicyKey
dbo.Policy.SourceQueryId=PACTStaging.vwPolicy.SourceQueryId
dbo.Policy.PolicyStatusKey=PACTStaging.vwPolicy.PolicyStatusKey
dbo.Policy.InceptionDate=PACTStaging.vwPolicy.InceptionDate
dbo.Policy.OpportunityTypeId=PACTStaging.vwPolicy.OpportunityTypeId
dbo.Policy.ExpiryDate=PACTStaging.vwPolicy.ExpiryDate
dbo.Policy.InsuranceTypeId=PACTStaging.vwPolicy.InsuranceTypeId
dbo.Policy.IsDeleted=PACTStaging.vwPolicy.IsDeleted
dbo.Policy.CustomAttribute=PACTStaging.vwPolicy.Attributes
dbo.Policy.SumInsured=PACTStaging.vwPolicy.SumInsured
dbo.Policy.SumInsuredCurrencyId=PACTStaging.vwPolicy.SumInsuredGlobalCurrencyId
dbo.Policy.RenewedFromPolicyId=PACTStaging.vwPolicy.RenewedFromPolicyId
dbo.Policy.RenewedFromPolicyId=dbo.Policy.PolicyId
dbo.Policy.RenewedFromPolicyKey=PACTStaging.vwPolicy.RenewedFromPolicyKey
dbo.Policy.PolicyTypeKey=PACTStaging.vwPolicy.PolicyTypeKey
dbo.Policy.RefPolicyStatusId=PACTStaging.vwPolicy.RefPolicyStatusId
dbo.Policy.ParentId=PACTStaging.vwPolicy.ParentId
dbo.Policy.ParentId=dbo.Policy.PolicyId
dbo.Policy.RenewalDate=PACTStaging.vwPolicy.RenewalDate
dbo.Policy.SegmentCode=PACTStaging.vwPolicy.SegmentCode
dbo.Policy.SegmentCode=PACTStaging.rpt_vwPolicy.SegmentCode
dbo.Policy.RenewedFromPACTPolicyId=PACTStaging.vwPolicy.RenewedFromPACTPolicyId
dbo.Policy.ParentKey=PACTStaging.vwPolicy.ParentPolicyKey
dbo.Policy.ParentPACTPolicyId=PACTStaging.vwPolicy.ParentPACTPolicyId
dbo.Policy.SourceUpdatedDate=PACTStaging.vwPolicy.ETLUpdatedDate
dbo.Policy.FinancialGeographyId=PACTStaging.vwPolicy.FinancialGeographyId
dbo.Policy.FinancialLegalEntityId=PACTStaging.vwPolicy.FinancialLegalEntityId
dbo.Policy.FinancialSegmentId=PACTStaging.vwPolicy.FinancialSegmentId
*/
CREATE PROCEDURE PACTStaging.MergePoliciesIntoProgrammeStore (
    @Success BIT OUTPUT
)
AS
SET XACT_ABORT, NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.Policy';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

DECLARE @AmountsActions TABLE (
    Change VARCHAR(20)
);

DECLARE @NullSegmentMarkedDeleted INT;
DECLARE @RenewedFromUpdateCount INT;
DECLARE @ParentUpdateCount INT;
DECLARE
    @AmountsInsertedCount INT
  , @AmountsUpdatedCount  INT
  , @AmountsDeletedCount  INT;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    BEGIN TRANSACTION;

    DROP TABLE IF EXISTS #PolicyUpdates;

    SELECT
        PACTPolicyId
      , PolicyReference
      , PolicyDescription
      , DataSourceInstanceId
      , PolicyKey
      , SourceQueryId
      , PolicyStatusKey
      , InceptionDate
      , OpportunityTypeId
      , PACTOpportunityTypeId
      , ExpiryDate
      , InsuranceTypeId
      , PACTInsuranceTypeId
      , IsDeleted
      , SourceUpdatedDate = ETLUpdatedDate
      , CustomAttribute = Attributes
      , SumInsured
      , SumInsuredCurrencyId = SumInsuredGlobalCurrencyId
      , RenewedFromPolicyId
      , RenewedFromPolicyKey
      , RenewedFromPACTPolicyId
      , PolicyTypeKey
      , PACTPolicyTypeId
      , RefPolicyStatusId
      , ParentId
      , ParentKey = ParentPolicyKey
      , ParentPACTPolicyId
      , RenewalDate
      , SegmentCode
      , FinancialGeographyId
      , FinancialLegalEntityId
      , FinancialSegmentId
      , InInclusionCriteria
    INTO #PolicyUpdates
    FROM
        PACTStaging.vwPolicy
    WHERE
        InceptionDate IS NOT NULL
        AND ETLCreatedDate IS NOT NULL; /* How PACT shows a record that we no longer have access to */

    /* Split out the merge */
    UPDATE T
    SET
        T.PACTPolicyId = S.PACTPolicyId
      , T.PolicyReference = S.PolicyReference
      , T.PolicyDescription = S.PolicyDescription
      , T.DataSourceInstanceId = S.DataSourceInstanceId
      , T.PolicyKey = S.PolicyKey
      , T.SourceQueryId = S.SourceQueryId
      , T.PolicyStatusKey = S.PolicyStatusKey
      , T.InceptionDate = S.InceptionDate
      , T.OpportunityTypeId = S.OpportunityTypeId
      , T.ExpiryDate = S.ExpiryDate
      , T.InsuranceTypeId = S.InsuranceTypeId
      , T.IsDeleted = S.IsDeleted
      , T.CustomAttribute = S.CustomAttribute
      , T.SumInsured = S.SumInsured
      , T.SumInsuredCurrencyId = S.SumInsuredCurrencyId
      , T.RenewedFromPolicyId = S.RenewedFromPolicyId
      , T.RenewedFromPolicyKey = S.RenewedFromPolicyKey
      , T.PolicyTypeKey = S.PolicyTypeKey
      , T.RefPolicyStatusId = S.RefPolicyStatusId
      , T.ParentId = S.ParentId
      , T.RenewalDate = S.RenewalDate
      , T.SegmentCode = S.SegmentCode
      , T.RenewedFromPACTPolicyId = S.RenewedFromPACTPolicyId
      , T.ParentKey = S.ParentKey
      , T.ParentPACTPolicyId = S.ParentPACTPolicyId
      , T.ETLUpdatedDate = GETUTCDATE()
      , T.SourceUpdatedDate = S.SourceUpdatedDate
      , T.FinancialGeographyId = S.FinancialGeographyId
      , T.FinancialLegalEntityId = S.FinancialLegalEntityId
      , T.FinancialSegmentId = S.FinancialSegmentId
    FROM
        dbo.Policy T
        INNER JOIN #PolicyUpdates S
            ON T.PACTPolicyId = S.PACTPolicyId
    WHERE
        NOT EXISTS (
        SELECT
            T.PACTPolicyId
          , T.PolicyReference
          , T.PolicyDescription
          , T.DataSourceInstanceId
          , T.PolicyKey
          , T.SourceQueryId
          , T.PolicyStatusKey
          , T.InceptionDate
          , T.OpportunityTypeId
          , T.ExpiryDate
          , T.InsuranceTypeId
          , T.IsDeleted
          , CAST(T.CustomAttribute AS NVARCHAR(MAX))
          , T.SumInsured
          , T.SumInsuredCurrencyId
          , T.RenewedFromPolicyId
          , T.RenewedFromPolicyKey
          , T.PolicyTypeKey
          , T.RefPolicyStatusId
          , T.ParentId
          , T.RenewalDate
          , T.SegmentCode
          , T.RenewedFromPACTPolicyId
          , T.ParentKey
          , T.ParentPACTPolicyId
          , T.FinancialGeographyId
          , T.FinancialLegalEntityId
          , T.FinancialSegmentId
        INTERSECT
        SELECT
            S.PACTPolicyId
          , S.PolicyReference
          , S.PolicyDescription
          , S.DataSourceInstanceId
          , S.PolicyKey
          , S.SourceQueryId
          , S.PolicyStatusKey
          , S.InceptionDate
          , S.OpportunityTypeId
          , S.ExpiryDate
          , S.InsuranceTypeId
          , S.IsDeleted
          , CAST(S.CustomAttribute AS NVARCHAR(MAX))
          , S.SumInsured
          , S.SumInsuredCurrencyId
          , S.RenewedFromPolicyId
          , S.RenewedFromPolicyKey
          , S.PolicyTypeKey
          , S.RefPolicyStatusId
          , S.ParentId
          , S.RenewalDate
          , S.SegmentCode
          , S.RenewedFromPACTPolicyId
          , S.ParentKey
          , S.ParentPACTPolicyId
          , S.FinancialGeographyId
          , S.FinancialLegalEntityId
          , S.FinancialSegmentId
    );

    SELECT @UpdatedCount = ISNULL(@UpdatedCount, 0) + @@ROWCOUNT;

    INSERT INTO
        dbo.Policy
        (
            PACTPolicyId
          , PolicyReference
          , PolicyDescription
          , DataSourceInstanceId
          , PolicyKey
          , SourceQueryId
          , PolicyStatusKey
          , InceptionDate
          , OpportunityTypeId
          , ExpiryDate
          , InsuranceTypeId
          , IsDeleted
          , CustomAttribute
          , SumInsured
          , SumInsuredCurrencyId
          , RenewedFromPolicyId
          , RenewedFromPolicyKey
          , PolicyTypeKey
          , RefPolicyStatusId
          , ParentId
          , RenewalDate
          , SegmentCode
          , RenewedFromPACTPolicyId
          , ParentKey
          , ParentPACTPolicyId
          , ETLCreatedDate
          , ETLUpdatedDate
          , SourceUpdatedDate
          , FinancialGeographyId
          , FinancialLegalEntityId
          , FinancialSegmentId
        )
    SELECT
        S.PACTPolicyId
      , S.PolicyReference
      , S.PolicyDescription
      , S.DataSourceInstanceId
      , S.PolicyKey
      , S.SourceQueryId
      , S.PolicyStatusKey
      , S.InceptionDate
      , S.OpportunityTypeId
      , S.ExpiryDate
      , S.InsuranceTypeId
      , S.IsDeleted
      , S.CustomAttribute
      , S.SumInsured
      , S.SumInsuredCurrencyId
      , S.RenewedFromPolicyId
      , S.RenewedFromPolicyKey
      , S.PolicyTypeKey
      , S.RefPolicyStatusId
      , S.ParentId
      , S.RenewalDate
      , S.SegmentCode
      , S.RenewedFromPACTPolicyId
      , S.ParentKey
      , S.ParentPACTPolicyId
      , GETUTCDATE()
      , GETUTCDATE()
      , S.SourceUpdatedDate
      , S.FinancialGeographyId
      , S.FinancialLegalEntityId
      , S.FinancialSegmentId
    FROM
        #PolicyUpdates S
    WHERE
        NOT EXISTS (
        SELECT * FROM dbo.Policy T WHERE T.PACTPolicyId = S.PACTPolicyId
    )
        AND S.InInclusionCriteria = 1 /* Only add if matches criteria to be included */;

    ;

    SELECT @InsertedCount = ISNULL(@InsertedCount, 0) + @@ROWCOUNT;

    /*
    MERGE dbo.Policy T
    USING (
        SELECT
            PACTPolicyId
          , PolicyReference
          , PolicyDescription
          , DataSourceInstanceId
          , PolicyKey
          , SourceQueryId
          , PolicyStatusID
          , InceptionDate
          , OpportunityTypeId
          , PACTOpportunityTypeId
          , ExpiryDate
          , InsuranceTypeId
          , PACTInsuranceTypeId
          , IsDeleted
          , SourceUpdatedDate = ETLUpdatedDate
          , CustomAttribute
          , SumInsured
          , SumInsuredCurrencyId
          , RenewedFromPolicyId
          , RenewedFromPolicyKey
          , RenewedFromPACTPolicyId
          , PolicyTypeKey
          , PACTPolicyTypeId
          , RefPolicyStatusId
          , ParentId
          , ParentKey
          , ParentPACTPolicyId
          , RenewalDate
          , SegmentCode
          , FinancialGeographyId
          , FinancialLegalEntityId
          , FinancialSegmentId
          , InInclusionCriteria
        FROM
            #PolicyUpdates
    ) S
    ON T.PACTPolicyId = S.PACTPolicyId
    WHEN NOT MATCHED AND InInclusionCriteria = 1 /* Only add if matches criteria to be included */
        THEN INSERT (
                 PACTPolicyId
               , PolicyReference
               , PolicyDescription
               , DataSourceInstanceId
               , PolicyKey
               , SourceQueryId
               , PolicyStatusId
               , InceptionDate
               , OpportunityTypeId
               , ExpiryDate
               , InsuranceTypeId
               , IsDeleted
               , CustomAttribute
               , SumInsured
               , SumInsuredCurrencyId
               , RenewedFromPolicyId
               , RenewedFromPolicyKey
               , PolicyTypeKey
               , RefPolicyStatusId
               , ParentId
               , RenewalDate
               , SegmentCode
               , RenewedFromPACTPolicyId
               , ParentKey
               , ParentPACTPolicyId
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , FinancialGeographyId
               , FinancialLegalEntityId
               , FinancialSegmentId
             )
             VALUES
                 (
                     S.PACTPolicyId
                   , S.PolicyReference
                   , S.PolicyDescription
                   , S.DataSourceInstanceId
                   , S.PolicyKey
                   , S.SourceQueryId
                   , S.PolicyStatusID
                   , S.InceptionDate
                   , S.OpportunityTypeId
                   , S.ExpiryDate
                   , S.InsuranceTypeId
                   , S.IsDeleted
                   , S.CustomAttribute
                   , S.SumInsured
                   , S.SumInsuredCurrencyId
                   , S.RenewedFromPolicyId
                   , S.RenewedFromPolicyKey
                   , S.PolicyTypeKey
                   , S.RefPolicyStatusId
                   , S.ParentId
                   , S.RenewalDate
                   , S.SegmentCode
                   , S.RenewedFromPACTPolicyId
                   , S.ParentKey
                   , S.ParentPACTPolicyId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.FinancialGeographyId
                   , S.FinancialLegalEntityId
                   , S.FinancialSegmentId
                 )

    /* Updated Policies */
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.PACTPolicyId
                               , T.PolicyReference
                               , T.PolicyDescription
                               , T.DataSourceInstanceId
                               , T.PolicyKey
                               , T.SourceQueryId
                               , T.PolicyStatusId
                               , T.InceptionDate
                               , T.OpportunityTypeId
                               , T.ExpiryDate
                               , T.InsuranceTypeId
                               , T.IsDeleted
                               , CAST(T.CustomAttribute AS NVARCHAR(MAX))
                               , T.SumInsured
                               , T.SumInsuredCurrencyId
                               , T.RenewedFromPolicyId
                               , T.RenewedFromPolicyKey
                               , T.PolicyTypeKey
                               , T.RefPolicyStatusId
                               , T.ParentId
                               , T.RenewalDate
                               , T.SegmentCode
                               , T.RenewedFromPACTPolicyId
                               , T.ParentKey
                               , T.ParentPACTPolicyId
                               , T.FinancialGeographyId
                               , T.FinancialLegalEntityId
                               , T.FinancialSegmentId
                             INTERSECT
                             SELECT
                                 S.PACTPolicyId
                               , S.PolicyReference
                               , S.PolicyDescription
                               , S.DataSourceInstanceId
                               , S.PolicyKey
                               , S.SourceQueryId
                               , S.PolicyStatusID
                               , S.InceptionDate
                               , S.OpportunityTypeId
                               , S.ExpiryDate
                               , S.InsuranceTypeId
                               , S.IsDeleted
                               , CAST(S.CustomAttribute AS NVARCHAR(MAX))
                               , S.SumInsured
                               , S.SumInsuredCurrencyId
                               , S.RenewedFromPolicyId
                               , S.RenewedFromPolicyKey
                               , S.PolicyTypeKey
                               , S.RefPolicyStatusId
                               , S.ParentId
                               , S.RenewalDate
                               , S.SegmentCode
                               , S.RenewedFromPACTPolicyId
                               , S.ParentKey
                               , S.ParentPACTPolicyId
                               , S.FinancialGeographyId
                               , S.FinancialLegalEntityId
                               , S.FinancialSegmentId
                         )
        THEN UPDATE SET
                 T.PACTPolicyId = S.PACTPolicyId
               , T.PolicyReference = S.PolicyReference
               , T.PolicyDescription = S.PolicyDescription
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PolicyKey = S.PolicyKey
               , T.SourceQueryId = S.SourceQueryId
               , T.PolicyStatusId = S.PolicyStatusID
               , T.InceptionDate = S.InceptionDate
               , T.OpportunityTypeId = S.OpportunityTypeId
               , T.ExpiryDate = S.ExpiryDate
               , T.InsuranceTypeId = S.InsuranceTypeId
               , T.IsDeleted = S.IsDeleted
               , T.CustomAttribute = S.CustomAttribute
               , T.SumInsured = S.SumInsured
               , T.SumInsuredCurrencyId = S.SumInsuredCurrencyId
               , T.RenewedFromPolicyId = S.RenewedFromPolicyId
               , T.RenewedFromPolicyKey = S.RenewedFromPolicyKey
               , T.PolicyTypeKey = S.PolicyTypeKey
               , T.RefPolicyStatusId = S.RefPolicyStatusId
               , T.ParentId = S.ParentId
               , T.RenewalDate = S.RenewalDate
               , T.SegmentCode = S.SegmentCode
               , T.RenewedFromPACTPolicyId = S.RenewedFromPACTPolicyId
               , T.ParentKey = S.ParentKey
               , T.ParentPACTPolicyId = S.ParentPACTPolicyId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.FinancialGeographyId = S.FinancialGeographyId
               , T.FinancialLegalEntityId = S.FinancialLegalEntityId
               , T.FinancialSegmentId = S.FinancialSegmentId
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
        */
    COMMIT TRANSACTION;

    BEGIN TRANSACTION;

    -- Set the Renewed from Policy IDs
    UPDATE PO
    SET
        RenewedFromPolicyId = RFP.PolicyId
      , ETLUpdatedDate = GETUTCDATE()
    FROM
        dbo.Policy PO
        INNER JOIN dbo.Policy RFP
            ON RFP.PACTPolicyId = PO.RenewedFromPACTPolicyId
    WHERE
        PO.RenewedFromPolicyId <> RFP.PolicyId;

    SELECT @UpdatedCount = ISNULL(@UpdatedCount, 0) + @@ROWCOUNT;

    -- Set the Parent Ids
    UPDATE PO
    SET
        ParentId = PPO.PolicyId
      , ETLUpdatedDate = GETUTCDATE()
    FROM
        dbo.Policy PO
        INNER JOIN dbo.Policy PPO
            ON PPO.PACTPolicyId = PO.ParentPACTPolicyId
    WHERE
        PO.ParentId <> PPO.PolicyId;

    SELECT @UpdatedCount = @UpdatedCount + @@ROWCOUNT;

    --> Logically Remove Non-CRB policies
    UPDATE dbo.Policy
    SET
        IsDeleted = 1
      , ETLUpdatedDate = GETUTCDATE()
    WHERE
        SegmentCode IS NULL
        AND IsDeleted = 0;

    SELECT @UpdatedCount = @UpdatedCount + @@ROWCOUNT;

    /*
            Mark any Policy deleted if we have records in the PACTStaging.rpt_vwPolicy table which have a NULL ETLCreatedDate or InceptionDate.
            This is usually because they have moved segment in which case the update is not possible by normal update path
            as PACT sends NULL in columns that are not normally NULL.
        */
    UPDATE P
    SET
        P.IsDeleted = 1
      , P.ETLUpdatedDate = GETUTCDATE()
      , P.SegmentCode = PD.SegmentCode /* As I have it I may as well add it for information */
    FROM
        dbo.Policy P
        INNER JOIN PACTStaging.rpt_vwPolicy PD
            ON PD.PolicyId = P.PACTPolicyId
    WHERE
        P.IsDeleted = 0
        AND (
            PD.ETLCreatedDate IS NULL
            OR PD.InceptionDate IS NULL
        );

    SELECT @UpdatedCount = @UpdatedCount + @@ROWCOUNT;

    COMMIT TRANSACTION;
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;

    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @Success = 0;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
