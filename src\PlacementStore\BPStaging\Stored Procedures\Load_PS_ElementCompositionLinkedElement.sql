/*
Lineage
PS.ElementCompositionLinkedElement.SourceElementCompositionLinkedElementKey=BPStaging.ElementCompositionLinkedElement.Id
PS.ElementCompositionLinkedElement.ElementCompositionLinkedElementId=BPStaging.ElementCompositionLinkedElement.Id
PS.ElementCompositionLinkedElement.RootElementId=BPStaging.ElementCompositionLinkedElement.RootElementId
PS.ElementCompositionLinkedElement.AnchorElementId=BPStaging.ElementCompositionLinkedElement.AnchorElementId
PS.ElementCompositionLinkedElement.Index=BPStaging.ElementCompositionLinkedElement.Index
PS.ElementCompositionLinkedElement.LinkedRootElementId=BPStaging.ElementCompositionLinkedElement.LinkedRootElementId
PS.ElementCompositionLinkedElement.LinkedElementId=BPStaging.ElementCompositionLinkedElement.LinkedElementId
PS.ElementCompositionLinkedElement.SourceUpdatedDate=BPStaging.ElementCompositionLinkedElement.ValidTo
PS.ElementCompositionLinkedElement.SourceUpdatedDate=BPStaging.ElementCompositionLinkedElement.ValidFrom
PS.ElementCompositionLinkedElement.IsDeleted=BPStaging.ElementCompositionLinkedElement.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_ElementCompositionLinkedElement
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ElementCompositionLinkedElement';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.ElementCompositionLinkedElement T
    USING (
        SELECT
            SourceElementCompositionLinkedElementKey = CONCAT('ECLE|', Id)
          , DataSourceInstanceId = 50366
          , ElementCompositionLinkedElementId = Id
          , RootElementId
          , AnchorElementId
          , [Index]
          , LinkedRootElementId
          , LinkedElementId
          , SourceUpdatedDate = IIF(YEAR(ValidTo) < 9999, ValidTo, ValidFrom)
          , IsDeleted = IIF(YEAR(ValidTo) < 9999, 1, 0)
        FROM (
        SELECT
            Id
          , RootElementId
          , AnchorElementId
          , [Index]
          , LinkedRootElementId
          , LinkedElementId
          , ValidFrom
          , ValidTo
          , RowNumber = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ElementCompositionLinkedElement
    ) inner_select
        WHERE
            inner_select.RowNumber = 1
    ) S
    ON S.SourceElementCompositionLinkedElementKey = T.SourceElementCompositionLinkedElementKey
       AND S.DataSourceInstanceId = T.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 SourceElementCompositionLinkedElementKey
               , DataSourceInstanceId
               , ElementCompositionLinkedElementId
               , RootElementId
               , AnchorElementId
               , [Index]
               , LinkedRootElementId
               , LinkedElementId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.SourceElementCompositionLinkedElementKey
                   , S.DataSourceInstanceId
                   , S.ElementCompositionLinkedElementId
                   , S.RootElementId
                   , S.AnchorElementId
                   , S.[Index]
                   , S.LinkedRootElementId
                   , S.LinkedElementId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.SourceElementCompositionLinkedElementKey
                               , T.DataSourceInstanceId
                               , T.RootElementId
                               , T.AnchorElementId
                               , T.[Index]
                               , T.LinkedRootElementId
                               , T.LinkedElementId
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                             INTERSECT
                             SELECT
                                 S.SourceElementCompositionLinkedElementKey
                               , S.DataSourceInstanceId
                               , S.RootElementId
                               , S.AnchorElementId
                               , S.[Index]
                               , S.LinkedRootElementId
                               , S.LinkedElementId
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                         )
        THEN UPDATE SET
                 T.SourceElementCompositionLinkedElementKey = S.SourceElementCompositionLinkedElementKey
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RootElementId = S.RootElementId
               , T.AnchorElementId = S.AnchorElementId
               , T.[Index] = S.[Index]
               , T.LinkedRootElementId = S.LinkedRootElementId
               , T.LinkedElementId = S.LinkedElementId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN;