/*
Lineage
dbo.ValueTypeLookupValue.ValueTypeLookupValueId=BPStaging.ValueTypeLookupValue.Id
dbo.ValueTypeLookupValue.ValueTypeId=BPStaging.ValueTypeLookupValue.ValueTypeId
dbo.ValueTypeLookupValue.Value=BPStaging.ValueTypeLookupValue.Value
dbo.ValueTypeLookupValue.IsDeprecated=BPStaging.ValueTypeLookupValue.IsDeprecated
dbo.ValueTypeLookupValue.IsDeprecated=BPStaging.ValueTypeLookupValue.ValidTo
dbo.ValueTypeLookupValue.ExternalCode=BPStaging.ValueTypeLookupValue.ExternalCode
dbo.ValueTypeLookupValue.SourceUpdatedDate=BPStaging.ValueTypeLookupValue.ValidTo
dbo.ValueTypeLookupValue.SourceUpdatedDate=BPStaging.ValueTypeLookupValue.ValidFrom
*/
CREATE PROCEDURE BPStaging.LoadValueTypeLookupValue
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.ValueTypeLookupValue';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ValueTypeLookupValue)
BEGIN TRY
    MERGE INTO dbo.ValueTypeLookupValue T
    USING (
        SELECT
            ValueTypeLookupValueId = Id
          , ValueTypeId
          , Value
          , ExternalCode
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                         THEN inner_select.ValidTo
                                     ELSE inner_select.ValidFrom END
          , IsDeprecated = CASE WHEN IsDeprecated = 1
                                    THEN 1
                                WHEN YEAR(inner_select.ValidTo) < 9999
                                    THEN 1
                                ELSE 0 END
        FROM (
        SELECT
            Id
          , ValueTypeId
          , Value
          , ExternalCode
          , IsDeprecated
          , ValidTo
          , ValidFrom
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ValueTypeLookupValue
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.ValueTypeLookupValueId = S.ValueTypeLookupValueId
    WHEN NOT MATCHED
        THEN INSERT (
                 ValueTypeLookupValueId
               , ValueTypeId
               , Value
               , IsDeprecated
               , CreatedUTCDate
               , LastUpdatedUTCDate
               , ExternalCode
               , DataSourceInstanceId
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.ValueTypeLookupValueId
                   , S.ValueTypeId
                   , S.Value
                   , S.IsDeprecated
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.ExternalCode
                   , S.DataSourceInstanceId
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.ValueTypeId
      , S.Value
      , S.IsDeprecated
      , S.ExternalCode
      , S.DataSourceInstanceId
    INTERSECT
    SELECT
        T.ValueTypeId
      , T.Value
      , T.IsDeprecated
      , T.ExternalCode
      , T.DataSourceInstanceId
)
        THEN UPDATE SET
                 T.ValueTypeId = S.ValueTypeId
               , T.Value = S.Value
               , T.IsDeprecated = S.IsDeprecated
               , T.ExternalCode = S.ExternalCode
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.LastUpdatedUTCDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;