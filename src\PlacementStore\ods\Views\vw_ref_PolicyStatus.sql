/*
Lineage
DataSourceInstanceId=PAS.PolicyStatus.DataSourceInstanceId
PolicyStatusKey=PAS.PolicyStatus.PolicyStatusKey
PolicyStatus=PAS.PolicyStatus.PolicyStatus
PACTPolicyStatusId=PAS.PolicyStatus.PASPolicyStatusId
RefPolicyStatusId=PAS.PolicyStatus.RefPolicyStatusId
ETLCreatedDate=PAS.PolicyStatus.ETLCreatedDate
ETLUpdatedDate=PAS.PolicyStatus.ETLUpdatedDate
SourceUpdatedDate=PAS.PolicyStatus.SourceUpdatedDate
IsDeprecated=PAS.PolicyStatus.IsDeleted
*/

CREATE VIEW ods.vw_ref_PolicyStatus
AS
SELECT
    PolicyStatusId = -1
  , DataSourceInstanceId
  , PolicyStatusKey
  , PolicyStatus = ISNULL(PolicyStatus,'')
  , PACTPolicyStatusId = PASPolicyStatusId
  , RefPolicyStatusId
  , ETLCreatedDate
  , ETLUpdatedDate
  , SourceUpdatedDate
  , IsDeprecated = IsDeleted
FROM
    PAS.PolicyStatus;
GO

-- Add View Level Detail
EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The view from Placement Store which contains details of the Policy Status '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus';
GO

-- Add Column Level Detail
EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id for the Policy Status from Placement Store '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyStatusId';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id from ERD which describes the source of this data'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'DataSourceInstanceId';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique key for the Policy Status from Placement Store'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyStatusKey';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The status of a Policy at a point in time '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyStatus';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique PACT Id for a Policy Status '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'PACTPolicyStatusId';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The ERD Id for a Policy Status '
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'RefPolicyStatusId';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The UTC date when the record was created in the Placement Store'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'ETLCreatedDate';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The UTC date when the record was last updated in the Placement Store'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'ETLUpdatedDate';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'The date when the record was last updated in the source system'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'SourceUpdatedDate';
GO

EXEC sp_addextendedproperty
    @name = N'Description'
  , @value = N'An indicator to show if the record has been deprecated'
  , @level0type = N'SCHEMA'
  , @level0name = N'ods'
  , @level1type = N'VIEW'
  , @level1name = N'vw_ref_PolicyStatus'
  , @level2type = N'COLUMN'
  , @level2name = N'IsDeprecated';
GO