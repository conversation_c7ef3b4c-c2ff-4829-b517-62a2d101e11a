/*
Lineage
PS.MarketQuoteResponse.SourceMarketQuoteResponseKey=BPStaging.MarketQuoteResponse.Id
PS.MarketQuoteResponse.MarketResponseId=PS.MarketResponse.MarketResponseId
PS.MarketQuoteResponse.PremiumCurrencyId=BPStaging.MarketQuoteResponse.PremiumCurrencyId
PS.MarketQuoteResponse.Premium=BPStaging.MarketQuoteResponse.Premium
PS.MarketQuoteResponse.OfferedLine=BPStaging.MarketQuoteResponse.OfferedLine
PS.MarketQuoteResponse.PremiumRate=BPStaging.MarketQuoteResponse.PremiumRate
PS.MarketQuoteResponse.CommissionRate=BPStaging.MarketQuoteResponse.CommissionRate
PS.MarketQuoteResponse.Subjectivity=BPStaging.MarketQuoteResponse.Subjectivity
PS.MarketQuoteResponse.IsPremiumRatePerMille=BPStaging.MarketQuoteResponse.IsPremiumRatePerMille
PS.MarketQuoteResponse.OfferedLineRate=BPStaging.MarketQuoteResponse.OfferedLineRate
PS.MarketQuoteResponse.OutcomeStatusId=BPStaging.MarketQuoteResponse.OutcomeStatusId
PS.MarketQuoteResponse.QuotedToLead=BPStaging.MarketQuoteResponse.QuotedToLead
PS.MarketQuoteResponse.QuotedToLead=ref.FollowType.FollowType
PS.MarketQuoteResponse.OutcomeReasonId=BPStaging.MarketQuoteResponse.OutcomeReasonId
PS.MarketQuoteResponse.AdditionalPolicyCostCurrencyId=BPStaging.MarketQuoteResponse.AdditionalPolicyCostCurrencyId
PS.MarketQuoteResponse.AdditionalPolicyCost=BPStaging.MarketQuoteResponse.AdditionalPolicyCost
PS.MarketQuoteResponse.FrontingCarrierId=BPStaging.MarketQuoteResponse.FrontingCarrierId
PS.MarketQuoteResponse.NetPremium=BPStaging.MarketQuoteResponse.NetPremium
PS.MarketQuoteResponse.CommissionCurrencyId=BPStaging.MarketQuoteResponse.CommissionCurrencyId
PS.MarketQuoteResponse.Commission=BPStaging.MarketQuoteResponse.Commission
PS.MarketQuoteResponse.QuoteExpiryDate=BPStaging.MarketQuoteResponse.QuoteExpiryDate
PS.MarketQuoteResponse.BindRequestedDate=BPStaging.MarketQuoteResponse.BindRequestedDate
PS.MarketQuoteResponse.BoundDate=BPStaging.MarketQuoteResponse.BoundDate
PS.MarketQuoteResponse.SignedLine=BPStaging.MarketQuoteResponse.SignedLine
PS.MarketQuoteResponse.SignedLineRate=BPStaging.MarketQuoteResponse.SignedLineRate
PS.MarketQuoteResponse.TriaRequired=BPStaging.MarketQuoteResponse.TriaRequired
PS.MarketQuoteResponse.TriaIncluded=BPStaging.MarketQuoteResponse.TriaIncluded
PS.MarketQuoteResponse.TriaPremium=BPStaging.MarketQuoteResponse.TriaPremium
PS.MarketQuoteResponse.TriaPremiumCurrencyId=BPStaging.MarketQuoteResponse.TriaPremiumCurrencyId
PS.MarketQuoteResponse.DeductibleCurrencyId=BPStaging.MarketQuoteResponse.DeductibleCurrencyId
PS.MarketQuoteResponse.Deductible=BPStaging.MarketQuoteResponse.Deductible
PS.MarketQuoteResponse.IsOverride=BPStaging.MarketQuoteResponse.IsOverride
PS.MarketQuoteResponse.AdditionalDetails=BPStaging.MarketQuoteResponse.AdditionalDetails
PS.MarketQuoteResponse.PrimaryRate=BPStaging.MarketQuoteResponse.PrimaryRate
PS.MarketQuoteResponse.PrimaryExposureValue=BPStaging.MarketQuoteResponse.PrimaryExposureValue
PS.MarketQuoteResponse.FollowTypeId=BPStaging.MarketQuoteResponse.FollowTypeId
PS.MarketQuoteResponse.OfferedLineRateOfOrder=BPStaging.MarketQuoteResponse.OfferedLineRateOfOrder
PS.MarketQuoteResponse.MinimumLine=BPStaging.MarketQuoteResponse.MinimumLine
PS.MarketQuoteResponse.MinimumLineRate=BPStaging.MarketQuoteResponse.MinimumLineRate
PS.MarketQuoteResponse.MinimumLineRateOfOrder=BPStaging.MarketQuoteResponse.MinimumLineRateOfOrder
PS.MarketQuoteResponse.SourceUpdatedDate=BPStaging.MarketQuoteResponse.ValidTo
PS.MarketQuoteResponse.SourceUpdatedDate=BPStaging.MarketQuoteResponse.ValidFrom
PS.MarketQuoteResponse.IsDeleted=BPStaging.MarketQuoteResponse.ValidTo
PS.MarketQuoteResponse.FirstResponseAcceptedDate=BPStaging.MarketQuoteResponseAcceptedDates.FirstResponseAcceptedDate
PS.MarketQuoteResponse.LastResponseAcceptedDate=BPStaging.MarketQuoteResponseAcceptedDates.LastResponseAcceptedDate
*/

CREATE PROCEDURE BPStaging.Load_PS_MarketQuoteResponse
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketQuoteResponse';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    -- Create source query for MarketQuoteResponse
    DROP TABLE IF EXISTS #MarketQuoteResponseSource;

    SELECT
        mqr.Id
      , SourceKey = CONCAT('MQR|', mqr.Id)
      , DataSourceInstanceId = 50366
      , mr.MarketResponseId
      , mqr.PremiumCurrencyId
      , mqr.Premium
      , mqr.OfferedLine
      , mqr.PremiumRate
      , mqr.CommissionRate
      , mqr.Subjectivity
      , mqr.IsPremiumRatePerMille
      , mqr.OfferedLineRate
      , mqr.OutcomeStatusId
      , QuotedToLead = CASE WHEN f.FollowType = 'Co-Lead'
                                THEN 1
                            ELSE mqr.QuotedToLead END
      , mqr.OutcomeReasonId
      , mqr.AdditionalPolicyCostCurrencyId
      , mqr.AdditionalPolicyCost
      , mqr.FrontingCarrierId
      , mqr.NetPremium
      , mqr.CommissionCurrencyId
      , mqr.Commission
      , mqr.QuoteExpiryDate
      , mqr.BindRequestedDate
      , mqr.BoundDate
      , mqr.SignedLine
      , mqr.SignedLineRate
      , mqr.TriaRequired
      , mqr.TriaIncluded
      , mqr.TriaPremium
      , mqr.TriaPremiumCurrencyId
      , mqr.DeductibleCurrencyId
      , mqr.Deductible
      , mqr.IsOverride
      , mqr.AdditionalDetails
      , mqr.ValidFrom
      , mqr.ValidTo
      , mqr.PrimaryRate
      , mqr.PrimaryExposureValue
      , mqr.FollowTypeId
      , mqr.OfferedLineRateOfOrder
      , mqr.MinimumLine
      , mqr.MinimumLineRate
      , mqr.MinimumLineRateOfOrder
      , SourceUpdatedDate = CASE WHEN YEAR(mqr.ValidTo) < 9999
                                     THEN mqr.ValidTo
                                 ELSE mqr.ValidFrom END
      , IsDeleted = CASE WHEN YEAR(mqr.ValidTo) < 9999
                             THEN 1
                         ELSE 0 END
      , RowNo = ROW_NUMBER() OVER (PARTITION BY mqr.Id ORDER BY mqr.ValidFrom DESC, mqr.ValidTo ASC)
    INTO #MarketQuoteResponseSource
    FROM
        BPStaging.MarketQuoteResponse mqr
        INNER JOIN PS.MarketResponse mr
            ON mr.MarketResponseKey = CONCAT('MKTRES|', mqr.MarketResponseId)

        LEFT JOIN ref.FollowType f
            ON mqr.FollowTypeId = f.FollowTypeId
               AND f.IsDeprecated = 0;

    MERGE PS.MarketQuoteResponse t
    USING (
        SELECT
            mqrs.SourceKey
          , mqrs.DataSourceInstanceId
          , mqrs.MarketResponseId
          , mqrs.PremiumCurrencyId
          , mqrs.Premium
          , mqrs.OfferedLine
          , mqrs.PremiumRate
          , mqrs.CommissionRate
          , mqrs.Subjectivity
          , mqrs.IsPremiumRatePerMille
          , mqrs.OfferedLineRate
          , mqrs.OutcomeStatusId
          , mqrs.QuotedToLead
          , mqrs.OutcomeReasonId
          , mqrs.AdditionalPolicyCostCurrencyId
          , mqrs.AdditionalPolicyCost
          , mqrs.FrontingCarrierId
          , mqrs.NetPremium
          , mqrs.CommissionCurrencyId
          , mqrs.Commission
          , mqrs.QuoteExpiryDate
          , mqrs.BindRequestedDate
          , mqrs.BoundDate
          , mqrs.SignedLine
          , mqrs.SignedLineRate
          , mqrs.TriaRequired
          , mqrs.TriaIncluded
          , mqrs.TriaPremium
          , mqrs.TriaPremiumCurrencyId
          , mqrs.DeductibleCurrencyId
          , mqrs.Deductible
          , mqrs.IsOverride
          , mqrs.AdditionalDetails
          , mqrs.ValidFrom
          , mqrs.ValidTo
          , mqrs.PrimaryRate
          , mqrs.PrimaryExposureValue
          , mqrs.FollowTypeId
          , mqrs.OfferedLineRateOfOrder
          , mqrs.MinimumLine
          , mqrs.MinimumLineRate
          , mqrs.MinimumLineRateOfOrder
          , mqrs.SourceUpdatedDate
          , mqrs.IsDeleted
          , mqrad.FirstResponseAcceptedDate
          , mqrad.LastResponseAcceptedDate
        FROM
            #MarketQuoteResponseSource mqrs
            LEFT JOIN BPStaging.MarketQuoteResponseAcceptedDates mqrad
                ON mqrad.Id = mqrs.Id
        WHERE
            mqrs.RowNo = 1
    ) s
    ON t.SourceMarketQuoteResponseKey = s.SourceKey
       AND t.DataSourceInstanceId = s.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 SourceMarketQuoteResponseKey
               , DataSourceInstanceId
               , MarketResponseId
               , PremiumCurrencyId
               , Premium
               , OfferedLine
               , PremiumRate
               , CommissionRate
               , Subjectivity
               , IsPremiumRatePerMille
               , OfferedLineRate
               , OutcomeStatusId
               , QuotedToLead
               , OutcomeReasonId
               , AdditionalPolicyCostCurrencyId
               , AdditionalPolicyCost
               , FrontingCarrierId
               , NetPremium
               , CommissionCurrencyId
               , Commission
               , QuoteExpiryDate
               , BindRequestedDate
               , BoundDate
               , SignedLine
               , SignedLineRate
               , TriaRequired
               , TriaIncluded
               , TriaPremium
               , TriaPremiumCurrencyId
               , DeductibleCurrencyId
               , Deductible
               , IsOverride
               , AdditionalDetails
               , PrimaryRate
               , PrimaryExposureValue
               , FollowTypeId
               , OfferedLineRateOfOrder
               , MinimumLine
               , MinimumLineRate
               , MinimumLineRateOfOrder
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
               , FirstResponseAcceptedDate
               , LastResponseAcceptedDate
             )
             VALUES
                 (
                     s.SourceKey
                   , s.DataSourceInstanceId
                   , s.MarketResponseId
                   , s.PremiumCurrencyId
                   , s.Premium
                   , s.OfferedLine
                   , s.PremiumRate
                   , s.CommissionRate
                   , s.Subjectivity
                   , s.IsPremiumRatePerMille
                   , s.OfferedLineRate
                   , s.OutcomeStatusId
                   , s.QuotedToLead
                   , s.OutcomeReasonId
                   , s.AdditionalPolicyCostCurrencyId
                   , s.AdditionalPolicyCost
                   , s.FrontingCarrierId
                   , s.NetPremium
                   , s.CommissionCurrencyId
                   , s.Commission
                   , s.QuoteExpiryDate
                   , s.BindRequestedDate
                   , s.BoundDate
                   , s.SignedLine
                   , s.SignedLineRate
                   , s.TriaRequired
                   , s.TriaIncluded
                   , s.TriaPremium
                   , s.TriaPremiumCurrencyId
                   , s.DeductibleCurrencyId
                   , s.Deductible
                   , s.IsOverride
                   , s.AdditionalDetails
                   , s.PrimaryRate
                   , s.PrimaryExposureValue
                   , s.FollowTypeId
                   , s.OfferedLineRateOfOrder
                   , s.MinimumLine
                   , s.MinimumLineRate
                   , s.MinimumLineRateOfOrder
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , s.SourceUpdatedDate
                   , s.IsDeleted
                   , s.FirstResponseAcceptedDate
                   , s.LastResponseAcceptedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.MarketResponseId
                               , s.PremiumCurrencyId
                               , s.Premium
                               , s.OfferedLine
                               , s.PremiumRate
                               , s.CommissionRate
                               , s.Subjectivity
                               , s.IsPremiumRatePerMille
                               , s.OfferedLineRate
                               , s.OutcomeStatusId
                               , s.QuotedToLead
                               , s.OutcomeReasonId
                               , s.AdditionalPolicyCostCurrencyId
                               , s.AdditionalPolicyCost
                               , s.FrontingCarrierId
                               , s.NetPremium
                               , s.CommissionCurrencyId
                               , s.Commission
                               , s.QuoteExpiryDate
                               , s.BindRequestedDate
                               , s.BoundDate
                               , s.SignedLine
                               , s.SignedLineRate
                               , s.TriaRequired
                               , s.TriaIncluded
                               , s.TriaPremium
                               , s.TriaPremiumCurrencyId
                               , s.DeductibleCurrencyId
                               , s.Deductible
                               , s.IsOverride
                               , s.AdditionalDetails
                               , s.PrimaryRate
                               , s.PrimaryExposureValue
                               , s.FollowTypeId
                               , s.OfferedLineRateOfOrder
                               , s.MinimumLine
                               , s.MinimumLineRate
                               , s.MinimumLineRateOfOrder
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                               , s.FirstResponseAcceptedDate
                               , s.LastResponseAcceptedDate
                             INTERSECT
                             SELECT
                                 t.MarketResponseId
                               , t.PremiumCurrencyId
                               , t.Premium
                               , t.OfferedLine
                               , t.PremiumRate
                               , t.CommissionRate
                               , t.Subjectivity
                               , t.IsPremiumRatePerMille
                               , t.OfferedLineRate
                               , t.OutcomeStatusId
                               , t.QuotedToLead
                               , t.OutcomeReasonId
                               , t.AdditionalPolicyCostCurrencyId
                               , t.AdditionalPolicyCost
                               , t.FrontingCarrierId
                               , t.NetPremium
                               , t.CommissionCurrencyId
                               , t.Commission
                               , t.QuoteExpiryDate
                               , t.BindRequestedDate
                               , t.BoundDate
                               , t.SignedLine
                               , t.SignedLineRate
                               , t.TriaRequired
                               , t.TriaIncluded
                               , t.TriaPremium
                               , t.TriaPremiumCurrencyId
                               , t.DeductibleCurrencyId
                               , t.Deductible
                               , t.IsOverride
                               , t.AdditionalDetails
                               , t.PrimaryRate
                               , t.PrimaryExposureValue
                               , t.FollowTypeId
                               , t.OfferedLineRateOfOrder
                               , t.MinimumLine
                               , t.MinimumLineRate
                               , t.MinimumLineRateOfOrder
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                               , t.FirstResponseAcceptedDate
                               , t.LastResponseAcceptedDate
                         )
        THEN UPDATE SET
                 t.SourceMarketQuoteResponseKey = s.SourceKey
               , t.DataSourceInstanceId = s.DataSourceInstanceId
               , t.MarketResponseId = s.MarketResponseId
               , t.PremiumCurrencyId = s.PremiumCurrencyId
               , t.Premium = s.Premium
               , t.OfferedLine = s.OfferedLine
               , t.PremiumRate = s.PremiumRate
               , t.CommissionRate = s.CommissionRate
               , t.Subjectivity = s.Subjectivity
               , t.IsPremiumRatePerMille = s.IsPremiumRatePerMille
               , t.OfferedLineRate = s.OfferedLineRate
               , t.OutcomeStatusId = s.OutcomeStatusId
               , t.QuotedToLead = s.QuotedToLead
               , t.OutcomeReasonId = s.OutcomeReasonId
               , t.AdditionalPolicyCostCurrencyId = s.AdditionalPolicyCostCurrencyId
               , t.AdditionalPolicyCost = s.AdditionalPolicyCost
               , t.FrontingCarrierId = s.FrontingCarrierId
               , t.NetPremium = s.NetPremium
               , t.CommissionCurrencyId = s.CommissionCurrencyId
               , t.Commission = s.Commission
               , t.QuoteExpiryDate = s.QuoteExpiryDate
               , t.BindRequestedDate = s.BindRequestedDate
               , t.BoundDate = s.BoundDate
               , t.SignedLine = s.SignedLine
               , t.SignedLineRate = s.SignedLineRate
               , t.TriaRequired = s.TriaRequired
               , t.TriaIncluded = s.TriaIncluded
               , t.TriaPremium = s.TriaPremium
               , t.TriaPremiumCurrencyId = s.TriaPremiumCurrencyId
               , t.DeductibleCurrencyId = s.DeductibleCurrencyId
               , t.Deductible = s.Deductible
               , t.IsOverride = s.IsOverride
               , t.AdditionalDetails = s.AdditionalDetails
               , t.PrimaryRate = s.PrimaryRate
               , t.PrimaryExposureValue = s.PrimaryExposureValue
               , t.FollowTypeId = s.FollowTypeId
               , t.OfferedLineRateOfOrder = s.OfferedLineRateOfOrder
               , t.MinimumLine = s.MinimumLine
               , t.MinimumLineRate = s.MinimumLineRate
               , t.MinimumLineRateOfOrder = s.MinimumLineRateOfOrder
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.IsDeleted = s.IsDeleted
               , t.FirstResponseAcceptedDate = s.FirstResponseAcceptedDate
               , t.LastResponseAcceptedDate = s.LastResponseAcceptedDate
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;