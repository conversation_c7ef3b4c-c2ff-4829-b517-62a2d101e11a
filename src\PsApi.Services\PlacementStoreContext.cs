#pragma warning disable CA1707
using Microsoft.EntityFrameworkCore;
using Wtw.Crb.Common.Validation;
using Wtw.Crb.PlacementStore.Api.Model;

namespace PsApi.Services;

public partial class PlacementStoreContext : DbContext
{
    public PlacementStoreContext(DbContextOptions<PlacementStoreContext> options)
        : base(options)
    {
    }

    public virtual DbSet<vw_erd_City> vw_erd_Cities { get; set; }

    public virtual DbSet<vw_erd_Country> vw_erd_Countries { get; set; }

    public virtual DbSet<vw_erd_CountrySubdivision> vw_erd_CountrySubdivisions { get; set; }

    public virtual DbSet<vw_erd_Currency> vw_erd_Currencies { get; set; }

    public virtual DbSet<vw_erd_DataSource> vw_erd_DataSources { get; set; }

    public virtual DbSet<vw_erd_DataSourceInstance> vw_erd_DataSourceInstances { get; set; }

    public virtual DbSet<vw_erd_EmploymentStatus> vw_erd_EmploymentStatuses { get; set; }

    public virtual DbSet<vw_erd_ExchangeRate> vw_erd_ExchangeRates { get; set; }

    public virtual DbSet<vw_erd_GeographyGroup> vw_erd_GeographyGroups { get; set; }

    public virtual DbSet<vw_erd_GeographyGroupMembership> vw_erd_GeographyGroupMemberships { get; set; }

    public virtual DbSet<vw_erd_GeographyGroupingScheme> vw_erd_GeographyGroupingSchemes { get; set; }

    public virtual DbSet<vw_erd_Party> vw_erd_Parties { get; set; }

    public virtual DbSet<vw_erd_PartyRole> vw_erd_PartyRoles { get; set; }

    public virtual DbSet<vw_erd_PolicyStatus> vw_erd_PolicyStatuses { get; set; }

    public virtual DbSet<vw_erd_Product> vw_erd_Products { get; set; }

    public virtual DbSet<vw_erd_ProductClass> vw_erd_ProductClasses { get; set; }

    public virtual DbSet<vw_erd_ProductLine> vw_erd_ProductLines { get; set; }

    public virtual DbSet<vw_erd_SIC87Industry> vw_erd_SIC87Industries { get; set; }

    public virtual DbSet<vw_erd_Segmentation> vw_erd_Segmentations { get; set; }

    public virtual DbSet<vw_ps_AdditionalDataItem> vw_ps_AdditionalDataItems { get; set; }

    public virtual DbSet<vw_ps_AppetiteNarrative> vw_ps_AppetiteNarratives { get; set; }

    public virtual DbSet<vw_ps_AppetiteResponse> vw_ps_AppetiteResponses { get; set; }

    public virtual DbSet<vw_ps_ClientProfile> vw_ps_ClientProfiles { get; set; }

    public virtual DbSet<vw_ps_Contract> vw_ps_Contracts { get; set; }

    public virtual DbSet<vw_ps_ContractAttribute> vw_ps_ContractAttributes { get; set; }

    public virtual DbSet<vw_ps_ContractEndorsement> vw_ps_ContractEndorsements { get; set; }

    public virtual DbSet<vw_ps_ContractEndorsementAttribute> vw_ps_ContractEndorsementAttributes { get; set; }

    public virtual DbSet<vw_ps_ContractProgramme> vw_ps_ContractProgrammes { get; set; }

    public virtual DbSet<vw_ps_ContractProgrammeAttribute> vw_ps_ContractProgrammeAttributes { get; set; }

    public virtual DbSet<vw_ps_ContractRiskProfile> vw_ps_ContractRiskProfiles { get; set; }

    public virtual DbSet<vw_ps_ContractVersion> vw_ps_ContractVersions { get; set; }

    public virtual DbSet<vw_ps_ContractVersionAttribute> vw_ps_ContractVersionAttributes { get; set; }

    public virtual DbSet<vw_ps_ContractsClausesAndCondition> vw_ps_ContractsClausesAndConditions { get; set; }

    public virtual DbSet<vw_ps_ElementTagInclusionRule> vw_ps_ElementTagInclusionRules { get; set; }

    public virtual DbSet<vw_ps_ExposureAttribute> vw_ps_ExposureAttributes { get; set; }

    public virtual DbSet<vw_ps_GlobalPartySecurity> vw_ps_GlobalPartySecurities { get; set; }

    public virtual DbSet<vw_ps_LookUp> vw_ps_LookUps { get; set; }

    public virtual DbSet<vw_ps_MarketQuoteResponsePolicyRef> vw_ps_MarketQuoteResponsePolicyRefs { get; set; }

    public virtual DbSet<vw_ps_MarketResponse> vw_ps_MarketResponses { get; set; }

    public virtual DbSet<vw_ps_MarketResponseAttribute> vw_ps_MarketResponseAttributes { get; set; }

    public virtual DbSet<vw_ps_MarketResponseBasis> vw_ps_MarketResponseBases { get; set; }

    public virtual DbSet<vw_ps_MarketResponsePlacementPolicy> vw_ps_MarketResponsePlacementPolicies { get; set; }

    public virtual DbSet<vw_ps_MarketResponseSplit> vw_ps_MarketResponseSplits { get; set; }

    public virtual DbSet<vw_ps_Negotiation> vw_ps_Negotiations { get; set; }

    public virtual DbSet<vw_ps_NegotiationContract> vw_ps_NegotiationContracts { get; set; }

    public virtual DbSet<vw_ps_NegotiationMarket> vw_ps_NegotiationMarkets { get; set; }

    public virtual DbSet<vw_ps_NegotiationMarketContract> vw_ps_NegotiationMarketContracts { get; set; }

    public virtual DbSet<vw_ps_NegotiationRiskProfile> vw_ps_NegotiationRiskProfiles { get; set; }

    public virtual DbSet<vw_ps_NegotiationSpecification> vw_ps_NegotiationSpecifications { get; set; }

    public virtual DbSet<vw_ps_Placement> vw_ps_Placements { get; set; }

    public virtual DbSet<vw_ps_PlacementExposure> vw_ps_PlacementExposures { get; set; }

    public virtual DbSet<vw_ps_PlacementExposureGroup> vw_ps_PlacementExposureGroups { get; set; }

    public virtual DbSet<vw_ps_PlacementMarketValidation> vw_ps_PlacementMarketValidations { get; set; }

    public virtual DbSet<vw_ps_PlacementPartyRole> vw_ps_PlacementPartyRoles { get; set; }

    public virtual DbSet<vw_ps_PlacementPolicy> vw_ps_PlacementPolicies { get; set; }

    public virtual DbSet<vw_ps_PlacementRuleValidation> vw_ps_PlacementRuleValidations { get; set; }

    public virtual DbSet<vw_ps_PlacementSecurity> vw_ps_PlacementSecurities { get; set; }

    public virtual DbSet<vw_ps_PlacementServiceSatisfactionValidation> vw_ps_PlacementServiceSatisfactionValidations { get; set; }

    public virtual DbSet<vw_ps_PlacementServicingRole> vw_ps_PlacementServicingRoles { get; set; }

    public virtual DbSet<vw_ps_Policy> vw_ps_Policies { get; set; }

    public virtual DbSet<vw_ps_RiskProfile> vw_ps_RiskProfiles { get; set; }

    public virtual DbSet<vw_ps_RiskProfileAttribute> vw_ps_RiskProfileAttributes { get; set; }

    public virtual DbSet<vw_ps_RiskProfilePlacementExposureGroup> vw_ps_RiskProfilePlacementExposureGroups { get; set; }

    public virtual DbSet<vw_ps_RiskStructure> vw_ps_RiskStructures { get; set; }

    public virtual DbSet<vw_ps_RiskStructureContract> vw_ps_RiskStructureContracts { get; set; }

    public virtual DbSet<vw_ps_RiskStructureMarketResponse> vw_ps_RiskStructureMarketResponses { get; set; }

    public virtual DbSet<vw_ps_RiskStructurePolicy> vw_ps_RiskStructurePolicies { get; set; }

    public virtual DbSet<vw_ps_RiskStructureSpecification> vw_ps_RiskStructureSpecifications { get; set; }

    public virtual DbSet<vw_ps_ServiceSatisfactionIssueOutcome> vw_ps_ServiceSatisfactionIssueOutcomes { get; set; }

    public virtual DbSet<vw_ps_Specification> vw_ps_Specifications { get; set; }

    public virtual DbSet<vw_ps_SpecificationAttribute> vw_ps_SpecificationAttributes { get; set; }

    public virtual DbSet<vw_ps_ValidationRuleOutcome> vw_ps_ValidationRuleOutcomes { get; set; }

    public virtual DbSet<vw_ref_AppetiteLevel> vw_ref_AppetiteLevels { get; set; }

    public virtual DbSet<vw_ref_AppraisalType> vw_ref_AppraisalTypes { get; set; }

    public virtual DbSet<vw_ref_BrokingRegion> vw_ref_BrokingRegions { get; set; }

    public virtual DbSet<vw_ref_BrokingSegment> vw_ref_BrokingSegments { get; set; }

    public virtual DbSet<vw_ref_BrokingSubSegment> vw_ref_BrokingSubSegments { get; set; }

    public virtual DbSet<vw_ref_CancellationReason> vw_ref_CancellationReasons { get; set; }

    public virtual DbSet<vw_ref_Carrier> vw_ref_Carriers { get; set; }

    public virtual DbSet<vw_ref_CarrierRelationship> vw_ref_CarrierRelationships { get; set; }

    public virtual DbSet<vw_ref_CarrierType> vw_ref_CarrierTypes { get; set; }

    public virtual DbSet<vw_ref_ClassOfBusiness> vw_ref_ClassOfBusinesses { get; set; }

    public virtual DbSet<vw_ref_ContractStatus> vw_ref_ContractStatuses { get; set; }

    public virtual DbSet<vw_ref_CoverageType> vw_ref_CoverageTypes { get; set; }

    public virtual DbSet<vw_ref_DeclinationReason> vw_ref_DeclinationReasons { get; set; }

    public virtual DbSet<vw_ref_ElementAttributeType> vw_ref_ElementAttributeTypes { get; set; }

    public virtual DbSet<vw_ref_ExposurePeriod> vw_ref_ExposurePeriods { get; set; }

    public virtual DbSet<vw_ref_ExposureType> vw_ref_ExposureTypes { get; set; }

    public virtual DbSet<vw_ref_Facility> vw_ref_Facilities { get; set; }

    public virtual DbSet<vw_ref_FacilitySection> vw_ref_FacilitySections { get; set; }

    public virtual DbSet<vw_ref_FacilitySectionCarrier> vw_ref_FacilitySectionCarriers { get; set; }

    public virtual DbSet<vw_ref_FollowType> vw_ref_FollowTypes { get; set; }

    public virtual DbSet<vw_ref_Geography> vw_ref_Geographies { get; set; }

    public virtual DbSet<vw_ref_Industry> vw_ref_Industries { get; set; }

    public virtual DbSet<vw_ref_InsuranceType> vw_ref_InsuranceTypes { get; set; }

    public virtual DbSet<vw_ref_JustificationReason> vw_ref_JustificationReasons { get; set; }

    public virtual DbSet<vw_ref_JustificationReasonType> vw_ref_JustificationReasonTypes { get; set; }

    public virtual DbSet<vw_ref_LayerType> vw_ref_LayerTypes { get; set; }

    public virtual DbSet<vw_ref_LegalEntity> vw_ref_LegalEntities { get; set; }

    public virtual DbSet<vw_ref_LineOfBusiness> vw_ref_LineOfBusinesses { get; set; }

    public virtual DbSet<vw_ref_MTAType> vw_ref_MTATypes { get; set; }

    public virtual DbSet<vw_ref_MarketKind> vw_ref_MarketKinds { get; set; }

    public virtual DbSet<vw_ref_MarketingDecision> vw_ref_MarketingDecisions { get; set; }

    public virtual DbSet<vw_ref_NotRemarketingReason> vw_ref_NotRemarketingReasons { get; set; }

    public virtual DbSet<vw_ref_OpportunityType> vw_ref_OpportunityTypes { get; set; }

    public virtual DbSet<vw_ref_OptionReference> vw_ref_OptionReferences { get; set; }

    public virtual DbSet<vw_ref_OutcomeReason> vw_ref_OutcomeReasons { get; set; }

    public virtual DbSet<vw_ref_OutcomeStatus> vw_ref_OutcomeStatuses { get; set; }

    public virtual DbSet<vw_ref_Panel> vw_ref_Panels { get; set; }

    public virtual DbSet<vw_ref_PanelMember> vw_ref_PanelMembers { get; set; }

    public virtual DbSet<vw_ref_PanelMemberCarrier> vw_ref_PanelMemberCarriers { get; set; }

    public virtual DbSet<vw_ref_PanelMemberFacility> vw_ref_PanelMemberFacilities { get; set; }

    public virtual DbSet<vw_ref_Party> vw_ref_Parties { get; set; }

    public virtual DbSet<vw_ref_PartyAttribute> vw_ref_PartyAttributes { get; set; }

    public virtual DbSet<vw_ref_PartyRole> vw_ref_PartyRoles { get; set; }

    public virtual DbSet<vw_ref_PendingActionReason> vw_ref_PendingActionReasons { get; set; }

    public virtual DbSet<vw_ref_PlacementPolicyRelationshipType> vw_ref_PlacementPolicyRelationshipTypes { get; set; }

    public virtual DbSet<vw_ref_PlacementStatus> vw_ref_PlacementStatuses { get; set; }

    public virtual DbSet<vw_ref_PolicyRefType> vw_ref_PolicyRefTypes { get; set; }

    public virtual DbSet<vw_ref_PolicyStatus> vw_ref_PolicyStatuses { get; set; }

    public virtual DbSet<vw_ref_PremiumRange> vw_ref_PremiumRanges { get; set; }

    public virtual DbSet<vw_ref_PricingFactor> vw_ref_PricingFactors { get; set; }

    public virtual DbSet<vw_ref_ProducingOffice> vw_ref_ProducingOffices { get; set; }

    public virtual DbSet<vw_ref_Product> vw_ref_Products { get; set; }

    public virtual DbSet<vw_ref_ProgramStructureType> vw_ref_ProgramStructureTypes { get; set; }

    public virtual DbSet<vw_ref_ProgramType> vw_ref_ProgramTypes { get; set; }

    public virtual DbSet<vw_ref_RenewableOption> vw_ref_RenewableOptions { get; set; }

    public virtual DbSet<vw_ref_ResponseType> vw_ref_ResponseTypes { get; set; }

    public virtual DbSet<vw_ref_ServiceLevel> vw_ref_ServiceLevels { get; set; }

    public virtual DbSet<vw_ref_ServiceLevelIssue> vw_ref_ServiceLevelIssues { get; set; }

    public virtual DbSet<vw_ref_ServicingRole> vw_ref_ServicingRoles { get; set; }

    public virtual DbSet<vw_ref_Team> vw_ref_Teams { get; set; }

    public virtual DbSet<vw_ref_TimeZone> vw_ref_TimeZones { get; set; }

    public virtual DbSet<vw_ref_Trigger> vw_ref_Triggers { get; set; }

    public virtual DbSet<vw_ref_VerticalIndustry> vw_ref_VerticalIndustries { get; set; }

    public virtual DbSet<vw_ref_Worker> vw_ref_Workers { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        Precondition.ThrowIf(modelBuilder.IsNull());

        modelBuilder.Entity<vw_erd_City>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_City", "ods");

            entity.Property(e => e.CityCode).HasMaxLength(3);
            entity.Property(e => e.CityName).HasMaxLength(250);
            entity.Property(e => e.LocationLatitude).HasColumnType("decimal(8, 6)");
            entity.Property(e => e.LocationLongitude).HasColumnType("decimal(9, 6)");
        });

        modelBuilder.Entity<vw_erd_Country>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_Country", "ods");

            entity.Property(e => e.CountryAlpha2Code).HasMaxLength(2);
            entity.Property(e => e.CountryAlpha3Code).HasMaxLength(3);
            entity.Property(e => e.CountryName).HasMaxLength(100);
            entity.Property(e => e.CountryNumericCode).HasMaxLength(3);
            entity.Property(e => e.RetiredCountryCode).HasMaxLength(10);
        });

        modelBuilder.Entity<vw_erd_CountrySubdivision>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_CountrySubdivision", "ods");

            entity.Property(e => e.CountrySubdivisionCategoryName).HasMaxLength(100);
            entity.Property(e => e.CountrySubdivisionCode).HasMaxLength(6);
            entity.Property(e => e.CountrySubdivisionName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_erd_Currency>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_Currency", "ods");

            entity.Property(e => e.CurrencyAlphaCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.CurrencyDefinition).HasMaxLength(500);
            entity.Property(e => e.CurrencyName).HasMaxLength(60);
            entity.Property(e => e.WithdrawalPeriod).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_erd_DataSource>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_DataSource", "ods");

            entity.Property(e => e.DataSourceDescription).HasMaxLength(250);
            entity.Property(e => e.DataSourceName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_erd_DataSourceInstance>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_DataSourceInstance", "ods");

            entity.Property(e => e.DataSourceInstanceDescription).HasMaxLength(250);
            entity.Property(e => e.DataSourceInstanceName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_erd_EmploymentStatus>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_EmploymentStatus", "ods");

            entity.Property(e => e.EmploymentStatus).HasMaxLength(50);
            entity.Property(e => e.EmploymentStatusKey)
                .HasMaxLength(19)
                .IsUnicode(false);
        });

        modelBuilder.Entity<vw_erd_ExchangeRate>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_ExchangeRate", "ods");

            entity.Property(e => e.ExchangeRate).HasColumnType("decimal(18, 8)");
            entity.Property(e => e.LastUpdateBy).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_erd_GeographyGroup>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_GeographyGroup", "ods");

            entity.Property(e => e.GeographyGroupName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_erd_GeographyGroupMembership>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_GeographyGroupMembership", "ods");
        });

        modelBuilder.Entity<vw_erd_GeographyGroupingScheme>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_GeographyGroupingScheme", "ods");

            entity.Property(e => e.GeographyGroupingSchemeName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_erd_Party>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_Party", "ods");

            entity.Property(e => e.EmailAddress).HasMaxLength(250);
            entity.Property(e => e.ExternalReference).HasMaxLength(250);
            entity.Property(e => e.OperatingRevenue).HasColumnType("decimal(19, 8)");
            entity.Property(e => e.OperatingRevenueUSD).HasColumnType("decimal(19, 8)");
            entity.Property(e => e.PartyKey).HasMaxLength(50);
            entity.Property(e => e.PartyName).HasMaxLength(250);
            entity.Property(e => e.SIC87IndustryName).HasMaxLength(250);
            entity.Property(e => e.SegmentationDescription).HasMaxLength(250);
            entity.Property(e => e.SegmentationName).HasMaxLength(250);
            entity.Property(e => e.SignetExternalReference).HasMaxLength(250);
            entity.Property(e => e.WebsiteAddress).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_erd_PartyRole>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_PartyRole", "ods");

            entity.Property(e => e.PartyRoleDescription).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_erd_PolicyStatus>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_PolicyStatus", "ods");

            entity.Property(e => e.PolicyStatus).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_erd_Product>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_Product", "ods");

            entity.Property(e => e.ProductDescription).HasMaxLength(4000);
            entity.Property(e => e.ProductKey).HasMaxLength(50);
            entity.Property(e => e.ProductName).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_erd_ProductClass>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_ProductClass", "ods");

            entity.Property(e => e.ProductClassDescription).HasMaxLength(255);
            entity.Property(e => e.ProductClassKey).HasMaxLength(50);
            entity.Property(e => e.ProductClassName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_erd_ProductLine>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_ProductLine", "ods");

            entity.Property(e => e.ProductLineDescription).HasMaxLength(255);
            entity.Property(e => e.ProductLineKey).HasMaxLength(50);
            entity.Property(e => e.ProductLineName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_erd_SIC87Industry>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_SIC87Industry", "ods");

            entity.Property(e => e.SIC87DivisionCode).HasMaxLength(1);
            entity.Property(e => e.SIC87DivisionName).HasMaxLength(250);
            entity.Property(e => e.SIC87FullCode).HasMaxLength(5);
            entity.Property(e => e.SIC87IndustryCode).HasColumnType("numeric(1, 0)");
            entity.Property(e => e.SIC87IndustryGroupCode).HasColumnType("numeric(1, 0)");
            entity.Property(e => e.SIC87IndustryGroupName).HasMaxLength(250);
            entity.Property(e => e.SIC87IndustryKey).HasMaxLength(50);
            entity.Property(e => e.SIC87IndustryName).HasMaxLength(250);
            entity.Property(e => e.SIC87MajorGroupCode).HasColumnType("numeric(2, 0)");
            entity.Property(e => e.SIC87MajorGroupName).HasMaxLength(250);
            entity.Property(e => e.VerticalIndustryName).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_erd_Segmentation>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_erd_Segmentation", "ods");

            entity.Property(e => e.SegmentationDescription).HasMaxLength(250);
            entity.Property(e => e.SegmentationKey).HasMaxLength(50);
            entity.Property(e => e.SegmentationName).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_ps_AdditionalDataItem>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_AdditionalDataItem", "ods");

            entity.Property(e => e.ExternalCode).HasMaxLength(50);
            entity.Property(e => e.StorageType).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ps_AppetiteNarrative>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_AppetiteNarrative", "ods");

            entity.Property(e => e.AppetiteNarrativeKey).HasMaxLength(50);
            entity.Property(e => e.NegotiationMarketKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_AppetiteResponse>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_AppetiteResponse", "ods");

            entity.Property(e => e.AppetiteResponseKey).HasMaxLength(50);
            entity.Property(e => e.NegotiationMarketKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_ClientProfile>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ClientProfile", "ods");

            entity.Property(e => e.ClientProfileKey).HasMaxLength(50);
            entity.Property(e => e.DebtRatio).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.EBITDA).HasColumnType("decimal(18, 0)");
            entity.Property(e => e.FinancialsAsAtDate).HasColumnType("datetime");
            entity.Property(e => e.GlobalAnnualRevenue).HasColumnType("decimal(18, 0)");
            entity.Property(e => e.OperationalCashFlow).HasColumnType("decimal(18, 0)");
            entity.Property(e => e.ProfitMargin).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.RegistrationNumber).HasMaxLength(50);
            entity.Property(e => e.SPCreditRating)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.WebsiteAddress).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ps_Contract>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_Contract", "ods");

            entity.Property(e => e.AttachmentPoint).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.AttachmentPointCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.BrokerCode).HasMaxLength(50);
            entity.Property(e => e.Deductible).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.DeductibleCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.Description).HasMaxLength(900);
            entity.Property(e => e.ExpiryStartTime)
                .HasMaxLength(5)
                .IsFixedLength();
            entity.Property(e => e.InceptionStartTime)
                .HasMaxLength(5)
                .IsFixedLength();
            entity.Property(e => e.LegalEntity).HasMaxLength(2000);
            entity.Property(e => e.Limit).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.LimitCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.OrderPercentage).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.PolicyReference).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_ContractAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractAttribute", "ods");

            entity.Property(e => e.ContractAttributeKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ElementType).HasMaxLength(500);
            entity.Property(e => e.ElementTypeOverride).HasMaxLength(500);
            entity.Property(e => e.TypeKeyPath).HasMaxLength(300);
        });

        modelBuilder.Entity<vw_ps_ContractEndorsement>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractEndorsement", "ods");

            entity.Property(e => e.ContractEndorsementKey).HasMaxLength(20);
            entity.Property(e => e.Description).HasMaxLength(450);
            entity.Property(e => e.EffectiveFrom).HasPrecision(2);
            entity.Property(e => e.EndorsementStatus).HasMaxLength(255);
            entity.Property(e => e.SourceUpdatedDate).HasPrecision(2);
        });

        modelBuilder.Entity<vw_ps_ContractEndorsementAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractEndorsementAttribute", "ods");

            entity.Property(e => e.ContractEndorsementAttributeKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ElementType).HasMaxLength(500);
            entity.Property(e => e.ElementTypeOverride).HasMaxLength(500);
            entity.Property(e => e.TypeKeyPath).HasMaxLength(300);
        });

        modelBuilder.Entity<vw_ps_ContractProgramme>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractProgramme", "ods");

            entity.Property(e => e.ContractProgrammeId).ValueGeneratedOnAdd();
            entity.Property(e => e.ContractProgrammeKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_ContractProgrammeAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractProgrammeAttribute", "ods");

            entity.Property(e => e.ContractProgrammeAttributeKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ElementType).HasMaxLength(500);
            entity.Property(e => e.ElementTypeOverride).HasMaxLength(500);
            entity.Property(e => e.TypeKeyPath).HasMaxLength(300);
        });

        modelBuilder.Entity<vw_ps_ContractRiskProfile>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractRiskProfile", "ods");

            entity.Property(e => e.ContractRiskProfileKey).HasMaxLength(20);
        });

        modelBuilder.Entity<vw_ps_ContractVersion>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractVersion", "ods");

            entity.Property(e => e.ETLCreatedDate).HasPrecision(2);
            entity.Property(e => e.ETLUpdatedDate).HasPrecision(2);
            entity.Property(e => e.SourceUpdatedDate).HasPrecision(2);
        });

        modelBuilder.Entity<vw_ps_ContractVersionAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractVersionAttribute", "ods");

            entity.Property(e => e.ContractVersionAttributeKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ElementType).HasMaxLength(500);
            entity.Property(e => e.ElementTypeOverride).HasMaxLength(500);
            entity.Property(e => e.TypeKeyPath).HasMaxLength(300);
        });

        modelBuilder.Entity<vw_ps_ContractsClausesAndCondition>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ContractsClausesAndConditions", "ods");

            entity.Property(e => e.ContractDocumentType).HasMaxLength(50);
            entity.Property(e => e.ContractStatus).HasMaxLength(100);
            entity.Property(e => e.ContractsClausesAndConditionsId).ValueGeneratedOnAdd();
            entity.Property(e => e.Description).HasMaxLength(900);
            entity.Property(e => e.ElementKey).HasMaxLength(100);
            entity.Property(e => e.ElementTypePath).HasMaxLength(4000);
        });

        modelBuilder.Entity<vw_ps_ElementTagInclusionRule>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ElementTagInclusionRule", "ods");

            entity.Property(e => e.ElementTagTypeKey).HasMaxLength(100);
            entity.Property(e => e.GroupKey).HasMaxLength(100);
            entity.Property(e => e.LabelTranslationKey).HasMaxLength(255);
            entity.Property(e => e.LinkedElementTagTypeKey).HasMaxLength(100);
            entity.Property(e => e.LinkedGroupKey).HasMaxLength(100);
            entity.Property(e => e.LinkedLabelTranslationKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ps_ExposureAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ExposureAttribute", "ods");

            entity.Property(e => e.ElementAttributeType).HasMaxLength(500);
            entity.Property(e => e.ElementType).HasMaxLength(500);
            entity.Property(e => e.ElementTypeKey).HasMaxLength(100);
            entity.Property(e => e.ExposureAttributeKey).HasMaxLength(100);
            entity.Property(e => e.ExposureElementKey).HasMaxLength(50);
            entity.Property(e => e.ParentExposureElementKey).HasMaxLength(50);
            entity.Property(e => e.TypeKeyPath).HasMaxLength(500);
        });

        modelBuilder.Entity<vw_ps_GlobalPartySecurity>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_GlobalPartySecurity", "ods");

            entity.Property(e => e.GlobalPartySecurityKey).HasMaxLength(100);
            entity.Property(e => e.UserPrincipalName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ps_LookUp>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_LookUp", "ods");

            entity.Property(e => e.MarketResponseKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.NegotiationKey).HasMaxLength(50);
            entity.Property(e => e.NegotiationMarketKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_MarketQuoteResponsePolicyRef>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_MarketQuoteResponsePolicyRef", "ods");

            entity.Property(e => e.MarketQuoteResponsePolicyRefKey).HasMaxLength(200);
            entity.Property(e => e.PolicyRef)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.PolicyRefTypeKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.TranslationKey)
                .HasMaxLength(255)
                .IsUnicode(false);
        });

        modelBuilder.Entity<vw_ps_MarketResponse>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_MarketResponse", "ods");

            entity.Property(e => e.AdditionalPolicyCost).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.AdditionalPolicyCostCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.AttachmentPoint).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.AttachmentPointCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.BindRequestedDate).HasColumnType("datetime");
            entity.Property(e => e.BoundDate).HasColumnType("datetime");
            entity.Property(e => e.Commission).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.CommissionCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.CommissionRate).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.Deductible).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.DeductibleCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.Limit).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.LimitCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.MarketResponseKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MinimumLine).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.MinimumLineRate).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.MinimumLineRateOfOrder).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.NetPremium).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.OfferedLine).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.OfferedLineRate).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.OfferedLineRateOfOrder).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.Premium).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.PremiumCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.PremiumRate).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.SignedLine).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.SignedLineRate).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.Split).HasColumnType("decimal(21, 6)");
            entity.Property(e => e.UnderwriterEmail).HasMaxLength(320);
            entity.Property(e => e.UnderwriterName).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ps_MarketResponseAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_MarketResponseAttribute", "ods");

            entity.Property(e => e.ElementAttributeType).HasMaxLength(500);
            entity.Property(e => e.ElementType).HasMaxLength(500);
            entity.Property(e => e.ElementTypeKey).HasMaxLength(100);
            entity.Property(e => e.MarketResponseAttributeKey).HasMaxLength(100);
            entity.Property(e => e.MarketResponseElementKey).HasMaxLength(50);
            entity.Property(e => e.ParentMarketResponseElementKey).HasMaxLength(50);
            entity.Property(e => e.TypePathKey).HasMaxLength(500);
        });

        modelBuilder.Entity<vw_ps_MarketResponseBasis>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_MarketResponseBasis", "ods");

            entity.Property(e => e.MarketResponseBasisId).ValueGeneratedOnAdd();
            entity.Property(e => e.MarketResponseBasisKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_MarketResponsePlacementPolicy>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_MarketResponsePlacementPolicy", "ods");

            entity.Property(e => e.BoundPositionType).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ps_MarketResponseSplit>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_MarketResponseSplit", "ods");

            entity.Property(e => e.MarketResponseKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MarketResponseSplitKey)
                .HasMaxLength(19)
                .IsUnicode(false);
            entity.Property(e => e.Split).HasColumnType("decimal(19, 6)");
        });

        modelBuilder.Entity<vw_ps_Negotiation>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_Negotiation", "ods");

            entity.Property(e => e.ContactEmail).HasMaxLength(320);
            entity.Property(e => e.ContactName).HasMaxLength(100);
            entity.Property(e => e.ContactPhone).HasMaxLength(100);
            entity.Property(e => e.ContactRole).HasMaxLength(100);
            entity.Property(e => e.NegotiationKey).HasMaxLength(50);
            entity.Property(e => e.NegotiationName).HasMaxLength(500);
            entity.Property(e => e.NegotiationType).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_NegotiationContract>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_NegotiationContract", "ods");

            entity.Property(e => e.NegotiationKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_NegotiationMarket>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_NegotiationMarket", "ods");

            entity.Property(e => e.NegotiationMarketKey).HasMaxLength(50);
            entity.Property(e => e.PanelMemberName).HasMaxLength(255);
            entity.Property(e => e.ThirdPartyMarketName).HasMaxLength(355);
            entity.Property(e => e.UnderwriterEmail).HasMaxLength(320);
            entity.Property(e => e.UnderwriterName).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ps_NegotiationMarketContract>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_NegotiationMarketContract", "ods");

            entity.Property(e => e.ETLCreatedDate).HasPrecision(2);
            entity.Property(e => e.NegotiationMarketKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_NegotiationRiskProfile>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_NegotiationRiskProfile", "ods");

            entity.Property(e => e.NegotiationRiskProfileId).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<vw_ps_NegotiationSpecification>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_NegotiationSpecification", "ods");

            entity.Property(e => e.NegotiationSpecificationId).ValueGeneratedOnAdd();
            entity.Property(e => e.NegotiationSpecificationKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_Placement>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_Placement", "ods");

            entity.Property(e => e.CRMOpportunityId).HasMaxLength(255);
            entity.Property(e => e.ExpiryStartTime)
                .HasMaxLength(5)
                .IsFixedLength();
            entity.Property(e => e.InceptionStartTime)
                .HasMaxLength(5)
                .IsFixedLength();
            entity.Property(e => e.InstructionDetails).HasMaxLength(1000);
            entity.Property(e => e.PlacementKey).HasMaxLength(100);
            entity.Property(e => e.PlacementName).HasMaxLength(500);
        });

        modelBuilder.Entity<vw_ps_PlacementExposure>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementExposure", "ods");

            entity.Property(e => e.ElementAttributeKey).HasMaxLength(100);
            entity.Property(e => e.ElementKey).HasMaxLength(100);
            entity.Property(e => e.ElementPath).HasMaxLength(2000);
            entity.Property(e => e.ElementType).HasMaxLength(255);
            entity.Property(e => e.PlacementExposureKey).HasMaxLength(20);
            entity.Property(e => e.RiskProfileKey).HasMaxLength(16);
        });

        modelBuilder.Entity<vw_ps_PlacementExposureGroup>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementExposureGroup", "ods");

            entity.Property(e => e.PlacementExposureGroupKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_PlacementMarketValidation>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementMarketValidation", "ods");

            entity.Property(e => e.PlacementMarketValidationKey)
                .HasMaxLength(16)
                .IsUnicode(false);
            entity.Property(e => e.ValidationRule).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ps_PlacementPartyRole>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementPartyRole", "ods");

            entity.Property(e => e.PartyName).HasMaxLength(500);
        });

        modelBuilder.Entity<vw_ps_PlacementPolicy>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementPolicy", "ods");
        });

        modelBuilder.Entity<vw_ps_PlacementRuleValidation>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementRuleValidation", "ods");

            entity.Property(e => e.ValidationRule)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<vw_ps_PlacementSecurity>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementSecurity", "ods");

            entity.Property(e => e.PlacementSecurityKey).HasMaxLength(50);
            entity.Property(e => e.UserPrincipalName).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ps_PlacementServiceSatisfactionValidation>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementServiceSatisfactionValidation", "ods");

            entity.Property(e => e.PlacementServiceSatisfactionValidationKey).HasMaxLength(50);
            entity.Property(e => e.ServiceLevel).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ps_PlacementServicingRole>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_PlacementServicingRole", "ods");
        });

        modelBuilder.Entity<vw_ps_Policy>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_Policy", "ods");

            entity.Property(e => e.ParentKey).HasMaxLength(101);
            entity.Property(e => e.PolicyAttributeXML).HasColumnType("xml");
            entity.Property(e => e.PolicyDescription).HasMaxLength(500);
            entity.Property(e => e.PolicyId).ValueGeneratedOnAdd();
            entity.Property(e => e.PolicyKey).HasMaxLength(101);
            entity.Property(e => e.PolicyReference).HasMaxLength(50);
            entity.Property(e => e.RenewedFromPolicyKey).HasMaxLength(50);
            entity.Property(e => e.SegmentCode)
                .HasMaxLength(6)
                .IsFixedLength();
        });

        modelBuilder.Entity<vw_ps_RiskProfile>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskProfile", "ods");

            entity.Property(e => e.ClassOfBusiness).HasMaxLength(255);
            entity.Property(e => e.LineOfBusiness).HasMaxLength(255);
            entity.Property(e => e.RiskProfileId).ValueGeneratedOnAdd();
            entity.Property(e => e.RiskProfileKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_RiskProfileAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskProfileAttribute", "ods");

            entity.Property(e => e.ElementAttributeKey).HasMaxLength(100);
            entity.Property(e => e.ElementKey).HasMaxLength(100);
            entity.Property(e => e.ElementPath).HasMaxLength(2000);
            entity.Property(e => e.ElementType).HasMaxLength(255);
            entity.Property(e => e.RiskProfileAttributeKey).HasMaxLength(20);
            entity.Property(e => e.RiskProfileKey).HasMaxLength(16);
        });

        modelBuilder.Entity<vw_ps_RiskProfilePlacementExposureGroup>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskProfilePlacementExposureGroup", "ods");

            entity.Property(e => e.RiskProfilePlacementExposureGroupId).ValueGeneratedOnAdd();
            entity.Property(e => e.RiskProfilePlacementExposureGroupKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_RiskStructure>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskStructure", "ods");

            entity.Property(e => e.AttachmentPoint).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.AttachmentPointCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.Deductible).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.DeductibleCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.Limit).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.LimitCurrencyCode)
                .HasMaxLength(3)
                .IsFixedLength();
            entity.Property(e => e.RiskStructureKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_RiskStructureContract>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskStructureContract", "ods");

            entity.Property(e => e.RiskStructureContractId).ValueGeneratedOnAdd();
            entity.Property(e => e.RiskStructureContractKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_RiskStructureMarketResponse>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskStructureMarketResponse", "ods");

            entity.Property(e => e.RiskStructureMarketResponseId).ValueGeneratedOnAdd();
            entity.Property(e => e.RiskStructureMarketResponseKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_RiskStructurePolicy>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskStructurePolicy", "ods");

            entity.Property(e => e.RiskStructurePolicyId).ValueGeneratedOnAdd();
            entity.Property(e => e.RiskStructurePolicyKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_RiskStructureSpecification>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_RiskStructureSpecification", "ods");

            entity.Property(e => e.RiskStructureSpecificationId).ValueGeneratedOnAdd();
            entity.Property(e => e.RiskStructureSpecificationKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_ServiceSatisfactionIssueOutcome>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ServiceSatisfactionIssueOutcome", "ods");

            entity.Property(e => e.ServiceLevelIssue).HasMaxLength(255);
            entity.Property(e => e.ServiceSatisfactionIssueOutcomeKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_Specification>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_Specification", "ods");

            entity.Property(e => e.Label).HasMaxLength(200);
            entity.Property(e => e.SpecificationId).ValueGeneratedOnAdd();
            entity.Property(e => e.SpecificationKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ps_SpecificationAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_SpecificationAttribute", "ods");

            entity.Property(e => e.ElementType).HasMaxLength(500);
            entity.Property(e => e.ElementTypeKey).HasMaxLength(100);
            entity.Property(e => e.ElementTypeOverride).HasMaxLength(500);
            entity.Property(e => e.SpecificationAttributeKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.TypePathKey).HasMaxLength(500);
        });

        modelBuilder.Entity<vw_ps_ValidationRuleOutcome>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ps_ValidationRuleOutcome", "ods");
        });

        modelBuilder.Entity<vw_ref_AppetiteLevel>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_AppetiteLevel", "ods");

            entity.Property(e => e.AppetiteLevelKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_AppraisalType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_AppraisalType", "ods");

            entity.Property(e => e.AppraisalType).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_BrokingRegion>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_BrokingRegion", "ods");

            entity.Property(e => e.BrokingRegion).HasMaxLength(100);
            entity.Property(e => e.BrokingRegionKey).HasMaxLength(100);
            entity.Property(e => e.TranslationKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_BrokingSegment>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_BrokingSegment", "ods");

            entity.Property(e => e.BrokingSegment).HasMaxLength(100);
            entity.Property(e => e.BrokingSegmentKey).HasMaxLength(100);
            entity.Property(e => e.TranslationKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_BrokingSubSegment>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_BrokingSubSegment", "ods");

            entity.Property(e => e.BrokingSubSegment).HasMaxLength(100);
            entity.Property(e => e.BrokingSubSegmentKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_CancellationReason>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_CancellationReason", "ods");

            entity.Property(e => e.CancellationReason).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_Carrier>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Carrier", "ods");

            entity.Property(e => e.CarrierId).ValueGeneratedOnAdd();
            entity.Property(e => e.CarrierKey).HasMaxLength(50);
            entity.Property(e => e.CarrierName).HasMaxLength(255);
            entity.Property(e => e.CompCode).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_CarrierRelationship>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_CarrierRelationship", "ods");

            entity.Property(e => e.FromCarrierType).HasMaxLength(50);
            entity.Property(e => e.ToCarrierType).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_CarrierType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_CarrierType", "ods");

            entity.Property(e => e.CarrierType).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_ClassOfBusiness>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ClassOfBusiness", "ods");

            entity.Property(e => e.ClassOfBusinessKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_ContractStatus>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ContractStatus", "ods");

            entity.Property(e => e.ContractStatus).HasMaxLength(100);
            entity.Property(e => e.ContractStatusId).ValueGeneratedOnAdd();
            entity.Property(e => e.ContractStatusKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_CoverageType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_CoverageType", "ods");

            entity.Property(e => e.CoverageTypeKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_DeclinationReason>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_DeclinationReason", "ods");

            entity.Property(e => e.DeclinationReason).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_ElementAttributeType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ElementAttributeType", "ods");

            entity.Property(e => e.ElementAttributeTypeKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_ExposurePeriod>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ExposurePeriod", "ods");

            entity.Property(e => e.ExposurePeriodKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_ExposureType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ExposureType", "ods");

            entity.Property(e => e.ExposureTypeKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_Facility>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Facility", "ods");

            entity.Property(e => e.FacilityName).HasMaxLength(200);
            entity.Property(e => e.FacilityType).HasMaxLength(80);
            entity.Property(e => e.PolicyReference).HasMaxLength(50);
            entity.Property(e => e.RefPolicyStatus).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_ref_FacilitySection>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_FacilitySection", "ods");

            entity.Property(e => e.FacilityName).HasMaxLength(200);
            entity.Property(e => e.FacilitySectionKey).HasMaxLength(100);
            entity.Property(e => e.FacilityType).HasMaxLength(80);
            entity.Property(e => e.LineSlipRef).HasMaxLength(100);
            entity.Property(e => e.PolicyReference).HasMaxLength(50);
            entity.Property(e => e.PolicySectionStatus).HasMaxLength(500);
            entity.Property(e => e.RefPolicyStatus).HasMaxLength(250);
            entity.Property(e => e.SectionCode).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_FacilitySectionCarrier>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_FacilitySectionCarrier", "ods");

            entity.Property(e => e.FacilitySectionCarrierId).ValueGeneratedOnAdd();
            entity.Property(e => e.FacilitySectionCarrierKey).HasMaxLength(100);
            entity.Property(e => e.FacilitySignedLine).HasColumnType("decimal(10, 7)");
            entity.Property(e => e.FacilityWrittenLine).HasColumnType("decimal(10, 7)");
        });

        modelBuilder.Entity<vw_ref_FollowType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_FollowType", "ods");

            entity.Property(e => e.FollowType).HasMaxLength(100);
            entity.Property(e => e.FollowTypeId).ValueGeneratedOnAdd();
            entity.Property(e => e.FollowTypeKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_Geography>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Geography", "ods");

            entity.Property(e => e.Geography).HasMaxLength(100);
            entity.Property(e => e.GeographyId).ValueGeneratedOnAdd();
            entity.Property(e => e.GeographyKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_Industry>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Industry", "ods");

            entity.Property(e => e.ElementTagTypeKey).HasMaxLength(100);
            entity.Property(e => e.IndustryCode).HasMaxLength(20);
            entity.Property(e => e.IndustryId).ValueGeneratedOnAdd();
            entity.Property(e => e.IndustryKey).HasMaxLength(50);
            entity.Property(e => e.IndustryName).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_ref_InsuranceType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_InsuranceType", "ods");

            entity.Property(e => e.InsuranceType).HasMaxLength(250);
        });

        modelBuilder.Entity<vw_ref_JustificationReason>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_JustificationReason", "ods");

            entity.Property(e => e.JustificationReason).HasMaxLength(200);
            entity.Property(e => e.JustificationReasonKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_JustificationReasonType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_JustificationReasonType", "ods");

            entity.Property(e => e.JustificationReasonType).HasMaxLength(200);
            entity.Property(e => e.JustificationReasonTypeKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_LayerType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_LayerType", "ods");

            entity.Property(e => e.LayerType).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_LegalEntity>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_LegalEntity", "ods");

            entity.Property(e => e.BrokerCode).HasMaxLength(50);
            entity.Property(e => e.LegalEntityKey).HasMaxLength(50);
            entity.Property(e => e.LegalEntityName).HasMaxLength(2000);
        });

        modelBuilder.Entity<vw_ref_LineOfBusiness>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_LineOfBusiness", "ods");

            entity.Property(e => e.LineOfBusinessKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_MTAType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_MTAType", "ods");
        });

        modelBuilder.Entity<vw_ref_MarketKind>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_MarketKind", "ods");

            entity.Property(e => e.MarketKind).HasMaxLength(200);
            entity.Property(e => e.MarketKindKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_MarketingDecision>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_MarketingDecision", "ods");

            entity.Property(e => e.JustificationReason).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_NotRemarketingReason>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_NotRemarketingReason", "ods");

            entity.Property(e => e.Description)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.NotRemarketingReasonKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_OpportunityType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_OpportunityType", "ods");

            entity.Property(e => e.OpportunityType).HasMaxLength(100);
            entity.Property(e => e.OpportunityTypeId).ValueGeneratedOnAdd();
            entity.Property(e => e.OpportunityTypeKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_OptionReference>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_OptionReference", "ods");

            entity.Property(e => e.OptionReferenceId).ValueGeneratedOnAdd();
            entity.Property(e => e.OptionReferenceKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_OutcomeReason>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_OutcomeReason", "ods");

            entity.Property(e => e.OutcomeReason).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_OutcomeStatus>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_OutcomeStatus", "ods");

            entity.Property(e => e.OutcomeStatus).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_Panel>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Panel", "ods");

            entity.Property(e => e.PanelName).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_PanelMember>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PanelMember", "ods");

            entity.Property(e => e.PanelMemberName).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_PanelMemberCarrier>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PanelMemberCarrier", "ods");

            entity.Property(e => e.ApplicableFrom).HasColumnType("datetime");
            entity.Property(e => e.ApplicableUntil).HasColumnType("datetime");
            entity.Property(e => e.PanelMemberCarrierId).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<vw_ref_PanelMemberFacility>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PanelMemberFacility", "ods");

            entity.Property(e => e.ApplicableFrom).HasColumnType("datetime");
            entity.Property(e => e.ApplicableUntil).HasColumnType("datetime");
        });

        modelBuilder.Entity<vw_ref_Party>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Party", "ods");

            entity.Property(e => e.DUNSNumber).HasMaxLength(20);
            entity.Property(e => e.PartyId).ValueGeneratedOnAdd();
            entity.Property(e => e.PartyKey).HasMaxLength(50);
            entity.Property(e => e.PartyName).HasMaxLength(500);
        });

        modelBuilder.Entity<vw_ref_PartyAttribute>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PartyAttribute", "ods");

            entity.Property(e => e.ObjectType).HasMaxLength(50);
            entity.Property(e => e.PartyAttributeKey)
                .HasMaxLength(27)
                .IsUnicode(false);
            entity.Property(e => e.Value).HasMaxLength(4000);
        });

        modelBuilder.Entity<vw_ref_PartyRole>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PartyRole", "ods");

            entity.Property(e => e.PartyRole).HasMaxLength(100);
            entity.Property(e => e.PartyRoleId).ValueGeneratedOnAdd();
            entity.Property(e => e.PartyRoleKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_PendingActionReason>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PendingActionReason", "ods");

            entity.Property(e => e.PendingActionReason).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_PlacementPolicyRelationshipType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PlacementPolicyRelationshipType", "ods");

            entity.Property(e => e.PlacementPolicyRelationshipType).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_PlacementStatus>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PlacementStatus", "ods");

            entity.Property(e => e.PlacementStatus).HasMaxLength(200);
            entity.Property(e => e.PlacementStatusId).ValueGeneratedOnAdd();
            entity.Property(e => e.PlacementStatusKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_PolicyRefType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PolicyRefType", "ods");

            entity.Property(e => e.PolicyRefTypeId).ValueGeneratedOnAdd();
            entity.Property(e => e.PolicyRefTypeKey)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.TranslationKey)
                .HasMaxLength(255)
                .IsUnicode(false);
        });

        modelBuilder.Entity<vw_ref_PolicyStatus>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PolicyStatus", "ods");

            entity.Property(e => e.PolicyStatus).HasMaxLength(200);
            entity.Property(e => e.PolicyStatusId).ValueGeneratedOnAdd();
            entity.Property(e => e.PolicyStatusKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_PremiumRange>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PremiumRange", "ods");

            entity.Property(e => e.PremiumRange).HasMaxLength(100);
            entity.Property(e => e.PremiumRangeKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_PricingFactor>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_PricingFactor", "ods");

            entity.Property(e => e.PricingFactorKey)
                .HasMaxLength(255)
                .IsUnicode(false);
        });

        modelBuilder.Entity<vw_ref_ProducingOffice>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ProducingOffice", "ods");

            entity.Property(e => e.ProducingOffice).HasMaxLength(500);
            entity.Property(e => e.ProducingOfficeId).ValueGeneratedOnAdd();
            entity.Property(e => e.ProducingOfficeKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_Product>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Product", "ods");

            entity.Property(e => e.ProductClass).HasMaxLength(200);
            entity.Property(e => e.ProductId).ValueGeneratedOnAdd();
            entity.Property(e => e.ProductKey).HasMaxLength(200);
            entity.Property(e => e.ProductLine).HasMaxLength(200);
            entity.Property(e => e.ReferenceSourceProductDescription).HasMaxLength(4000);
            entity.Property(e => e.ReferenceSourceProductName).HasMaxLength(200);
            entity.Property(e => e.SourceProductDescription).HasMaxLength(4000);
            entity.Property(e => e.SourceProductName).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_ProgramStructureType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ProgramStructureType", "ods");

            entity.Property(e => e.ProgramStructureTypeId).ValueGeneratedOnAdd();
            entity.Property(e => e.ProgramStructureTypeKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_ProgramType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ProgramType", "ods");

            entity.Property(e => e.ProgramType).HasMaxLength(200);
            entity.Property(e => e.ProgramTypeKey).HasMaxLength(50);
        });

        modelBuilder.Entity<vw_ref_RenewableOption>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_RenewableOption", "ods");

            entity.Property(e => e.RenewableOption).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_ResponseType>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ResponseType", "ods");

            entity.Property(e => e.ResponseType).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_ServiceLevel>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ServiceLevel", "ods");

            entity.Property(e => e.ServiceLevel).HasMaxLength(255);
            entity.Property(e => e.ServiceLevelKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_ServiceLevelIssue>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ServiceLevelIssue", "ods");

            entity.Property(e => e.ServiceLevelIssue).HasMaxLength(255);
            entity.Property(e => e.ServiceLevelIssueKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_ServicingRole>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_ServicingRole", "ods");

            entity.Property(e => e.ServicingRole).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_Team>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Team", "ods");

            entity.Property(e => e.OrgLevel1).HasMaxLength(500);
            entity.Property(e => e.OrgLevel2).HasMaxLength(500);
            entity.Property(e => e.OrgLevel3).HasMaxLength(500);
            entity.Property(e => e.OrgLevel4).HasMaxLength(500);
            entity.Property(e => e.OrgLevel5).HasMaxLength(500);
            entity.Property(e => e.OrgLevel6).HasMaxLength(500);
            entity.Property(e => e.OrganisationRole).HasMaxLength(50);
            entity.Property(e => e.TeamName).HasMaxLength(200);
        });

        modelBuilder.Entity<vw_ref_TimeZone>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_TimeZone", "ods");

            entity.Property(e => e.TimeZone).HasMaxLength(150);
            entity.Property(e => e.TimeZoneKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_Trigger>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Trigger", "ods");

            entity.Property(e => e.TriggerKey).HasMaxLength(100);
        });

        modelBuilder.Entity<vw_ref_VerticalIndustry>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_VerticalIndustry", "ods");

            entity.Property(e => e.VerticalIndustry).HasMaxLength(100);
            entity.Property(e => e.VerticalIndustryKey).HasMaxLength(255);
        });

        modelBuilder.Entity<vw_ref_Worker>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vw_ref_Worker", "ods");

            entity.Property(e => e.GivenName).HasMaxLength(100);
            entity.Property(e => e.Surname).HasMaxLength(100);
            entity.Property(e => e.UserPrincipalName).HasMaxLength(100);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}

