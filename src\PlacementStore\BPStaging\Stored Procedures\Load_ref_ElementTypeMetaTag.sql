/*
Lineage
ref.ElementTypeMetaTag.ElementTypeMetaTagKey=BPStaging.ElementTypeMetaTag.Id
ref.ElementTypeMetaTag.ElementTypeId=BPStaging.ElementTypeMetaTag.ElementTypeId
ref.ElementTypeMetaTag.MetaTagId=ref.MetaTag.MetaTagId
ref.ElementTypeMetaTag.SourceUpdatedDate=BPStaging.ElementTypeMetaTag.ValidTo
ref.ElementTypeMetaTag.SourceUpdatedDate=BPStaging.ElementTypeMetaTag.ValidFrom
ref.ElementTypeMetaTag.IsDeprecated=BPStaging.ElementTypeMetaTag.ValidTo
*/

CREATE PROCEDURE BPStaging.Load_ref_ElementTypeMetaTag
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ElementTypeMetaTag';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ElementTypeMetaTag)
BEGIN
    BEGIN TRY
        MERGE ref.ElementTypeMetaTag T
        USING (
            SELECT
                ElementTypeMetaTagKey = CAST(inner_select.Id AS NVARCHAR(50))
              , inner_select.ElementTypeId
              , mt.MetaTagId
              , IsDeprecated = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                        THEN 1
                                    ELSE 0 END
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , DataSourceInstanceId = 50366
            FROM (
            SELECT
                Id
              , ElementTypeId
              , MetaTagId
              , ValidTo
              , ValidFrom
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.ElementTypeMetaTag cp
        ) inner_select
                 INNER JOIN ref.MetaTag mt
                     ON inner_select.MetaTagId = mt.MetaTagKey
                        AND mt.DataSourceInstanceId = 50366
            WHERE
                inner_select.RowNo = 1
                AND EXISTS (
                SELECT 1 FROM ref.ElementType et WHERE et.ElementTypeId = inner_select.ElementTypeId
            )
        ) S
        ON T.ElementTypeMetaTagKey = S.ElementTypeMetaTagKey
           AND T.DataSourceInstanceId = S.DataSourceInstanceId
        WHEN NOT MATCHED
            THEN INSERT (
                     ElementTypeMetaTagKey
                   , ElementTypeId
                   , MetaTagId
                   , SourceUpdatedDate
                   , IsDeprecated
                   , DataSourceInstanceId
                 )
                 VALUES
                     (
                         S.ElementTypeMetaTagKey
                       , S.ElementTypeId
                       , S.MetaTagId
                       , S.SourceUpdatedDate
                       , S.IsDeprecated
                       , S.DataSourceInstanceId
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.ElementTypeMetaTagKey
      , T.ElementTypeId
      , T.MetaTagId
      , T.SourceUpdatedDate
      , T.IsDeprecated
      , T.DataSourceInstanceId
    INTERSECT
    SELECT
        S.ElementTypeMetaTagKey
      , S.ElementTypeId
      , S.MetaTagId
      , S.SourceUpdatedDate
      , S.IsDeprecated
      , S.DataSourceInstanceId
)
            THEN UPDATE SET
                     T.ElementTypeMetaTagKey = S.ElementTypeMetaTagKey
                   , T.ElementTypeId = S.ElementTypeId
                   , T.MetaTagId = S.MetaTagId
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.IsDeprecated = S.IsDeprecated
                   , T.DataSourceInstanceId = S.DataSourceInstanceId
                   , T.ETLUpdatedDate = GETUTCDATE()
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);