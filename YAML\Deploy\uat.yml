﻿parameters:
  configOnly: false
  reporting: false
  enableReplica: false
  download: current
  variableGroupName: crbbro-ps-preprod
  variableGroupNameShared: crbbro-bkt-preprod
  microservice: ps
  appId: 002757
  billingCode: 'Placement Store'
  envName: uat
  regionName: em20
  regionNameDR: em21
  azureServiceConnection: AKS-CRBBRO-UAT
  aksIdentityName: aks-u-em20-identity
  agentPoolName: Private-CRB-Linux-AKS-U
  resourceGroupRoleAssignment:
  - roleName: Reader                                                        # Azure role
    members:
    - memberName: R-AZC-CRBBRO-PREPROD-RGRP-READER                          # Cloud Group
      type: Group
  - roleName: 'Website Contributor'                                         # Azure role
    members:
    - memberName: R-AZC-CRBBRO-PREPROD-RGRP-CONTRIBUTOR                     # Cloud Group
      type: Group
  - roleName: 'Web Plan Contributor'                                        # Azure role
    members:
    - memberName: R-AZC-CRBBRO-PREPROD-RGRP-CONTRIBUTOR                     # Cloud Group
      type: Group
  - roleName: 'Monitoring Reader'                                           # Azure role
    members:
    - memberName: R-AZC-CRBBRO-PREPROD-RGRP-READER                          # Cloud Group
      type: Group
    - memberName: crbbil-md-u-em20-wa                                       # Monitoring Dashboard App
      type: ServicePrincipal
    - memberName: crbbil-md-u-em20-wa/slots/staging
      type: ServicePrincipal
  - roleName: 'Data Factory ReadOnly'                                       # Data Factory ReadOnly
    members:
    - memberName: R-AZC-CRBBRO-PREPROD-RGRP-READER                          # Cloud Group
      type: Group
  - roleName: 'Data Factory Contributor'                                    # Data Factory Contributor
    members:
    - memberName: crbbro-ps-##envLetter##-##regionName##-adf                # Data Factory
      type: ServicePrincipal
  storageAccount:
    create: true                                                            # Create Storage Account
    fileShares:
    - name: datasource-cam-analytics
    rbacAssignments:
    - roleName: 'Storage File Data SMB Share Reader'                        # Azure role
      members:
      - memberName: R-AZC-CRBBRO-PREPROD-RGRP-READER                        # Cloud Group
        type: Group
  virtualNetwork:
    resourceGroupName1: crbbro-bkt-##envLetter##-##regionName##-rgrp
    virtualNetworkName1: crbbro-bkt-##envLetter##-##regionName##-vnet2
    resourceGroupName2: crbbro-bkt-##envLetter##-##regionName##-rgrp
    virtualNetworkName2: crbbro-bkt-##envLetter##-##regionName##-vnet
  appServicePlans:
  - name: crbbro-ps-##envLetter##-##regionName##-asp
    shortName: dedicated
    sku: P0v3
    kind: app                                                            # app or linux
    scaleOut:
      capacity:
        minimum: 1                                                       # 1-8
        maximum: 1                                                       # 1-8
        default: 1                                                       # between minimum and maximum values
  keyVault:                                                              # Create KeyVault
    create: true
    vaultAccess:                                                         # Access list for KeyVault
      - displayName: aks-u-em20-identity
        type: ServicePrincipal
        permissions:                                                     # Permissions to enable on the KeyVault
          keys: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore'
          secrets: 'Get,List,Set,Delete,Recover,Backup,Restore'
          certificates: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore,ManageContacts,ManageIssuers,GetIssuers,ListIssuers,SetIssuers,DeleteIssuers'
      - displayName: crbbro-ps-##envLetter##-em20-adf
        type: ServicePrincipal
        permissions:                                                     # Permissions to enable on the KeyVault
          secrets: 'Get,List'
      - displayName: R-AZC-CRBBRO-PREPROD-RGRP-CONTRIBUTOR               # Cloud Group
        type: Group
        permissions:                                                     # Permissions to enable on the KeyVault
          secrets: 'Get,List'
  vaultSecrets:                                                          # Secrets to automatically generate in KeyVault (if not already present)
  - name: ServiceAccountPassword--SVC-PSPACT-I                           # Database connection
    type: value                                                          # Secret type
    value: $(ServiceAccountPassword-SVC-PSPACT-I)
  - name: ServiceAccountPassword--SVC-W-GPPPACTETL-IAT                   # SVC-W-GPPPACTETL-IAT
    type: value                                                          # Secret type
    value: $(ServiceAccountPassword-SVC-W-GPPPACTETL-IAT)
  webApps:
  - project: PsWeb                                                          # Name of project containing Web application
    dotnetVersion: '6.0'                                                    # DotNet Framework Version
    appServicePlan: crbbro-ps-##envLetter##-##regionName##-asp              # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##envLetter##-##regionName##-subn1                # Subnet name to connect Web application
    clientAffinityEnabled: false
    use32BitWorkerProcess: false
    autoHealEnabled: true
    applicationGateway:                                                     # App Gateway network details
      resourceGroupName: crbbro-dvo-##envLetter##-##regionName##-rgrp
      virtualNetworkName: crbbro-dvo-##envLetter##-##regionName##-vnet
      subnetName: crbbro-dvo-##envLetter##-##regionName##-subn2
    applicationGatewayPrivate:                                              # App Gateway APIM network details
      resourceGroupName: crbbro-dvo-##envLetter##-##regionName##-rgrp
      virtualNetworkName: crbbro-dvo-##envLetter##-##regionName##-vnet
      subnetName: crbbro-dvo-##envLetter##-##regionName##-subn4
    artifactName: WebApp                                                    # Name of published artefact from build stage
    healthCheckPath: '/api/HealthCheck'                                     # Path of health check api to call to check health during deployment
    appSettingsName: webAppSettings                                         # Artefact name with common app settings
    overrideAppSettings:                                                    # Environment specific overrides for app.settings
    - name: AzureAd__ClientId
      value: 80003a4d-e9f8-4188-839d-cf922e137af3
    - name: AzureAd__Scope
      value: api://80003a4d-e9f8-4188-839d-cf922e137af3/.default
    - name: PlacementStore__ConnectionString
      value: "Server=crbbro-dvo-u-em-sql.secondary.database.windows.net;Database=crbbro-ps-u-em20-db;Authentication=Active Directory Default"
    - name: PlacementStore__Mappings__0__ApplicationId
      value: 3ad846d6-ed83-4d42-9cc7-9b0400de25d0                           # PSA.Client.DevTest
    - name: PlacementStore__Mappings__0__SharedKeys__0
      value: 8B8434E3-5039-4F9C-ABD0-DFDED3120CED                           # 50000
    - name: PlacementStore__Mappings__1__ApplicationId
      value: 4fed33cd-57ee-4cb0-8dc9-029d19abbffa                           # PSA.Client.COL-PowerApps
    - name: PlacementStore__Mappings__1__SharedKeys__0
      value: 46B9A73F-FC55-4173-B7CE-7A2B4F89B264                           # 50003
    - name: PlacementStore__Mappings__2__ApplicationId
      value: a84d3f28-774e-4e9e-a8d9-f125329c329d                           # PSA.Client.Eclipse-PowerApps
    - name: PlacementStore__Mappings__2__SharedKeys__0
      value: 8B8434E3-5039-4F9C-ABD0-DFDED3120CED                           # 50000
    - name: PlacementStore__Mappings__3__ApplicationId
      value: b2c0f719-3761-41f6-82e8-2c731c3dc09c                           # PSA.Client.Broking-net-PowerApps
    - name: PlacementStore__Mappings__3__SharedKeys__0
      value: 19CA43BD-B79F-44C5-BB5B-F9C09BB67EE9                           # 50358
    - name: PlacementStore__Mappings__4__ApplicationId
      value: 6dd357be-3c2b-4bd2-acd9-84382845bd21                           # PSA.Client.Epic-US-PowerApps
    - name: PlacementStore__Mappings__4__SharedKeys__0
      value: CB57DD98-E69B-48FA-9258-76505B48E5A9                           # 50001
    - name: PlacementStore__Mappings__5__ApplicationId
      value: aaf5420b-948f-468d-a850-6474b8d0b343                           # PSA.Client.CPT
    - name: PlacementStore__Mappings__5__SharedKeys__0
      value: CB57DD98-E69B-48FA-9258-76505B48E5A9                           # 50001
    - name: PlacementStore__Mappings__6__ApplicationId
      value: 2d872520-5288-4737-927f-724065054b17                           # PSA.Client.CGW
    - name: PlacementStore__Mappings__7__ApplicationId
      value: 5e4434ef-0f60-47a7-aea4-5ba57aeed1d7                           # PSA.Client.Eglobal-Australia-PowerApps
    - name: PlacementStore__Mappings__7__SharedKeys__0
      value: 0AFF89AC-C352-48B9-90CA-4283382D3896                           # 50004
    - name: PlacementStore__Mappings__8__ApplicationId
      value: b5fde768-4b50-42a0-8534-45718631ad27                           # PSA.Client.BPA
    - name: PlacementStore__Mappings__9__ApplicationId
      value: 56825227-c263-43e9-b4e1-8624be0f8589                           # PSA.Client.WIBS (Italy)
    - name: PlacementStore__Mappings__9__SharedKeys__0
      value: 1ABF41B3-BE9F-4E5C-B053-D70310D4E982                           # 50045
    - name: PlacementStore__Mappings__10__ApplicationId
      value: eb7d1bb3-2194-485b-814d-73b334c0fccc                           # PSA.Client.ASYS-Germany
    - name: PlacementStore__Mappings__10__SharedKeys__0
      value: 4E1AEF3A-FB97-4CE3-B3E6-87A5FA7FD725                           # 50029
    - name: PlacementStore__Mappings__11__ApplicationId
      value: 03a20d44-0d5e-41fa-aa9a-e0df63f49ec8                           # PSA.Client.Gras-Savoye-EGS (France)
    - name: PlacementStore__Mappings__11__SharedKeys__0
      value: C762D601-6F8A-4AEC-A7FC-DC47617BC7BF                           # 50364
    - name: PlacementStore__Mappings__12__ApplicationId
      value: f7dd3b46-17e2-4059-bfe3-7e8c545b1dcb                           # PSA.Client.VisualSeg-Spain
    - name: PlacementStore__Mappings__12__SharedKeys__0
      value: 879725C5-72C9-4421-9C52-6DE1B7140FDD                           # 50044
    - name: PlacementStore__Mappings__13__ApplicationId
      value: 37fd81fa-a452-48c1-9815-9244f73a59d0                           # PSA.Client.eGlobal-Netherlands
    - name: PlacementStore__Mappings__13__SharedKeys__0
      value: 75F798E7-61A4-4F99-A8CF-B02D77C6367E                           # 50010
    - name: PlacementStore__Mappings__14__ApplicationId
      value: e15d1e81-8315-4e8c-8fb6-2764ee5f9d65                           # PSA.Client.eGlobal-HongKong
    - name: PlacementStore__Mappings__14__SharedKeys__0
      value: E82BD58C-F207-4FE6-AD79-C3544C5CF97E                           # 50007
    - name: PlacementStore__Mappings__15__ApplicationId
      value: c24999e0-cd38-44b6-80bd-5f2ec070cfa4                           # PSA.Client.BisCore
    - name: PlacementStore__Mappings__15__SharedKeys__0
      value: 10A4D7CF-02AA-41EB-B34F-288BA3E342FF                           # 50500
    - name: PlacementStore__Mappings__16__ApplicationId
      value: 063d3f9f-7486-4760-ba66-d8bf8ca5f404                           # PSA.Client.eGlobal-South-Africa
    - name: PlacementStore__Mappings__16__SharedKeys__0
      value: 0BB9DDDD-1892-4792-9C5F-3F0DBC20EAC8                           # 50015
    - name: PlacementStore__Mappings__17__ApplicationId
      value: 87bf6957-206a-4331-bc95-fc2f946d1b2b                           # PSA.Client.Reference-Data
    - name: PlacementStore__Mappings__17__SharedKeys__0
      value: B5F6B099-EA5A-4FD5-9F86-7E45C5BD9514                           # 50355
    - name: PlacementStore__Mappings__18__ApplicationId
      value: ccae9c27-30b1-44dd-a103-3173fe40fe1c                           # PSA.Client.Carrier-Gateway
    - name: PlacementStore__Mappings__19__ApplicationId
      value: 92943c30-4a4c-414c-a90f-e023a08d8602                           # PSA.Client.SegElevia-Portugal
    - name: PlacementStore__Mappings__19__SharedKeys__0
      value: 23FD35A6-8849-4B88-A512-7F37252F71E4                           # 50041
    - name: PlacementStore__Mappings__20__ApplicationId
      value: 8fd4f945-09da-445a-9d71-9b547f16bb18                           # PSA.Client.eGlobal-China
    - name: PlacementStore__Mappings__20__SharedKeys__0
      value: E1696C42-29A6-464F-83A3-F9EF42AC0EFE                           # 50006
  funcApps:
  - project: PsFunc                                                         # Name of project containing Function application
    dotnetVersion: 'v8.0'                                                   # DotNet Framework Version
    artifactName: FuncApp                                                   # Name of published artefact from build stage
    appServicePlan: crbbro-ps-##envLetter##-##regionName##-asp              # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##envLetter##-##regionName##-subn1                # Subnet name to connect Web application
    healthCheckPath: '/api/HealthCheck'                                     # Path of Http Trigger Function to call to check health during deployment
    disabledFunctions:                                                      # Functions that should remain disabled
    - name: HealthCheckExt                                                  # Extended healthcheck for manual run only
    appSettingsName: funcAppSettings                                        # Artefact name with common app settings
    overrideAppSettings:                                                    # Environment specific overrides for app.settings
    - name: ConnectionStrings__EventHubEndpoint__fullyQualifiedNamespace
      value: crb-data-placement-i-na20-ev.servicebus.windows.net            # Event Hub url
    - name: ConnectionStrings__BKPDatabase
      value: "Server=crbbro-dvo-u-em-sql.database.windows.net;Database=crbbro-bkp-u-em20-db;Authentication=Active Directory Default"
    - name: ConnectionStrings__ServiceHub
      value: "Server=crbbro-dvo-u-em-sql.database.windows.net;Database=crbbro-sh-u-em20-db;Authentication=Active Directory Default"
    - name: ConnectionStrings__ServiceHubMetadata
      value: "Server=crbbro-dvo-u-em-sql.database.windows.net;Database=crbbro-sh-u-em20-db-metadata;Authentication=Active Directory Default"
    - name: AasServerName
      value: "crbbropsem20ussas"
    - name: AasRegionName
      value: "westeurope"
  sqlDatabases:
  - project: PlacementStoreDb.Build                                         # Project containing database
    name: crbbro-ps-u-em20-db
    shortName: ps
    useManagedIdentity: true                                                # Using Microsoft.Data.SqlClient instead of System.Data.SqlClient to allow automatic token authentication
    vaultSecretName: ConnectionStrings--Database
    dest:                                                                   # SQL Server / Elastic Pool which will host the database (to manage Azure costs)
      resourceGroupName: crbbro-dvo-u-em20-rgrp
      resourceGroupNameDR: crbbro-dvo-u-em21-rgrp                           # SQL Server DR Resource Group
      sqlServerName: crbbro-dvo-u-em20-sql
      sqlServerNameDR: crbbro-dvo-u-em21-sql                                # SQL Server for DR site
      sqlPrimaryServerName: crbbro-dvo-u-em-sql                             # Primary (Read/Write) SQL server
      sqlSecondaryServerName: crbbro-dvo-u-em-sql.secondary                 # Secondary (Read Only) SQL server
      elasticPoolName: crbbro-dvo-u-em-epool-ps
    artifactName: PlacementStore.Database                                   # Name of published artefact from build stage
    roleMembers:
    - roleName: db_owner                                                    # Database role
      members:
      - memberName: R-AZC-CRBBRO-PREPROD-SQL-CONTRIBUTOR                    # Cloud Group
        type: Group
    - roleName: db_datareader                                               # Database role
      members:
      - memberName: R-AZC-CRBBRO-PREPROD-SQL-READER                         # Cloud Group
        type: Group
    - roleName: DevReadOnly                                                 # Database role
      members:
      - memberName: R-AZC-CRBBRO-DATAENGINEER                               # Cloud group
        type: Group
    - roleName: PlacementStoreAPIConsumerRole                               # Database role
      members:
      - memberName: crbbro-ps-##envLetter##-##regionName##-wa               # PS Web App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-wa/slots/staging # PS Staging Web App
        type: ServicePrincipal
    - roleName: ProgrammestoreODSConsumerRole                               # Database role
      members:
      - memberName: R-AZC-CRBBRO-PS-ODS-PREPROD-CONSUMER-READ               # Database role
        type: ServicePrincipal
    - roleName: ProgrammeStoreMarineMarConsumerRole                         # Database role
      members:
      - memberName: R-AZC-CRBBRO-PS-MARINEMAR-PREPROD-CONSUMER-READ         # Database role
        type: ServicePrincipal
    - roleName: ETL_Role                                                    # Database role
      members:
      - memberName: crbbro-ps-##envLetter##-##regionName##-fa               # PS Func App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-fa/slots/staging # PS Staging Func App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-adf              # PS ADF
        type: ServicePrincipal
      - memberName: <EMAIL>               # PS Service Account
        type: User
    - roleName: ProgrammeStoreQueryRole                                     # Database role
      members:
      - memberName: <EMAIL>           # PS Service Account
        type: User
      - memberName: E20-CB-BKT001U
        type: ServicePrincipal
    - roleName: TaskControllerRole                                          # Database role
      members:                                                              
      - memberName: <EMAIL>           # PS Service Account
        type: User
      - memberName: E20-CB-BKT001U
        type: ServicePrincipal
  ssas:
    ssasServer: 'asazure://westeurope.asazure.windows.net/crbbropsem20ussas'
    resourceGroupName: crbbro-ps-u-em20-rgrp
    models:
    - name: 'GA'
      databaseName: 'GeneralAnalytics_AS_TAB'
    - name: 'Usage'
      databaseName: 'BrokingPlatformUsage_AS_TAB'
    - name: 'Carrier'
      databaseName: 'CarrierAnalytics_AS_TAB'
    impersonationUserId: 'int\svc-w-psbroker-iat'
    ssasAdminUserPassword: $(SSASAdminUserPassword)
    analysisServicesUserName: '<EMAIL>'
    envName: uat
    placementStoreSqlServer: 'crbbro-dvo-u-em-sql.secondary.database.windows.net'
    placementStoreDatabaseName: crbbro-ps-u-em20-db
  dataFactory:
    create: true
    subscriptionId: f388d80c-59db-4f8f-b31f-bdd5d3195716
    artefacts: PS.ADF
    psKeyVaultUrl: 'https://crbbro-ps-u-em20-kv.vault.azure.net/'
    selfHostedIntegrationRuntimeResourceId: '/subscriptions/f388d80c-59db-4f8f-b31f-bdd5d3195716/resourcegroups/crbbro-bkt-u-em20-rgrp/providers/Microsoft.DataFactory/factories/crbbro-bkt-u-em20-adf/integrationruntimes/SharedSelfHostedIntegrationRuntime'
    mpeSqlServerResourceId: '/subscriptions/f388d80c-59db-4f8f-b31f-bdd5d3195716/resourceGroups/crbbro-dvo-u-em20-rgrp/providers/Microsoft.Sql/servers/crbbro-dvo-u-em20-sql'  # SQL Server Managed Private End point
    mpePactServerResourceId: '/subscriptions/92ae2673-1f32-4529-aeb0-197f7b14d0f7/resourceGroups/PAS-U-EM22-RGRP/providers/Microsoft.Synapse/workspaces/pas-u-em22-saws'       # PACT Server Managed Private End point
    mpeWillisReferenceServerResourceId: '/subscriptions/103aaa2d-b0cd-43f8-86df-a12b6f9340b6/resourceGroups/DPSERD-SQL-EM20-I-RGRP/providers/Microsoft.Sql/managedInstances/e20-eds-erdmd1i'        # Willis Reference Server Managed Private End point
    mpeCrbDataLakeResourceId: '/subscriptions/5c432ba7-a72c-4510-93aa-166f8fc865fe/resourceGroups/CRB-DATA-T-NA20-RGRP/providers/Microsoft.Storage/storageAccounts/crbdatatna20sa'        # CRB data lake Managed Private End point
    triggers:
    - Name : 'Every3Hours'
      enabled: false
    - Name: 'Every24Hours'
      enabled: true
    - Name: 'SetPactLoad'
      enabled: true
stages:
- stage: ${{parameters.envName}}
  dependsOn:
  - start
  jobs:
  - deployment: approve
    displayName: 'Approve ${{parameters.envName}}'
    pool: server
    environment: y-deploy-crbbro-${{parameters.microservice}}-${{parameters.envName}}

  - template: deploy_infra.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      regionNameDR: ${{parameters.regionNameDR}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      resourceGroupRoleAssignment: ${{parameters.resourceGroupRoleAssignment}}
      virtualNetwork: ${{parameters.virtualNetwork}}
      storageAccount: ${{parameters.storageAccount}}
      keyVault: ${{parameters.keyVault}}
      vaultSecrets: ${{parameters.vaultSecrets}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      appServicePlans: ${{parameters.appServicePlans}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      dataFactory: ${{parameters.dataFactory}}

- stage: ${{parameters.envName}}_apps
  ${{ if or(eq(parameters.configOnly,'true'),eq(parameters.reporting,'true')) }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
  ${{ else }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
    - build_apps
  variables:
  - name: virtualNetworkResourceGroupName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkResourceGroupName'] ]
  - name: virtualNetworkName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkName'] ]
  - name: appSubnetNamesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['appSubnets.appSubnetNamesCSV'] ]
  - name: appIdentitiesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['identities.appIdentitiesCSV'] ]
  - name: virtualNetworkResourceGroupNameDR
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionNameDR}}_vnet.outputs['vnet.virtualNetworkResourceGroupName'] ]
  - name: virtualNetworkNameDR
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionNameDR}}_vnet.outputs['vnet.virtualNetworkName'] ]
  - name: appSubnetNamesCSVDR
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionNameDR}}_security.outputs['appSubnets.appSubnetNamesCSV'] ]
  - name: appIdentitiesCSVDR
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionNameDR}}_security.outputs['identities.appIdentitiesCSV'] ]
  - name: dataFactoryName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_dedicated.outputs['dataFactory.dataFactoryName'] ]
  - name: dataFactoryresourceGroupName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_dedicated.outputs['dataFactory.resourceGroupName'] ]
  jobs:
  - template: deploy_apps.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      regionNameDR: ${{parameters.regionNameDR}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      etl: ${{parameters.etl}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      ssas: ${{ parameters.ssas }}
      virtualNetworkResourceGroupName: $(virtualNetworkResourceGroupName)
      virtualNetworkName: $(virtualNetworkName)
      appSubnetNamesCSV: $(appSubnetNamesCSV)
      appIdentitiesCSV: $(appIdentitiesCSV)
      virtualNetworkResourceGroupNameDR: $(virtualNetworkResourceGroupNameDR)
      virtualNetworkNameDR: $(virtualNetworkNameDR)
      appSubnetNamesCSVDR: $(appSubnetNamesCSVDR)
      appIdentitiesCSVDR: $(appIdentitiesCSVDR)
      dataFactory: ${{parameters.dataFactory}}
      dataFactoryName: $(dataFactoryName)
      dataFactoryResourceGroupName: $(dataFactoryResourceGroupName)
      enableReplica: ${{parameters.enableReplica}}

# Scale down after deployment
- ${{ if eq(parameters.enableReplica,'true') }}:
  - stage: ${{parameters.envName}}_ssas_scale_down
    condition: succeededOrFailed()
    dependsOn: ${{parameters.envName}}_apps
    jobs:
    - deployment: approve
      displayName: 'Approve ${{parameters.envName}}'
      pool: server
      environment: y-deploy-crbbro-${{parameters.microservice}}-${{parameters.envName}}
    - template: deploy_reporting_scale_sync.yml
      parameters:
        envName: ${{parameters.envName}}
        microservice: ${{parameters.microservice}}
        variableGroupName: ${{parameters.variableGroupName}}
        variableGroupNameShared: ${{parameters.variableGroupNameShared}}
        download: ${{parameters.download}}
        azureServiceConnection: ${{parameters.azureServiceConnection}}
        ssas: ${{parameters.ssas}}
        replicaCount: 0
        operation: 'scale_down'
        enableReplica: ${{parameters.enableReplica}}
