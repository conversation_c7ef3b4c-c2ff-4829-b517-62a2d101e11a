/*
Lineage
ref.ElementAttributeReferenceOption.ElementAttributeReferenceOptionKey=BPStaging.ElementAttributeReferenceOption.Id
ref.ElementAttributeReferenceOption.TranslationKey=BPStaging.ElementAttributeReferenceOption.LabelTranslationKey
ref.ElementAttributeReferenceOption.ElementAttributeReference=BPStaging.ElementAttributeReferenceOption.Text
ref.ElementAttributeReferenceOption.SourceUpdatedDate=BPStaging.ElementAttributeReferenceOption.ValidFrom
ref.ElementAttributeReferenceOption.IsDeprecated=BPStaging.ElementAttributeReferenceOption.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_ElementAttributeReferenceOption
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ElementAttributeReferenceOption';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.ElementAttributeReferenceOption T
    USING (
        SELECT
            ElementAttributeReferenceOptionKey = CAST(Id AS NVARCHAR(100))
          , DataSourceInstanceId = 50366
          , TranslationKey = LabelTranslationKey
          , ElementAttributeReference = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.ElementAttributeReferenceOption
    ) S
    ON T.ElementAttributeReferenceOptionKey = S.ElementAttributeReferenceOptionKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 ElementAttributeReferenceOptionKey
               , DataSourceInstanceId
               , TranslationKey
               , ElementAttributeReference
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.ElementAttributeReferenceOptionKey
                   , S.DataSourceInstanceId
                   , S.TranslationKey
                   , S.ElementAttributeReference
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.TranslationKey
                               , T.ElementAttributeReference
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.TranslationKey
                               , S.ElementAttributeReference
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.TranslationKey = S.TranslationKey
               , T.ElementAttributeReference = S.ElementAttributeReference
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);