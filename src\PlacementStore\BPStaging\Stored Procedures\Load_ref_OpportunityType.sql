/*
Lineage
ref.OpportunityType.OpportunityTypeKey=BPStaging.OpportunityType.Id
ref.OpportunityType.TranslationKey=BPStaging.OpportunityType.LabelTranslationKey
ref.OpportunityType.OpportunityType=BPStaging.OpportunityType.Text
ref.OpportunityType.IsDeprecated=BPStaging.OpportunityType.IsDeprecated
ref.OpportunityType.SourceUpdatedDate=BPStaging.OpportunityType.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_ref_OpportunityType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.OpportunityType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @BPDataSourceInstanceId INT = 50366;

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

-- Only process is something staged.
IF (EXISTS (SELECT 1 FROM BPStaging.OpportunityType))
BEGIN
    BEGIN TRY
        MERGE ref.OpportunityType T
        USING (
            SELECT
                DataSourceInstanceId = @BPDataSourceInstanceId
              , OpportunityTypeKey = CAST(ot.Id AS NVARCHAR(255))
              , TranslationKey = ot.LabelTranslationKey
              , OpportunityType = ot.Text
              , ot.IsDeprecated
              , SourceUpdatedDate = ot.ValidFrom
            FROM
                BPStaging.OpportunityType ot
        ) S
        ON S.DataSourceInstanceId = T.DataSourceInstanceId
           AND S.OpportunityTypeKey = T.OpportunityTypeKey
        WHEN NOT MATCHED
            THEN INSERT (
                     DataSourceInstanceId
                   , OpportunityTypeKey
                   , TranslationKey
                   , OpportunityType
                   , IsDeprecated
                   , SourceUpdatedDate
                 )
                 VALUES
                     (
                         S.DataSourceInstanceId
                       , S.OpportunityTypeKey
                       , S.TranslationKey
                       , S.OpportunityType
                       , S.IsDeprecated
                       , S.SourceUpdatedDate
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.DataSourceInstanceId
      , S.OpportunityTypeKey
      , S.TranslationKey
      , S.OpportunityType
      , S.IsDeprecated
      , S.SourceUpdatedDate
    INTERSECT
    SELECT
        T.DataSourceInstanceId
      , T.OpportunityTypeKey
      , T.TranslationKey
      , T.OpportunityType
      , T.IsDeprecated
      , T.SourceUpdatedDate
)
            THEN UPDATE SET
                     T.TranslationKey = S.TranslationKey
                   , T.OpportunityType = S.OpportunityType
                   , T.IsDeprecated = S.IsDeprecated
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.ETLUpdatedDate = GETUTCDATE()
        WHEN NOT MATCHED BY SOURCE AND T.DataSourceInstanceId = @BPDataSourceInstanceId
                                       AND T.IsDeprecated = 0
            THEN UPDATE SET
                     T.IsDeprecated = 1
                   , T.ETLUpdatedDate = GETUTCDATE()
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;