﻿using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.BPStaging.StoredProcedures;

[ExcludeFromCodeCoverage]
public class UpdatePlacementDatesAndStatusTests : PlacementStoreTestBase
{
    /// <summary>
    /// If we have duplicate policies and they agree we can still use the data
    /// If they don't agree we can't
    /// Use this to configure the tests.
    /// </summary>
    enum DuplicateRecord
    {
        None,
        SameData,
        DifferentData
    }

    /// <summary>
    /// Trying to split this into three.
    /// First is checking that the Status is updated (or not) correctly.
    /// </summary>
    /// <param name="dataSourceInstance"></param>
    /// <param name="placementStatus"></param>
    /// <param name="placementInceptionDateDaysRelativeToToday"></param>
    /// <param name="placementTeamId"></param>
    /// <param name="teamFullPath"></param>
    /// <param name="expiringPolicy1Status"></param>
    /// <param name="expiringPolicy2Status"></param>
    /// <param name="currentPolicy1Status"></param>
    /// <param name="currentPolicy2Status"></param>
    /// <param name="isMTA"></param>
    /// <param name="expectedPlacementStatus"></param>
    [Theory]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.NotStarted, null, null, "CRB > GB > NONMARINE", null, null, RefPolicyStatus.NotTakenUp, RefPolicyStatus.Live, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", RefPolicyStatus.Lapsed, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", RefPolicyStatus.Cancelled, null, null, null, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", RefPolicyStatus.NotTakenUp, null, null, null, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", null, null, RefPolicyStatus.Live, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", null, null, RefPolicyStatus.Cancelled, null, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", null, null, RefPolicyStatus.NotTakenUp, null, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", RefPolicyStatus.Lapsed, null, RefPolicyStatus.NotTakenUp, null, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.Complete, null, null, "CRB > GB > NONMARINE", RefPolicyStatus.Lapsed, null, RefPolicyStatus.NotTakenUp, null, null, null)]

    /* For Marine Team do not cancel too fast */
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, 3000262, "CRB > GB > MARINE", null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, -70, 3000262, "CRB > GB > MARINE", null, null, null, null, null, null)] /* Not convinced */
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, -70, 3000262, "CRB > GB > MARINE", null, null, RefPolicyStatus.NotTakenUp, null, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, -50, 3000262, "CRB > GB > MARINE", null, null, RefPolicyStatus.NotTakenUp, null, null, null)]

    // A marine one mustn't cancel too fast, but others can
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, -50, 3000262, "CRB > GB > NONMARINE", null, null, RefPolicyStatus.NotTakenUp, null, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, -70, 3000262, "CRB > GB > MARINE", null, null, RefPolicyStatus.NotTakenUp, RefPolicyStatus.NotTakenUp, null, PlacementStatus.Cancelled)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, -70, 3000262, "CRB > GB > MARINE", null, null, RefPolicyStatus.Live, RefPolicyStatus.NotTakenUp, null, null)]

    // MTAs don't update for current policy
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", null, null, RefPolicyStatus.Cancelled, null, true, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, null, null, "CRB > GB > NONMARINE", null, null, RefPolicyStatus.NotTakenUp, null, true, null)]
    public void CheckPlacementStatusForInputTest(
        DataSourceInstance dataSourceInstance,
        PlacementStatus placementStatus,
        int? placementInceptionDateDaysRelativeToToday,
        int? placementTeamId,
        string? teamFullPath,
        RefPolicyStatus? expiringPolicy1Status,
        RefPolicyStatus? expiringPolicy2Status,
        RefPolicyStatus? currentPolicy1Status,
        RefPolicyStatus? currentPolicy2Status,
        bool? isMTA,
        PlacementStatus? expectedPlacementStatus) /* Null is no change */
    {
        /* We need to ensure we have PAS.RefPolicyStatus and PAS.PolicyStatus populated for these to work */
        CreateRow(
            tableName: "PAS.RefPolicyStatus",
            values: @"RefPolicyStatusKey	RefPolicyStatusID	DataSourceInstanceId	RefPolicyStatus
                    100	100	50437	Placement
                    101	101	50437	Not Taken Up (NTU)
                    102	102	50437	Live
                    103	103	50437	Lapsed
                    104	104	50437	Cancelled
                    105	105	50437	Not applicable
                    106	106	50437	Suspended
        ");
        if(expiringPolicy1Status != null)
        {
            dynamic policyStatusRecord1 = CreateRow(tableName: "PAS.PolicyStatus", values: new
            {
                PolicyStatusKey = expiringPolicy1Status,
                DataSourceInstanceId = (int)dataSourceInstance,
                PolicyStatus = expiringPolicy1Status,
                RefPolicyStatusId = (int)expiringPolicy1Status
            });
        }
        if(expiringPolicy2Status != null && expiringPolicy2Status != expiringPolicy1Status)
        {
            dynamic policyStatusRecord2 = CreateRow(tableName: "PAS.PolicyStatus", values: new
            {
                PolicyStatusKey = expiringPolicy2Status,
                DataSourceInstanceId = (int)dataSourceInstance,
                PolicyStatus = expiringPolicy2Status,
                RefPolicyStatusId = (int)expiringPolicy2Status
            });
        }
        if(currentPolicy1Status != null && currentPolicy1Status != expiringPolicy1Status && currentPolicy1Status != expiringPolicy2Status)
        {
            dynamic policyStatusRecord3 = CreateRow(tableName: "PAS.PolicyStatus", values: new
            {
                PolicyStatusKey = currentPolicy1Status,
                DataSourceInstanceId = (int)dataSourceInstance,
                PolicyStatus = currentPolicy1Status,
                RefPolicyStatusId = (int)currentPolicy1Status
            });
        }
        if(currentPolicy2Status != null && currentPolicy2Status != expiringPolicy1Status && currentPolicy2Status != expiringPolicy2Status && currentPolicy2Status != currentPolicy1Status)
        {
            dynamic policyStatusRecord4 = CreateRow(tableName: "PAS.PolicyStatus", values: new
            {
                PolicyStatusKey = currentPolicy2Status,
                DataSourceInstanceId = (int)dataSourceInstance,
                PolicyStatus = currentPolicy2Status,
                RefPolicyStatusId = (int)currentPolicy2Status
            });
        }

        PopulateRefPlacementStatus();

        dynamic teamRecord = CreateRow(tableName: "ref.Team", values: new
        {
            TeamId = placementTeamId.HasValue ? placementTeamId.Value : 3000262,
            TeamName = "Team2",
            TeamKey = "1000",
            OrgLevel = 1,
            FullPath = teamFullPath
        });

        var specifiedPlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, placementStatus);

        // Create an MTA when one is needed.
        dynamic placementMTARecord = null;
        if(isMTA.HasValue && isMTA.Value)
        {
            placementMTARecord = CreateRow(tableName: "dbo.Placement", values: new
            {
                PlacementStatusId = specifiedPlacementStatusId,
                IsDeleted = 0,
                SuppressPolicyLink = 0,
                PolicyTriggeredUpdate = 0,
                InceptionDate = DateTime.UtcNow.AddDays(-50),
                LastUpdatedUser = "FMAImport",
                PlacementSystemId = 1,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                ServicingPlatformId = (int)DataSourceInstance.BrokingPlatform
            });
        }

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementId = 1234,
            PlacementStatusId = specifiedPlacementStatusId,
            IsDeleted = 0,
            SuppressPolicyLink = 0,
            PolicyTriggeredUpdate = 0,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ServicingPlatformId = (int)dataSourceInstance,
            PlacementSystemId = 123456,
            InceptionDate = placementInceptionDateDaysRelativeToToday == null ? null : (object)DateTime.UtcNow.AddDays(placementInceptionDateDaysRelativeToToday.Value),
            MTACreatedFromPlacementId = placementMTARecord?.PlacementId,
        });
        dynamic placementTeamsRecord = CreateRow(tableName: "dbo.PlacementTeams", values: new
        {
            PlacementId = placementRecord.PlacementId,
            TeamID = placementTeamId == null ? null : (object)placementTeamId.Value,
            DataSourceInstanceID = (int)dataSourceInstance
        });

        if(expiringPolicy1Status != null)
        {
            dynamic policy1ExpiringRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)expiringPolicy1Status.Value,
                PolicyStatusKey = expiringPolicy1Status.Value,
                DataSourceInstanceId = (int)dataSourceInstance
            });
            dynamic placementPolicyExpiring1Record = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = policy1ExpiringRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Expiring,
                DataSourceInstanceID = (int)dataSourceInstance
            });
        }

        if(expiringPolicy2Status != null)
        {
            dynamic policy2ExpiringRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)expiringPolicy2Status.Value,
                PolicyStatusKey = expiringPolicy2Status.Value,
                DataSourceInstanceId = (int)dataSourceInstance
            });
            dynamic placementPolicyExpiring2Record = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = policy2ExpiringRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Expiring,
                DataSourceInstanceID = (int)dataSourceInstance
            });
        }

        if(currentPolicy1Status != null)
        {
            dynamic policy1CurrentRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)currentPolicy1Status.Value,
                PolicyStatusKey = currentPolicy1Status.Value,
                DataSourceInstanceId = (int)dataSourceInstance
            });
            dynamic placementPolicyCurrent1Record = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = policy1CurrentRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceID = (int)dataSourceInstance
            });
        }

        if(currentPolicy2Status != null)
        {
            dynamic policy2CurrentRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)currentPolicy2Status.Value,
                PolicyStatusKey = currentPolicy2Status.Value,
                DataSourceInstanceId = (int)dataSourceInstance
            });
            dynamic placementPolicyCurrent2Record = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = policy2CurrentRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceID = (int)dataSourceInstance
            });
        }

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.UpdatePlacementDatesAndStatus");

        dynamic row = GetResultRow(tableName: "dbo.Placement", whereClause: $"PlacementId = {placementRecord.PlacementId}");
        Assert.NotNull(row);
        if(expectedPlacementStatus.HasValue)
        {
            // If expectedPlacementStatus is not null we are expecting a difference
            var specifiedExpectedPlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, expectedPlacementStatus.Value);
            Assert.Equal(expected: (int)specifiedExpectedPlacementStatusId, actual: row.PlacementStatusId);
            Assert.Equal(expected: "PACTImport", actual: row.LastUpdatedUser);
            Assert.Equal(expected: true, actual: row.PolicyTriggeredUpdate);
            Assert.True(condition: row.LastUpdatedUTCDate > DateTime.UtcNow.AddMinutes(-1));
        }
        else
        {
            // No change expected.
            Assert.True(condition: (int)specifiedPlacementStatusId == row.PlacementStatusId, userMessage: $"The placement status was not expected to change. Was {specifiedPlacementStatusId} is now {row.PlacementStatusId}.");
        }
    }

    // Germany (ASYS) don't want date updates for current policies
    // Portugal (segElevia - Portugal) don't want date updates for current policies
    // Spain (segElevia - Spain) don't want date updates for current policies
    // The Netherlands (eGlobal) don't want date updates for current policies
    // Italy don't want date updates for current policies
    // Norway (BizCore) don't want date updates for current policies
    // Sweden (BizCore) don't want date updates for current policies
    // Finland (BizCore) don't want date updates for current policies

    [Theory]
    // Most things will take the current policy inception date if available.
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > Construction", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 1, null, "CRB > GB > FINEX", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > P&C", null, null, null, null, 90, null, 90)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > P&C", 89, null, null, null, null, null, 90)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > FINEX", 89, null, 89, null, null, null, 90)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > P&C", 89, null, 88, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > Financial Solutions", 89, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > Financial Solutions", 89, null, null, null, 70, 70, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > P&C", 89, null, null, null, 70, 71, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.Cancelled, 0, null, "CRB > GB > Financial Solutions", 89, null, null, null, 70, null, null)]
    // Global FAC NA is a special case for Eclipse
    // Test using the team name (which I've made up for this) rather than having to add another field or two.
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > NA > FAC", 90, null, null, null, null, null, 90)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > NA > FAC", null, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > NA > FAC", 90, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > NA > FAC", 90, null, 90, null, 70, null, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > NA > FAC", 90, null, null, null, 70, 70, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > NA > FAC", 90, null, null, null, 70, 71, null)]
    // These sources use the policy expiry date as the placement inception date
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Austria", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Austria", 90, null, null, null, null, null, 90)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Austria", 90, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Austria", null, null, null, null, 80, null, 80)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.Cancelled, 0, null, "CRB > Austria", null, null, null, null, 80, null, null)]
    // Germany (ASYS) don't want date updates
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Germany > Real Estate", null, null, null, null, 80, null, null)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Germany > Financial Lines/ FinEx", null, null, null, null, 80, null, null)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Germany > Liability", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Germany > Financial Lines/ FinEx", 90, null, null, null, null, null, null)]
    // Portugal (segElevia - Portugal) don't want date updates
    [InlineData(DataSourceInstance.segEleviaPortugal, PlacementStatus.InProgress, 0, null, "CRB > Portugal > FINEX", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.segEleviaPortugal, PlacementStatus.InProgress, 0, null, "CRB > Portugal > Acidentes de Trabalho", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.segEleviaPortugal, PlacementStatus.InProgress, 0, null, "CRB > Portugal > FINEX > FINEX - Sul", null, null, null, null, 80, null, null)]
    // Spain (segElevia - Spain) don't want date updates
    [InlineData(DataSourceInstance.VisualSegSpain, PlacementStatus.InProgress, 0, null, "CRB > Spain > P&C", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.VisualSegSpain, PlacementStatus.InProgress, 0, null, "CRB > Spain > P&C", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.VisualSegSpain, PlacementStatus.InProgress, 0, null, "CRB > Spain > Surety > Surety  Internacional", null, null, null, null, 80, null, null)]
    // The Netherlands (eGlobal) don't want date updates
    [InlineData(DataSourceInstance.eGlobalNetherlands, PlacementStatus.InProgress, 0, null, "CRB > The Netherlands > CRM Opportunities", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.eGlobalNetherlands, PlacementStatus.InProgress, 0, null, "CRB > The Netherlands > Mergers and Acquisitions", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.eGlobalNetherlands, PlacementStatus.InProgress, 0, null, "CRB > The Netherlands > Marine", null, null, null, null, 80, null, null)]
    // Italy don't want date updates
    [InlineData(DataSourceInstance.WIBS, PlacementStatus.InProgress, 0, null, "CRB > Italy > Facultative", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.WIBS, PlacementStatus.InProgress, 0, null, "CRB > Italy > Construction", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.WIBS, PlacementStatus.InProgress, 0, null, "CRB > Italy > Construction", null, null, null, null, 80, null, null)]
    // France don't have an updated policy feed so ignoring the policy data
    [InlineData(DataSourceInstance.GrasSavoyeEGS, PlacementStatus.InProgress, 0, null, "CRB > France > Facultative", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.GrasSavoyeEGS, PlacementStatus.InProgress, 0, null, "CRB > France > Construction", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.GrasSavoyeEGS, PlacementStatus.InProgress, 0, null, "CRB > France > Construction", null, null, null, null, 80, null, null)]
    // Norway (BizCore) don't want date updates
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Norway > Marine Cargo", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Norway > Marine Cargo", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Norway > Marine Cargo", null, null, null, null, 80, null, null)]
    // Sweden (BizCore) don't want date updates
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Sweden > Liability", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Sweden > Liability", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Sweden > Liability", null, null, null, null, 80, null, null)]
    // Finland (BizCore) don't want date updates
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Finland > Combined", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Finland > Combined", 90, null, null, null, 70, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Finland > Combined", null, null, null, null, 80, null, null)]
    // Denmark haven't asked for it not to happen
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Denmark > Combined", 90, null, null, null, null, null, 91)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Denmark > Combined", 90, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Denmark > Combined", null, null, null, null, 80, null, 80)]
    // US
    [InlineData(DataSourceInstance.EpicUS, PlacementStatus.InProgress, 0, null, "North America > Life Sciences", 90, null, null, null, null, null, 90)]
    [InlineData(DataSourceInstance.EpicUS, PlacementStatus.InProgress, 0, null, "North America > FINEX > FINEX Cyber", null, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.EpicUS, PlacementStatus.InProgress, 0, null, "North America > Marine", 90, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.EpicCanada, PlacementStatus.InProgress, 0, null, "CRB > Canada > Surety", 90, null, null, null, null, null, 90)]
    [InlineData(DataSourceInstance.EpicCanada, PlacementStatus.InProgress, 0, null, "CRB > Canada > Finex Cyber", null, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.EpicCanada, PlacementStatus.InProgress, 0, null, "CRB > Canada > Marine", 90, null, null, null, 70, null, 70)]
    // Based on Renewal Date for other eGlobals
    [InlineData(DataSourceInstance.eGlobalSouthAfrica, PlacementStatus.InProgress, 0, null, "CRB > South Africa > Property", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.eGlobalSouthAfrica, PlacementStatus.InProgress, 0, null, "CRB > South Africa > Casualty", null, 50, null, null, null, null, 50)]
    [InlineData(DataSourceInstance.eGlobalSouthAfrica, PlacementStatus.InProgress, 0, null, "CRB > South Africa > Construction", 60, 50, null, null, null, null, 50)]
    [InlineData(DataSourceInstance.eGlobalSouthAfrica, PlacementStatus.InProgress, 0, null, "CRB > South Africa > FINEX", 60, 50, null, null, 40, null, 40)]
    [InlineData(DataSourceInstance.eGlobalHK, PlacementStatus.InProgress, 0, null, "CRB > Hong Kong > Construction", null, 50, null, null, null, null, 50)]
    [InlineData(DataSourceInstance.eGlobalHK, PlacementStatus.InProgress, 0, null, "CRB > Hong Kong > Network", 60, 50, null, null, null, null, 50)]
    [InlineData(DataSourceInstance.eGlobalHK, PlacementStatus.InProgress, 0, null, "CRB > Hong Kong > Macau", 60, 50, null, null, 40, null, 40)]
    // Except China which takes the placement inception date (ie No change)
    [InlineData(DataSourceInstance.eGlobalChina, PlacementStatus.InProgress, 0, null, "CRB > China > Affinity Auto", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.eGlobalChina, PlacementStatus.InProgress, 0, null, "CRB > China > Finex", null, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.eGlobalChina, PlacementStatus.InProgress, 0, null, "CRB > China > Marine", 99, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.eGlobalChina, PlacementStatus.InProgress, 0, null, "CRB > China > Power", 99, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.eGlobalChina, PlacementStatus.Cancelled, 0, null, "CRB > China > Food & Beverage", 99, null, null, null, 70, null, null)]
    // Brazil (COL) doesn't change
    [InlineData(DataSourceInstance.BrazilCOL, PlacementStatus.InProgress, 0, null, "CRB > Brazil > AERONÁUTICO", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BrazilCOL, PlacementStatus.InProgress, 0, null, "CRB > Brazil > MARÍTIMO", 90, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BrazilCOL, PlacementStatus.InProgress, 0, null, "CRB > Brazil > PATRIMONIAL", null, null, null, null, 90, null, null)]
    [InlineData(DataSourceInstance.BrazilCOL, PlacementStatus.Cancelled, 0, null, "CRB > Brazil > CRÉDITO", null, null, null, null, 90, null, null)]
    // Inspace also uses the Placement InceptionDate for expiring policies
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > Inspace", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > Inspace", null, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > Inspace", 99, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > Inspace", 99, null, 99, null, 70, null, 70)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > Inspace", 99, null, 99, null, 70, 72, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > Inspace", 99, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.Cancelled, 0, 3000261, "CRB > GB > Inspace", 99, null, null, null, 70, null, null)]
    // If the Expiry date is too far into the future it uses the Inception date
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "GB > Financial Solutions > Aviation", 1000 * 365, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "GB > Financial Solutions > Aviation", 1000 * 365, null, null, null, 70, null, 70)]
    [InlineData(DataSourceInstance.GrasSavoyeEGS, PlacementStatus.InProgress, 0, null, "CRB > France > Financial Solutions", 1000 * 365, null, null, null, 70, null, null)]
    public void InceptionDatesTest(
        DataSourceInstance servicingPlatform,
        PlacementStatus placementStatus,
        int placementInceptionDateRelativeToNow,
        int? placementTeamId,
        string? teamFullPath,
        int? expiringPolicy1ExpiryDateRelativeToNow,
        int? expiringPolicy1RenewalDateRelativeToNow,
        int? expiringPolicy2ExpiryDateRelativeToNow,
        int? expiringPolicy2RenewalDateRelativeToNow,
        int? currentPolicy1InceptionDateRelativeToNow,
        int? currentPolicy2InceptionDateRelativeToNow,
        int? expectedPlacementInceptionDateRelativeToNow /* Null if no change expected */
        )
    {
        CreatedReferenceRecords(servicingPlatform);

        dynamic teamRecord = CreateTeamRecords(servicingPlatform, placementTeamId, teamFullPath);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementStatusId = (int)placementStatus,
            PlacementSystemid = 1,
            IsDeleted = 0,
            SuppressPolicyLink = 0,
            PolicyTriggeredUpdate = 0,
            InceptionDate = DateTime.UtcNow.Date.AddDays(placementInceptionDateRelativeToNow),
            ExpiryDate = DateTime.UtcNow.Date.AddDays(365 + placementInceptionDateRelativeToNow),
            ServicingPlatformId = (int)servicingPlatform,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            LastUpdatedUser = "FMAImport",
            BrokingRegionId = (int?)(teamFullPath == "CRB > NA > FAC" ? 1 : null),
            BrokingSubSegmentId = (int?)(teamFullPath == "CRB > NA > FAC" ? 24 : null),
            LastUpdatedUTCDate = DateTime.UtcNow.AddDays(-1)
        });

        var savedPlacementInceptionDate = placementRecord.InceptionDate;

        dynamic placementTeamsRecord = CreateRow(tableName: "dbo.PlacementTeams", values: new
        {
            PlacementId = placementRecord.PlacementId,
            TeamID = teamRecord.TeamId,
            DataSourceInstanceID = (int)servicingPlatform,
        });

        if(expiringPolicy1ExpiryDateRelativeToNow.HasValue || expiringPolicy1RenewalDateRelativeToNow.HasValue)
        {
            var expiryDate = expiringPolicy1ExpiryDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy1ExpiryDateRelativeToNow.Value) : DateTime.UtcNow.Date;
            dynamic expiringPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = expiryDate.AddDays(-365),
                ExpiryDate = expiryDate,
                DataSourceInstanceId = (int)servicingPlatform,
                RenewalDate = (DateTime?)(expiringPolicy1RenewalDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy1RenewalDateRelativeToNow.Value) : DateTime.UtcNow.Date)
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = expiringPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Expiring,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        if(expiringPolicy2ExpiryDateRelativeToNow.HasValue || expiringPolicy2RenewalDateRelativeToNow.HasValue)
        {
            var expiryDate = expiringPolicy2ExpiryDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy2ExpiryDateRelativeToNow.Value) : DateTime.UtcNow.Date;
            dynamic expiringPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = expiryDate.AddDays(-365),
                ExpiryDate = expiryDate,
                DataSourceInstanceId = (int)servicingPlatform,
                RenewalDate = (DateTime?)(expiringPolicy2RenewalDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy2RenewalDateRelativeToNow.Value) : DateTime.UtcNow.Date)
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = expiringPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Expiring,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        if(currentPolicy1InceptionDateRelativeToNow.HasValue)
        {
            dynamic currentPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = DateTime.UtcNow.Date.AddDays(currentPolicy1InceptionDateRelativeToNow.Value),
                ExpiryDate = DateTime.UtcNow.Date.AddDays(365 + currentPolicy1InceptionDateRelativeToNow.Value),
                DataSourceInstanceId = (int)servicingPlatform,
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = currentPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        if(currentPolicy2InceptionDateRelativeToNow.HasValue)
        {
            dynamic currentPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = DateTime.UtcNow.Date.AddDays(currentPolicy2InceptionDateRelativeToNow.Value),
                ExpiryDate = DateTime.UtcNow.Date.AddDays(365 + currentPolicy2InceptionDateRelativeToNow.Value),
                DataSourceInstanceId = (int)servicingPlatform,
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = currentPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        dynamic result1 = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.UpdatePlacementDatesAndStatus");

        dynamic row = GetResultRow(tableName: "dbo.Placement");
        Assert.NotNull(row);
        Assert.Equal(expected: (int)placementStatus, actual: row.PlacementStatusId);
        if(expectedPlacementInceptionDateRelativeToNow.HasValue)
        {
            Assert.Equal(expected: DateTime.UtcNow.Date.AddDays(expectedPlacementInceptionDateRelativeToNow.Value), actual: row.InceptionDate);
            Assert.True(row.LastUpdatedUTCDate > DateTime.UtcNow.AddMinutes(-1));
            Assert.Equal(expected: "PACTImport", actual: row.LastUpdatedUser);
            Assert.Equal(expected: true, actual: row.PolicyTriggeredUpdate);
        }
        else
        {
            // Date not supposed to have changed.
            // Using assert true as the message can be clearer.
            Assert.True(condition: savedPlacementInceptionDate == row.InceptionDate, userMessage: $"The Inception Date wasn't expected to change. Was '{savedPlacementInceptionDate}' is now '{row.InceptionDate}'.");
        }
    }

    /// <summary>
    /// Here we check the setting of the ExpiryDate.
    /// </summary>
    /// <param name="servicingPlatform"></param>
    /// <param name="placementStatus"></param>
    /// <param name="placementExpiryDateRelativeToNow"></param>
    /// <param name="placementTeamId"></param>
    /// <param name="teamFullPath"></param>
    /// <param name="expiringPolicy1InceptionDateRelativeToNow"></param>
    /// <param name="expiringPolicy1ExpiryDateRelativeToNow"></param>
    /// <param name="expiringPolicy2InceptionDateRelativeToNow"></param>
    /// <param name="expiringPolicy2ExpiryDateRelativeToNow"></param>
    /// <param name="currentPolicy1ExpiryDateRelativeToNow"></param>
    /// <param name="currentPolicy2ExpiryDateRelativeToNow"></param>
    /// <param name="expectedPlacementExpiryDateRelativeToNow"></param>
    [Theory]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", null, null, null, null, 100, null, 100)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.Cancelled, 0, null, "CRB > GB > NONMARINE", null, null, null, null, 100, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", null, null, null, null, 100, 100, 100)]
    // If the two current policies are different then we can't set the date.
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", null, null, null, null, 100, 105, null)]
    // Some of these tests may become fragile around a leap year. I'm working in days but we actually add on years.
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -500, -200, null, null, null, null, 100)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -500, -200, -500, -200, null, null, 100)]
    // This is an edge case. What if the current policies are inconsistent. Do we use the expiring policies? No we don't.
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -500, -200, null, null, 100, 105, null)]
    /* If the two expiring policies are different we can't pick a date */
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -500, -200, -500, -205, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -400, -200, null, null, null, null, 0)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.Cancelled, 0, null, "CRB > GB > NONMARINE", -400, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -200 - 365, -200, null, null, null, null, -200 + 365)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -200 - (2 * 365), -200, null, null, null, null, -200 + (365 * 2))]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -500, -200, null, null, 100, null, 100)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -500, -200, null, null, 100, 100, 100)]
    // Inspace also uses the Placement ExpiryDate for expiring policies
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > INSPACE", null, null, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > INSPACE", -300, -1, null, null, null, null, null)]
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, 3000261, "CRB > GB > INSPACE", null, null, null, null, -1, null, -1)]
    // Germany (ASYS) don't want date updates for current policies
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Germany > Real Estate", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.ASYSGermanyAndAustria, PlacementStatus.InProgress, 0, null, "CRB > Germany > Real Estate", null, null, null, null, 100, null, null)]
    // Portugal (segElevia - Portugal) don't want date updates for current policies
    [InlineData(DataSourceInstance.segEleviaPortugal, PlacementStatus.InProgress, 0, null, "CRB > Portugal > FINEX", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.segEleviaPortugal, PlacementStatus.InProgress, 0, null, "CRB > Portugal > FINEX", null, null, null, null, 100, null, null)]
    // Spain (segElevia - Spain) don't want date updates for current policies
    [InlineData(DataSourceInstance.VisualSegSpain, PlacementStatus.InProgress, 0, null, "CRB > Spain > Finex > Ciber", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.VisualSegSpain, PlacementStatus.InProgress, 0, null, "CRB > Spain > Finex > Crime", null, null, null, null, 100, null, null)]
    // The Netherlands (eGlobal) don't want date updates for current policies
    [InlineData(DataSourceInstance.eGlobalNetherlands, PlacementStatus.InProgress, 0, null, "CRB > The Netherlands > Marine", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.eGlobalNetherlands, PlacementStatus.InProgress, 0, null, "CRB > The Netherlands > Marine", null, null, null, null, 100, null, null)]
    // Italy don't want date updates for current policies
    [InlineData(DataSourceInstance.WIBS, PlacementStatus.InProgress, 0, null, "CRB > Italy > Fine Art", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.WIBS, PlacementStatus.InProgress, 0, null, "CRB > Italy > Fine Art", null, null, null, null, 100, null, null)]
    // France have an out of date policy feed so ignoring data from policy
    [InlineData(DataSourceInstance.GrasSavoyeEGS, PlacementStatus.InProgress, 0, null, "CRB > France > Fine Art", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.GrasSavoyeEGS, PlacementStatus.InProgress, 0, null, "CRB > France > Fine Art", null, null, null, null, 100, null, null)]
    // Norway (BizCore) don't want date updates for current policies
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Norway > Marine Offshore & Energy", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Norway > Marine Offshore & Energy", null, null, null, null, 100, null, null)]
    // Sweden (BizCore) don't want date updates for current policies
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Sweden > Property", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Sweden > Property", null, null, null, null, 100, null, null)]
    // Finland (BizCore) don't want date updates for current policies
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Finland > Motor", -500, -200, null, null, null, null, null)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Finland > Motor", null, null, null, null, 100, null, null)]
    // Denmark (BizCore) haven't said
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Denmark > Combined", -500, -200, null, null, null, null, 100)]
    [InlineData(DataSourceInstance.BizCore, PlacementStatus.InProgress, 0, null, "CRB > Denmark > Combined", null, null, null, null, 100, null, 100)]
    // Silly date.
    [InlineData(DataSourceInstance.Eclipse, PlacementStatus.InProgress, 0, null, "CRB > GB > NONMARINE", -300, 365 * 1000, null, null, null, null, null)]
    public void ExpiryDatesTest(
    DataSourceInstance servicingPlatform,
    PlacementStatus placementStatus,
    int placementExpiryDateRelativeToNow,
    int? placementTeamId,
    string? teamFullPath,
    int? expiringPolicy1InceptionDateRelativeToNow,
    int? expiringPolicy1ExpiryDateRelativeToNow,
    int? expiringPolicy2InceptionDateRelativeToNow,
    int? expiringPolicy2ExpiryDateRelativeToNow,
    int? currentPolicy1ExpiryDateRelativeToNow,
    int? currentPolicy2ExpiryDateRelativeToNow,
    int? expectedPlacementExpiryDateRelativeToNow /* Null if no change expected */
    )
    {
        CreatedReferenceRecords(servicingPlatform);

        dynamic teamRecord = CreateTeamRecords(servicingPlatform, placementTeamId, teamFullPath);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementStatusId = (int)placementStatus,
            PlacementSystemid = 1,
            IsDeleted = 0,
            SuppressPolicyLink = 0,
            PolicyTriggeredUpdate = 0,
            InceptionDate = DateTime.UtcNow.Date.AddDays(placementExpiryDateRelativeToNow - 365),
            ExpiryDate = DateTime.UtcNow.Date.AddDays(placementExpiryDateRelativeToNow),
            ServicingPlatformId = (int)servicingPlatform,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            LastUpdatedUser = "FMAImport",
            LastUpdatedUTCDate = DateTime.UtcNow.AddDays(-1)
        });

        var savedPlacementExpiryDate = placementRecord.ExpiryDate;

        dynamic placementTeamsRecord = CreateRow(tableName: "dbo.PlacementTeams", values: new
        {
            PlacementId = placementRecord.PlacementId,
            TeamID = teamRecord.TeamId,
            DataSourceInstanceID = (int)servicingPlatform,
        });

        if(expiringPolicy1ExpiryDateRelativeToNow.HasValue || expiringPolicy1InceptionDateRelativeToNow.HasValue)
        {
            var inceptionDate = expiringPolicy1InceptionDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy1InceptionDateRelativeToNow.Value) : DateTime.UtcNow.Date.AddDays(-365);
            var expiryDate = expiringPolicy1ExpiryDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy1ExpiryDateRelativeToNow.Value) : DateTime.UtcNow.Date;
            dynamic expiringPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = inceptionDate,
                ExpiryDate = expiryDate,
                DataSourceInstanceId = (int)servicingPlatform,
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = expiringPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Expiring,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        // If we need go and create an additional expiring policy to see how it behaves
        if(expiringPolicy2ExpiryDateRelativeToNow.HasValue || expiringPolicy2InceptionDateRelativeToNow.HasValue)
        {
            var inceptionDate = expiringPolicy2InceptionDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy2InceptionDateRelativeToNow.Value) : DateTime.UtcNow.Date.AddDays(-365);
            var expiryDate = expiringPolicy2ExpiryDateRelativeToNow.HasValue ? DateTime.UtcNow.Date.AddDays(expiringPolicy2ExpiryDateRelativeToNow.Value) : DateTime.UtcNow.Date;
            dynamic expiringPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = inceptionDate,
                ExpiryDate = expiryDate,
                DataSourceInstanceId = (int)servicingPlatform,
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = expiringPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Expiring,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        // If we need go and create an additional current policy to see how it behaves
        if(currentPolicy1ExpiryDateRelativeToNow.HasValue)
        {
            dynamic currentPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = DateTime.UtcNow.Date.AddDays(currentPolicy1ExpiryDateRelativeToNow.Value - 365),
                ExpiryDate = DateTime.UtcNow.Date.AddDays(currentPolicy1ExpiryDateRelativeToNow.Value),
                DataSourceInstanceId = (int)servicingPlatform,
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = currentPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        if(currentPolicy2ExpiryDateRelativeToNow.HasValue)
        {
            dynamic currentPolicyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RefPolicyStatusId = (int)RefPolicyStatus.Live,
                PolicyStatusKey = "102",
                InceptionDate = DateTime.UtcNow.Date.AddDays(currentPolicy2ExpiryDateRelativeToNow.Value - 365),
                ExpiryDate = DateTime.UtcNow.Date.AddDays(currentPolicy2ExpiryDateRelativeToNow.Value),
                DataSourceInstanceId = (int)servicingPlatform,
            });

            dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PlacementId = placementRecord.PlacementId,
                PolicyId = currentPolicyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceID = (int)servicingPlatform
            });
        }

        dynamic result1 = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.UpdatePlacementDatesAndStatus");

        dynamic row = GetResultRow(tableName: "dbo.Placement");
        Assert.NotNull(row);
        Assert.Equal(expected: (int)placementStatus, actual: row.PlacementStatusId);
        if(expectedPlacementExpiryDateRelativeToNow.HasValue)
        {
            Assert.Equal(expected: DateTime.UtcNow.Date.AddDays(expectedPlacementExpiryDateRelativeToNow.Value), actual: row.ExpiryDate);
            Assert.True(row.LastUpdatedUTCDate > DateTime.UtcNow.AddMinutes(-1));
            Assert.Equal(expected: "PACTImport", actual: row.LastUpdatedUser);
            Assert.Equal(expected: true, actual: row.PolicyTriggeredUpdate);
        }
        else
        {
            // Date not supposed to have changed.
            // Using assert true as the message can be clearer.
            Assert.True(condition: savedPlacementExpiryDate == row.ExpiryDate, userMessage: $"The Expiry Date wasn't expected to change. Was '{savedPlacementExpiryDate}' is now '{row.ExpiryDate}'.");
        }
    }

    [Fact]
    public void PlacementExclusionTest()
    {
        CreatedReferenceRecords(DataSourceInstance.EpicUS);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementStatusId = (int)PlacementStatus.InProgress,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ServicingPlatformId = (int)DataSourceInstance.EpicUS,
            PlacementSystemId = 123,
            IsDeleted = 0,
            InceptionDate = DateTime.UtcNow.AddDays(50),
            ExpiryDate = DateTime.UtcNow.AddDays(400),
            LastUpdatedUser = "PACTImport"

        });

        dynamic placementTeamsRecord = CreateRow(tableName: "dbo.PlacementTeams", values: new
        {
            PlacementId = placementRecord.PlacementId,
            TeamID = 3000474,
            DataSourceInstanceID = (int)DataSourceInstance.EpicUS
        });

        dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.EpicUS,
            RefPolicyStatusId = (int)RefPolicyStatus.Live,
            PolicyStatusKey = "102",
            InceptionDate = DateTime.UtcNow.AddDays(-30),
            ExpiryDate = DateTime.UtcNow.AddDays(30),
        });

        dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
        {
            PlacementId = placementRecord.PlacementId,
            PolicyId = policyRecord.PolicyId,
            PlacementPolicyRelationshipTypeId = 2,
            DataSourceInstanceID = (int)DataSourceInstance.EpicUS
        });


        dynamic result1 = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.UpdatePlacementDatesAndStatus");

        Assert.Equal(expected: 2, actual: result1.UpdatedCount);
        dynamic row = GetResultRow(tableName: "dbo.Placement");
        Assert.NotNull(row);
        Assert.Equal(expected: "PACTImport", actual: row.LastUpdatedUser);

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdatePlacementDatesExclusion", new
        {
            PlacementId = placementRecord.PlacementId
        });

        dynamic result2 = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.UpdatePlacementDatesAndStatus");
        // After Inclusion of placement in the table Updates should Ignore
        Assert.Equal(expected: 0, actual: result2.UpdatedCount);

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdatePlacementDatesExclusion", new
        {
            PlacementId = placementRecord.PlacementId,
            IsActive = 0
        });
        dynamic result3 = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.UpdatePlacementDatesAndStatus");
        // After making record inactive the updates should happen
        Assert.Equal(expected: 2, actual: result3.UpdatedCount);

    }

    #region Private

    private dynamic CreateTeamRecords(DataSourceInstance servicingPlatform, int? placementTeamId, string? teamFullPath)
    {
        // eGlobals need to be identified.
        if(servicingPlatform.ToString().StartsWith("eGlobal"))
        {
            CreateRow(tableName: "Reference.DataSourceInstance", values: new
            {
                DataSourceInstanceId = (int)servicingPlatform,
                DataSourceId = (int)DataSource.eGlobal
            });
        }

        dynamic teamRecord = CreateRow(tableName: "ref.Team", values: new
        {
            TeamId = placementTeamId.HasValue ? placementTeamId.Value : 3000262,
            TeamName = "Team2",
            TeamKey = "1000",
            OrgLevel = 1,
            FullPath = teamFullPath

        });
        return teamRecord;
    }

    private void CreatedReferenceRecords(DataSourceInstance servicingPlatform)
    {
        CreateRow(
            tableName: "PAS.RefPolicyStatus",
            values: @"RefPolicyStatusKey	RefPolicyStatusID	DataSourceInstanceId	RefPolicyStatus
                    100	100	50437	Placement
                    101	101	50437	Not Taken Up (NTU)
                    102	102	50437	Live
                    103	103	50437	Lapsed
                    104	104	50437	Cancelled
                    105	105	50437	Not applicable
                    106	106	50437	Suspended
        ");
        dynamic policyStatusRecord = CreateRow(tableName: "PAS.PolicyStatus", values: new
        {
            PolicyStatusKey = "102",
            DataSourceInstanceId = (int)servicingPlatform,
            PolicyStatus = "102",
            RefPolicyStatusId = 102
        });

        PopulateRefPlacementStatus();
    }

    #endregion

    #region Constructor
    public UpdatePlacementDatesAndStatusTests(DatabaseFixture fixture) : base(fixture)
    {
    }
    #endregion
}