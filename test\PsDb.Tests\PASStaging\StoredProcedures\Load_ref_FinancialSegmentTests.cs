﻿using PsDb.Tests.PlacementStoreHelpers;
using Xunit.Abstractions;

namespace PsDb.Tests.PASStaging.StoredProcedures;
public class Load_ref_FinancialSegmentTests : PlacementStoreIncrementalLoadProcedureTestBase
{
    protected override dynamic CreateStagingRecord(TestType testType, DateTime sourceUpdatedDate, bool isDeleted, bool changeSomething)
    {
        return new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            FinancialSegmentKey = "ABC",
            FinancialSegmentId = 1,
            FinancialSegment = changeSomething ? "Financial Segment B" : "Financial Segment A",
            FinancialSegmentCode = changeSomething ? "FS CODE B" : "FS CODE A",
            FinancialSegmentDescription = changeSomething ? "Financial Segment Description B" : "Financial Segment Description A",
            GlobalFinancialSegmentId = 1,
            Level1 = changeSomething ? "Level 1 Updated" : "Level 1 New",
            Level2 = changeSomething ? "Level 2 Updated" : "Level 2 New",
            Level3 = changeSomething ? "Level 3 Updated" : "Level 3 New",
            Level4 = changeSomething ? "Level 4 Updated" : "Level 4 New",
            Level5 = changeSomething ? "Level 5 Updated" : "Level 5 New",
            SourceKey = changeSomething ? "SRCKEYB" : "SRCKEYA",
            SourceQuery = changeSomething ? "QRYB" : "QRYA",
            ETLUpdatedDate = sourceUpdatedDate,
            IsDeleted = isDeleted
        };
    }

    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            FinancialSegmentKey = "ABC",
            FinancialSegment = "Financial Segment A",
            FinancialSegmentCode = "FS CODE A",
            FinancialSegmentDescription = "Financial Segment Description A",
            GlobalFinancialSegmentId = 1,
            Level1 = "Level 1 New",
            Level2 = "Level 2 New",
            Level3 = "Level 3 New",
            Level4 = "Level 4 New",
            Level5 = "Level 5 New",
            PASFinancialSegmentId = 1,
            SourceKey = "SRCKEYA",
            SourceQuery = "QRYA",
            ETLCreatedDate = DateTime.UtcNow.AddMinutes(-50),
            ETLUpdatedDate = DateTime.UtcNow.AddMinutes(-50),
            SourceUpdatedDate = stagingRecord.ETLUpdatedDate,
            IsDeprecated = false
        };
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: stagingRecord.DataSourceInstanceId, actual: targetResult.DataSourceInstanceId);
        Assert.Equal(expected: stagingRecord.FinancialSegmentKey, actual: targetResult.FinancialSegmentKey);
        Assert.Equal(expected: stagingRecord.FinancialSegment, actual: targetResult.FinancialSegment);
        Assert.Equal(expected: stagingRecord.FinancialSegmentCode, actual: targetResult.FinancialSegmentCode);
        Assert.Equal(expected: stagingRecord.FinancialSegmentDescription, actual: targetResult.FinancialSegmentDescription);
        Assert.Equal(expected: stagingRecord.GlobalFinancialSegmentId, actual: targetResult.GlobalFinancialSegmentId);
        Assert.Equal(expected: stagingRecord.Level1, actual: targetResult.Level1);
        Assert.Equal(expected: stagingRecord.Level2, actual: targetResult.Level2);
        Assert.Equal(expected: stagingRecord.Level3, actual: targetResult.Level3);
        Assert.Equal(expected: stagingRecord.Level4, actual: targetResult.Level4);
        Assert.Equal(expected: stagingRecord.Level5, actual: targetResult.Level5);
        Assert.Equal(expected: stagingRecord.FinancialSegmentId, actual: targetResult.PASFinancialSegmentId);
        Assert.Equal(expected: stagingRecord.SourceKey, actual: targetResult.SourceKey);
        Assert.Equal(expected: stagingRecord.SourceQuery, actual: targetResult.SourceQuery);
    }

    protected override dynamic? GetResultRowOverride()
    {
        return GetResultRow(tableName: TargetTableName, whereClause: $"FinancialSegmentKey = 'ABC' AND DataSourceInstanceId = {(int)DataSourceInstance.Eclipse}");
    }

    protected override object GetLogicalDeletionValue(dynamic row)
    {
        return row.IsDeprecated;
    }

    protected override void SetUpExtraRecords(TestType testType)
    {
        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse
        });
    }


    public Load_ref_FinancialSegmentTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture: fixture, output: output)
    {

    }
}
