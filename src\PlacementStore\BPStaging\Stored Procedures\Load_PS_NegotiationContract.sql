/*
Lineage
PS.NegotiationContract.NegotiationContractKey=BPStaging.SubmissionContainerContract.Id
PS.NegotiationContract.NegotiationId=PS.Negotiation.NegotiationId
PS.NegotiationContract.ContractId=PS.Contract.ContractId
PS.NegotiationContract.IsDeleted=BPStaging.SubmissionContainerContract.ValidTo
PS.NegotiationContract.SourceUpdatedDate=BPStaging.SubmissionContainerContract.ValidTo
PS.NegotiationContract.SourceUpdatedDate=BPStaging.SubmissionContainerContract.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_PS_NegotiationContract
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.NegotiationContract';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.NegotiationContract T
    USING (
        SELECT
            NegotiationContractKey = CAST(Id AS NVARCHAR(11))
          , NegotiationId = SC.NegotiationId
          , ContractId = C.ContractId
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                         THEN inner_select.ValidTo
                                     ELSE inner_select.ValidFrom END
          , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            Id
          , SubmissionContainerId
          , ContractId
          , ValidTo
          , ValidFrom
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.SubmissionContainerContract
    ) inner_select
             INNER JOIN PS.Negotiation SC
                 ON CONCAT('SUBC|', inner_select.SubmissionContainerId) = SC.NegotiationKey
                    AND SC.DataSourceInstanceId = 50366

             INNER JOIN PS.Contract C
                 ON C.ContractId = inner_select.ContractId
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.NegotiationContractKey = S.NegotiationContractKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 NegotiationContractKey
               , NegotiationId
               , ContractId
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
               , DataSourceInstanceId
               , SourceUpdatedDate
             )
             VALUES
                 (
                     S.NegotiationContractKey
                   , S.NegotiationId
                   , S.ContractId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                   , S.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.NegotiationId
                               , S.ContractId
                               , S.IsDeleted
                               , S.DataSourceInstanceId
                             INTERSECT
                             SELECT
                                 T.NegotiationId
                               , T.ContractId
                               , T.IsDeleted
                               , T.DataSourceInstanceId
                         )
        THEN UPDATE SET
                 T.NegotiationId = S.NegotiationId
               , T.ContractId = S.ContractId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;