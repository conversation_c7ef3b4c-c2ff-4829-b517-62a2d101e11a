/*
Lineage
ref.ProgramStructureType.ProgramStructureTypeKey=BPStaging.ProgramStructureType.Id
ref.ProgramStructureType.ProgramStructureType=BPStaging.ProgramStructureType.Text
ref.ProgramStructureType.SourceUpdatedDate=BPStaging.ProgramStructureType.ValidFrom
ref.ProgramStructureType.IsDeprecated=BPStaging.ProgramStructureType.IsDeprecated
*/
CREATE PROCEDURE BPStaging.Load_ref_ProgramStructureType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ProgramStructureType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ProgramStructureType)
BEGIN
    BEGIN TRY
        MERGE ref.ProgramStructureType T
        USING (
            SELECT TOP 100 PERCENT
                   DataSourceInstanceId = 50366
                 , ProgramStructureTypeKey = Id
                 , ProgramStructureType = Text
                 , SourceUpdatedDate = ValidFrom
                 , IsDeprecated
            FROM
                BPStaging.ProgramStructureType
            ORDER BY
                DataSourceInstanceId
              , ProgramStructureTypeKey
        ) S
        ON T.DataSourceInstanceId = S.DataSourceInstanceId
           AND T.ProgramStructureTypeKey = S.ProgramStructureTypeKey
        WHEN NOT MATCHED
            THEN INSERT (
                     DataSourceInstanceId
                   , ProgramStructureTypeKey
                   , ProgramStructureType
                   , SourceUpdatedDate
                   , IsDeprecated
                 )
                 VALUES
                     (
                         S.DataSourceInstanceId
                       , S.ProgramStructureTypeKey
                       , S.ProgramStructureType
                       , S.SourceUpdatedDate
                       , S.IsDeprecated
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.DataSourceInstanceId
      , T.ProgramStructureTypeKey
      , T.ProgramStructureType
      , T.SourceUpdatedDate
      , T.IsDeprecated
    INTERSECT
    SELECT
        S.DataSourceInstanceId
      , S.ProgramStructureTypeKey
      , S.ProgramStructureType
      , S.SourceUpdatedDate
      , S.IsDeprecated
)
            THEN UPDATE SET
                     T.ProgramStructureTypeKey = S.ProgramStructureTypeKey
                   , T.ProgramStructureType = S.ProgramStructureType
                   , T.ETLUpdatedDate = GETUTCDATE()
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.IsDeprecated = S.IsDeprecated
        WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
                                       AND T.DataSourceInstanceId = 50366
            THEN UPDATE SET
                     T.IsDeprecated = 1
                   , T.ETLUpdatedDate = GETUTCDATE()
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
