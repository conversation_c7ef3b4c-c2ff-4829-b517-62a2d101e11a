/*
Lineage
PS.MarketResponseElementAttribute.DataSourceInstanceId=PS.MarketResponse.DataSourceInstanceId
PS.MarketResponseElementAttribute.MarketResponseElementAttributeKey=dbo.ElementCache.ElementId
PS.MarketResponseElementAttribute.MarketResponseElementAttributeKey=dbo.ElementCache.ElementBranchId
PS.MarketResponseElementAttribute.MarketResponseElementAttributeKey=dbo.ElementCache.ParentElementId
PS.MarketResponseElementAttribute.MarketResponseElementAttributeKey=dbo.ElementAttributeCache.ElementAttributeId
PS.MarketResponseElementAttribute.MarketResponseElementAttributeKey=PS.ElementCompositionLinkedElement.AnchorElementId
PS.MarketResponseElementAttribute.MarketResponseElementAttributeKey=dbo.ElementTagCache.ElementTagId
PS.MarketResponseElementAttribute.MarketResponseElementKey=PS.MarketResponse.MarketResponseId
PS.MarketResponseElementAttribute.MarketResponseElementKey=dbo.ElementCache.ElementId
PS.MarketResponseElementAttribute.MarketResponseElementKey=dbo.ElementCache.ElementBranchId
PS.MarketResponseElementAttribute.MarketResponseElementKey=dbo.ElementCache.ParentElementId
PS.MarketResponseElementAttribute.MarketResponseElementKey=PS.ElementCompositionLinkedElement.AnchorElementId
PS.MarketResponseElementAttribute.ElementAttributeId=dbo.ElementAttributeCache.ElementAttributeId
PS.MarketResponseElementAttribute.ElementAttributeId=dbo.ElementTagCache.ElementTagId
PS.MarketResponseElementAttribute.ElementAttributeTypeId=dbo.ElementAttributeCache.ElementAttributeTypeId
PS.MarketResponseElementAttribute.ElementAttributeTypeId=dbo.ElementTagCache.ElementTagTypeId
PS.MarketResponseElementAttribute.ElementAttributeType=dbo.ElementAttributeType.ElementAttributeType
PS.MarketResponseElementAttribute.ElementAttributeType=ref.ElementTagType.ElementTagType
PS.MarketResponseElementAttribute.DisplayValue=dbo.ElementAttributeCache.DisplayValue
PS.MarketResponseElementAttribute.DisplayValue=ref.ElementTagType.ElementTagTypeKey
PS.MarketResponseElementAttribute.Value=dbo.ElementAttributeCache.Value
PS.MarketResponseElementAttribute.Value=ref.ElementTagType.ElementTagType
PS.MarketResponseElementAttribute.SourceUpdatedDate=BP.MarketResponseElement.ETLUpdatedDate
PS.MarketResponseElementAttribute.SourceUpdatedDate=BP.ResponseManagementElement.ETLUpdatedDate
PS.MarketResponseElementAttribute.SourceUpdatedDate=dbo.ElementCache.LastUpdatedUTCDate
PS.MarketResponseElementAttribute.SourceUpdatedDate=dbo.ElementAttributeCache.LastUpdatedUTCDate
PS.MarketResponseElementAttribute.SourceUpdatedDate=dbo.ElementTagCache.LastUpdatedUTCDate
PS.MarketResponseElementAttribute.SourceUpdatedDate=BP.ExpiringResponseElement.ETLUpdatedDate
PS.MarketResponseElementAttribute.SourceUpdatedDate=BP.AdjustmentResponseElement.ETLUpdatedDate
PS.MarketResponseElementAttribute.IsDeleted=BP.MarketResponseElement.IsDeleted
PS.MarketResponseElementAttribute.IsDeleted=BP.ResponseManagementElement.IsDeleted
PS.MarketResponseElementAttribute.IsDeleted=BP.ExpiringResponseElement.IsDeleted
PS.MarketResponseElementAttribute.IsDeleted=BP.AdjustmentResponseElement.IsDeleted
*/

CREATE PROCEDURE BPStaging.Load_PS_MarketResponseElementAttribute
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketResponseElementAttribute';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @UpdateDate DATETIME2(7) = GETUTCDATE();

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #MarketResponse;
    DROP TABLE IF EXISTS #MarketExpiringResponse;
    DROP TABLE IF EXISTS #MarketAdjustmentResponse;
    DROP TABLE IF EXISTS #MarketResponseElementAttribute;

    CREATE TABLE #MarketResponse (
        DataSourceInstanceId INT          NOT NULL
      , MarketResponseId     INT          NOT NULL
      , ElementId            INT          NOT NULL
      , ElementBranchId      INT          NOT NULL
      , SourceUpdatedDate    DATETIME2(7) NULL
      , IsDeleted            BIT          NOT NULL
      ,
      UNIQUE CLUSTERED
      (
          MarketResponseId ASC
      )
    );

    CREATE TABLE #MarketExpiringResponse (
        DataSourceInstanceId INT          NOT NULL
      , MarketResponseId     INT          NOT NULL
      , ElementId            INT          NOT NULL
      , ElementBranchId      INT          NOT NULL
      , SourceUpdatedDate    DATETIME2(7) NULL
      , IsDeleted            BIT          NOT NULL
      ,
      UNIQUE CLUSTERED
      (
          MarketResponseId ASC
      )
    );

    CREATE TABLE #MarketAdjustmentResponse (
        DataSourceInstanceId INT          NOT NULL
      , MarketResponseId     INT          NOT NULL
      , ElementId            INT          NOT NULL
      , ElementBranchId      INT          NOT NULL
      , SourceUpdatedDate    DATETIME2(7) NULL
      , IsDeleted            BIT          NOT NULL
      ,
      UNIQUE CLUSTERED
      (
          MarketResponseId ASC
      )
    );

    CREATE TABLE #MarketResponseElementAttribute (
        DataSourceInstanceId              INT           NOT NULL
      , MarketResponseElementAttributeKey NVARCHAR(100) NOT NULL
      , MarketResponseElementKey          NVARCHAR(50)  NULL
      , ElementAttributeId                INT           NULL
      , ElementAttributeTypeId            INT           NULL
      , ElementAttributeType              NVARCHAR(500) NOT NULL
      , DisplayValue                      NVARCHAR(MAX) NULL
      , Value                             NVARCHAR(MAX) NULL
      , SourceUpdatedDate                 DATETIME2(7)  NOT NULL
      , IsDeleted                         BIT           NOT NULL
    );

    INSERT INTO
        #MarketResponse
        (
            DataSourceInstanceId
          , MarketResponseId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , mr.MarketResponseId
      , rme.ElementId
      , mre.ElementBranchId
      , SourceUpdatedDate = GREATEST(mre.ETLUpdatedDate, rme.ETLUpdatedDate)
      , IsDeleted = (mre.IsDeleted | rme.IsDeleted)
    FROM
        BP.MarketResponseElement mre WITH (NOLOCK)
        INNER JOIN PS.MarketResponse mr WITH (NOLOCK)
            ON mr.DataSourceInstanceId = 50366
               AND mr.MarketResponseKey = CONCAT('MKTRES|', mre.MarketResponseId)

        INNER JOIN BP.ResponseManagementElement rme WITH (NOLOCK)
            ON rme.Id = mre.ResponseManagementElementId
    WHERE
        mre.IsDeleted = 0
        AND rme.IsDeleted = 0;

    INSERT INTO
        #MarketExpiringResponse
        (
            DataSourceInstanceId
          , MarketResponseId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , mr.MarketResponseId
      , rme.ElementId
      , mre.ElementBranchId
      , SourceUpdatedDate = GREATEST(mre.ETLUpdatedDate, rme.ETLUpdatedDate)
      , IsDeleted = (mre.IsDeleted | rme.IsDeleted)
    FROM
        BP.ExpiringResponseElement mre WITH (NOLOCK)
        INNER JOIN PS.MarketResponse mr WITH (NOLOCK)
            ON mr.MarketResponseKey = CONCAT('EXPRESP|', mre.ExpiringResponseGroupId, '|', mre.Id)
               AND mr.DataSourceInstanceId = 50366

        INNER JOIN BP.ResponseManagementElement rme WITH (NOLOCK)
            ON rme.Id = mre.ResponseManagementElementId
    WHERE
        mre.IsDeleted = 0
        AND rme.IsDeleted = 0;

    INSERT INTO
        #MarketAdjustmentResponse
        (
            DataSourceInstanceId
          , MarketResponseId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , mr.MarketResponseId
      , rme.ElementId
      , are.ElementBranchId
      , SourceUpdatedDate = GREATEST(are.ETLUpdatedDate, rme.ETLUpdatedDate)
      , IsDeleted = (are.IsDeleted | rme.IsDeleted)
    FROM
        BP.AdjustmentResponseElement are WITH (NOLOCK)
        INNER JOIN PS.MarketResponse mr WITH (NOLOCK)
            ON mr.MarketResponseKey = CONCAT('ADJRESP|', are.AdjustmentResponseGroupId, '|', are.Id)
               AND mr.DataSourceInstanceId = 50366

        INNER JOIN BP.ResponseManagementElement rme WITH (NOLOCK)
            ON rme.Id = are.ResponseManagementElementId
    WHERE
        are.IsDeleted = 0
        AND rme.IsDeleted = 0;

    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseElementAttributeKey = CONCAT(
                                                N'RESPATTR|'
                                              , ec.ElementId
                                              , N'|'
                                              , ec.ElementBranchId
                                              , N'|'
                                              , ec.ParentElementId
                                              , N'|'
                                              , eattr.ElementAttributeId
                                            )
      , MarketResponseElementKey = CONCAT(mr.MarketResponseId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , eattr.ElementAttributeId
      , eattr.ElementAttributeTypeId
      , eat.ElementAttributeType
      , eattr.DisplayValue
      , eattr.Value
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, eattr.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = mr.ElementId
               AND ec.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementAttributeCache eattr WITH (NOLOCK)
            ON eattr.ElementId = ec.ElementId
               AND eattr.ElementBranchId = ec.ElementBranchId
               AND COALESCE(NULLIF(eattr.Value, ''),NULLIF(eattr.DisplayValue, '')) IS NOT NULL

        INNER JOIN dbo.ElementAttributeType eat WITH (NOLOCK)
            ON eat.ElementAttributeTypeId = eattr.ElementAttributeTypeId
    WHERE
        mr.IsDeleted = 0;

    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    /* Market Element Composition Linked Element Attributes */
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseElementAttributeKey = CONCAT(
                                                N'RESPATTR|'
                                              , ec.ElementId
                                              , N'|'
                                              , ec.ElementBranchId
                                              , N'|'
                                              , ec.ParentElementId
                                              , N'|'
                                              , eattr.ElementAttributeId
                                              , N'|'
                                              , ecle.AnchorElementId
                                              , N'|'
                                              , ecAnchor.ElementBranchId
                                            )
      , MarketResponseElementKey = CONCAT(
                                       mr.MarketResponseId
                                     , N'|'
                                     , ec.ElementId
                                     , N'|'
                                     , ec.ElementBranchId
                                     , N'|'
                                     , ec.ParentElementId
                                     , N'|'
                                     , ecle.AnchorElementId
                                     , N'|'
                                     , ecAnchor.ElementBranchId
                                   )
      , eattr.ElementAttributeId
      , eattr.ElementAttributeTypeId
      , eat.ElementAttributeType
      , eattr.DisplayValue
      , eattr.Value
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, eattr.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketResponse mr WITH (NOLOCK)
        INNER JOIN PS.ElementCompositionLinkedElement ecle WITH (NOLOCK)
            ON mr.ElementId = ecle.RootElementId
               AND ecle.IsDeleted = 0

        INNER JOIN PS.ElementCompositionLinkedElementBranch ecleb WITH (NOLOCK)
            ON ecleb.ElementCompositionLinkedElementId = ecle.ElementCompositionLinkedElementId
               AND mr.ElementBranchId = ecleb.RootElementBranchId
               AND ecleb.IsDeleted = 0

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = ecle.LinkedRootElementId
               AND ec.ElementBranchId = ecleb.LinkedElementBranchId

        INNER JOIN dbo.ElementCache ecAnchor WITH (NOLOCK)
            ON ecAnchor.ElementId = ecle.AnchorElementId
               AND ecAnchor.ElementBranchId = ecleb.RootElementBranchId

        INNER JOIN dbo.ElementAttributeCache eattr WITH (NOLOCK)
            ON eattr.ElementId = ec.ElementId
               AND eattr.ElementBranchId = ec.ElementBranchId
               AND COALESCE(NULLIF(eattr.Value, ''),NULLIF(eattr.DisplayValue, '')) IS NOT NULL

        INNER JOIN dbo.ElementAttributeType eat WITH (NOLOCK)
            ON eat.ElementAttributeTypeId = eattr.ElementAttributeTypeId
    WHERE
        mr.IsDeleted = 0
    ORDER BY
        MarketResponseElementAttributeKey;

    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    /* Market Element Tags */
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseAttributeKey = CONCAT(
                                         N'RESPTAG|'
                                       , ec.ElementId
                                       , N'|'
                                       , ec.ElementBranchId
                                       , N'|'
                                       , ec.ParentElementId
                                       , N'|'
                                       , etag.ElementTagId
                                     )
      , MarketResponseElementKey = CONCAT(mr.MarketResponseId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , etag.ElementTagId
      , etag.ElementTagTypeId
      , tt.ElementTagType
      , tt.ElementTagTypeKey
      , tt.ElementTagType
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, etag.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketResponse mr
        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = mr.ElementId
               AND ec.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementTagCache etag WITH (NOLOCK)
            ON etag.ElementId = ec.ElementId
               AND etag.ElementBranchId = ec.ElementBranchId

        INNER JOIN ref.ElementTagType tt WITH (NOLOCK)
            ON etag.ElementTagTypeId = tt.ElementTagTypeId
    WHERE
        mr.IsDeleted = 0;

    /* Expiring Element Attributes */
    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseElementAttributeKey = CONCAT(
                                                N'EXRESPATTR|'
                                              , ec.ElementId
                                              , N'|'
                                              , ec.ElementBranchId
                                              , N'|'
                                              , ec.ParentElementId
                                              , N'|'
                                              , eattr.ElementAttributeId
                                            )
      , MarketResponseElementKey = CONCAT(mr.MarketResponseId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , eattr.ElementAttributeId
      , eattr.ElementAttributeTypeId
      , eat.ElementAttributeType
      , eattr.DisplayValue
      , eattr.Value
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, eattr.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketExpiringResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementBranch eb WITH (NOLOCK)
            ON eb.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = mr.ElementId
               AND ec.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementAttributeCache eattr WITH (NOLOCK)
            ON eattr.ElementId = ec.ElementId
               AND eattr.ElementBranchId = ec.ElementBranchId
               AND COALESCE(NULLIF(eattr.Value, ''),NULLIF(eattr.DisplayValue, '')) IS NOT NULL

        INNER JOIN dbo.ElementAttributeType eat WITH (NOLOCK)
            ON eat.ElementAttributeTypeId = eattr.ElementAttributeTypeId
    WHERE
        mr.IsDeleted = 0
    ORDER BY
        MarketResponseElementAttributeKey;

    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseElementAttributeKey = CONCAT(
                                                N'EXRESPATTR|'
                                              , ec.ElementId
                                              , N'|'
                                              , ec.ElementBranchId
                                              , N'|'
                                              , ec.ParentElementId
                                              , N'|'
                                              , eattr.ElementAttributeId
                                              , N'|'
                                              , ecle.AnchorElementId
                                              , N'|'
                                              , ecAnchor.ElementBranchId
                                            )
      , MarketResponseElementKey = CONCAT(
                                       mr.MarketResponseId
                                     , N'|'
                                     , ec.ElementId
                                     , N'|'
                                     , ec.ElementBranchId
                                     , N'|'
                                     , ec.ParentElementId
                                     , N'|'
                                     , ecle.AnchorElementId
                                     , N'|'
                                     , ecAnchor.ElementBranchId
                                   )
      , eattr.ElementAttributeId
      , eattr.ElementAttributeTypeId
      , eat.ElementAttributeType
      , eattr.DisplayValue
      , eattr.Value
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, eattr.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketExpiringResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementBranch eb WITH (NOLOCK)
            ON eb.ElementBranchId = mr.ElementBranchId

        INNER JOIN PS.ElementCompositionLinkedElement ecle WITH (NOLOCK)
            ON mr.ElementId = ecle.RootElementId
               AND ecle.IsDeleted = 0

        INNER JOIN PS.ElementCompositionLinkedElementBranch ecleb WITH (NOLOCK)
            ON ecleb.ElementCompositionLinkedElementId = ecle.ElementCompositionLinkedElementId
               AND mr.ElementBranchId = ecleb.RootElementBranchId
               AND ecleb.IsDeleted = 0

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = ecle.LinkedRootElementId
               AND ec.ElementBranchId = ecleb.LinkedElementBranchId

        INNER JOIN dbo.ElementCache ecAnchor WITH (NOLOCK)
            ON ecAnchor.ElementId = ecle.AnchorElementId
               AND ecAnchor.ElementBranchId = ecleb.RootElementBranchId

        INNER JOIN dbo.ElementAttributeCache eattr WITH (NOLOCK)
            ON eattr.ElementId = ec.ElementId
               AND eattr.ElementBranchId = ec.ElementBranchId
               AND COALESCE(NULLIF(eattr.Value, ''),NULLIF(eattr.DisplayValue, '')) IS NOT NULL

        INNER JOIN dbo.ElementAttributeType eat WITH (NOLOCK)
            ON eat.ElementAttributeTypeId = eattr.ElementAttributeTypeId
    WHERE
        mr.IsDeleted = 0
    ORDER BY
        MarketResponseElementAttributeKey;

    /* Expiring Element Tags */
    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseElementAttributeKey = CONCAT(
                                                N'EXRESPTAG|'
                                              , ec.ElementId
                                              , N'|'
                                              , ec.ElementBranchId
                                              , N'|'
                                              , ec.ParentElementId
                                              , N'|'
                                              , etag.ElementTagId
                                            )
      , MarketResponseElementKey = CONCAT(mr.MarketResponseId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , etag.ElementTagId
      , etag.ElementTagTypeId
      , tt.ElementTagType
      , tt.ElementTagTypeKey
      , tt.ElementTagType
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, etag.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketExpiringResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementBranch eb WITH (NOLOCK)
            ON eb.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = mr.ElementId
               AND ec.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementTagCache etag WITH (NOLOCK)
            ON etag.ElementId = ec.ElementId
               AND etag.ElementBranchId = ec.ElementBranchId

        INNER JOIN ref.ElementTagType tt WITH (NOLOCK)
            ON etag.ElementTagTypeId = tt.ElementTagTypeId
    WHERE
        mr.IsDeleted = 0
    ORDER BY
        MarketResponseElementAttributeKey;

    /* Adjustment Element Attributes */
    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseAttributeKey = CONCAT(
                                         N'ADJESPATTR|'
                                       , ec.ElementId
                                       , N'|'
                                       , ec.ElementBranchId
                                       , N'|'
                                       , ec.ParentElementId
                                       , N'|'
                                       , eattr.ElementAttributeId
                                     )
      , MarketResponseElementKey = CONCAT(mr.MarketResponseId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , eattr.ElementAttributeId
      , eattr.ElementAttributeTypeId
      , eat.ElementAttributeType
      , eattr.DisplayValue
      , eattr.Value
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, eattr.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketAdjustmentResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementBranch eb WITH (NOLOCK)
            ON eb.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = mr.ElementId
               AND ec.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementAttributeCache eattr WITH (NOLOCK)
            ON eattr.ElementId = ec.ElementId
               AND eattr.ElementBranchId = ec.ElementBranchId
               AND COALESCE(NULLIF(eattr.Value, ''),NULLIF(eattr.DisplayValue, '')) IS NOT NULL

        INNER JOIN dbo.ElementAttributeType eat WITH (NOLOCK)
            ON eat.ElementAttributeTypeId = eattr.ElementAttributeTypeId
    WHERE
        mr.IsDeleted = 0;

    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    /* Adjustment Element Composition Linked Element Attributes */
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseAttributeKey = CONCAT(
                                         N'ADJESPATTR|'
                                       , ec.ElementId
                                       , N'|'
                                       , ec.ElementBranchId
                                       , N'|'
                                       , ec.ParentElementId
                                       , N'|'
                                       , eattr.ElementAttributeId
                                       , N'|'
                                       , ecle.AnchorElementId
                                       , N'|'
                                       , ecAnchor.ElementBranchId
                                     )
      , MarketResponseElementKey = CONCAT(
                                       mr.MarketResponseId
                                     , N'|'
                                     , ec.ElementId
                                     , N'|'
                                     , ec.ElementBranchId
                                     , N'|'
                                     , ec.ParentElementId
                                     , N'|'
                                     , ecle.AnchorElementId
                                     , N'|'
                                     , ecAnchor.ElementBranchId
                                   )
      , eattr.ElementAttributeId
      , eattr.ElementAttributeTypeId
      , eat.ElementAttributeType
      , eattr.DisplayValue
      , eattr.Value
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, eattr.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketAdjustmentResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementBranch eb WITH (NOLOCK)
            ON eb.ElementBranchId = mr.ElementBranchId

        INNER JOIN PS.ElementCompositionLinkedElement ecle WITH (NOLOCK)
            ON mr.ElementId = ecle.RootElementId

        INNER JOIN PS.ElementCompositionLinkedElementBranch ecleb WITH (NOLOCK)
            ON ecleb.ElementCompositionLinkedElementId = ecle.ElementCompositionLinkedElementId
               AND mr.ElementBranchId = ecleb.RootElementBranchId

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = ecle.LinkedRootElementId
               AND ec.ElementBranchId = ecleb.LinkedElementBranchId

        INNER JOIN dbo.ElementCache ecAnchor WITH (NOLOCK)
            ON ecAnchor.ElementId = ecle.AnchorElementId
               AND ecAnchor.ElementBranchId = ecleb.RootElementBranchId

        INNER JOIN dbo.ElementAttributeCache eattr WITH (NOLOCK)
            ON eattr.ElementId = ec.ElementId
               AND eattr.ElementBranchId = ec.ElementBranchId
               AND COALESCE(NULLIF(eattr.Value, ''),NULLIF(eattr.DisplayValue, '')) IS NOT NULL

        INNER JOIN dbo.ElementAttributeType eat WITH (NOLOCK)
            ON eat.ElementAttributeTypeId = eattr.ElementAttributeTypeId
    WHERE
        mr.IsDeleted = 0
        AND ecle.IsDeleted = 0
        AND ecleb.IsDeleted = 0
    ORDER BY
        MarketResponseAttributeKey;

    /* Adjustment Element Tags */
    INSERT INTO
        #MarketResponseElementAttribute
        (
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        mr.DataSourceInstanceId
      , MarketResponseAttributeKey = CONCAT(
                                         N'ADJESPTAG|'
                                       , ec.ElementId
                                       , N'|'
                                       , ec.ElementBranchId
                                       , N'|'
                                       , ec.ParentElementId
                                       , N'|'
                                       , etag.ElementTagId
                                     )
      , MarketResponseElementKey = CONCAT(mr.MarketResponseId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , etag.ElementTagId
      , etag.ElementTagTypeId
      , tt.ElementTagType
      , tt.ElementTagTypeKey
      , tt.ElementTagType
      , SourceUpdatedDate = GREATEST(mr.SourceUpdatedDate, ec.LastUpdatedUTCDate, etag.LastUpdatedUTCDate)
      , mr.IsDeleted
    FROM
        #MarketAdjustmentResponse mr WITH (NOLOCK)
        INNER JOIN dbo.ElementBranch eb WITH (NOLOCK)
            ON eb.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementCache ec WITH (NOLOCK)
            ON ec.RootElementId = mr.ElementId
               AND ec.ElementBranchId = mr.ElementBranchId

        INNER JOIN dbo.ElementTagCache etag WITH (NOLOCK)
            ON etag.ElementId = ec.ElementId
               AND etag.ElementBranchId = ec.ElementBranchId

        INNER JOIN ref.ElementTagType tt WITH (NOLOCK)
            ON etag.ElementTagTypeId = tt.ElementTagTypeId
    WHERE
        mr.IsDeleted = 0
    ORDER BY
        MarketResponseAttributeKey;

    CREATE CLUSTERED INDEX PK_#MarketResponseElementAttribute ON #MarketResponseElementAttribute
      (
          MarketResponseElementAttributeKey ASC
        , DataSourceInstanceId ASC
      );

    MERGE PS.MarketResponseElementAttribute WITH (HOLDLOCK) t
    USING (
        SELECT
            DataSourceInstanceId
          , MarketResponseElementAttributeKey
          , MarketResponseElementKey
          , ElementAttributeId
          , ElementAttributeTypeId
          , ElementAttributeType
          , DisplayValue
          , Value
          , SourceUpdatedDate
          , IsDeleted
        FROM
            #MarketResponseElementAttribute WITH (NOLOCK)
    ) s
    ON t.MarketResponseElementAttributeKey = s.MarketResponseElementAttributeKey
       AND t.DataSourceInstanceId = s.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , MarketResponseElementAttributeKey
               , MarketResponseElementKey
               , ElementAttributeId
               , ElementAttributeTypeId
               , ElementAttributeType
               , DisplayValue
               , Value
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.DataSourceInstanceId
                   , s.MarketResponseElementAttributeKey
                   , s.MarketResponseElementKey
                   , s.ElementAttributeId
                   , s.ElementAttributeTypeId
                   , s.ElementAttributeType
                   , s.DisplayValue
                   , s.Value
                   , @UpdateDate
                   , @UpdateDate
                   , s.SourceUpdatedDate
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.MarketResponseElementKey
                               , s.ElementAttributeId
                               , s.ElementAttributeTypeId
                               , s.ElementAttributeType
                               , s.DisplayValue
                               , s.Value
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.MarketResponseElementKey
                               , t.ElementAttributeId
                               , t.ElementAttributeTypeId
                               , t.ElementAttributeType
                               , t.DisplayValue
                               , t.Value
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.MarketResponseElementKey = s.MarketResponseElementKey
               , t.ElementAttributeId = s.ElementAttributeId
               , t.ElementAttributeTypeId = s.ElementAttributeTypeId
               , t.ElementAttributeType = s.ElementAttributeType
               , t.DisplayValue = s.DisplayValue
               , t.Value = s.Value
               , t.ETLUpdatedDate = @UpdateDate
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.IsDeleted = s.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND t.IsDeleted = 0
        THEN UPDATE SET
                 t.IsDeleted = 1
               , t.ETLUpdatedDate = @UpdateDate
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
GO