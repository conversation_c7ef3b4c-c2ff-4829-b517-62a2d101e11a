/*
Lineage
dbo.UserScope.UserScopeId=BPStaging.UserScope.Id
dbo.UserScope.UserId=BPStaging.UserScope.UserId
dbo.UserScope.UserPrincipalName=BPStaging.UserScope.UserPrincipalName
dbo.UserScope.ScopeId=BPStaging.UserScope.ScopeId
dbo.UserScope.RoleId=BPStaging.UserScope.RoleId
*/
CREATE PROCEDURE BPStaging.LoadUserScope
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.UserScope';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.UserScope
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE dbo.UserScope T
    USING (
        SELECT
            UserScopeId = Id
          , UserId
          , UserPrincipalName
          , ScopeId
          , DataSourceInstanceId = 50366
          , RoleId
          , IsDeleted = 0
        FROM
            BPStaging.UserScope
        WHERE
            UserId IS NOT NULL
            AND ScopeId IS NOT NULL
    ) S
    ON S.UserScopeId = T.UserScopeId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 UserScopeId
               , UserId
               , UserPrincipalName
               , ScopeId
               , DataSourceInstanceId
               , RoleId
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.UserScopeId
                   , S.UserId
                   , S.UserPrincipalName
                   , S.ScopeId
                   , S.DataSourceInstanceId
                   , S.RoleId
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.UserScopeId
      , T.UserId
      , T.UserPrincipalName
      , T.ScopeId
      , T.DataSourceInstanceId
      , T.RoleId
      , T.IsDeleted
    INTERSECT
    SELECT
        S.UserScopeId
      , S.UserId
      , S.UserPrincipalName
      , S.ScopeId
      , S.DataSourceInstanceId
      , S.RoleId
      , S.IsDeleted
)
        THEN UPDATE SET
                 T.UserId = S.UserId
               , T.UserPrincipalName = S.UserPrincipalName
               , T.ScopeId = S.ScopeId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RoleId = S.RoleId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
