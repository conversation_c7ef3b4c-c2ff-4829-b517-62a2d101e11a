/*
Lineage
ref.RiskClassification.RiskClassificationId=BPStaging.RiskClassification.Id
ref.RiskClassification.RiskClassificationKey=BPStaging.RiskClassification.LabelTranslationKey
ref.RiskClassification.RiskClassification=BPStaging.RiskClassification.Text
ref.RiskClassification.SourceUpdatedDate=BPStaging.RiskClassification.ValidFrom
ref.RiskClassification.IsDeprecated=BPStaging.RiskClassification.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadRiskClassification
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.RiskClassification';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.RiskClassification T
    USING (
        SELECT
            RiskClassificationId = Id
          , DataSourceInstanceId = 50366
          , RiskClassificationKey = LabelTranslationKey
          , RiskClassification = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.RiskClassification
    ) S
    ON T.RiskClassificationId = S.RiskClassificationId
    WHEN NOT MATCHED
        THEN INSERT (
                 RiskClassificationId
               , DataSourceInstanceId
               , RiskClassificationKey
               , RiskClassification
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.RiskClassificationId
                   , S.DataSourceInstanceId
                   , S.RiskClassificationKey
                   , S.RiskClassification
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.RiskClassificationId
                               , T.DataSourceInstanceId
                               , T.RiskClassificationKey
                               , T.RiskClassification
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.RiskClassificationId
                               , S.DataSourceInstanceId
                               , S.RiskClassificationKey
                               , S.RiskClassification
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.RiskClassificationId = S.RiskClassificationId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RiskClassificationKey = S.RiskClassificationKey
               , T.RiskClassification = S.RiskClassification
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
