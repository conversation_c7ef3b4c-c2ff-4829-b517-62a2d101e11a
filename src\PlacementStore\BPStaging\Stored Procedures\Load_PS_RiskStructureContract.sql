/*
Lineage
PS.RiskStructureContract.RiskStructureContractKey=PS.Contract.ContractId
PS.RiskStructureContract.RiskStructureContractKey=PS.RiskStructure.RiskStructureId
PS.RiskStructureContract.RiskStructureId=PS.RiskStructure.RiskStructureId
PS.RiskStructureContract.ContractId=PS.Contract.ContractId
PS.RiskStructureContract.SourceUpdatedDate=PS.RiskStructure.ETLUpdatedDate
PS.RiskStructureContract.SourceUpdatedDate=PS.Contract.SourceUpdatedDate
PS.RiskStructureContract.IsDeleted=PS.RiskStructure.IsDeleted
PS.RiskStructureContract.IsDeleted=PS.Contract.IsDeleted
*/
CREATE PROCEDURE BPStaging.Load_PS_RiskStructureContract
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.RiskStructureContract';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.RiskStructureContract T
    USING (
        SELECT
            DataSourceInstanceId = 50366
          , RiskStructureContractKey = CONCAT('CON|', C.ContractId, '|RS|', RS.RiskStructureId)
          , RS.RiskStructureId
          , C.ContractId
          , SourceUpdatedDate = (
                SELECT MAX(value.v) FROM (VALUES (RS.ETLUpdatedDate), (C.SourceUpdatedDate)) value (v)
            )
          , IsDeleted = (
                SELECT MAX(value.v) FROM (VALUES (CAST(RS.IsDeleted AS INT)), (CAST(C.IsDeleted AS INT))) value (v)
            )
        FROM
            PS.Contract C
            LEFT JOIN (
                SELECT
                    CR.ContractId
                  , CR.RiskProfileId
                  , RowNo = ROW_NUMBER() OVER (PARTITION BY CR.ContractId, ISNULL(CR.RiskProfileId, 0) ORDER BY CR.ETLUpdatedDate DESC)
                FROM
                    PS.ContractRiskProfile CR
                WHERE
                    CR.IsDeleted = 0
            ) CR
                ON CR.ContractId = C.ContractId
                   AND CR.RowNo = 1

            INNER JOIN PS.RiskStructure RS
                ON RS.DataSourceInstanceId = 50366
                   AND RS.PlacementId = C.PlacementId
                   AND ISNULL(RS.RiskProfileId, 0) = ISNULL(CR.RiskProfileId, 0)
                   AND ISNULL(RS.LayerTypeId, 0) = ISNULL(C.LayerTypeId, 0)
                   AND RS.RiskStructureKey NOT LIKE 'LAYER|%'
                   AND ISNULL(RS.LimitCurrencyId, 0) = ISNULL(C.LimitCurrencyTypeId, 0)
                   AND ISNULL(RS.Limit, -1) = ISNULL(C.Limit, -1)
                   AND ISNULL(RS.AttachmentPointCurrencyId, 0) = ISNULL(C.AttachmentPointCurrencyTypeId, 0)
                   AND ISNULL(RS.AttachmentPoint, -1) = ISNULL(C.AttachmentPoint, -1)
                   AND ISNULL(RS.DeductibleCurrencyId, 0) = ISNULL(C.DeductibleCurrencyTypeId, 0)
                   AND ISNULL(RS.Deductible, -1) = ISNULL(C.Deductible, -1)
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.RiskStructureId = S.RiskStructureId
       AND T.ContractId = S.ContractId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , RiskStructureContractKey
               , RiskStructureId
               , ContractId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.RiskStructureContractKey
                   , S.RiskStructureId
                   , S.ContractId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.DataSourceInstanceId
                               , S.RiskStructureContractKey
                               , S.RiskStructureId
                               , S.ContractId
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.DataSourceInstanceId
                               , T.RiskStructureContractKey
                               , T.RiskStructureId
                               , T.ContractId
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RiskStructureContractKey = S.RiskStructureContractKey
               , T.RiskStructureId = S.RiskStructureId
               , T.ContractId = S.ContractId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

RETURN 0;