/*
Lineage
APIv1.PartyAttributesTable.PartyId=APIv1.PartyAttributes.PartyId
APIv1.PartyAttributesTable.IndustryId=APIv1.PartyAttributes.IndustryID
APIv1.PartyAttributesTable.OwnerId=APIv1.PartyAttributes.OwnerID
APIv1.PartyAttributesTable.ProgramTypeId=APIv1.PartyAttributes.ProgramTypeID
APIv1.PartyAttributesTable.TurnoverId=APIv1.PartyAttributes.TurnoverID
APIv1.PartyAttributesTable.SegmentationId=APIv1.PartyAttributes.SegmentationID
APIv1.PartyAttributesTable.BranchId=APIv1.PartyAttributes.BranchID
*/
/* 
    This needs checking.
    Why do we seem to populate a version of this but not use it to replace the view used by COL ?!!! 
*/
CREATE PROCEDURE APIv1.PopulateAPIv1PartyAttributesTable
AS
DECLARE @InsertedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'APIv1.PartyAttributesTable';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SELECT @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    TRUNCATE TABLE APIv1.PartyAttributesTable;

    INSERT INTO
        APIv1.PartyAttributesTable
        (
            PartyId
          , IndustryId
          , OwnerId
          , ProgramTypeId
          , TurnoverId
          , SegmentationId
          , BranchId
        )
    SELECT
        PartyId = PartyId
      , IndustryId = IndustryID
      , OwnerId = OwnerID
      , ProgramTypeId = ProgramTypeID
      , TurnoverId = TurnoverID
      , SegmentationId = SegmentationID
      , BranchId = BranchID
    FROM
        APIv1.PartyAttributes;

    SET @Action = N'Insert into ' + @TargetTable;

    EXEC ADF.StoredProcSetSqlLog
        @SprocName
      , @InsertedCount
      , NULL
      , NULL
      , NULL
      , @Action
      , NULL;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;
END CATCH;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = 0
  , DeletedCount = 0
  , RejectedCount = 0;

RETURN 0;
