/*
Lineage
PS.AppetiteNarrative.AppetiteNarrativeKey=BPStaging.AppetiteNarrative.Id
PS.AppetiteNarrative.NegotiationMarketKey=BPStaging.AppetiteNarrative.SubmissionContainerMarketId
PS.AppetiteNarrative.AppetiteNarrativeText=BPStaging.AppetiteNarrative.AppetiteNarrativeText
PS.AppetiteNarrative.SourceUpdatedDate=BPStaging.AppetiteNarrative.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_PS_AppetiteNarrative
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.AppetiteNarrative';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE PS.AppetiteNarrative T
    USING (
        SELECT
            NegotiationMarketKey = CONCAT('SUBCONMKT|', SubmissionContainerMarketId)
          , AppetiteNarrativeKey = CAST(Id AS NVARCHAR(50))
          , AppetiteNarrativeText
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = ValidFrom
          , IsDeleted = 0
        FROM
            BPStaging.AppetiteNarrative
    ) S
    ON T.AppetiteNarrativeKey = S.AppetiteNarrativeKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , AppetiteNarrativeKey
               , NegotiationMarketKey
               , AppetiteNarrativeText
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.AppetiteNarrativeKey
                   , S.NegotiationMarketKey
                   , S.AppetiteNarrativeText
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.AppetiteNarrativeKey
                               , T.NegotiationMarketKey
                               , T.AppetiteNarrativeText
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.AppetiteNarrativeKey
                               , S.NegotiationMarketKey
                               , S.AppetiteNarrativeText
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                         )
        THEN UPDATE SET
                 T.NegotiationMarketKey = S.NegotiationMarketKey
               , T.AppetiteNarrativeText = S.AppetiteNarrativeText
               , T.AppetiteNarrativeKey = S.AppetiteNarrativeKey
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);