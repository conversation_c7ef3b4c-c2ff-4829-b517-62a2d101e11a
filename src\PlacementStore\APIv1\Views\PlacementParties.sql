/*
Lineage
PlacementId=dbo.Placement.PlacementId
GlobalPartyRoleId=ref.PartyRole.GlobalPartyRoleId
IsPrimaryParty=dbo.PlacementPartyRole.IsPrimaryParty
IsPrimaryParty=dbo.PlacementMarket.IsPrimaryParty
PartyId=dbo.Party.PartyId
PartyKey=dbo.Party.PartyKey
PartyName=dbo.Party.PartyName
*/
CREATE VIEW APIv1.PlacementParties
AS
WITH MainCTE AS (
    SELECT
        P.PlacementId
      , PPR.PartyRoleId
      , PPR.DataSourceInstanceId
      , PRL.GlobalPartyRoleId
      , IsPrimaryParty = ISNULL(CAST(PPR.IsPrimaryParty AS INT), 0)
      , PY.PartyId
      , PY.PartyKey
      , PY.PartyName
    FROM
        dbo.Placement P
        INNER JOIN dbo.PlacementPartyRole PPR
            ON PPR.PlacementId = P.PlacementId
               AND PPR.IsDeleted = 0
               AND PPR.PartyId IS NOT NULL /* Original code excluded "Non-Parties" */

        INNER JOIN ref.PartyRole PRL
            ON PRL.PartyRoleId = PPR.PartyRoleId
               AND PRL.IsDeprecated = 0

        INNER JOIN dbo.Party PY
            ON PY.PartyId = PPR.PartyId
               AND PY.IsDeleted = 0
    WHERE
        P.IsDeleted = 0
    UNION ALL

    /* As Carriers are being sent to a different table to get the same behaviour need a union of both */
    SELECT
        P.PlacementId
      , PPR.PartyRoleId
      , PPR.DataSourceInstanceId
      , PRL.GlobalPartyRoleId
      , IsPrimaryParty = ISNULL(CAST(PPR.IsPrimaryParty AS INT), 0)
      , PY.PartyId
      , PY.PartyKey
      , PY.PartyName
    FROM
        dbo.Placement P
        INNER JOIN dbo.PlacementMarket PPR
            ON PPR.PlacementId = P.PlacementId
               AND PPR.IsDeleted = 0

        INNER JOIN ref.PartyRole PRL
            ON PRL.PartyRoleId = PPR.PartyRoleId
               AND PRL.IsDeprecated = 0

        INNER JOIN dbo.Party PY
            ON PY.PartyId = PPR.PartyId
               AND PY.IsDeleted = 0
    WHERE
        P.IsDeleted = 0
)
SELECT
    PlacementId
  , GlobalPartyRoleId
  , IsPrimaryParty = CAST(MAX(IsPrimaryParty) AS BIT)
  , PartyId
  , PartyKey
  , PartyName
FROM (
    SELECT
        PlacementId
      , m.GlobalPartyRoleId
      , IsPrimaryParty
      , PartyId
      , PartyKey
      , PartyName
    FROM
        MainCTE m
    UNION
    SELECT
        m.PlacementId
      , GlobalPartyRoleId = pseudoinsured.BPGlobalPartyRoleId
      , m.IsPrimaryParty
      , m.PartyId
      , m.PartyKey
      , m.PartyName
    FROM
        MainCTE m
        JOIN (
            SELECT
                PR.PartyRoleId
              , BPGlobalPartyRoleId = 101
            FROM
                ref.PartyRole PR
            WHERE
                PR.GlobalPartyRoleId = 100
        ) pseudoinsured
            ON pseudoinsured.PartyRoleId = m.PartyRoleId
    WHERE
        NOT EXISTS (
        SELECT * FROM MainCTE x WHERE x.PlacementId = m.PlacementId AND x.GlobalPartyRoleId = 101
    )/* Only if it doesn't already have insureds. */
) subsel
GROUP BY
    PlacementId
  , GlobalPartyRoleId
  , PartyId
  , PartyKey
  , PartyName;