/*
Lineage
ref.EligibilityRule.EligibilityRuleId=BPStaging.EligibilityRule.Id
ref.EligibilityRule.EligibilityRuleKey=BPStaging.EligibilityRule.LabelTranslationKey
ref.EligibilityRule.EligibilityRule=BPStaging.EligibilityRule.LabelTranslationText
ref.EligibilityRule.SourceUpdatedDate=BPStaging.EligibilityRule.ValidFrom
ref.EligibilityRule.IsDeprecated=BPStaging.EligibilityRule.IsDeprecated
*/

CREATE PROCEDURE BPStaging.Load_ref_EligibilityRule
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.EligibilityRule';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.EligibilityRule t
    USING (
        SELECT
            EligibilityRuleId = Id
          , EligibilityRuleKey = LabelTranslationKey
          , EligibilityRule = LabelTranslationText
          , DataSourceInstanceId = 50366
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.EligibilityRule
    ) s
    ON t.EligibilityRuleId = s.EligibilityRuleId
    WHEN NOT MATCHED
        THEN INSERT (
                 EligibilityRuleId
               , DataSourceInstanceId
               , EligibilityRuleKey
               , EligibilityRule
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     s.EligibilityRuleId
                   , s.DataSourceInstanceId
                   , s.EligibilityRuleKey
                   , s.EligibilityRule
                   , s.SourceUpdatedDate
                   , s.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 t.EligibilityRuleId
                               , t.DataSourceInstanceId
                               , t.EligibilityRuleKey
                               , t.EligibilityRule
                               , t.SourceUpdatedDate
                               , t.IsDeprecated
                             INTERSECT
                             SELECT
                                 s.EligibilityRuleId
                               , s.DataSourceInstanceId
                               , s.EligibilityRuleKey
                               , s.EligibilityRule
                               , s.SourceUpdatedDate
                               , s.IsDeprecated
                         )
        THEN UPDATE SET
                 t.DataSourceInstanceId = s.DataSourceInstanceId
               , t.EligibilityRuleKey = s.EligibilityRuleKey
               , t.EligibilityRule = s.EligibilityRule
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.IsDeprecated = s.IsDeprecated
               , t.ETLUpdatedDate = GETUTCDATE()
    WHEN NOT MATCHED BY SOURCE AND t.IsDeprecated = 0
        THEN UPDATE SET
                 t.IsDeprecated = 1
               , t.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);