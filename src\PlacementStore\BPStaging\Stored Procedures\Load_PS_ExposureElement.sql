/*
Lineage
PS.ExposureElement.DataSourceInstanceId=PS.PlacementExposureGroup.DataSourceInstanceId
PS.ExposureElement.ExposureElementKey=PS.PlacementExposureGroup.PlacementExposureGroupId
PS.ExposureElement.ExposureElementKey=dbo.ElementCache.ElementId
PS.ExposureElement.ExposureElementKey=dbo.ElementCache.ElementBranchId
PS.ExposureElement.ParentDataSourceInstanceId=PS.PlacementExposureGroup.DataSourceInstanceId
PS.ExposureElement.ParentExposureElementKey=PS.PlacementExposureGroup.PlacementExposureGroupId
PS.ExposureElement.ParentExposureElementKey=dbo.ElementCache.ElementId
PS.ExposureElement.ParentExposureElementKey=dbo.ElementCache.ElementBranchId
PS.ExposureElement.PlacementExposureGroupId=PS.PlacementExposureGroup.PlacementExposureGroupId
PS.ExposureElement.TypeKeyPath=dbo.ElementCache.TypeKeyPath
PS.ExposureElement.ElementLevel=dbo.ElementCache.HierarchyId
PS.ExposureElement.ElementDisplayOrder=dbo.ElementDelta.Index
PS.ExposureElement.ElementId=dbo.ElementCache.ElementId
PS.ExposureElement.ElementTypeId=dbo.ElementCache.ElementTypeId
PS.ExposureElement.ElementType=ref.ElementType.ElementType
PS.ExposureElement.ElementTypeOverride=dbo.ElementDelta.Label
PS.ExposureElement.ParentElementId=dbo.ElementCache.ParentElementId
PS.ExposureElement.ParentElementTypeId=dbo.ElementCache.ElementTypeId
PS.ExposureElement.ParentElementType=ref.ElementType.ElementType
PS.ExposureElement.ParentElementTypeOverride=dbo.ElementDelta.Label
PS.ExposureElement.SourceUpdatedDate=BP.PlacementExposureSummary.ETLUpdatedDate
PS.ExposureElement.SourceUpdatedDate=PS.PlacementExposureGroup.ETLUpdatedDate
PS.ExposureElement.SourceUpdatedDate=dbo.ElementCache.LastUpdatedUTCDate
PS.ExposureElement.IsDeleted=BP.PlacementExposureSummary.IsDeleted
PS.ExposureElement.IsDeleted=PS.PlacementExposureGroup.IsDeleted
PS.ExposureElement.ElementBranchId=dbo.ElementCache.ElementBranchId
*/
CREATE PROCEDURE BPStaging.Load_PS_ExposureElement
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ExposureElement';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #Exposure;
    DROP TABLE IF EXISTS #ExposureElement;

    CREATE TABLE #Exposure (
        DataSourceInstanceId     INT          NOT NULL
      , PlacementExposureGroupId INT          NOT NULL
      , ElementId                INT          NOT NULL
      , ElementBranchId          INT          NOT NULL
      , SourceUpdatedDate        DATETIME2(7) NULL
      , IsDeleted                BIT          NOT NULL
      ,
      UNIQUE CLUSTERED
      (
          PlacementExposureGroupId ASC
      )
    );

    CREATE TABLE #ExposureElement (
        DataSourceInstanceId       INT           NOT NULL
      , ExposureElementKey         NVARCHAR(50)  NOT NULL
      , ParentDataSourceInstanceId INT           NULL
      , ParentExposureElementKey   NVARCHAR(50)  NULL
      , PlacementExposureGroupId   INT           NOT NULL
      , TypeKeyPath                NVARCHAR(500) NULL
      , ElementLevel               INT           NOT NULL
      , ElementDisplayOrder        INT           NULL
      , ElementId                  INT           NOT NULL
      , ElementTypeId              INT           NOT NULL
      , ElementType                NVARCHAR(500) NOT NULL
      , ElementTypeOverride        NVARCHAR(500) NULL
      , ParentElementId            INT           NULL
      , ParentElementTypeId        INT           NULL
      , ParentElementType          NVARCHAR(500) NULL
      , ParentElementTypeOverride  NVARCHAR(500) NULL
      , SourceUpdatedDate          DATETIME2(7)  NULL
      , IsDeleted                  BIT           NOT NULL
      , ElementBranchId            INT           NULL
      ,
      UNIQUE CLUSTERED
      (
          ExposureElementKey ASC
        , DataSourceInstanceId ASC
      )
    );

    INSERT INTO
        #Exposure
        (
            DataSourceInstanceId
          , PlacementExposureGroupId
          , ElementId
          , ElementBranchId
          , SourceUpdatedDate
          , IsDeleted
        )
    SELECT
        peg.DataSourceInstanceId
      , peg.PlacementExposureGroupId
      , ese.ElementId
      , pes.ElementBranchId
      , SourceUpdatedDate = GREATEST(pes.ETLUpdatedDate, peg.ETLUpdatedDate)
      , IsDeleted = (pes.IsDeleted | peg.IsDeleted)
    FROM
        BP.PlacementExposureSummary pes
        INNER JOIN BP.ExposureSummaryElement ese
            ON ese.Id = pes.ExposureSummaryElementId

        INNER JOIN PS.PlacementExposureGroup peg
            ON peg.PlacementExposureGroupKey = CONCAT(N'PLEXPSUM|', pes.Id)
               AND peg.DataSourceInstanceId = 50366
    WHERE
        pes.IsDeleted = 0
        AND peg.IsDeleted = 0;

    INSERT INTO
        #ExposureElement
        (
            DataSourceInstanceId
          , ExposureElementKey
          , ParentDataSourceInstanceId
          , ParentExposureElementKey
          , PlacementExposureGroupId
          , TypeKeyPath
          , ElementLevel
          , ElementDisplayOrder
          , ElementId
          , ElementTypeId
          , ElementType
          , ElementTypeOverride
          , ParentElementId
          , ParentElementTypeId
          , ParentElementType
          , ParentElementTypeOverride
          , SourceUpdatedDate
          , IsDeleted
          , ElementBranchId
        )
    SELECT
        e.DataSourceInstanceId
      , ExposureElementKey = CONCAT(e.PlacementExposureGroupId, N'|', ec.ElementId, N'|', ec.ElementBranchId)
      , ParentDataSourceInstanceId = e.DataSourceInstanceId
      , ParentExposureElementKey = CASE WHEN ecparent.ElementId IS NOT NULL
                                            THEN CONCAT(
                                                     e.PlacementExposureGroupId
                                                   , N'|'
                                                   , ecparent.ElementId
                                                   , N'|'
                                                   , ecparent.ElementBranchId
                                                 ) END
      , e.PlacementExposureGroupId
      , ec.TypeKeyPath
      , ElementLevel = ec.HierarchyId.GetLevel()
      , ElementDisplayOrder = ed.[Index]
      , ec.ElementId
      , ec.ElementTypeId
      , et.ElementType
      , ElementTypeOverride = ed.Label
      , ec.ParentElementId
      , ParentElementTypeId = ecparent.ElementTypeId
      , ParentElementType = etparent.ElementType
      , ParentElementTypeOverride = edparent.Label
      , SourceUpdatedDate = GREATEST(e.SourceUpdatedDate, ec.LastUpdatedUTCDate)
      , e.IsDeleted
      , ec.ElementBranchId
    FROM
        #Exposure e
        INNER JOIN dbo.ElementCache ec
            ON ec.RootElementId = e.ElementId
               AND ISNULL(ec.ElementBranchId, -1) = ISNULL(e.ElementBranchId, -1)

        INNER JOIN dbo.ElementDelta ed
            ON ed.ElementDeltaId = ec.ElementDeltaId

        INNER JOIN ref.ElementType et WITH (NOLOCK)
            ON et.ElementTypeId = ec.ElementTypeId

        LEFT JOIN dbo.ElementCache ecparent WITH (NOLOCK)
            ON ecparent.ElementId = ec.ParentElementId
               AND ISNULL(ecparent.ElementBranchId, -1) = ISNULL(ec.ElementBranchId, -1)

        LEFT JOIN ref.ElementType etparent WITH (NOLOCK)
            ON etparent.ElementTypeId = ecparent.ElementTypeId

        LEFT JOIN dbo.ElementDelta edparent WITH (NOLOCK)
            ON edparent.ElementDeltaId = ecparent.ElementDeltaId
    ORDER BY
        ExposureElementKey;

    MERGE PS.ExposureElement T
    USING (
        SELECT
            DataSourceInstanceId
          , ExposureElementKey
          , ParentDataSourceInstanceId
          , ParentExposureElementKey
          , PlacementExposureGroupId
          , TypeKeyPath
          , ElementLevel
          , ElementDisplayOrder
          , ElementId
          , ElementTypeId
          , ElementType
          , ElementTypeOverride
          , ParentElementId
          , ParentElementTypeId
          , ParentElementType
          , ParentElementTypeOverride
          , SourceUpdatedDate
          , IsDeleted
          , ElementBranchId
        FROM
            #ExposureElement
    ) S
    ON T.ExposureElementKey = S.ExposureElementKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , ExposureElementKey
               , ParentDataSourceInstanceId
               , ParentExposureElementKey
               , PlacementExposureGroupId
               , TypeKeyPath
               , ElementLevel
               , ElementDisplayOrder
               , ElementId
               , ElementTypeId
               , ElementType
               , ElementTypeOverride
               , ParentElementId
               , ParentElementTypeId
               , ParentElementType
               , ParentElementTypeOverride
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
               , ElementBranchId
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.ExposureElementKey
                   , S.ParentDataSourceInstanceId
                   , S.ParentExposureElementKey
                   , S.PlacementExposureGroupId
                   , S.TypeKeyPath
                   , S.ElementLevel
                   , S.ElementDisplayOrder
                   , S.ElementId
                   , S.ElementTypeId
                   , S.ElementType
                   , S.ElementTypeOverride
                   , S.ParentElementId
                   , S.ParentElementTypeId
                   , S.ParentElementType
                   , S.ParentElementTypeOverride
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.ElementBranchId
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.ParentDataSourceInstanceId
                               , S.ParentExposureElementKey
                               , S.PlacementExposureGroupId
                               , S.TypeKeyPath
                               , S.ElementLevel
                               , S.ElementDisplayOrder
                               , S.ElementId
                               , S.ElementTypeId
                               , S.ElementType
                               , S.ElementTypeOverride
                               , S.ParentElementId
                               , S.ParentElementTypeId
                               , S.ParentElementType
                               , S.ParentElementTypeOverride
                               , S.IsDeleted
                               , S.ElementBranchId
                             INTERSECT
                             SELECT
                                 T.ParentDataSourceInstanceId
                               , T.ParentExposureElementKey
                               , T.PlacementExposureGroupId
                               , T.TypeKeyPath
                               , T.ElementLevel
                               , T.ElementDisplayOrder
                               , T.ElementId
                               , T.ElementTypeId
                               , T.ElementType
                               , T.ElementTypeOverride
                               , T.ParentElementId
                               , T.ParentElementTypeId
                               , T.ParentElementType
                               , T.ParentElementTypeOverride
                               , T.IsDeleted
                               , T.ElementBranchId
                         )
        THEN UPDATE SET
                 T.ParentDataSourceInstanceId = S.ParentDataSourceInstanceId
               , T.ParentExposureElementKey = S.ParentExposureElementKey
               , T.PlacementExposureGroupId = S.PlacementExposureGroupId
               , T.TypeKeyPath = S.TypeKeyPath
               , T.ElementLevel = S.ElementLevel
               , T.ElementDisplayOrder = S.ElementDisplayOrder
               , T.ElementId = S.ElementId
               , T.ElementTypeId = S.ElementTypeId
               , T.ElementType = S.ElementType
               , T.ElementTypeOverride = S.ElementTypeOverride
               , T.ParentElementId = S.ParentElementId
               , T.ParentElementTypeId = S.ParentElementTypeId
               , T.ParentElementType = S.ParentElementType
               , T.ParentElementTypeOverride = S.ParentElementTypeOverride
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.ElementBranchId = S.ElementBranchId
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;