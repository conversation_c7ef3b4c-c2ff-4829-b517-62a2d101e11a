/*
Lineage
PS.ClientProposalQuoteComparison.Id=BPStaging.ClientProposalQuoteComparison.Id
PS.ClientProposalQuoteComparison.ClientProposalId=BPStaging.ClientProposalQuoteComparison.ClientProposalId
PS.ClientProposalQuoteComparison.QuoteComparisonId=BPStaging.ClientProposalQuoteComparison.QuoteComparisonId
PS.ClientProposalQuoteComparison.DisplayIndex=BPStaging.ClientProposalQuoteComparison.DisplayIndex
PS.ClientProposalQuoteComparison.SourceUpdatedDate=BPStaging.ClientProposalQuoteComparison.ValidTo
PS.ClientProposalQuoteComparison.SourceUpdatedDate=BPStaging.ClientProposalQuoteComparison.ValidFrom
PS.ClientProposalQuoteComparison.IsDeleted=BPStaging.ClientProposalQuoteComparison.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_ClientProposalQuoteComparison
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ClientProposalQuoteComparison';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ClientProposalQuoteComparison)
BEGIN TRY
    MERGE PS.ClientProposalQuoteComparison T
    USING (
        SELECT
            Id
          , ClientProposalId
          , QuoteComparisonId
          , DisplayIndex
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , ClientProposalId
          , QuoteComparisonId
          , DisplayIndex
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ClientProposalQuoteComparison
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , ClientProposalId
               , QuoteComparisonId
               , DisplayIndex
               , SourceUpdatedDate
               , IsDeleted
               , DataSourceInstanceId
             )
             VALUES
                 (
                     S.Id
                   , S.ClientProposalId
                   , S.QuoteComparisonId
                   , S.DisplayIndex
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.ClientProposalId
      , T.QuoteComparisonId
      , T.DisplayIndex
      , T.SourceUpdatedDate
      , T.IsDeleted
      , T.DataSourceInstanceId
    INTERSECT
    SELECT
        S.ClientProposalId
      , S.QuoteComparisonId
      , S.DisplayIndex
      , S.SourceUpdatedDate
      , S.IsDeleted
      , S.DataSourceInstanceId
)
        THEN UPDATE SET
                 T.ClientProposalId = S.ClientProposalId
               , T.QuoteComparisonId = S.QuoteComparisonId
               , T.DisplayIndex = S.DisplayIndex
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);