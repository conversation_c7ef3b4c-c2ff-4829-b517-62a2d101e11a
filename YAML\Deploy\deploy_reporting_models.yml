parameters:
  envName: ''
  microservice: ''
  variableGroupName: ''
  variableGroupNameShared: ''
  azureServiceConnection: ''
  download: ''
  ssas: { }
  dependsOn: []
  agentPoolName: 'Private-VM-CRB-VS2022'

jobs:
  - deployment: ${{parameters.envName}}_reports_deploy
    displayName: 'Analysis Server: Model Deployment'
    dependsOn: ${{ parameters.dependsOn }}
    variables:
    - group: ${{parameters.variableGroupName}}
    - group: ${{parameters.variableGroupNameShared}}
    pool:
      name: ${{parameters.agentPoolName}}
      demands: AZP_REGION -equals EM
    environment: crbbro-${{parameters.microservice}}-${{parameters.envName}}
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
            fetchDepth: 1    # shallow
            fetchTags: false # dont fetch tags
          - download: ${{parameters.download}}
            artifact: PlacementStore.Reporting
            displayName: 'Download: PlacementStore.Reporting'
          - ${{ each model in parameters.ssas.models }}:
            - task: PowerShell@2
              displayName: 'Deploy: ${{model.name}} - Model'
              inputs:
                filePath: '$(Pipeline.Workspace)/PlacementStore.Reporting/Azure_DevOps_Deploy_AS_TAB.PS1'
                arguments: >
                  -Environment '${{parameters.ssas.envName}}'
                  -AnalysisServicesUserName '${{parameters.ssas.analysisServicesUserName}}'
                  -AnalysisServicesPassword '${{parameters.ssas.ssasAdminUserPassword}}'
                  -ImpersonationUserId '${{parameters.ssas.impersonationUserId}}'
                  -ImpersonationPassword '${{parameters.ssas.ssasAdminUserPassword}}'
                  -PlacementStoreServer '${{parameters.ssas.placementStoreSqlServer}}'
                  -PlacementStoreDB '${{parameters.ssas.placementStoreDatabaseName}}'
                  -SSASServer '${{parameters.ssas.ssasServer}}'
                  -SSASCubeName '${{model.databaseName}}'
                  -SSASModelName '${{model.name}}'
