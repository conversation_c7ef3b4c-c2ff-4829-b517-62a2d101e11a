/*
Lineage
BP.BoundPositionPlacementPolicy.Id=BPStaging.BoundPositionPlacementPolicy.Id
BP.BoundPositionPlacementPolicy.BoundPositionId=BPStaging.BoundPositionPlacementPolicy.BoundPositionId
BP.BoundPositionPlacementPolicy.PlacementPolicyId=BPStaging.BoundPositionPlacementPolicy.PlacementPolicyId
BP.BoundPositionPlacementPolicy.SourceUpdatedDate=BPStaging.BoundPositionPlacementPolicy.ValidTo
BP.BoundPositionPlacementPolicy.SourceUpdatedDate=BPStaging.BoundPositionPlacementPolicy.ValidFrom
BP.BoundPositionPlacementPolicy.IsDeleted=BPStaging.BoundPositionPlacementPolicy.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_BoundPositionPlacementPolicy
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.BoundPositionPlacementPolicy';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.BoundPositionPlacementPolicy)
BEGIN
    BEGIN TRY
        MERGE BP.BoundPositionPlacementPolicy T
        USING (
            SELECT
                inner_select.Id
              , inner_select.BoundPositionId
              , inner_select.PlacementPolicyId
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                     THEN 1
                                 ELSE 0 END
            FROM (
            SELECT
                Id
              , BoundPositionId
              , PlacementPolicyId
              , ValidFrom
              , ValidTo
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.BoundPositionPlacementPolicy
        ) inner_select
            WHERE
                inner_select.RowNo = 1
        ) S
        ON T.Id = S.Id
        WHEN NOT MATCHED
            THEN INSERT (
                     Id
                   , BoundPositionId
                   , PlacementPolicyId
                   , SourceUpdatedDate
                   , IsDeleted
                 )
                 VALUES
                     (
                         S.Id
                       , S.BoundPositionId
                       , S.PlacementPolicyId
                       , S.SourceUpdatedDate
                       , S.IsDeleted
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.BoundPositionId
      , T.PlacementPolicyId
      , T.SourceUpdatedDate
      , T.IsDeleted
    INTERSECT
    SELECT
        S.BoundPositionId
      , S.PlacementPolicyId
      , S.SourceUpdatedDate
      , S.IsDeleted
)
            THEN UPDATE SET
                     T.BoundPositionId = S.BoundPositionId
                   , T.PlacementPolicyId = S.PlacementPolicyId
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.ETLUpdatedDate = GETUTCDATE()
                   , T.IsDeleted = S.IsDeleted
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);