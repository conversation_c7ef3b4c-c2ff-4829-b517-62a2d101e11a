/*
Lineage
dbo.SubmissionRecipient.SubmissionMarketId=BP.SubmissionMarketRecipient.SubmissionMarketId
dbo.SubmissionRecipient.RecipientId=BP.SubmissionMarketRecipient.Id
dbo.SubmissionRecipient.RecipientName=BP.SubmissionMarketRecipient.Name
dbo.SubmissionRecipient.RecipientEmail=BP.SubmissionMarketRecipient.Email
dbo.SubmissionRecipient.IncludeAttachments=BP.SubmissionMarketRecipient.IncludeAttachments
*/
CREATE PROCEDURE BPStaging.Load_dbo_SubmissionRecipient
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.SubmissionRecipient';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BP.SubmissionMarketRecipient
        );

BEGIN TRY
    IF @CheckCount <> 0
    BEGIN
        MERGE dbo.SubmissionRecipient T
        USING (
            SELECT
                smr.SubmissionMarketId
              , RecipientId = smr.Id
              , RecipientName = ISNULL(smr.Name, '')
              , RecipientEmail = smr.Email
              , smr.IncludeAttachments
            FROM
                BP.SubmissionMarketRecipient smr
            WHERE
                EXISTS (
                SELECT * FROM dbo.SubmissionDistribution sd WHERE sd.SubmissionMarketId = smr.SubmissionMarketId
            )
        ) S
        ON S.RecipientId = T.RecipientId
        WHEN NOT MATCHED BY TARGET
            THEN INSERT (
                     SubmissionMarketId
                   , RecipientId
                   , RecipientName
                   , RecipientEmail
                   , IncludeAttachments
                 )
                 VALUES
                     (
                         S.SubmissionMarketId
                       , S.RecipientId
                       , S.RecipientName
                       , S.RecipientEmail
                       , S.IncludeAttachments
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT
            T.SubmissionMarketId
          , T.RecipientName
          , T.RecipientEmail
          , T.IncludeAttachments
        INTERSECT
        SELECT
            S.SubmissionMarketId
          , S.RecipientName
          , S.RecipientEmail
          , S.IncludeAttachments
    )
            THEN UPDATE SET
                     T.SubmissionMarketId = S.SubmissionMarketId
                   , T.RecipientName = S.RecipientName
                   , T.RecipientEmail = S.RecipientEmail
                   , T.IncludeAttachments = S.IncludeAttachments
        WHEN NOT MATCHED BY SOURCE
            THEN DELETE
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;