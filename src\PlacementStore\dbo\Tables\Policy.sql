CREATE TABLE dbo.Policy (
    PolicyId                BIGINT         IDENTITY(1, 1) NOT NULL
  , PACTPolicyId            INT            NOT NULL
  , PolicyReference         NVARCHAR(50)   NULL
  , PolicyDescription       NVARCHAR(500)  NULL
  , DataSourceInstanceId    INT            NULL
  , PolicyKey               NVARCHAR(101)  NULL
  , SourceQueryId           INT            NULL
  , PolicyStatusId          INT            NULL
  , InceptionDate           DATETIME2      NULL
  , OpportunityTypeId       INT            NULL
  , ExpiryDate              DATETIME2      NULL
  , InsuranceTypeId         INT            NULL
  , IsDeleted               BIT
        DEFAULT 0 NOT NULL
  , CreatedUTCDate          DATETIME2(7)   NULL /* Deprecated R2022.9 */
  , LastUpdatedUTCDate      DATETIME2(7)   NULL /* Deprecated R2022.9 */
  , CollectionId            INT            NULL
  , CustomAttribute         XML            NULL
  , SumInsured              DECIMAL(18, 4) NULL
  , SumInsuredCurrencyId    INT            NULL
  , RenewedFromPolicyId     BIGINT         NULL
  , RenewedFromPolicyKey    NVARCHAR(50)   NULL
  , PolicyTypeId            INT            NULL
  , PlacementId             BIGINT         NULL -- No longer consistently populated.  Use dbo.PlacementPolicy instead
  , IsFacility              BIT
        DEFAULT 0 NOT NULL
  , RefPolicyStatusId       INT            NULL
  , GrossPremium            DECIMAL(38, 4) NULL
  , NetPremium              DECIMAL(38, 4) NULL
  , ParentId                INT            NULL
  , FacilityType            NVARCHAR(80)   NULL
  , ParentKey               NVARCHAR(101)  NULL
  , GrossPremiumCurrencyId  INT            NULL
  , NetPremiumCurrencyId    INT            NULL
  , IsMainPolicy            INT            NULL
  , WTWIsLeadBroker         INT            NULL
  , RuleId                  INT            NULL
  , PreviousPlacementId     INT            NULL
  , RunId                   INT            NULL
  , AutoRenewFlag           BIT            NULL
  , RenewableFlag           BIT            NULL
  , AutoInvoiceFlag         BIT            NULL
  , RenewalDate             DATETIME2      NULL
  , Department              NVARCHAR(50)   NULL
  , VNAB_Number             NVARCHAR(20)   NULL
  , eABSDocSignature        NVARCHAR(50)   NULL
  , IndexationTacit         BIT            NULL
  , ply_branch              NVARCHAR(50)   NULL
  , MarketSegment           NVARCHAR(50)   NULL
  , BrokingRegionId         INT            NULL
  , BrokingSegmentId        INT            NULL
  , BrokingSubSegmentId     INT            NULL
  , TacitConsecutive        INT            NULL
  , SegmentCode             NCHAR(6)       NULL
  , SuppressPlacementLink   BIT
        DEFAULT 0 NOT NULL
  , SegmentOverride         NVARCHAR(50)   NULL
  , NASegmentValidatedFlag  BIT
        DEFAULT 0 NOT NULL
  , PreviousRuleId          INT            NULL
  , IncomingGlobalBusiness  BIT            NULL
  , Backloaded              NVARCHAR(500)  NULL
  , RenewedFromPACTPolicyId INT            NULL
  , ParentPACTPolicyId      INT            NULL
  , FinancialGeographyId    INT            NULL
  , FinancialLegalEntityId  INT            NULL
  , FinancialSegmentId      INT            NULL
  , CreatedDate             DATETIME2      NULL /* Deprecated R2022.9 */
  , UpdatedDate             DATETIME2      NULL /* Deprecated R2022.9 */
  , ETLCreatedDate          DATETIME2(7)   NOT NULL
        DEFAULT GETUTCDATE()
  , ETLUpdatedDate          DATETIME2(7)   NOT NULL
        DEFAULT GETUTCDATE()
  , SourceUpdatedDate       DATETIME2(7)   NULL
  , PolicyStatusKey         NVARCHAR(100)  NULL
  , PolicyTypeKey           NVARCHAR(100)  NULL
  , CONSTRAINT PK_dbo_Policy
        PRIMARY KEY CLUSTERED
        (
            PolicyId ASC
        )
  , CONSTRAINT FK_dbo_Policy_Reference_Currency
        FOREIGN KEY
        (
            SumInsuredCurrencyId
        )
        REFERENCES Reference.Currency
        (
            CurrencyId
        )
  , CONSTRAINT FK_dbo_Policy_Reference_DataSourceInstance
        FOREIGN KEY
        (
            DataSourceInstanceId
        )
        REFERENCES Reference.DataSourceInstance
        (
            DataSourceInstanceId
        )
  , CONSTRAINT FK_dbo_Policy_ref_InsuranceType
        FOREIGN KEY
        (
            InsuranceTypeId
        )
        REFERENCES ref.InsuranceType
        (
            InsuranceTypeId
        )
  , CONSTRAINT FK_dbo_Policy_ref_OpportunityType
        FOREIGN KEY
        (
            OpportunityTypeId
        )
        REFERENCES ref.OpportunityType
        (
            OpportunityTypeId
        )
);
GO

CREATE INDEX IX_dbo_Policy_BrokingRegionId
ON dbo.Policy
(
    BrokingRegionId ASC
);
GO

CREATE INDEX IX_dbo_Policy_BrokingSegmentId
ON dbo.Policy
(
    BrokingSegmentId ASC
);
GO

CREATE INDEX IX_dbo_Policy_BrokingSubSegmentId
ON dbo.Policy
(
    BrokingSubSegmentId ASC
);
GO

CREATE INDEX IX_dbo_Policy_InsuranceTypeId
ON dbo.Policy
(
    InsuranceTypeId ASC
);
GO

CREATE INDEX IX_dbo_Policy_PolicyStatusKeyDataSourceInstanceId
ON dbo.Policy
(
    PolicyStatusKey ASC
  , DataSourceInstanceId ASC
);
GO

CREATE INDEX IX_dbo_Policy_SumInsuredCurrencyId
ON dbo.Policy
(
    SumInsuredCurrencyId ASC
);
GO

CREATE INDEX IX_dbo_Policy_PolicyTypeId
ON dbo.Policy
(
    PolicyTypeId
);
GO

CREATE INDEX IX_dbo_Policy_PACTPolicyId
ON dbo.Policy
(
    PACTPolicyId
);
GO

CREATE INDEX IX_dbo_Policy_DataSourceInstanceIdPolicyKeySourceQueryId
ON dbo.Policy
(
    DataSourceInstanceId
  , PolicyKey
  , SourceQueryId
);
GO

CREATE INDEX IX_dbo_Policy_OpportunityTypeId
ON dbo.Policy
(
    OpportunityTypeId
);
GO

CREATE INDEX IX_dbo_Policy_PolicyReferenceDataSourceInstanceId
ON dbo.Policy
(
    PolicyReference
  , DataSourceInstanceId
)
INCLUDE
(
    PACTPolicyId
  , IsDeleted
  , InceptionDate
);
GO

-- deleted because covered by IX_Policy_PlacementCarriersView (a slightly larger index though)
--CREATE INDEX IX_Policy_IsDeleted
--ON dbo.Policy (IsDeleted)
--INCLUDE (PolicyId, InceptionDate, ExpiryDate);
--GO

CREATE INDEX IX_dbo_Policy_RefPolicyStatusId
ON dbo.Policy
(
    RefPolicyStatusId
);
GO

CREATE NONCLUSTERED INDEX IX_dbo_Policy_IsDeletedRenewedFromPolicyIdIsFacilityInceptionDateRefPolicyStatusId
ON dbo.Policy
(
    IsDeleted
  , RenewedFromPolicyId
  , IsFacility
  , InceptionDate
  , RefPolicyStatusId
)
INCLUDE
(
    PolicyId
  , ExpiryDate
);
GO

CREATE NONCLUSTERED INDEX IX_dbo_Policy_IsDeletedInceptionDateRefPolicyStatusId
ON dbo.Policy
(
    IsDeleted ASC
  , InceptionDate ASC
  , RefPolicyStatusId ASC
)
INCLUDE
(
    PolicyId
  , DataSourceInstanceId
  , ExpiryDate
  , RenewedFromPolicyId
);
GO

CREATE NONCLUSTERED INDEX IX_dbo_Policy_RuleIdDataSourceInstanceIdIsDeletedExpiryDate
ON dbo.Policy
(
    RuleId
  , DataSourceInstanceId
  , IsDeleted
  , ExpiryDate
)
INCLUDE
(
    InceptionDate
  , PlacementId
  , RenewalDate
);
GO

/* Covering index for APIv1.AdditionDataItem */
CREATE UNIQUE INDEX IXU_dbo_Policy_PolicyId
ON dbo.Policy
(
    PolicyId
)
INCLUDE
(
    DataSourceInstanceId
  , VNAB_Number
  , ETLUpdatedDate
  , RenewableFlag
  , AutoInvoiceFlag
);
GO

CREATE NONCLUSTERED INDEX IX_dbo_Policy_RenewedFromPolicyId
ON dbo.Policy
(
    RenewedFromPolicyId
);
GO