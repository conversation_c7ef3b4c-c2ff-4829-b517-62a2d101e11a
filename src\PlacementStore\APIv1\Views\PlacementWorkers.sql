/*
Lineage
PlacementID=dbo.PlacementWorker.PlacementId
PlacementID=dbo.PlacementTeamMember.PlacementId
PlacementSystemRoleID=dbo.PlacementSystemRoleMapping.PlacementSystemRoleId
PlacementSystemRoleID=dbo.PlacementTeamMember.RoleId
UserPrincipalName=APIv1.ActiveDirectory.UserPrincipalName
UserPrincipalName=dbo.PlacementSystemUser.UserPrincipalName
PlacementDataSourceInstanceID=dbo.PlacementSystemRoleMapping.PlacementDataSourceInstanceId
PlacementDataSourceInstanceID=dbo.PlacementTeamMember.DataSourceInstanceId
LastUpdatedUTCDate=dbo.PlacementWorker.LastUpdatedUTCDate
LastUpdatedUTCDate=dbo.PlacementListener.ValidFrom
LastUpdatedUTCDate=dbo.PlacementTeamMember.ETLUpdatedDate
RenewalProcessStartDate=dbo.PlacementListener.RenewalProcessStartDate
Isdeleted=dbo.PlacementWorker.IsDeleted
Isdeleted=dbo.PlacementTeamMember.IsDeleted
*/
CREATE VIEW APIv1.PlacementWorkers
AS
SELECT
    PlacementID = sub.PlacementId
  , PlacementSystemRoleID = sub.PlacementSystemRoleId
  , sub.UserPrincipalName
  , PlacementDataSourceInstanceID = sub.PlacementDataSourceInstanceId
  , LastUpdatedUTCDate = MAX(sub.LastUpdatedUTCDate)
  , RenewalProcessStartDate = MAX(sub.RenewalProcessStartDate)
  , Isdeleted = MIN(CONVERT(INT, sub.IsDeleted))
FROM (
    --take placement workers from policy system
    SELECT
        pw.PlacementWorkerId
      , pw.PlacementId
      , LastUpdatedUTCDate = GREATEST(pw.LastUpdatedUTCDate, pli.ValidFrom)
      , pli.RenewalProcessStartDate
      , psrm.PlacementSystemRoleId
      --      , PlacementSystemRole = PSR.ServicingRole
      , psrm.PlacementDataSourceInstanceId
      , ad.UserPrincipalName
      , IsDeleted = ISNULL(pw.IsDeleted, 0)
    FROM
        dbo.PlacementWorker pw WITH (NOLOCK)
        INNER JOIN PAS.ServicingRole sr WITH (NOLOCK)
            ON sr.ServicingRoleKey = pw.ServicingRoleKey
               AND sr.DataSourceInstanceId = pw.DataSourceInstanceId
               AND sr.IsDeleted = 0

        INNER JOIN dbo.Placement pl WITH (NOLOCK)
            ON pw.PlacementId = pl.PlacementId

        LEFT JOIN dbo.PlacementListener pli WITH (NOLOCK)
            ON pli.PlacementId = pl.PlacementId
               AND pli.PlacementDataSourceInstanceId = 50366
               AND pli.IsDeleted = 0

        INNER JOIN dbo.PlacementSystemRoleMapping psrm WITH (NOLOCK)
            ON psrm.ServicingRoleKey = sr.ServicingRoleKey
               AND psrm.DataSourceInstanceId = sr.DataSourceInstanceId
               AND psrm.IsDeleted = 0
               AND ISNULL(pl.BrokingRegionId, 0) = COALESCE(psrm.BrokingRegionId, pl.BrokingRegionId, 0)
        --AND ISNULL(PL.BrokingSegmentID,0) = ISNULL(PSRM.BrokingSegmentId,0)
        --AND ISNULL(PL.BrokingSubSegmentID,0) = ISNULL(PSRM.BrokingSubSegmentId,0)

        INNER JOIN ref.ServicingRole psr WITH (NOLOCK)
            ON psr.ServicingRoleId = psrm.PlacementSystemRoleId

        LEFT JOIN (
            SELECT
                wad.WorkerId
              , wad.ReferenceWorkerId
              , ActiveDirectoryId = CASE WHEN wad.ActiveDirectoryId IS NULL
                                             THEN waad.ActiveDirectoryId
                                         ELSE wad.ActiveDirectoryId END
              , UserPrincipalName = CASE WHEN wad.UserPrincipalName IS NULL
                                             THEN waad.UserPrincipalName
                                         ELSE wad.UserPrincipalName END
            FROM (
                SELECT
                    WorkerId
                  , ReferenceWorkerId
                  , a.IsDeleted
                  , a.ActiveDirectoryId
                  , a.UserPrincipalName
                FROM (
                    SELECT
                        ROW_NO = ROW_NUMBER() OVER (PARTITION BY pw.WorkerSK
                                                    ORDER BY
                                                        CASE WHEN ad.UserPrincipalName LIKE '%WILLISTOWERSWATSON%'
                                                                  AND ad.AccountName NOT LIKE '%.%'
                                                                 THEN 2
                                                             ELSE
                                                                 CASE WHEN ad.UserPrincipalName LIKE '%WILLISTOWERSWATSON%'
                                                                          THEN 1
                                                                      ELSE 0 END END DESC --Prioritise WTW Accounts
                                              )
                      , WorkerId = pw.WorkerSK
                      , w.ReferenceWorkerId
                      , w.IsDeleted
                      , ad.ActiveDirectoryId
                      , ad.UserPrincipalName
                    FROM
                        PAS.Worker w WITH (NOLOCK)
                        INNER JOIN PS.Worker pw WITH (NOLOCK)
                            ON pw.WorkerKey = W.WorkerKey
                               AND pw.DataSourceInstanceId = w.DataSourceInstanceId

                        LEFT JOIN APIv1.ActiveDirectory ad WITH (NOLOCK)
                            ON REPLACE(ad.AccountName, '_', '') = REPLACE(
                                                                      SUBSTRING(
                                                                          w.WorkerAccount
                                                                        , CHARINDEX('\', w.WorkerAccount) + 1
                                                                        , LEN(w.WorkerAccount)
                                                                      )
                                                                    , '_'
                                                                    , ''
                                                                  )
                ) a
                WHERE
                    a.ROW_NO = 1
            ) wad
                 LEFT JOIN (
                     SELECT
                         b.ReferenceWorkerId
                       , b.IsDeleted
                       , b.ActiveDirectoryId
                       , b.UserPrincipalName
                     FROM (
                         SELECT
                             ROW_NO = ROW_NUMBER() OVER (PARTITION BY wa.WorkerId
                                                         ORDER BY
                                                             CASE WHEN ad.UserPrincipalName LIKE '%WILLISTOWERSWATSON%'
                                                                       AND ad.AccountName NOT LIKE '%.%'
                                                                      THEN 2
                                                                  ELSE
                                                                      CASE WHEN ad.UserPrincipalName LIKE '%WILLISTOWERSWATSON%'
                                                                               THEN 1
                                                                           ELSE 0 END END DESC --Prioritise WTW Accounts
                                                   )
                           , ReferenceWorkerId = wa.WorkerId
                           , wa.IsDeleted
                           , ad.ActiveDirectoryId
                           , ad.UserPrincipalName
                         FROM
                             Reference.WorkerAccount wa WITH (NOLOCK)
                             LEFT JOIN APIv1.ActiveDirectory ad WITH (NOLOCK)
                                 ON wa.ActiveDirectoryId = ad.ActiveDirectoryId
                                    AND ad.IsDeleted = 0
                                    AND ad.UserPrincipalName IS NOT NULL
                     ) b
                     WHERE
                         b.ROW_NO = 1
                 ) waad
                     ON waad.ReferenceWorkerId = wad.ReferenceWorkerId
        ) ad
            ON ad.WorkerId = pw.WorkerId
    WHERE
        ad.UserPrincipalName IS NOT NULL
        --Send all workers other than for EPIC US, where we send only validated brokers or support workers
        AND (
            pl.ServicingPlatformId <> 50001 --not EPIC US
            OR pw.UserProvided = 1 -- include any validated user from policy side for US
            OR psr.ServicingRole NOT IN (
                   'Broker', 'Strategic Broking Advisor', 'Support Broker'
               ) --we can send any support role for US
        )
    UNION ALL

    --combine with team member as per Broking platform
    SELECT
        PlacementWorkerId = tm.PlacementTeamMemberId
      , PlacementId = tm.PlacementId
      , LastUpdatedUTCDate = GREATEST(tm.ETLUpdatedDate, pli.ValidFrom)
      , pli.RenewalProcessStartDate
      , PlacementSystemRoleId = tm.RoleId
      --      , ro.ServicingRole
      , PlacementDataSourceInstanceId = tm.DataSourceInstanceId
      , u.UserPrincipalName
      , tm.IsDeleted
    FROM (
        SELECT
            PlacementTeamMemberId
          , PlacementId
          , DataSourceInstanceId
          , UserId
          , RoleId
          , IsDeleted
          , ETLUpdatedDate
          , RowNo = ROW_NUMBER() OVER (PARTITION BY PlacementId, RoleId, UserId ORDER BY PlacementTeamMemberId DESC)
        FROM
            dbo.PlacementTeamMember WITH (NOLOCK)
    ) tm
         INNER JOIN dbo.PlacementSystemUser u WITH (NOLOCK)
             ON tm.UserId = u.UserId

         INNER JOIN ref.ServicingRole ro WITH (NOLOCK)
             ON tm.RoleId = ro.ServicingRoleId

         LEFT JOIN dbo.PlacementListener pli WITH (NOLOCK)
             ON pli.PlacementId = tm.PlacementId
                AND pli.PlacementDataSourceInstanceId = 50366
                AND pli.IsDeleted = 0
    WHERE
        tm.DataSourceInstanceId = 50366
        AND tm.RowNo = 1
) sub
GROUP BY
    sub.PlacementId
  , sub.PlacementSystemRoleId
  , sub.UserPrincipalName
  , sub.PlacementDataSourceInstanceId;