﻿#------------------------------------------------------------------------------
# Azure Analysis Services Replica Count Management Script
#
# Description: This script allows you to set the replica count for an Azure Analysis Services server.
#              It can be used to scale up or down the number of replicas as needed.
# Version: 1.0
#------------------------------------------------------------------------------

param(
    [string]$serverUrl,
    [string]$resourceGroupName,
    [int]$replicaCount,
    [int]$waitTimeSeconds = 5
)


# Function to extract server name from URL (using approved verb)
function Get-ServerNameFromUrl {
    param (
        [string]$Url
    )

    $serverNamePattern = "asazure://([^/]+)/([^/]+)"
    $serverMatch = [regex]::Match($Url, $serverNamePattern)

    if ($serverMatch.Success -and $serverMatch.Groups.Count -ge 3) {
        $region = $serverMatch.Groups[1].Value
        $serverName = $serverMatch.Groups[2].Value

        Write-Host "Server name: $serverName in region: $region"
        return $serverName
    }
    else {
        Write-Error "Could not extract server name from $Url. Please ensure it's in the format 'asazure://region/servername'"
        exit 1
    }
}

try {
    # If using ServerUrl parameter, extract the server name
    if ($serverUrl) {
        $serverName = Get-ServerNameFromUrl -Url $serverUrl
    }
    
    Write-Host "Parameters: -serverName $serverName -resourceGroupName $resourceGroupName -replicaCount $replicaCount"
    # Get the current server configuration
    Write-Host "Getting current configuration for server '$serverName' in resource group '$resourceGroupName'..."
    $serverConfig = Get-AzAnalysisServicesServer -Name $serverName -ResourceGroupName $resourceGroupName -ErrorAction Stop

    if (!$serverConfig) {
        Write-Error "Failed to retrieve server configuration."
        exit 1
    }
    
    # Check the current replica count
    $currentReplicaCount = $serverConfig.Sku.Capacity
    Write-Host "Current replica count: $($currentReplicaCount-1)"

    # Check if the current count is already what we want
    # This is because the default instance count is 1, and the replica count is the number of replicas you want to have, so you need to add 1 to the replica count to get the total number of instances.
    if ($currentReplicaCount -eq $replicaCount + 1) {
        Write-Host "Server '$serverName' already has $($replicaCount) replicas. No changes needed."
        exit 0
    }

    # Set the replica count
    Write-Host "Setting replica count to $($replicaCount) for server '$serverName' in resource group '$resourceGroupName'..."
    try {
        # Attempt to set the replica count
        Set-AzAnalysisServicesServer -Name $serverName -ResourceGroupName $resourceGroupName -ReadonlyReplicaCount $replicaCount -ErrorAction Stop

        Write-Host "Sleeping for $waitTimeSeconds seconds to ensure changes are applied..."
        Start-Sleep -Seconds $waitTimeSeconds
        Write-Host 'Woken up!'
        $serverConfig = Get-AzAnalysisServicesServer -Name $serverName -ResourceGroupName $resourceGroupName -ErrorAction Stop

        if ($serverConfig) {
            $currentReplicaCount = $serverConfig.sku.Capacity
           
            if ($currentReplicaCount -eq $replicaCount + 1) {
                Write-Host "Current server configuration:"
                Write-Host "Replica Count: $($serverConfig.sku.Capacity-1)"
                $serverConfig | Format-List Name, ResourceGroupName, State
                Write-Host "Successfully verified replica count is now set to $($currentReplicaCount-1) for server '$serverName'"
            }
            else {
                Write-Warning "Replica count setting attempted, but current count is $currentReplicaCount (expected $($replicaCount+1))"
                Write-Host "Server configuration:"
                Write-Host "Replica Count: $($serverConfig.sku.Capacity-1)"
                $serverConfig | Format-List Name, ResourceGroupName, State
            }
        }
        else {
            Write-Error "Failed to retrieve server configuration after setting replica count."
        }
    }
    catch {
        Write-Error "Failed to set replica count for server '$serverName' and the error is: $($_.Exception.Message)"
    }
}
catch {
    Write-Error "An error occurred: $_"
    exit 1
}
