/*
Lineage
CarrierID=PS.CarrierHierarchyExtended.CarrierId
TeamId=ods.vw_ref_Team.TeamId
CarrierStatusId=dbo.CarrierStatus.CarrierStatusId
CarrierStatusId=dbo.Carrier.CarrierId
IsOverride=dbo.Carrier.CarrierId
LastUpdatedUTCDate=PS.CarrierHierarchyExtended.LastUpdatedUTCDate
*/
CREATE VIEW APIv1.BrazilCarrierStatusOverride
AS
SELECT
    CarrierID = CH.CarrierId
  , T.TeamId
  , CarrierStatusId = CASE WHEN OV.CarrierId IS NOT NULL
                               THEN 1 --approved for local use (override exists)
                           ELSE CS.CarrierStatusId --use standard status logic 
                      END
  , IsOverride = CASE WHEN OV.CarrierId IS NOT NULL
                          THEN 1
                      ELSE 0 END
  , CH.LastUpdatedUTCDate
FROM (
    --Brazil Carrier Parents with an Approval Status
    SELECT
        CH.CarrierId
      , CH.ApprovalStatus
      , CH.CompCode
      , CH.LastUpdatedUTCDate
    FROM
        PS.CarrierHierarchyExtended CH
    WHERE
        CH.ApprovalStatus IS NOT NULL
        AND CH.CarrierId > 20000000
        AND EXISTS (
        SELECT 1
        FROM
             PS.CarrierHierarchyExtended CH2
        WHERE
            CH2.SignetCarrierNode.IsDescendantOf(CH.SignetCarrierNode) = 1
            AND CH2.DataSourceInstanceId = 50003
    )
    UNION ALL
    --COL Carriers with an Approval Status
    SELECT
        CH.CarrierId
      , CH.ApprovalStatus
      , CH.CompCode
      , CH.LastUpdatedUTCDate
    FROM
        PS.CarrierHierarchyExtended CH
    WHERE
        CH.ApprovalStatus IS NOT NULL
        AND CH.DataSourceInstanceId = 50003
) CH
     LEFT JOIN (
         SELECT DISTINCT
                C.CarrierId
              , C.CarrierEntityCompCode
         FROM
             dbo.Carrier C
             INNER JOIN dbo.CarrierRestriction CR
                 ON CR.CarrierId = C.CarrierId -- SourceCarrierId is now the SignetId

             INNER JOIN dbo.CarrierRestrictionDefinition CRD
                 ON CRD.RestrictionCode_Id = CR.CarrierRestrictionDefinitionId
                    AND CRD.RestrictionDesc IN (
                            'Local (A) business only including domestic MNCs'
                          , 'Local (A) business only including foreign MNCs'
                          , 'Approved for Brazilian healthcare business only'
                          , 'Approved for Employee Benefits risks in Brazil'
                          , 'Approved for Employee Benefits risks in Brazil of policyholders headquartered in Brazil'
                          , 'Approved for Extended Warranty risks in Brazil', 'Approved for Healthcare risks in Brazil'
                          , 'Approved for reinsurance with underlying risks in Brazil'
                          , 'Approved for reinsurance with underlying risks in Brazil of cedents headquartered in Brazil'
                          , 'Approved for risks in Brazil'
                          , 'Approved for risks in Brazil of policyholders headquartered in Brazil'
                          , 'Approved FOR Credit, Bonding & Political Risk risks IN Brazil OF policyholders headquartered IN Brazil'
                          , 'Approved FOR Credit, Bonding & Political Risk risks IN Brazil'
                        )
         WHERE
             C.CarrierTypeId = 1
             AND CR.IsDeprecated = 0
     ) OV
         ON OV.CarrierEntityCompCode = CH.CompCode

     LEFT JOIN (
         SELECT DISTINCT
                OrigApprovalStatus = ApprovalStatus
              , ApprovalStatus = CASE ApprovalStatus
                                      WHEN 'Deactivated'
                                          THEN 'Not Approved'
                                      WHEN 'HOLD - NO NEW OR RENEWALS'
                                          THEN 'Restricted'
                                      WHEN 'International'
                                          THEN 'Approved'
                                      WHEN 'Global Approval'
                                          THEN 'Approved'
                                      WHEN 'Not Approved'
                                          THEN 'Not Approved'
                                      WHEN 'Approved Willis assoc. entity'
                                          THEN 'Approved'
                                      WHEN 'Approved WTW associated entity'
                                          THEN 'Approved'
                                      WHEN 'Restricted'
                                          THEN 'Restricted'
                                      WHEN 'xxxxxxxxxxxxxxx'
                                          THEN 'Not Approved'
                                      WHEN 'Undecided'
                                          THEN 'Not Approved'
                                      WHEN 'See Qualifier'
                                          THEN 'Not Approved'
                                      WHEN 'Not Approved - See Qualifier'
                                          THEN 'Not Approved' END
         FROM
             dbo.Carrier
     ) ASM
         ON ASM.OrigApprovalStatus = CH.ApprovalStatus

     LEFT JOIN dbo.CarrierStatus CS
         ON CS.CarrierStatus = ASM.ApprovalStatus

     INNER JOIN (
         SELECT TeamId
         FROM
             ods.vw_ref_Team
         WHERE
             ServicingPlatformId = 50003
             AND ParentId > 0 --sub-team
     ) T
         ON 1 = 1; --join to all Brazil teams