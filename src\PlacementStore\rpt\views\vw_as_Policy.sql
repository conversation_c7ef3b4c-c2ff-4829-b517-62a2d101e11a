/*
Lineage
PolicyId=dbo.Policy.PolicyId
PolicyReference=dbo.Policy.PolicyReference
PolicyKey=dbo.Policy.PolicyKey
PolicyDescription=dbo.Policy.PolicyDescription
InceptionDate=dbo.Policy.InceptionDate
ExpiryDate=dbo.Policy.ExpiryDate
PolicyStatus=PAS.PolicyStatus.PolicyStatus
RefPolicyStatus=PAS.RefPolicyStatus.RefPolicyStatus
PolicyType=PAS.PolicyType.PolicyType
OpportunityType=ref.OpportunityType.OpportunityType
PlacementId=dbo.PlacementPolicy.PlacementId
ServicingPlatform=Reference.DataSourceInstance.DataSourceInstanceName
GrossPremiumUSD=dbo.ClientUnderwriterPremium.GrossPremiumUSD
NetPremiumtoUWUSD=dbo.ClientUnderwriterPremium.NetPremiumtoUWUSD
*/
CREATE VIEW rpt.vw_as_Policy
AS
SELECT
    p.PolicyId
  , p.PolicyReference
  , p.<PERSON>
  , p.PolicyDescription
  , p.InceptionDate
  , p.ExpiryDate
  , ps.PolicyStatus
  , pt.PolicyType
  , rps.RefPolicyStatus
  , ot.OpportunityType
  , pp.PlacementId
  , ServicingPlatform = dsi.DataSourceInstanceName
  , GrossPremiumUSD = SUM(cup.GrossPremiumUSD)
  , NetPremiumtoUWUSD = SUM(cup.NetPremiumtoUWUSD)
FROM
    dbo.Policy p
    LEFT JOIN PAS.PolicyStatus ps
        ON ps.PolicyStatusKey = p.PolicyStatusKey
            AND ps.DataSourceInstanceId = p.DataSourceInstanceId

    LEFT JOIN PAS.PolicyType pt
        ON pt.PolicyTypeKey = p.PolicyTypeKey
           AND pt.DataSourceInstanceId = p.DataSourceInstanceId

    LEFT JOIN PAS.RefPolicyStatus rps
        ON rps.RefPolicyStatusId = p.RefPolicyStatusId

    LEFT JOIN ref.OpportunityType ot
        ON ot.OpportunityTypeId = p.OpportunityTypeId

    LEFT JOIN dbo.PlacementPolicy pp
        ON pp.PolicyId = p.PolicyId
           AND pp.IsDeleted = 0
           AND pp.PlacementPolicyRelationshipTypeId = 1

    LEFT JOIN Reference.DataSourceInstance dsi
        ON dsi.DataSourceInstanceId = p.DataSourceInstanceId

    LEFT JOIN dbo.ClientUnderwriterPremium cup
        ON cup.PolicyId = p.PolicyId
           AND cup.IsDeleted = 0
WHERE
    p.IsDeleted = 0
GROUP BY
    p.PolicyId
  , p.PolicyReference
  , p.PolicyDescription
  , p.PolicyKey
  , ps.PolicyStatus
  , p.InceptionDate
  , p.ExpiryDate
  , pt.PolicyType
  , rps.RefPolicyStatus
  , ot.OpportunityType
  , pp.PlacementId
  , dsi.DataSourceInstanceName;