/*
Lineage
dbo.PlacementPartyRole.PlacementId=BPStaging.InsertPlacementPartyRole.PlacementId
dbo.PlacementPartyRole.PartyId=BPStaging.InsertPlacementPartyRole.PartyId
dbo.PlacementPartyRole.PartyName=BPStaging.InsertPlacementPartyRole.Name
dbo.PlacementPartyRole.IsDeleted=BPStaging.InsertPlacementPartyRole.IsDeleted
dbo.PlacementPartyRole.PartyRoleId=BPStaging.InsertPlacementPartyRole.PartyRoleId
dbo.PlacementPartyRole.IsPrimaryParty=BPStaging.InsertPlacementPartyRole.IsPrimaryParty
dbo.PlacementPartyRole.CountryId=BPStaging.InsertPlacementPartyRole.CountryId
dbo.PlacementPartyRole.IndustryLevel1Id=BPStaging.InsertPlacementPartyRole.IndustryLevel1Id
dbo.PlacementPartyRole.IndustryLevel3Id=BPStaging.InsertPlacementPartyRole.IndustryLevel3Id
dbo.PlacementPartyRole.CountrySubdivisionId=BPStaging.InsertPlacementPartyRole.CountrySubdivisionId
dbo.PlacementPartyRole.NewClientId=BPStaging.InsertPlacementPartyRole.NewClientId
dbo.PlacementPartyRole.Incumbent=BPStaging.InsertPlacementPartyRole.Incumbent
dbo.PlacementPartyRole.DataSourceInstanceId=BPStaging.InsertPlacementPartyRole.DataSourceInstanceId
dbo.PlacementPartyRole.BPInsuredId=BPStaging.InsertPlacementPartyRole.BPInsuredId
dbo.PlacementPartyRole.SourceUpdatedDate=BPStaging.InsertPlacementPartyRole.SourceUpdatedDate
*/
CREATE PROCEDURE BPStaging.LoadPlacementPartyRole
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementPartyRole';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

-- Do we have rows in the staging table?
IF EXISTS (SELECT * FROM BPStaging.PlacementPartyRelationship)
BEGIN TRY

    -- PBI 118084 - PlacementInsured returns duplicate values
    -- Sometimes a duplicate record is created which is the same except for the DataSourceInstanceID. 
    -- As it doesn't hold carriers there is no need to hold facility.
    -- A "non-party" is a record where the PartyId is NULL. Then we use BPInsuredId (which is the ID of the staging record)
    -- The code ensures that BPInsuredId is only not null when PartyId is null to prevent duplicates. And vice versa.
    MERGE INTO dbo.PlacementPartyRole t
    USING (
        SELECT DISTINCT
               PlacementId
             , PartyId
             , PartyName = CASE WHEN PartyId IS NULL
                                    THEN Name END -- We only stored the PartyName if no PartyId exists. A "Non Party"
             , IsDeleted
             , PartyRoleId
             , GlobalPartyRoleId
             , IsPrimaryParty = ISNULL(IsPrimaryParty, 0) -- Doesn't take NULL.
             , CountryId
             , IndustryLevel1Id
             , IndustryLevel3Id
             , CountrySubdivisionId
             , NewClientId
             , Incumbent
             , DataSourceInstanceId
             , BPInsuredId = CASE WHEN PartyId IS NULL
                                      THEN BPInsuredId END -- We only want the BPInsuredId if no PartyId exists. A "Non Party"
             , OnPlacement = 1
             , SourceUpdatedDate
        FROM
            BPStaging.InsertPlacementPartyRole
        WHERE
            (
            (NewClientId IS NOT NULL)
            OR (PartyId IS NOT NULL)
            OR (Name IS NOT NULL)
        )
    ) s
    ON
    -- All the key fields except for DataSourceInstanceID
    s.PlacementId = t.PlacementId
    AND ISNULL(s.PartyId, 0) = ISNULL(t.PartyId, 0)
    AND s.PartyRoleId = t.PartyRoleId
    AND ISNULL(s.NewClientId, 0) = ISNULL(t.NewClientId, 0)
    AND ISNULL(s.BPInsuredId, 0) = ISNULL(t.BPInsuredId, 0) -- BPInsuredId added to index to deal with hold "Non Party" records where PartyId is null.
    WHEN NOT MATCHED
        THEN INSERT (
                 PlacementId
               , PartyId
               , PartyName
               , IsDeleted
               , CreatedUser
               , CreatedDate
               , UpdatedUser
               , UpdatedDate
               , PartyRoleId
               , IsPrimaryParty
               , CountryId
               , IndustryLevel1Id
               , IndustryLevel3Id
               , CountrySubdivisionId
               , NewClientId
               , Incumbent
               , DataSourceInstanceId
               , BPInsuredId
               , OnPlacement
               , SourceUpdatedDate
             )
             VALUES
                 (
                     s.PlacementId
                   , s.PartyId
                   , s.PartyName
                   , s.IsDeleted
                   , 'FMAImport'
                   , GETUTCDATE()
                   , 'FMAImport'
                   , GETUTCDATE()
                   , s.PartyRoleId
                   , s.IsPrimaryParty
                   , s.CountryId
                   , s.IndustryLevel1Id
                   , s.IndustryLevel3Id
                   , s.CountrySubdivisionId
                   , s.NewClientId
                   , s.Incumbent
                   , s.DataSourceInstanceId
                   , s.BPInsuredId
                   , s.OnPlacement
                   , s.SourceUpdatedDate
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        s.PartyId
      , s.NewClientId
      , s.BPInsuredId
      , s.IsPrimaryParty
      , s.CountryId
      , s.IndustryLevel1Id
      , s.IndustryLevel3Id
      , s.CountrySubdivisionId
      , s.Incumbent
      , s.DataSourceInstanceId
      , s.PartyName
      , s.OnPlacement
      , s.IsDeleted
    INTERSECT
    SELECT
        t.PartyId
      , t.NewClientId
      , t.BPInsuredId
      , t.IsPrimaryParty
      , t.CountryId
      , t.IndustryLevel1Id
      , t.IndustryLevel3Id
      , t.CountrySubdivisionId
      , t.Incumbent
      , t.DataSourceInstanceId
      , t.PartyName
      , t.OnPlacement
      , t.IsDeleted
)
        THEN UPDATE SET
                 t.UpdatedUser = 'FMAImport'
               , t.UpdatedDate = GETUTCDATE()
               , t.PartyId = s.PartyId
               , t.NewClientId = s.NewClientId
               , t.BPInsuredId = s.BPInsuredId
               , t.IsPrimaryParty = s.IsPrimaryParty
               , t.CountryId = s.CountryId
               , t.IndustryLevel1Id = s.IndustryLevel1Id
               , t.IndustryLevel3Id = s.IndustryLevel3Id
               , t.CountrySubdivisionId = s.CountrySubdivisionId
               , t.Incumbent = s.Incumbent
               , t.DataSourceInstanceId = s.DataSourceInstanceId
               , t.PartyName = s.PartyName
               , t.OnPlacement = s.OnPlacement
               , t.IsDeleted = s.IsDeleted
               , t.SourceUpdatedDate = s.SourceUpdatedDate
    WHEN NOT MATCHED BY SOURCE AND (
                                   (
                                       t.BPInsuredId IS NOT NULL
                                       AND t.IsDeleted = 0
                                   )
                                   OR t.OnPlacement = 1
                               )
        THEN UPDATE SET
                 t.IsDeleted = CASE WHEN t.BPInsuredId IS NOT NULL /* Previous code only Deleted Non-Party */
                                        THEN 1
                                    ELSE t.IsDeleted END
               , t.OnPlacement = 0
               , t.UpdatedUser = 'FMAImport'
               , t.UpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    -- Get number of records affected.
    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);