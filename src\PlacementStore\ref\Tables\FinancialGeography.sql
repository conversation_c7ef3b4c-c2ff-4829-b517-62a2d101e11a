CREATE TABLE ref.FinancialGeography (
    FinancialGeographyId          INT           IDENTITY(1, 1) PRIMARY KEY NOT NULL
  , DataSourceInstanceId          INT           NOT NULL
  , SourceQuery                   NVARCHAR(100) NULL
  , SourceKey                     NVARCHAR(100) NULL
  , FinancialGeographyKey         NVARCHAR(100) NOT NULL
  , FinancialGeography            NVARCHAR(500) NULL
  , GlobalFinancialGeographyId    INT           NULL
  , FinancialGeographyCode        NVARCHAR(500) NULL
  , FinancialGeographyDescription NVARCHAR(500) NULL
  , ETLCreatedDate                DATETIME2(7)  NOT NULL
        DEFAULT (GETUTCDATE())
  , ETLUpdatedDate                DATETIME2(7)  NOT NULL
        DEFAULT (GETUTCDATE())
  , IsDeprecated                  BIT           NOT NULL
        DEFAULT (0)
  , PACTFinancialGeographyId      INT           NULL
  , PASFinancialGeographyId       INT           NULL
  , SourceUpdatedDate             DATETIME2(7)  NULL
  , CONSTRAINT FK_ref_FinancialGeography_Reference_DataSourceInstance
        FOREIGN KEY
        (
            DataSourceInstanceId
        )
        REFERENCES Reference.DataSourceInstance
        (
            DataSourceInstanceId
        )
);
GO

CREATE UNIQUE INDEX IXU_ref_FinancialGeography_PASFinancialGeographyId
ON ref.FinancialGeography
(
    PASFinancialGeographyId
);