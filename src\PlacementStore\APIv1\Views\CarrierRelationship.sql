/*
Lineage
FromCarrierId=dbo.Carrier.PSCarrierId
FromCarrierTypeId=dbo.Carrier.CarrierTypeId
ToCarrierId=dbo.Carrier.PSCarrierId
ToCarrierTypeId=dbo.Carrier.CarrierTypeId
CreatedUTCDate=dbo.CarrierMapping.CreatedUTCDate
LastUpdatedUTCDate=dbo.CarrierMapping.LastUpdatedUTCDate
IsDeprecated=dbo.CarrierMapping.IsDeleted
*/
CREATE VIEW APIv1.CarrierRelationship
AS

/**
* Not all mappings are being exposed.
* If you add a new CarrierMapping and it is supposed to be
* exposed by this view add it into the IN clause below
**/
SELECT
    FromCarrierId = FromC.PSCarrierId
  , FromCarrierTypeId = FromC.CarrierTypeId
  , ToCarrierId = ToC.PSCarrierId
  , ToCarrierTypeId = ToC.CarrierTypeId
  , CM.CreatedUTCDate
  , CM.LastUpdatedUTCDate
  , IsDeprecated = CM.IsDeleted
FROM
    dbo.Carrier FromC
    INNER JOIN dbo.CarrierMapping CM
        ON CM.FromCarrierId = FromC.CarrierId

    INNER JOIN dbo.Carrier ToC
        ON ToC.CarrierId = CM.ToCarrierId
WHERE
    CM.CarrierMappingTypeId IN (
        1, 2, 4, 5, 7, 8, 9, 12, 13, 14, 15, 17, 18
    );
