﻿using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.PACTStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_ref_FinancialStructureAttribute : PlacementStoreTestBase
{
    [Fact]
    public void CreateFinancialStructureAttributeTest()
    {

        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.EpicUS
            });

        dynamic stagedFinancialStructureAttributeRecord = CreateRow(tableName: "PACTStaging.rpt_vwFinancialStructureAttribute", values: new
        {
            FinancialStructureId = 3243467,
            FinancialStructureKey = "Seg 1",
            EpicAgencyCode = "EpicAgency-1",
            DataSourceInstanceId = (int)DataSourceInstance.EpicUS,
            IsDeleted = 0
        });


        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PACTStaging.Load_ref_FinancialStructureAttribute");
        Assert.NotNull(spResult);
        Assert.Equal(expected: 1, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);


        dynamic row = GetResultRow(tableName: "ref.FinancialStructureAttribute", whereClause: $@"PACTFinancialStructureId = {stagedFinancialStructureAttributeRecord.FinancialStructureId}");
        Assert.NotNull(row);
        Assert.Equal(expected: (int)DataSourceInstance.EpicUS, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: stagedFinancialStructureAttributeRecord.FinancialStructureKey, actual: row.FinancialStructureKey);
        Assert.Equal(expected: stagedFinancialStructureAttributeRecord.EpicAgencyCode, actual: row.EpicAgencyCode);

    }
    [Fact]
    public void UpdateFinancialStructureAttributeTest()
    {

        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.eGlobalAustralia
            });
        dynamic refFinancialStructureAttributeRecord = CreateRow(tableName: "ref.FinancialStructureAttribute", values: new
        {
            PACTFinancialStructureId = 3243467,
            eGlobalDEP_CODE = "DEP",

            DataSourceInstanceId = (int)DataSourceInstance.eGlobalAustralia,
            IsDeprecated = 0
        });
        dynamic stagedFinancialStructureAttributeRecord = CreateRow(tableName: "PACTStaging.rpt_vwFinancialStructureAttribute", values: new
        {
            FinancialStructureId = 3243467,
            eGlobalDEP_CODE = "DEP_U",
            DataSourceInstanceId = (int)DataSourceInstance.eGlobalAustralia,
            IsDeleted = 0
        });



        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PACTStaging.Load_ref_FinancialStructureAttribute");
        Assert.NotNull(spResult);
        Assert.Equal(expected: 0, actual: spResult.InsertedCount);
        Assert.Equal(expected: 1, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);


        dynamic row = GetResultRow(tableName: "ref.FinancialStructureAttribute", whereClause: $@"PACTFinancialStructureId = {stagedFinancialStructureAttributeRecord.FinancialStructureId}");
        Assert.NotNull(row);
        Assert.Equal(expected: (int)DataSourceInstance.eGlobalAustralia, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: stagedFinancialStructureAttributeRecord.eGlobalDEP_CODE, actual: row.eGlobalDEP_CODE);

    }

    [Fact]
    public void NoStageDataTest()
    {
        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PACTStaging.Load_ref_FinancialStructureAttribute");

        dynamic row = GetResultRow(tableName: "ref.FinancialStructureAttribute");
        Assert.Null(row);

    }
    public Load_ref_FinancialStructureAttribute(DatabaseFixture fixture) : base(fixture)
    {

    }

}
