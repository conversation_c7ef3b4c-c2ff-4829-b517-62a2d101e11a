/*
Lineage
PS.MarketQuoteResponse.SourceMarketQuoteResponseKey=BPStaging.CarrierResponse.Id
PS.MarketQuoteResponse.MarketResponseId=PS.MarketResponse.MarketResponseId
PS.MarketQuoteResponse.PremiumCurrencyId=BPStaging.CarrierResponse.PremiumCurrencyId
PS.MarketQuoteResponse.Premium=BPStaging.CarrierResponse.Premium
PS.MarketQuoteResponse.OfferedLine=BPStaging.CarrierResponse.OfferedLine
PS.MarketQuoteResponse.PremiumRate=BPStaging.CarrierResponse.PremiumRate
PS.MarketQuoteResponse.CommissionRate=BPStaging.CarrierResponse.CommissionRate
PS.MarketQuoteResponse.Subjectivity=BPStaging.CarrierResponse.Subjectivity
PS.MarketQuoteResponse.OfferedLineRate=BPStaging.CarrierResponse.OfferedLineRate
PS.MarketQuoteResponse.OutcomeStatusId=BPStaging.CarrierResponse.OutcomeStatusId
PS.MarketQuoteResponse.QuotedToLead=BPStaging.CarrierResponse.QuotedToLead
PS.MarketQuoteResponse.OutcomeReasonId=BPStaging.CarrierResponse.ResponseTypeId
PS.MarketQuoteResponse.SourceUpdatedDate=BPStaging.CarrierResponse.ValidFrom
PS.MarketQuoteResponse.IsDeleted=BPStaging.CarrierResponse.IsDeleted
*/
CREATE PROCEDURE BPStaging.Load_PS_MarketQuoteResponseLegacy
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketQuoteResponse';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #RankedCarrierResponse;

    /* If there is more than 1 possible NegotiationMarket record we only want to link to the most appropriate */
    /* Code taken from LoadMarketResponse for the legacy (Brazil) Broking Platform */
    /* More data is taken than required to ensure that it can be easily maintained in the future*/
    SELECT
        CR.Id
      , SourceKey = CONCAT('CARRES|', CR.Id)
      , CR.ResponseTypeId
      , CR.QuotedToLead
      , CR.OutcomeStatusId
      , CR.OutcomeReasonId
      , CR.DeclinationReasonId
      , CR.LayerTypeId
      , CR.PremiumCurrencyId
      , CR.Premium
      , CR.PremiumRate
      , CR.OfferedLine
      , CR.OfferedLineRate
      , CR.CommissionRate
      , LimitCurrencyId = CR.LimitCurrencyTypeId
      , CR.Limit
      , AttachmentPointCurrencyId = CR.AttachmentPointCurrencyTypeId
      , CR.AttachmentPoint
      , CR.Comments
      , CR.Subjectivity
      , SourceUpdatedDate = CR.ValidFrom
      , CR.IsDeleted
      /* Prefer the MarketSelection intiated record where we have both this and a CarrierResponse based record */
      , ROW_NO = ROW_NUMBER() OVER (PARTITION BY CR.Id
                                    ORDER BY
                                        CASE WHEN NM.NegotiationMarketKey LIKE 'MKTSEL|%'
                                                 THEN 1
                                             WHEN NM.NegotiationMarketKey LIKE 'CARRES|%'
                                                 THEN 2
                                             ELSE 999 END ASC
                              )
    INTO #RankedCarrierResponse
    FROM
        BPStaging.CarrierResponse CR
        INNER JOIN dbo.Placement PL
            ON PL.PlacementSystemId = CR.PlacementId
               AND PL.DataSourceInstanceId = 50366

        INNER JOIN PS.NegotiationMarket NM
            ON NM.PlacementId = PL.PlacementId
               AND NM.MarketKindId = CR.MarketKindId
               AND ISNULL(NM.SourceCarrierId, 0) = ISNULL(CR.CarrierId, 0)
               AND ISNULL(NM.FacilityId, 0) = ISNULL(CR.FacilityId, 0)
               AND (
                   NM.NegotiationMarketKey LIKE 'MKTSEL|%'
                   OR NM.NegotiationMarketKey LIKE 'CARRES|%'
               );

    MERGE PS.MarketQuoteResponse T
    USING (
        SELECT
            CR.Id
          , CR.SourceKey
          , DataSourceInstanceId = 50366
          , MR.MarketResponseId
          , CR.PremiumCurrencyId
          , CR.Premium
          , CR.OfferedLine
          , CR.PremiumRate
          , CR.CommissionRate
          , CR.Subjectivity
          , IsPremiumRatePerMille = NULL
          , CR.OfferedLineRate
          , CR.OutcomeStatusId
          , CR.QuotedToLead
          , OutcomeReasonId = CR.ResponseTypeId
          , AdditionalPolicyCostCurrencyId = NULL
          , AdditionalPolicyCost = NULL
          , FrontingCarrierId = NULL
          , NetPremium = NULL
          , CommissionCurrencyId = NULL
          , Commission = NULL
          , QuoteExpiryDate = NULL
          , BindRequestedDate = NULL
          , BoundDate = NULL
          , SignedLine = NULL
          , SignedLineRate = NULL
          , TriaRequired = NULL
          , TriaIncluded = NULL
          , TriaPremium = NULL
          , TriaPremiumCurrencyId = NULL
          , DeductibleCurrencyId = NULL
          , Deductible = NULL
          , IsOverride = 0
          , AdditionalDetails = NULL
          , PrimaryRate = NULL
          , PrimaryExposureValue = NULL
          , CR.SourceUpdatedDate
          , CR.IsDeleted
        FROM
            #RankedCarrierResponse CR
            INNER JOIN PS.MarketResponse MR
                ON MR.MarketResponseKey = CONCAT('CARRES|', CR.Id)
                   AND MR.DataSourceInstanceId = 50366
        WHERE
            CR.ROW_NO = 1
    ) S
    ON S.SourceKey = T.SourceMarketQuoteResponseKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 SourceMarketQuoteResponseKey
               , DataSourceInstanceId
               , MarketResponseId
               , PremiumCurrencyId
               , Premium
               , OfferedLine
               , PremiumRate
               , CommissionRate
               , Subjectivity
               , IsPremiumRatePerMille
               , OfferedLineRate
               , OutcomeStatusId
               , QuotedToLead
               , OutcomeReasonId
               , AdditionalPolicyCostCurrencyId
               , AdditionalPolicyCost
               , FrontingCarrierId
               , NetPremium
               , CommissionCurrencyId
               , Commission
               , QuoteExpiryDate
               , BindRequestedDate
               , BoundDate
               , SignedLine
               , SignedLineRate
               , TriaRequired
               , TriaIncluded
               , TriaPremium
               , TriaPremiumCurrencyId
               , DeductibleCurrencyId
               , Deductible
               , IsOverride
               , AdditionalDetails
               , PrimaryRate
               , PrimaryExposureValue
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.SourceKey
                   , S.DataSourceInstanceId
                   , S.MarketResponseId
                   , S.PremiumCurrencyId
                   , S.Premium
                   , S.OfferedLine
                   , S.PremiumRate
                   , S.CommissionRate
                   , S.Subjectivity
                   , S.IsPremiumRatePerMille
                   , S.OfferedLineRate
                   , S.OutcomeStatusId
                   , S.QuotedToLead
                   , S.OutcomeReasonId
                   , S.AdditionalPolicyCostCurrencyId
                   , S.AdditionalPolicyCost
                   , S.FrontingCarrierId
                   , S.NetPremium
                   , S.CommissionCurrencyId
                   , S.Commission
                   , S.QuoteExpiryDate
                   , S.BindRequestedDate
                   , S.BoundDate
                   , S.SignedLine
                   , S.SignedLineRate
                   , S.TriaRequired
                   , S.TriaIncluded
                   , S.TriaPremium
                   , S.TriaPremiumCurrencyId
                   , S.DeductibleCurrencyId
                   , S.Deductible
                   , S.IsOverride
                   , S.AdditionalDetails
                   , S.PrimaryRate
                   , S.PrimaryExposureValue
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.MarketResponseId
                               , S.PremiumCurrencyId
                               , S.Premium
                               , S.OfferedLine
                               , S.PremiumRate
                               , S.CommissionRate
                               , S.Subjectivity
                               , S.IsPremiumRatePerMille
                               , S.OfferedLineRate
                               , S.OutcomeStatusId
                               , S.QuotedToLead
                               , S.OutcomeReasonId
                               , S.AdditionalPolicyCostCurrencyId
                               , S.AdditionalPolicyCost
                               , S.FrontingCarrierId
                               , S.NetPremium
                               , S.CommissionCurrencyId
                               , S.Commission
                               , S.QuoteExpiryDate
                               , S.BindRequestedDate
                               , S.BoundDate
                               , S.SignedLine
                               , S.SignedLineRate
                               , S.TriaRequired
                               , S.TriaIncluded
                               , S.TriaPremium
                               , S.TriaPremiumCurrencyId
                               , S.DeductibleCurrencyId
                               , S.Deductible
                               , S.IsOverride
                               , S.AdditionalDetails
                               , S.PrimaryRate
                               , S.PrimaryExposureValue
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.MarketResponseId
                               , T.PremiumCurrencyId
                               , T.Premium
                               , T.OfferedLine
                               , T.PremiumRate
                               , T.CommissionRate
                               , T.Subjectivity
                               , T.IsPremiumRatePerMille
                               , T.OfferedLineRate
                               , T.OutcomeStatusId
                               , T.QuotedToLead
                               , T.OutcomeReasonId
                               , T.AdditionalPolicyCostCurrencyId
                               , T.AdditionalPolicyCost
                               , T.FrontingCarrierId
                               , T.NetPremium
                               , T.CommissionCurrencyId
                               , T.Commission
                               , T.QuoteExpiryDate
                               , T.BindRequestedDate
                               , T.BoundDate
                               , T.SignedLine
                               , T.SignedLineRate
                               , T.TriaRequired
                               , T.TriaIncluded
                               , T.TriaPremium
                               , T.TriaPremiumCurrencyId
                               , T.DeductibleCurrencyId
                               , T.Deductible
                               , T.IsOverride
                               , T.AdditionalDetails
                               , T.PrimaryRate
                               , T.PrimaryExposureValue
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.MarketResponseId = S.MarketResponseId
               , T.PremiumCurrencyId = S.PremiumCurrencyId
               , T.Premium = S.Premium
               , T.OfferedLine = S.OfferedLine
               , T.PremiumRate = S.PremiumRate
               , T.CommissionRate = S.CommissionRate
               , T.Subjectivity = S.Subjectivity
               , T.IsPremiumRatePerMille = S.IsPremiumRatePerMille
               , T.OfferedLineRate = S.OfferedLineRate
               , T.OutcomeStatusId = S.OutcomeStatusId
               , T.QuotedToLead = S.QuotedToLead
               , T.OutcomeReasonId = S.OutcomeReasonId
               , T.AdditionalPolicyCostCurrencyId = S.AdditionalPolicyCostCurrencyId
               , T.AdditionalPolicyCost = S.AdditionalPolicyCost
               , T.FrontingCarrierId = S.FrontingCarrierId
               , T.NetPremium = S.NetPremium
               , T.CommissionCurrencyId = S.CommissionCurrencyId
               , T.Commission = S.Commission
               , T.QuoteExpiryDate = S.QuoteExpiryDate
               , T.BindRequestedDate = S.BindRequestedDate
               , T.BoundDate = S.BoundDate
               , T.SignedLine = S.SignedLine
               , T.SignedLineRate = S.SignedLineRate
               , T.TriaRequired = S.TriaRequired
               , T.TriaIncluded = S.TriaIncluded
               , T.TriaPremium = S.TriaPremium
               , T.TriaPremiumCurrencyId = S.TriaPremiumCurrencyId
               , T.DeductibleCurrencyId = S.DeductibleCurrencyId
               , T.Deductible = S.Deductible
               , T.IsOverride = S.IsOverride
               , T.AdditionalDetails = S.AdditionalDetails
               , T.PrimaryRate = S.PrimaryRate
               , T.PrimaryExposureValue = S.PrimaryExposureValue
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    /*Not attempting to do logical deletion of this as it could potentially remove the records from the other load*/
    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;