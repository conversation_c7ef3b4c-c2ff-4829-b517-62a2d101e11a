/*
Lineage
dbo.PlacementSystemAudit.Action=BPStaging.PlacementSystemAudit.EventType
dbo.PlacementSystemAudit.ModifiedById=BPStaging.PlacementSystemAudit.ModifiedById
dbo.PlacementSystemAudit.TableId=BPStaging.PlacementSystemAudit.TableId
dbo.PlacementSystemAudit.TableKey=BPStaging.PlacementSystemAudit.TableKey
dbo.PlacementSystemAudit.UserId=dbo.PlacementSystemUser.UserId
dbo.PlacementSystemAudit.UserId=BPStaging.PlacementSystemAudit.UserId
dbo.PlacementSystemAudit.ModifiedDate=BPStaging.PlacementSystemAudit.DateOfEvent
dbo.PlacementSystemAudit.ModifiedBy=BPStaging.PlacementSystemAudit.UserPrincipalName
dbo.PlacementSystemAudit.ModifiedBy=BPStaging.PlacementSystemAudit.UserId
dbo.PlacementSystemAudit.PlacementId=dbo.Placement.PlacementId
dbo.PlacementSystemAudit.Description=BPStaging.PlacementSystemAudit.Description
dbo.PlacementSystemAudit.Detail=BPStaging.PlacementSystemAudit.Detail
dbo.PlacementSystemAudit.GroupingSection=BPStaging.PlacementSystemAudit.GroupingSection
dbo.PlacementSystemAudit.GroupingSectionType=BPStaging.PlacementSystemAudit.GroupingSectionType
dbo.PlacementSystemAudit.PlacementSystemId=BPStaging.PlacementSystemAudit.PlacementId
*/
CREATE PROCEDURE BPStaging.LoadPlacementSystemAudit
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementSystemAudit';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.PlacementSystemAudit
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE dbo.PlacementSystemAudit T
    USING (
        SELECT
            Action = PSA.EventType
          , PSA.ModifiedById
          , PSA.TableId
          , PSA.TableKey
          , DataSourceInstanceId = 50366 --Broking Platform
          , UserId = CASE WHEN PSA.UserPrincipalName LIKE 'crbbro-bkp-%-em20-fa%'
                              THEN (
                              SELECT UserId FROM dbo.PlacementSystemUser WHERE UserPrincipalName = 'system'
                          )
                          ELSE PSA.UserId END
          , ModifiedDate = PSA.DateOfEvent
          , ModifiedBy = CASE WHEN PSA.UserPrincipalName LIKE 'crbbro-bkp-%-em20-fa%'
                                  THEN 'system'
                              WHEN PSA.UserId IS NULL
                                  THEN 'Unknown'
                              ELSE ISNULL(PSA.UserPrincipalName, 'Unknown') END
          , PL.PlacementId
          , PSA.Description
          , PSA.Detail
          , PSA.GroupingSection
          , PSA.GroupingSectionType
          , PlacementSystemId = PSA.PlacementId
        FROM
            BPStaging.PlacementSystemAudit PSA
            INNER JOIN dbo.Placement PL
                ON PL.PlacementSystemId = PSA.PlacementId
                   AND PL.DataSourceInstanceId = 50366
    ) S
    ON S.ModifiedById = T.ModifiedById
       AND S.Action = T.Action
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 Action
               , ModifiedById
               , TableId
               , TableKey
               , DataSourceInstanceId
               , UserId
               , ModifiedDate
               , ModifiedBy
               , PlacementId
               , Description
               , Detail
               , GroupingSection
               , GroupingSectionType
               , PlacementSystemId
             )
             VALUES
                 (
                     S.Action
                   , S.ModifiedById
                   , S.TableId
                   , S.TableKey
                   , S.DataSourceInstanceId
                   , S.UserId
                   , S.ModifiedDate
                   , S.ModifiedBy
                   , S.PlacementId
                   , S.Description
                   , S.Detail
                   , S.GroupingSection
                   , S.GroupingSectionType
                   , S.PlacementSystemId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.Action
      , S.ModifiedById
      , S.TableId
      , S.TableKey
      , S.DataSourceInstanceId
      , S.UserId
      , S.ModifiedDate
      , S.ModifiedBy
      , S.PlacementId
      , S.Description
      , S.Detail
      , S.GroupingSection
      , S.GroupingSectionType
      , S.PlacementSystemId
    INTERSECT
    SELECT
        T.Action
      , T.ModifiedById
      , T.TableId
      , T.TableKey
      , T.DataSourceInstanceId
      , T.UserId
      , T.ModifiedDate
      , T.ModifiedBy
      , T.PlacementId
      , T.Description
      , T.Detail
      , T.GroupingSection
      , T.GroupingSectionType
      , T.PlacementSystemId
)
        THEN UPDATE SET
                 T.Action = S.Action
               , T.TableId = S.TableId
               , T.TableKey = S.TableKey
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.UserId = S.UserId
               , T.ModifiedDate = S.ModifiedDate
               , T.ModifiedBy = S.ModifiedBy
               , T.PlacementId = S.PlacementId
               , T.Description = S.Description
               , T.Detail = S.Detail
               , T.GroupingSection = S.GroupingSection
               , T.GroupingSectionType = S.GroupingSectionType
               , T.PlacementSystemId = S.PlacementSystemId
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    --> Remove all audit data older than 3 years. PBI-174578
    DELETE
    PSA
    FROM
        dbo.PlacementSystemAudit PSA
        INNER JOIN dbo.Placement PL
            ON PSA.PlacementId = PL.PlacementId
    WHERE
        PL.InceptionDate < DATEADD(YY, -3, GETDATE());

    SELECT @DeletedCount = @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN;