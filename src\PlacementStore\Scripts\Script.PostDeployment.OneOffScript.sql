/*
    Post-Deployment Script.

    When this is cleared down for a new release don't forget about <PERSON><PERSON><PERSON>.PreDeployment.OneOffScript which
    may also have scripts for the previous release.

*/

/*
    ***** DO NOT DELETE *****
    Template for script part.

    SET @scriptName = '<< Function name >>';
    IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
    BEGIN
        PRINT CONCAT(@scriptName, '-', @scriptType);

        -- SQL

        INSERT INTO devops.ScriptDeploymentRegister
            (ScriptName, ScriptType)
        VALUES
            (@scriptName, @scriptType);
    END;
*/

DECLARE @scriptName NVARCHAR(100);
DECLARE @scriptType CHAR(4) = 'Post';

PRINT N'Post-deployment One-Off scripts (if any):';

SET @scriptName = N'326632-Populate-new-BP-MarketKind-attributes';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN

    /*
    Populate the new MarketKind attributes to allow removal of the old BPStaging tables and data
    The MarketKind load from BP will need to be updated to allow for any future updates to these values once the export view has been extended
    */
    PRINT CONCAT(@scriptName, '-', @scriptType);

    IF OBJECT_ID('BP.MarketKind') IS NOT NULL
    BEGIN
        DROP TABLE IF EXISTS #MarketKind;

        CREATE TABLE #MarketKind (
            Id                INT NOT NULL
          , IsChildMarketKind BIT NOT NULL
          , IsThirdParty      BIT NOT NULL
          , CanDistribute     BIT NOT NULL
        );

        INSERT INTO #MarketKind
            (Id, IsChildMarketKind, IsThirdParty, CanDistribute)
        VALUES
            (1, 0, 0, 1)
          , (2, 0, 0, 0)
          , (3, 1, 0, 1)
          , (4, 0, 0, 0)
          , (5, 1, 0, 1)
          , (6, 0, 1, 1)
          , (7, 0, 1, 1)
          , (8, 0, 1, 1)
          , (9, 0, 1, 1)
          , (10, 0, 1, 1)
          , (11, 1, 0, 0)
          , (12, 0, 1, 1);

        UPDATE mk
        SET IsChildMarketKind = tmk.IsChildMarketKind, IsThirdParty = tmk.IsThirdParty, CanDistribute = tmk.CanDistribute
        FROM
            BP.MarketKind mk
            INNER JOIN #MarketKind tmk
                ON tmk.Id = mk.Id;

        INSERT INTO devops.ScriptDeploymentRegister
            (ScriptName, ScriptType)
        VALUES
            (@scriptName, @scriptType);
    END;
END;

SET @scriptName = N'326632-BP-SubmissionContainerMarket-SourceCreatedDate';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    IF OBJECT_ID('BPStaging.SubmissionMarketDeprecated') IS NOT NULL
    BEGIN
        UPDATE scm
        SET scm.SubmissionDate = smd.SubmissionDate
        FROM
            BP.SubmissionContainerMarket scm
            INNER JOIN BPStaging.SubmissionMarketDeprecated smd
                ON smd.SubmissionContainerMarketId = scm.Id;

        INSERT INTO devops.ScriptDeploymentRegister
            (ScriptName, ScriptType)
        VALUES
            (@scriptName, @scriptType);
    END;
END;

SET @scriptName = N'320193-NewPASPolicyAttributes';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'PASStaging.Load_PAS_PolicyAttribute', @DateToUse = '1 Jan 1900', @IsActive = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'334695-LoadElementTimelineUpdate-Post-Deployment';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    EXEC Support.UpdateProcessIsDisabled @ProcessName = 'BPStaging.Load_PS_SpecificationAttribute', @IsDisabled = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'320159-PostDeployment-StopUsingPartyAttributes';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'PASStaging.rpt_vwPartyAttribute', @DateToUse = '1 Jan 1900', @IsActive = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'337654-PostDeployment-MarketQuoteResponse-FullLoad';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.MarketQuoteResponse', @DateToUse = '1 Jan 1900', @IsActive = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'321069-COLProductAttribute';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Delete COLProductCode data from dbo.ProductAttribute
    DELETE FROM
           dbo.ProductAttribute
    WHERE
        DataSourceInstanceId = 50003
        AND ObjectType = 'ColProductCode';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'322492---PIG-remove-inceptiondate-filter-to-processed-all-authorized-policies-WithoutOrgTable';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    -- Remove the filter on InceptionDate to allow all policies to be processed when authorized
    -- This is to stop tasks created for policies that are already authorised being sent. Otherwise, we will send tasks for every authorised policy in Eclipse on the first run.
    -- The script inserts a row into Task.PolicyAudit for each policy that is already authorised for PIG PolicyConfig and Event combination.
    PRINT CONCAT(@scriptName, '-', @scriptType);

    DECLARE @policyConfigId UNIQUEIDENTIFIER;
    DECLARE @eventId UNIQUEIDENTIFIER = 'D04F42C8-1C5D-E911-9130-005056ADC758'; -- PostBind
    DECLARE @isValid BIT = 0;

    DROP TABLE IF EXISTS #Policy;

    CREATE TABLE #Policy (
        Id             UNIQUEIDENTIFIER
            DEFAULT NEWSEQUENTIALID()
      , EventId        UNIQUEIDENTIFIER
      , PolicyConfigId UNIQUEIDENTIFIER
      , PolicyId       BIGINT
      , IsValid        BIT
    );

    -- Make sure PolicyId + PolicyConfigId + EventId is unique
    CREATE UNIQUE NONCLUSTERED INDEX IXU_Policy_PolicyIdPolicyConfigIdEventId
    ON #Policy
        (
        PolicyId ASC
      , PolicyConfigId ASC
      , EventId ASC
        );

    SET @policyConfigId = 'EE863201-EC53-4B4E-B554-E4A0950C5DC7'; -- Financial Solutions (Collection)

    INSERT INTO #Policy
        (PolicyId, PolicyConfigId, EventId, IsValid)
    SELECT DISTINCT p.PolicyId, @policyConfigId, @eventId, @isValid
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = p.PolicyStatusId
               AND ps.DataSourceInstanceId = p.DataSourceInstanceId

        INNER JOIN dbo.PolicyOrganisation po
            ON po.PolicyId = p.PolicyId

        INNER JOIN APIv1.OrganisationHierarchyTable oht
            ON oht.OrganisationId = po.OrganisationId
               AND oht.OrganisationName IN (
        'Singapore', 'European Team CPRI for Corporates', 'Bank 1', 'CPRI for Corporates', 'International', 'FI''s', 'European Team FI''s', 'Lenders Advisory (Singapore)', 'Bank 1 CPRI for Corporates', 'International CPRI for Corporates', 'Bank 2')
               AND oht.OrgLevel1 IN (
        'Willis (Singapore) Pte Ltd (Reinsurance)', 'WTW Legal Entity', 'Willis Towers Watson SA NV', 'Willis Limited', 'Willis Towers Watson France', 'Gras Savoye S.A', 'Willis Canada Inc.', 'Willis Towers Watson Northeast, Inc', 'Willis (Singapore) Pte Ltd (Direct)')
    WHERE
        p.DataSourceInstanceId = 50000
        AND oht.LevelNum = 5 -- Team
        AND ps.PolicyStatusId = 2; -- Authorised

    SET @policyConfigId = 'F229AE7A-4F65-410E-BD62-AA94623C38F3'; -- Energy G360 Upstream (Collection)

    INSERT INTO #Policy
        (PolicyId, PolicyConfigId, EventId, IsValid)
    SELECT DISTINCT p.PolicyId, @policyConfigId, @eventId, @isValid
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = p.PolicyStatusId
               AND ps.DataSourceInstanceId = p.DataSourceInstanceId

        INNER JOIN dbo.PolicyOrganisation po
            ON po.PolicyId = p.PolicyId

        INNER JOIN APIv1.OrganisationHierarchyTable oht
            ON oht.OrganisationId = po.OrganisationId
               AND oht.OrganisationName IN (
        'Upstream - Western Europe UK', 'Upstream - CEMEA Russia', 'Upstream - North America', 'Downstream - North America', 'Upstream - CEMEA Middle East', 'Downstream - Casualty', 'Upstream - Western Europe Europe', 'Upstream - Asia', 'Upstream - LATAM', 'Upstream - CEMEA Africa')
               AND oht.OrgLevel1 IN (
        'Willis Limited', 'Willis Towers Watson SA NV')
    WHERE
        p.DataSourceInstanceId = 50000
        AND oht.LevelNum = 5 -- Team
        AND ps.PolicyStatusId = 2; -- Authorised

    SET @policyConfigId = '43CB74C4-0F71-4DA9-928E-54EA48B176F2'; -- FAJS (Collection)

    INSERT INTO #Policy
        (PolicyId, PolicyConfigId, EventId, IsValid)
    SELECT DISTINCT p.PolicyId, @policyConfigId, @eventId, @isValid
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = p.PolicyStatusId
               AND ps.DataSourceInstanceId = p.DataSourceInstanceId

        INNER JOIN dbo.PolicyOrganisation po
            ON po.PolicyId = p.PolicyId

        INNER JOIN APIv1.OrganisationHierarchyTable oht
            ON oht.OrganisationId = po.OrganisationId
               AND oht.OrganisationName IN (
        'New Jersey – Specie', 'FAJS ROWJB', 'Maryland - Fine Arts', 'FAJS Cash In Transit (Ipswich)', 'FAJS Cash In Transit', 'FAJS Private Clients MRC', 'New York – Jewelry', 'FAJS Fine Art', 'FAJS General Specie (Ipswich)', 'FAJS Private Clients', 'FAJS Fine Art (Ipswich)', 'FAJS General Specie', 'FAJS USJB')
               AND oht.OrgLevel1 IN (
        'Willis Limited', 'Willis Towers Watson SA NV')
    WHERE
        p.DataSourceInstanceId = 50000
        AND oht.LevelNum = 5 -- Team
        AND ps.PolicyStatusId = 2; -- Authorised

    SET @policyConfigId = 'EC2A3ED9-612F-4D21-83DB-2078C05CBE2C'; -- Terrorism (Collection)

    INSERT INTO #Policy
        (PolicyId, PolicyConfigId, EventId, IsValid)
    SELECT DISTINCT p.PolicyId, @policyConfigId, @eventId, @isValid
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = p.PolicyStatusId
               AND ps.DataSourceInstanceId = p.DataSourceInstanceId

        INNER JOIN dbo.PolicyOrganisation po
            ON po.PolicyId = p.PolicyId

        INNER JOIN APIv1.OrganisationHierarchyTable oht
            ON oht.OrganisationId = po.OrganisationId
               AND oht.OrganisationName IN (
        'Terrorism', 'Terrorism - DIFC')
               AND oht.OrgLevel1 IN (
        'Willis Limited', 'Willis Towers Watson SA NV')
    WHERE
        p.DataSourceInstanceId = 50000
        AND oht.LevelNum = 5 -- Team
        AND ps.PolicyStatusId = 2; -- Authorised

    SET @policyConfigId = 'E1B11AB2-605C-4F98-970B-021001BD019B'; -- Marine (Collection)

    INSERT INTO #Policy
        (PolicyId, PolicyConfigId, EventId, IsValid)
    SELECT DISTINCT p.PolicyId, @policyConfigId, @eventId, @isValid
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = p.PolicyStatusId
               AND ps.DataSourceInstanceId = p.DataSourceInstanceId

        INNER JOIN dbo.PolicyOrganisation po
            ON po.PolicyId = p.PolicyId

        INNER JOIN APIv1.OrganisationHierarchyTable oht
            ON oht.OrganisationId = po.OrganisationId
               AND oht.OrganisationName IN (
        'ITL North America', 'Russia (Willis Ltd) – Marine', 'ITL Europe Italy', 'ITL Europe Iberia', 'ITL RoW Apex', 'ITL Europe Nordic', 'ITL Row India', 'ITL UK UK', 'ITL Europe Mediterranean', 'ITL Freight & Logistics', 'ITL RoW Africa/Middle East', 'ITL RoW Japan', 'ITL Europe Eastern Europe', 'ITL Americas Latin America', 'ITL RoW Australia/South Africa', 'ITL Europe Ger/Aus/Switz')
               AND oht.OrgLevel1 IN (
        'Willis Limited', 'Willis Towers Watson SA NV')
    WHERE
        p.DataSourceInstanceId = 50000
        AND oht.LevelNum = 5 -- Team
        AND ps.PolicyStatusId = 2; -- Authorised

    INSERT INTO Task.PolicyAudit
        (PolicyId, PolicyConfigId, EventId, IsValid)
    SELECT p.PolicyId, p.PolicyConfigId, p.EventId, p.IsValid
    FROM
        #Policy p
        LEFT JOIN Task.PolicyAudit pa
            ON p.PolicyId = pa.PolicyId
               AND p.PolicyConfigId = pa.PolicyConfigId
               AND p.EventId = pa.EventId
    WHERE
        pa.Id IS NULL;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; --322492---PIG-remove-inceptiondate-filter-to-processed-all-authorized-policies-WithoutOrgTable

SET @scriptName = N'169458-AddPASPolicySectionCode';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Adding the PolicySectionCode to the PAS.PolicySection table therefore a full load is required
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'PASStaging.rpt_vwPolicySection', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'342844-ElementTagSummaryImprovedByIncrementalLoading';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    /* Deprecating old audit columns for new ones. Need to copy the dates over.*/
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- SQL
    UPDATE dbo.ElementTagCache
    SET ETLCreatedDate = CreatedUTCDate, ETLUpdatedDate = LastUpdatedUTCDate
    WHERE
        1 = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 342844-ElementTagSummaryImprovedByIncrementalLoading

SET @scriptName = N'45526-Facility';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- SQL
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'FMATemp.StageFacility', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 45526-Facility

SET @scriptName = N'328126-PASPartyRoleMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- SQL
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'PASStaging.rpt_vwPartyRole', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328126-PASPartyRoleMigration

SET @scriptName = N'217238-ElementType';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    IF OBJECT_ID('dbo.ElementTypeDeprecated') IS NOT NULL
    BEGIN
        DELETE FROM
               ref.ElementType;

        INSERT INTO ref.ElementType
            (ElementTypeId, ElementTypeKey, ElementType, IsDeleted, ETLCreatedDate, ETLUpdatedDate, DataSourceInstanceId, SourceUpdatedDate)
        SELECT ElementTypeId, ElementTypeKey, ElementType, IsDeleted, ETLCreatedDate, ETLUpdatedDate, DataSourceInstanceId, SourceUpdatedDate
        FROM
            dbo.ElementTypeDeprecated;
    END;

    EXEC support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.ElementType', @DateToUse = '1900-01-01', @IsActive = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'328124-PASServicingRoleMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the ServcingRoleKey and DataSourceInstanceId on dbo.PlacementWorker
    UPDATE pw
    SET pw.ServicingRoleKey = passr.ServicingRoleKey, pw.DataSourceInstanceId = passr.DataSourceInstanceId
    FROM
        dbo.PlacementWorker pw
        INNER JOIN dbo.ServicingRole sr
            ON sr.ServicingRoleId = pw.ServicingRoleId

        INNER JOIN PAS.ServicingRole passr
            ON passr.PASServicingRoleId = sr.PACTServicingRoleId;

    -- Populating the ServcingRoleKey and DataSourceInstanceId on dbo.PolicyWorker
    UPDATE pw
    SET pw.ServicingRoleKey = passr.ServicingRoleKey, pw.DataSourceInstanceId = p.DataSourceInstanceId
    FROM
        dbo.PolicyWorker pw
        INNER JOIN dbo.Policy p
            ON p.PolicyId = pw.PolicyId

        INNER JOIN dbo.ServicingRole sr
            ON sr.ServicingRoleId = pw.ServicingRoleId

        INNER JOIN PAS.ServicingRole passr
            ON passr.PASServicingRoleId = sr.PACTServicingRoleId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328124-PASServicingRoleMigration

SET @scriptName = N'328130-PASPolicySectionStatusMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the PolicySectionStatusKey on dbo.PolicySection
    UPDATE ps
    SET ps.PolicySectionStatusKey = paspss.PolicySectionStatusKey
    FROM
        dbo.PolicySection ps
        INNER JOIN dbo.PolicySectionStatus pss
            ON pss.PolicySectionStatusId = ps.PolicySectionStatusId

        INNER JOIN PAS.PolicySectionStatus paspss
            ON paspss.PASPolicySectionStatusId = pss.PACTPolicySectionStatusId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'328127-PASGeographyMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the GeographyKey on dbo.Party
    UPDATE p
    SET p.GeographyKey = pasg.GeographyKey
    FROM
        dbo.Party p
        INNER JOIN dbo.Geography g
            ON g.GeographyId = p.GeographyId

        INNER JOIN PAS.Geography pasg
            ON pasg.PASGeographyId = g.PACTGeographyId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328127-PASGeographyMigration

SET @scriptName = N'347042-HandlingInvalidStatus';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Need to refresh from BP for where we are now using the column.
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.ExpiringResponseElement', @DateToUse = '1900-01-01';

    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.MarketResponse', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 3347042-HandlingInvalidStatus

SET @scriptName = N'328120-PASInsuranceTypeMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Updating the InsuranceTypeKey on ref.InsuranceType
    UPDATE rit
    SET rit.InsuranceTypeKey = pasit.InsuranceTypeKey
        , rit.PASInsuranceTypeId = rit.PACTInsuranceTypeId
    FROM
        ref.InsuranceType rit
        INNER JOIN PAS.InsuranceType pasit
            ON pasit.PASInsuranceTypeId = rit.PACTInsuranceTypeId
    WHERE
        rit.DataSourceInstanceId = pasit.DataSourceInstanceId
		AND rit.InsuranceTypeKey <> pasit.InsuranceTypeKey;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 3328120-PASInsuranceTypeMigration

SET @scriptName = N'328121-PASOpportunityTypeMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Need to refresh the ref.OpportunityType data to populate the new PASOpportunityType field.
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'PASStaging.rpt_vwOpportunityType', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328121-PASOpportunityTypeMigration

SET @scriptName = N'328128-PASPolicyTypeMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the PolicyTypeKey on dbo.Policy
    UPDATE p
    SET p.PolicyTypeKey = paspt.PolicyTypeKey
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyType pt
            ON pt.PolicyTypeId = p.PolicyTypeId

        INNER JOIN PAS.PolicyType paspt
            ON paspt.PASPolicyTypeId = pt.PACTPolicyTypeId;

    -- Populating the PolicyTypeKey on dbo.PolicyMarket
    UPDATE pm
    SET pm.PolicyTypeKey = paspt.PolicyTypeKey
    FROM
        dbo.PolicyMarket pm
        INNER JOIN dbo.PolicyType pt
            ON pt.PolicyTypeId = pm.PolicyTypeId

        INNER JOIN PAS.PolicyType paspt
            ON paspt.PASPolicyTypeId = pt.PACTPolicyTypeId;

END; -- 328128-PASPolicyTypeMigration

SET @scriptName = N'328125-PASPolicyStatusMigration';

IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the PolicyStatusKey on dbo.Policy
    UPDATE p
    SET p.PolicyStatusKey = pasps.PolicyStatusKey
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = p.PolicyStatusId

        INNER JOIN PAS.PolicyStatus pasps
            ON pasps.PASPolicyStatusId = ps.PACTPolicyStatusId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328125-PASPolicyStatusMigration

PRINT N'End of post deployment One-Off scripts.';