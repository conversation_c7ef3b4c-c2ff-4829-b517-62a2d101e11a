/*
Lineage
ref.RiskCode.RiskCodeId=BPStaging.RiskCode.Id
ref.RiskCode.RiskCodeKey=BPStaging.RiskCode.LabelTranslationKey
ref.RiskCode.RiskCode=BPStaging.RiskCode.Text
ref.RiskCode.SourceUpdatedDate=BPStaging.RiskCode.ValidFrom
ref.RiskCode.IsDeprecated=BPStaging.RiskCode.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadRiskCode
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.RiskCode';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.RiskCode T
    USING (
        SELECT
            RiskCodeId = Id
          , DataSourceInstanceId = 50366
          , RiskCodeKey = LabelTranslationKey
          , RiskCode = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.RiskCode
    ) S
    ON T.RiskCodeId = S.RiskCodeId
    WHEN NOT MATCHED
        THEN INSERT (
                 RiskCodeId
               , DataSourceInstanceId
               , RiskCodeKey
               , RiskCode
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.RiskCodeId
                   , S.DataSourceInstanceId
                   , S.RiskCodeKey
                   , S.RiskCode
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.RiskCodeId
                               , T.DataSourceInstanceId
                               , T.RiskCodeKey
                               , T.RiskCode
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.RiskCodeId
                               , S.DataSourceInstanceId
                               , S.RiskCodeKey
                               , S.RiskCode
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.RiskCodeId = S.RiskCodeId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RiskCodeKey = S.RiskCodeKey
               , T.RiskCode = S.RiskCode
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
