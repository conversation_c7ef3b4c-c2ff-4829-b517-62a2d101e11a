/*
Lineage
ref.CancellationReason.CancellationReasonId=BPStaging.PlacementCancellationReason.Id
ref.CancellationReason.CancellationReasonKey=BPStaging.PlacementCancellationReason.Id
ref.CancellationReason.CancellationReason=BPStaging.PlacementCancellationReason.LabelTranslationDescription
ref.CancellationReason.IsDeprecated=BPStaging.PlacementCancellationReason.IsDeprecated
ref.CancellationReason.SourceUpdatedDate=BPStaging.PlacementCancellationReason.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_ref_CancellationReason
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.CancellationReason';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    IF EXISTS (SELECT * FROM BPStaging.PlacementCancellationReason)
    BEGIN
        MERGE ref.CancellationReason T
        USING (
            SELECT
                CancellationReasonId = Id
              , DataSourceInstanceId = 50366
              , CancellationReasonKey = Id
              , CancellationReason = LabelTranslationDescription
              , IsDeprecated
              , SourceUpdatedDate = ValidFrom
            FROM
                BPStaging.PlacementCancellationReason
        ) S
        ON S.CancellationReasonId = T.CancellationReasonId
        WHEN NOT MATCHED
            THEN INSERT (
                     CancellationReasonId
                   , DataSourceInstanceId
                   , CancellationReasonKey
                   , CancellationReason
                   , IsDeprecated
                   , SourceUpdatedDate
                 )
                 VALUES
                     (
                         S.CancellationReasonId
                       , S.DataSourceInstanceId
                       , S.CancellationReasonKey
                       , S.CancellationReason
                       , S.IsDeprecated
                       , S.SourceUpdatedDate
                     )
        WHEN MATCHED AND NOT EXISTS (
        SELECT
            S.DataSourceInstanceId
          , S.CancellationReasonKey
          , S.CancellationReason
          , S.IsDeprecated
          , S.SourceUpdatedDate
        INTERSECT
        SELECT
            T.DataSourceInstanceId
          , T.CancellationReasonKey
          , T.CancellationReason
          , T.IsDeprecated
          , T.SourceUpdatedDate
    )
            THEN UPDATE SET
                     T.DataSourceInstanceId = S.DataSourceInstanceId
                   , T.CancellationReasonKey = S.CancellationReasonKey
                   , T.CancellationReason = S.CancellationReason
                   , T.IsDeprecated = S.IsDeprecated
                   , T.SourceUpdatedDate = S.SourceUpdatedDate
                   , T.ETLUpdatedDate = GETUTCDATE()
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;