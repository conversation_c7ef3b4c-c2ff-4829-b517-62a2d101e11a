/*
Lineage
PS.ClientProposalCoverageNotice.Id=BPStaging.ClientProposalCoverageNotice.Id
PS.ClientProposalCoverageNotice.ClientProposalId=BPStaging.ClientProposalCoverageNotice.ClientProposalId
PS.ClientProposalCoverageNotice.Header=BPStaging.ClientProposalCoverageNotice.Header
PS.ClientProposalCoverageNotice.Wording=BPStaging.ClientProposalCoverageNotice.Wording
PS.ClientProposalCoverageNotice.CanEdit=BPStaging.ClientProposalCoverageNotice.CanEdit
PS.ClientProposalCoverageNotice.TagName=BPStaging.ClientProposalCoverageNotice.TagName
PS.ClientProposalCoverageNotice.TagValue=BPStaging.ClientProposalCoverageNotice.TagValue
PS.ClientProposalCoverageNotice.IsSelected=BPStaging.ClientProposalCoverageNotice.IsSelected
PS.ClientProposalCoverageNotice.DisplayIndex=BPStaging.ClientProposalCoverageNotice.DisplayIndex
PS.ClientProposalCoverageNotice.CoverageNoticeId=BPStaging.ClientProposalCoverageNotice.CoverageNoticeId
PS.ClientProposalCoverageNotice.IsMandatory=BPStaging.ClientProposalCoverageNotice.IsMandatory
PS.ClientProposalCoverageNotice.SourceUpdatedDate=BPStaging.ClientProposalCoverageNotice.ValidTo
PS.ClientProposalCoverageNotice.SourceUpdatedDate=BPStaging.ClientProposalCoverageNotice.ValidFrom
PS.ClientProposalCoverageNotice.ParentId=BPStaging.ClientProposalCoverageNotice.ParentId
PS.ClientProposalCoverageNotice.IsDeleted=BPStaging.ClientProposalCoverageNotice.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_PS_ClientProposalCoverageNotice
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ClientProposalCoverageNotice';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.ClientProposalCoverageNotice)
BEGIN TRY
    MERGE PS.ClientProposalCoverageNotice T
    USING (
        SELECT
            Id
          , ClientProposalId
          , Header
          , Wording
          , CanEdit
          , TagName
          , TagValue
          , IsSelected
          , DisplayIndex
          , CoverageNoticeId
          , IsMandatory
          , ParentId
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , DataSourceInstanceId = 50366
        FROM (
        SELECT
            Id
          , ClientProposalId
          , Header
          , Wording
          , CanEdit
          , TagName
          , TagValue
          , IsSelected
          , DisplayIndex
          , CoverageNoticeId
          , ValidFrom
          , ValidTo
          , IsMandatory
          , ParentId
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ClientProposalCoverageNotice
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.Id = S.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , ClientProposalId
               , Header
               , Wording
               , CanEdit
               , TagName
               , TagValue
               , IsSelected
               , DisplayIndex
               , CoverageNoticeId
               , IsMandatory
               , SourceUpdatedDate
               , ParentId
               , IsDeleted
               , DataSourceInstanceId
             )
             VALUES
                 (
                     S.Id
                   , S.ClientProposalId
                   , S.Header
                   , S.Wording
                   , S.CanEdit
                   , S.TagName
                   , S.TagValue
                   , S.IsSelected
                   , S.DisplayIndex
                   , S.CoverageNoticeId
                   , S.IsMandatory
                   , S.SourceUpdatedDate
                   , S.ParentId
                   , S.IsDeleted
                   , S.DataSourceInstanceId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        T.ClientProposalId
      , T.Header
      , T.Wording
      , T.CanEdit
      , T.TagName
      , T.TagValue
      , T.IsSelected
      , T.DisplayIndex
      , T.CoverageNoticeId
      , T.IsMandatory
      , T.SourceUpdatedDate
      , T.ParentId
      , T.DataSourceInstanceId
      , T.IsDeleted
    INTERSECT
    SELECT
        S.ClientProposalId
      , S.Header
      , S.Wording
      , S.CanEdit
      , S.TagName
      , S.TagValue
      , S.IsSelected
      , S.DisplayIndex
      , S.CoverageNoticeId
      , S.IsMandatory
      , S.SourceUpdatedDate
      , S.ParentId
      , S.DataSourceInstanceId
      , S.IsDeleted
)
        THEN UPDATE SET
                 T.ClientProposalId = S.ClientProposalId
               , T.Header = S.Header
               , T.Wording = S.Wording
               , T.CanEdit = S.CanEdit
               , T.TagName = S.TagName
               , T.TagValue = S.TagValue
               , T.IsSelected = S.IsSelected
               , T.DisplayIndex = S.DisplayIndex
               , T.CoverageNoticeId = S.CoverageNoticeId
               , T.IsMandatory = S.IsMandatory
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
               , T.ParentId = S.ParentId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);