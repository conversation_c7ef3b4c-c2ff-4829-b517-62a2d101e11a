/*
Lineage
dbo.SubmissionPortalDetails.SubmissionId=BPStaging.SubmissionPortalDetails.SubmissionId
dbo.SubmissionPortalDetails.SubmissionContainerMarketId=BPStaging.SubmissionPortalDetails.SubmissionContainerMarketId
dbo.SubmissionPortalDetails.EmailAddress=BPStaging.SubmissionPortalDetails.EmailAddress
dbo.SubmissionPortalDetails.PortalUserId=BPStaging.SubmissionPortalDetails.PortalUserId
dbo.SubmissionPortalDetails.PortalUsername=BPStaging.SubmissionPortalDetails.PortalUsername
dbo.SubmissionPortalDetails.NotificationSent=BPStaging.SubmissionPortalDetails.NotificationSent
dbo.SubmissionPortalDetails.NotificationSentTimestamp=BPStaging.SubmissionPortalDetails.NotificationSentTimestamp
dbo.SubmissionPortalDetails.SubmissionOpened=BPStaging.SubmissionPortalDetails.SubmissionOpened
dbo.SubmissionPortalDetails.SubmissionOpenedTimestamp=BPStaging.SubmissionPortalDetails.SubmissionOpenedTimestamp
dbo.SubmissionPortalDetails.EmailSendStatusId=BPStaging.SubmissionPortalDetails.EmailSendStatusId
dbo.SubmissionPortalDetails.NotificationProviderKey=BPStaging.SubmissionPortalDetails.NotificationProviderKey
dbo.SubmissionPortalDetails.SourceUpdatedDate=BPStaging.SubmissionPortalDetails.ValidFrom
dbo.SubmissionPortalDetails.AssignmentOriginId=BPStaging.SubmissionPortalDetails.AssignmentOriginId
dbo.SubmissionPortalDetails.AssignmentOrigin=BPStaging.SubmissionPortalDetails.AssignmentOrigin
dbo.SubmissionPortalDetails.PSCarrierId=BPStaging.SubmissionPortalDetails.CarrierId
*/
CREATE PROCEDURE BPStaging.LoadSubmissionPortalDetails
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.SubmissionPortalDetails';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

DECLARE @CheckCount INT = (
            SELECT x = COUNT(*) FROM BPStaging.SubmissionPortalDetails
        );

IF @CheckCount <> 0
BEGIN TRY
    MERGE dbo.SubmissionPortalDetails T
    USING (
        SELECT DISTINCT
               SubmissionId
             , SubmissionContainerMarketId
             , EmailAddress
             , PortalUserId
             , PortalUsername
             , NotificationSent
             , NotificationSentTimestamp
             , SubmissionOpened
             , SubmissionOpenedTimestamp
             , SourceUpdatedDate = ValidFrom
             , EmailSendStatusId
             , NotificationProviderKey
             , AssignmentOriginId
             , AssignmentOrigin
             , CarrierId
        FROM
            BPStaging.SubmissionPortalDetails
    ) S
    ON S.SubmissionId = T.SubmissionId
       AND S.SubmissionContainerMarketId = T.SubmissionContainerMarketId
       AND S.PortalUserId = T.PortalUserId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 SubmissionId
               , SubmissionContainerMarketId
               , EmailAddress
               , PortalUserId
               , PortalUsername
               , NotificationSent
               , NotificationSentTimestamp
               , SubmissionOpened
               , SubmissionOpenedTimestamp
               , LastUpdatedUTCDate
               , EmailSendStatusId
               , NotificationProviderKey
               , SourceUpdatedDate
               , AssignmentOriginId
               , AssignmentOrigin
               , PSCarrierId
             )
             VALUES
                 (
                     S.SubmissionId
                   , S.SubmissionContainerMarketId
                   , S.EmailAddress
                   , S.PortalUserId
                   , S.PortalUsername
                   , S.NotificationSent
                   , S.NotificationSentTimestamp
                   , S.SubmissionOpened
                   , S.SubmissionOpenedTimestamp
                   , GETUTCDATE()
                   , S.EmailSendStatusId
                   , S.NotificationProviderKey
                   , S.SourceUpdatedDate
                   , S.AssignmentOriginId
                   , S.AssignmentOrigin
                   , S.CarrierId
                 )
    WHEN MATCHED AND NOT EXISTS (
    SELECT
        S.EmailAddress
      , S.PortalUsername
      , S.NotificationSent
      , S.NotificationSentTimestamp
      , S.SubmissionOpened
      , S.SubmissionOpenedTimestamp
      , S.EmailSendStatusId
      , S.NotificationProviderKey
      , S.AssignmentOriginId
      , S.AssignmentOrigin
      , S.CarrierId
    INTERSECT
    SELECT
        T.EmailAddress
      , T.PortalUsername
      , T.NotificationSent
      , T.NotificationSentTimestamp
      , T.SubmissionOpened
      , T.SubmissionOpenedTimestamp
      , T.EmailSendStatusId
      , T.NotificationProviderKey
      , T.AssignmentOriginId
      , T.AssignmentOrigin
      , T.PSCarrierId
)
        THEN UPDATE SET
                 T.EmailAddress = S.EmailAddress
               , T.PortalUsername = S.PortalUsername
               , T.NotificationSent = S.NotificationSent
               , T.NotificationSentTimestamp = S.NotificationSentTimestamp
               , T.SubmissionOpened = S.SubmissionOpened
               , T.SubmissionOpenedTimestamp = S.SubmissionOpenedTimestamp
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.EmailSendStatusId = S.EmailSendStatusId
               , T.NotificationProviderKey = S.NotificationProviderKey
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.AssignmentOriginId = S.AssignmentOriginId
               , T.AssignmentOrigin = S.AssignmentOrigin
               , T.PSCarrierId = S.CarrierId
    WHEN NOT MATCHED BY SOURCE
        THEN DELETE
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;