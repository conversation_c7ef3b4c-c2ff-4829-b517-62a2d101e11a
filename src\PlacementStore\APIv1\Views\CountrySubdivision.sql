/*
Lineage
CountrySubdivisionId=Reference.CountrySubdivision.CountrySubdivisionId
CountryId=Reference.CountrySubdivision.CountryId
HierarchyNode=Reference.CountrySubdivision.CountryId
HierarchyNode=Reference.CountrySubdivision.CountrySubdivisionId
CountrySubdivisionCategoryName=Reference.CountrySubdivision.CountrySubdivisionCategoryName
CountrySubdivisionName=Reference.CountrySubdivision.CountrySubdivisionName
CountrySubdivisionCode=Reference.CountrySubdivision.CountrySubdivisionCode
IsDeleted=Reference.CountrySubdivision.IsDeleted
LastUpdateTime=Reference.CountrySubdivision.LastUpdateTime
*/
CREATE VIEW APIv1.CountrySubdivision
AS
SELECT
    csd.CountrySubdivisionId
  , csd.CountryId
  , HierarchyNode = hierarchyid::Parse(
                                     '/' + CAST((csd.CountryId) AS VARCHAR(30)) + '/'
                                     + CAST((csd.CountrySubdivisionId) AS VARCHAR(30)) + '/'
                                 )
  , csd.CountrySubdivisionCategoryName
  , csd.CountrySubdivisionName
  , csd.CountrySubdivisionCode
  , csd.IsDeleted
  , csd.LastUpdateTime
FROM
    Reference.GeographyGroupingScheme sc
    INNER JOIN Reference.GeographyGroup gg
        ON sc.GeographyGroupingSchemeId = gg.GeographyGroupingSchemeId
           AND gg.IsDeleted = 0

    INNER JOIN Reference.GeographyGroupMembership ggm
        ON gg.GeographyGroupId = ggm.GeographyGroupId
           AND ggm.IsDeleted = 0

    INNER JOIN Reference.Country c
        ON ggm.CountryId = c.CountryId
           AND c.IsDeleted = 0

    INNER JOIN Reference.CountrySubdivision csd
        ON csd.CountryId = c.CountryId
           AND csd.IsDeleted = 0
WHERE
    sc.GeographyGroupingSchemeName = 'FINMAR Geography'
    AND gg.GeographyGroupName = 'North America';
