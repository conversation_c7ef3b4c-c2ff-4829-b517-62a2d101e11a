/*
Lineage
dbo.PlacementRiskDefinitionElement.PlacementId=dbo.Placement.PlacementId
dbo.PlacementRiskDefinitionElement.ElementId=dbo.ElementCache.ElementId
dbo.PlacementRiskDefinitionElement.Level=dbo.ElementCache.HierarchyId
dbo.PlacementRiskDefinitionElement.SourceUpdatedDate=BPStaging.Placement.ValidTo
dbo.PlacementRiskDefinitionElement.SourceUpdatedDate=BPStaging.Placement.ValidFrom
dbo.PlacementRiskDefinitionElement.IsDeleted=BPStaging.Placement.ValidTo
*/
CREATE PROCEDURE BPStaging.LoadPlacementRiskDefinitionElement
AS
SET NOCOUNT ON;

DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementRiskDefinitionElement';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #PlacementElement;

    SELECT DISTINCT
           P.PlacementId
         , ec.ElementId
         , Level = ec.HierarchyId.GetLevel()
         , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                        THEN inner_select.ValidTo
                                    ELSE inner_select.ValidFrom END
         , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                THEN 1
                            ELSE 0 END
    INTO #PlacementElement
    FROM (
        SELECT
            Id
          , ValidFrom
          , ValidTo
          , RiskDefinitionElementId
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.Placement
    ) inner_select
         INNER JOIN dbo.Placement P
             ON P.PlacementSystemId = inner_select.Id
                AND P.DataSourceInstanceId = 50366

         INNER JOIN dbo.ElementCache ec
             ON ec.ElementId = inner_select.RiskDefinitionElementId
    WHERE
        inner_select.RowNo = 1;

    MERGE INTO dbo.PlacementRiskDefinitionElement T
    USING (
        SELECT
            PlacementId
          , ElementId
          , Level
          , SourceUpdatedDate
          , DataSourceInstanceId = 50366
          , IsDeleted
        FROM
            #PlacementElement
    ) S
    ON T.ElementId = S.ElementId
    WHEN NOT MATCHED BY TARGET
        THEN INSERT (
                 DataSourceInstanceId
               , PlacementId
               , ElementId
               , Level
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.PlacementId
                   , S.ElementId
                   , S.Level
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.PlacementId
                               , T.Level
                               , T.IsDeleted
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.PlacementId
                               , S.Level
                               , S.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PlacementId = S.PlacementId
               , T.Level = S.Level
               , T.IsDeleted = S.IsDeleted
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
