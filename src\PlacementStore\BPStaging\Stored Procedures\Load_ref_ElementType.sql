/*
Lineage
ref.ElementType.ElementTypeId=BPStaging.ElementType.ElementTypeId
ref.ElementType.ElementTypeKey=BPStaging.ElementType.ElementKey
ref.ElementType.ElementType=BPStaging.ElementType.ElementType
ref.ElementType.ElementStructureContextId=BPStaging.ElementType.ElementStructureContextId
*/
CREATE PROCEDURE BPStaging.Load_ref_ElementType
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.ElementType';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.ElementType t
    USING (
        SELECT
            ElementTypeId
          , ElementTypeKey = CAST(ElementKey AS NVARCHAR(100))
          , ElementType = CAST(ElementType AS NVARCHAR(255))
          , IsDeleted = 0
          , ElementStructureContextId
        FROM
            BPStaging.ElementType
        WHERE
            SupportedLanguageId = 1
    ) S
    ON t.ElementTypeId = s.ElementTypeId
    WHEN NOT MATCHED
        THEN INSERT (
                 ElementTypeId
               , ElementTypeKey
               , ElementType
               , IsDeleted
               , ETLCreatedDate
               , ETLUpdatedDate
               , DataSourceInstanceId
               , SourceUpdatedDate
               , ElementStructureContextId
             )
             VALUES
                 (
                     s.ElementTypeId
                   , s.ElementTypeKey
                   , s.ElementType
                   , s.IsDeleted
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , 50366
                   , GETUTCDATE()
                   , s.ElementStructureContextId
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 t.ElementTypeKey
                               , t.ElementType
                               , t.IsDeleted
                               , t.ElementStructureContextId
                             INTERSECT
                             SELECT
                                 s.ElementTypeKey
                               , s.ElementType
                               , s.IsDeleted
                               , s.ElementStructureContextId
                         )
        THEN UPDATE SET
                 t.ElementTypeKey = s.ElementTypeKey
               , t.ElementType = s.ElementType
               , t.IsDeleted = s.IsDeleted
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.SourceUpdatedDate = GETUTCDATE()
               , t.ElementStructureContextId = s.ElementStructureContextId
    WHEN NOT MATCHED BY SOURCE AND t.IsDeleted = 0
        THEN UPDATE SET
                 t.IsDeleted = 1
               , t.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
