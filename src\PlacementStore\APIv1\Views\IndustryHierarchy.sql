/*
Lineage
IndustryID=Reference.DataSourceInstance.DataSourceInstanceId
IndustryID=Reference.ISIC4Industry.ISIC4ClassId
IndustryID=Reference.ISIC4Industry.ISIC4GroupId
IndustryID=Reference.ISIC4Industry.ISIC4DivisionId
IndustryID=Reference.ISIC4Industry.ISIC4SectionId
IndustryID=Reference.SIC87Industry.SIC87IndustryId
IndustryID=Reference.SIC87Industry.SIC87IndustryGroupId
IndustryID=Reference.SIC87Industry.SIC87MajorGroupId
IndustryID=Reference.SIC87Industry.SIC87DivisionId
IndustryID=Reference.IndustrySubSector.IndustrySubSectorId
IndustryID=Reference.IndustrySector.IndustrySectorId
IndustryID=Reference.Industry.IndustryId
IndustryID=dbo.Lookup.Id
IndustryID=dbo.Industry.PSIndustryId
DataSourceInstanceID=Reference.DataSourceInstance.DataSourceInstanceId
DataSourceInstanceID=dbo.Industry.DataSourceInstanceId
IndustryCode=Reference.ISIC4Industry.ISIC4SectionCode
IndustryCode=Reference.ISIC4Industry.ISIC4DivisionCode
IndustryCode=Reference.ISIC4Industry.ISIC4GroupCode
IndustryCode=Reference.ISIC4Industry.ISIC4ClassCode
IndustryCode=Reference.SIC87Industry.SIC87MajorGroupCode
IndustryCode=Reference.SIC87Industry.SIC87IndustryGroupCode
IndustryCode=Reference.SIC87Industry.SIC87IndustryCode
IndustryCode=Reference.SIC87Industry.SIC87DivisionCode
IndustryCode=dbo.Industry.IndustryCode
IndustryName=Reference.DataSourceInstance.DataSourceInstanceName
IndustryName=Reference.ISIC4Industry.ISIC4ClassName
IndustryName=Reference.ISIC4Industry.ISIC4GroupName
IndustryName=Reference.ISIC4Industry.ISIC4DivisionName
IndustryName=Reference.ISIC4Industry.ISIC4SectionName
IndustryName=Reference.SIC87Industry.SIC87IndustryName
IndustryName=Reference.SIC87Industry.SIC87IndustryGroupName
IndustryName=Reference.SIC87Industry.SIC87MajorGroupName
IndustryName=Reference.SIC87Industry.SIC87DivisionName
IndustryName=Reference.IndustrySubSector.IndustrySubsectorName
IndustryName=Reference.IndustrySector.IndustrySectorName
IndustryName=Reference.Industry.IndustryName
IndustryName=dbo.Lookup.Name
IndustryName=dbo.Industry.IndustryName
VerticalIndustryId=Reference.SIC87Industry.VerticalIndustryId
VerticalIndustryName=Reference.SIC87Industry.VerticalIndustryName
ParentID=Reference.ISIC4Industry.ISIC4GroupId
ParentID=Reference.ISIC4Industry.ISIC4DivisionId
ParentID=Reference.ISIC4Industry.ISIC4SectionId
ParentID=Reference.SIC87Industry.SIC87IndustryGroupId
ParentID=Reference.SIC87Industry.SIC87MajorGroupId
ParentID=Reference.SIC87Industry.SIC87DivisionId
ParentID=Reference.IndustrySubSector.IndustrySectorId
ParentID=Reference.IndustrySector.IndustryId
ParentID=dbo.Industry.PSIndustryId
LevelNum=dbo.Industry.LevelNumber
CreatedDate=Reference.ISIC4Industry.LastUpdateTime
CreatedDate=Reference.SIC87Industry.ETLCreatedDate
CreatedDate=Reference.IndustrySubSector.LastUpdateTime
CreatedDate=Reference.IndustrySector.LastUpdateTime
CreatedDate=Reference.Industry.LastUpdateTime
CreatedDate=dbo.Lookup.CreatedUTCDate
CreatedDate=dbo.Industry.CreatedDate
UpdatedDate=Reference.ISIC4Industry.LastUpdateTime
UpdatedDate=Reference.SIC87Industry.ETLUpdatedDate
UpdatedDate=Reference.IndustrySubSector.LastUpdateTime
UpdatedDate=Reference.IndustrySector.LastUpdateTime
UpdatedDate=Reference.Industry.LastUpdateTime
UpdatedDate=dbo.Lookup.LastUpdatedUTCDate
UpdatedDate=dbo.Industry.UpdatedDate
IsDeleted=Reference.ISIC4Industry.IsDeleted
IsDeleted=Reference.SIC87Industry.IsDeleted
IsDeleted=Reference.IndustrySubSector.IsDeleted
IsDeleted=Reference.IndustrySector.IsDeleted
IsDeleted=Reference.Industry.IsDeleted
IsDeleted=dbo.Lookup.IsActive
IsDeleted=dbo.Lookup.IsDeleted
IsDeleted=dbo.Industry.IsDeleted
*/
CREATE VIEW APIv1.IndustryHierarchy
AS
WITH Industries (IndustryId, DataSourceInstanceId, IndustryCode, IndustryName, VerticalIndustryId, VerticalIndustryName
               , ParentId, LevelNum, LevelName, ParentLevelName, IndustryNode, CreatedDate, UpdatedDate, IsDeleted
) AS (
    SELECT
        base.IndustryId
      , base.DataSourceInstanceId
      , base.IndustryCode
      , base.IndustryName
      , VerticalIndustryId = CAST(NULL AS INT)
      , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
      , base.ParentId
      , base.LevelNum
      , base.LevelName
      , base.ParentLevelName
      , ProductNode = CAST('/' + CAST(base.IndustryId AS VARCHAR(30)) + '/' AS HIERARCHYID)
      , base.CreatedDate
      , base.UpdatedDate
      , base.IsDeleted
    FROM (
        SELECT
            IndustryId = s.DataSourceInstanceId
          , s.DataSourceInstanceId
          , IndustryCode = CAST(NULL AS NVARCHAR(20))
          , IndustryName = CAST(s.DataSourceInstanceName AS NVARCHAR(250))
          , ParentId = CAST(NULL AS INT)
          , LevelNum = 0
          , LevelName = CAST('System' AS NVARCHAR(30))
          , ParentLevelName = CAST(NULL AS NVARCHAR(30))
          , CreatedDate = CAST(GETDATE() AS DATETIME2)
          , UpdatedDate = CAST(GETDATE() AS DATETIME2)
          , IsDeleted = CAST(0 AS BIT)
        FROM
            Reference.DataSourceInstance s
        WHERE
            s.DataSourceInstanceId IN (
                50355, 59000, 59001, 50003, 50351
            )
        UNION ALL
        SELECT
            59000
          , 59000
          , NULL
          , 'ISIC4'
          , NULL
          , 0
          , 'System'
          , NULL
          , GETDATE()
          , GETDATE()
          , IsDeleted = CAST(0 AS BIT)
        UNION ALL
        SELECT
            59001
          , 59001
          , NULL
          , 'SIC87'
          , NULL
          , 0
          , 'System'
          , NULL
          , GETDATE()
          , GETDATE()
          , IsDeleted = CAST(0 AS BIT)
    ) base
    WHERE
        base.ParentId IS NULL
    UNION ALL
    SELECT
        child.IndustryId
      , child.DataSourceInstanceId
      , child.IndustryCode
      , child.IndustryName
      , child.VerticalIndustryId
      , child.VerticalIndustryName
      , child.ParentId
      , child.LevelNum
      , child.LevelName
      , child.ParentLevelName
      , IndustryNode = CAST(i.IndustryNode.ToString() + CAST(child.IndustryId AS VARCHAR(30)) + '/' AS HIERARCHYID)
      , child.CreatedDate
      , child.UpdatedDate
      , child.IsDeleted
    FROM (
        SELECT DISTINCT
               IndustryId = cls4.ISIC4ClassId + 40000
             , DataSourceInstanceId = 59000
             , IndustryCode = CAST(cls4.ISIC4SectionCode + RIGHT('00' + CAST(cls4.ISIC4DivisionCode AS NVARCHAR(2)), 2)
                                   + CAST(cls4.ISIC4GroupCode AS NVARCHAR(2)) + CAST(cls4.ISIC4ClassCode AS NVARCHAR(2)) AS NVARCHAR(20))
             , IndustryName = CAST(cls4.ISIC4ClassName AS NVARCHAR(250))
             , VerticalIndustryId = CAST(NULL AS INT)
             , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
             , ParentId = cls4.ISIC4GroupId + 40000
             , LevelNum = 4
             , LevelName = CAST('ISIC4ClassName' AS NVARCHAR(30))
             , ParentLevelName = CAST('ISIC4GroupName' AS NVARCHAR(30))
             , CreatedDate = cls4.LastUpdateTime
             , UpdatedDate = cls4.LastUpdateTime
             , cls4.IsDeleted
        FROM
            Reference.ISIC4Industry cls4
        UNION ALL
        SELECT DISTINCT
               IndustryId = grp4.ISIC4GroupId + 40000
             , DataSourceInstanceId = 59000
             , IndustryCode = CAST(grp4.ISIC4SectionCode + RIGHT('00' + CAST(grp4.ISIC4DivisionCode AS NVARCHAR(2)), 2)
                                   + CAST(grp4.ISIC4GroupCode AS NVARCHAR(2)) AS NVARCHAR(20))
             , IndustryName = CAST(grp4.ISIC4GroupName AS NVARCHAR(250))
             , VerticalIndustryId = CAST(NULL AS INT)
             , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
             , ParentId = grp4.ISIC4DivisionId + 40000
             , LevelNum = 3
             , LevelName = CAST('ISIC4GroupName' AS NVARCHAR(30))
             , ParentLevelName = CAST('ISIC4DivisionName' AS NVARCHAR(30))
             , CreatedDate = grp4.LastUpdateTime
             , UpdatedDate = grp4.LastUpdateTime
             , grp4.IsDeleted
        FROM
            Reference.ISIC4Industry grp4
        UNION ALL
        SELECT DISTINCT
               IndustryId = div4.ISIC4DivisionId + 40000
             , DataSourceInstanceId = 59000
             , IndustryCode = CAST(div4.ISIC4SectionCode + RIGHT('00' + CAST(div4.ISIC4DivisionCode AS NVARCHAR(2)), 2) AS NVARCHAR(20))
             , IndustryName = CAST(div4.ISIC4DivisionName AS NVARCHAR(250))
             , VerticalIndustryId = CAST(NULL AS INT)
             , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
             , ParentId = div4.ISIC4SectionId + 40000
             , LevelNum = 2
             , LevelName = CAST('ISIC4DivisionName' AS NVARCHAR(30))
             , ParentLevelName = CAST('ISIC4SectionName' AS NVARCHAR(30))
             , CreatedDate = div4.LastUpdateTime
             , UpdatedDate = div4.LastUpdateTime
             , div4.IsDeleted
        FROM
            Reference.ISIC4Industry div4
        UNION ALL
        SELECT DISTINCT
               IndustryId = sec4.ISIC4SectionId + 40000
             , DataSourceInstanceId = 59000
             , IndustryCode = CAST(sec4.ISIC4SectionCode AS NVARCHAR(20))
             , IndustryName = CAST(sec4.ISIC4SectionName AS NVARCHAR(250))
             , VerticalIndustryId = CAST(NULL AS INT)
             , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
             , ParentId = 59000
             , LevelNum = 1
             , LevelName = CAST('ISIC4SectionName' AS NVARCHAR(30))
             , ParentLevelName = CAST('System' AS NVARCHAR(30))
             , CreatedDate = sec4.LastUpdateTime
             , UpdatedDate = sec4.LastUpdateTime
             , sec4.IsDeleted
        FROM
            Reference.ISIC4Industry sec4
        UNION ALL
        SELECT DISTINCT
               IndustryId = ind87.SIC87IndustryId + 60000
             , DataSourceInstanceId = 59001
             , IndustryCode = CAST(RIGHT('00' + CAST(ind87.SIC87MajorGroupCode AS NVARCHAR(2)), 2)
                                   + CAST(ind87.SIC87IndustryGroupCode AS NVARCHAR(2))
                                   + CAST(ind87.SIC87IndustryCode AS NVARCHAR(2)) AS NVARCHAR(20))
             , IndustryName = CAST(ind87.SIC87IndustryName AS NVARCHAR(250))
             , ind87.VerticalIndustryId
             , ind87.VerticalIndustryName
             , ParentId = ind87.SIC87IndustryGroupId + 60000
             , LevelNum = 4
             , LevelName = CAST('SIC87IndustryName' AS NVARCHAR(30))
             , ParentLevelName = CAST('SIC87IndustryGroupName' AS NVARCHAR(30))
             , CreatedDate = ind87.ETLCreatedDate
             , UpdatedDate = ind87.ETLUpdatedDate
             , ind87.IsDeleted
        FROM
            Reference.SIC87Industry ind87
        UNION ALL
        SELECT DISTINCT
               IndustryId = SIC87IndustryGroupId + 60000
             , DataSourceInstanceId = 59001
             , IndustryCode = CAST(RIGHT('00' + CAST(grp87.SIC87MajorGroupCode AS NVARCHAR(2)), 2)
                                   + CAST(grp87.SIC87IndustryGroupCode AS NVARCHAR(2)) AS NVARCHAR(20))
             , IndustryName = CAST(grp87.SIC87IndustryGroupName AS NVARCHAR(250))
             , VerticalIndustryId = CAST(NULL AS INT)
             , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
             , ParentId = grp87.SIC87MajorGroupId + 60000
             , LevelNum = 3
             , LevelName = CAST('SIC87IndustryGroupName' AS NVARCHAR(30))
             , ParentLevelName = CAST('SIC87MajorGroupName' AS NVARCHAR(30))
             , CreatedDate = grp87.ETLCreatedDate
             , UpdatedDate = grp87.ETLUpdatedDate
             , grp87.IsDeleted
        FROM
            Reference.SIC87Industry grp87
        UNION ALL
        SELECT DISTINCT
               IndustryId = mgrp87.SIC87MajorGroupId + 60000
             , DataSourceInstanceId = 59001
             , IndustryCode = CAST(RIGHT('00' + CAST(mgrp87.SIC87MajorGroupCode AS NVARCHAR(2)), 2) AS NVARCHAR(20))
             , IndustryName = CAST(mgrp87.SIC87MajorGroupName AS NVARCHAR(250))
             , VerticalIndustryId = CAST(NULL AS INT)
             , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
             , ParentId = mgrp87.SIC87DivisionId + 60000
             , LevelNum = 2
             , LevelName = CAST('SIC87MajorGroupName' AS NVARCHAR(30))
             , ParentLevelName = CAST('SIC87DivisionName' AS NVARCHAR(30))
             , CreatedDate = mgrp87.ETLCreatedDate
             , UpdatedDate = mgrp87.ETLUpdatedDate
             , mgrp87.IsDeleted
        FROM
            Reference.SIC87Industry mgrp87
        UNION ALL
        SELECT DISTINCT
               IndustryId = div87.SIC87DivisionId + 60000
             , DataSourceInstanceId = 59001
             , IndustryCode = CAST(div87.SIC87DivisionCode AS NVARCHAR(20))
             , IndustryName = CAST(div87.SIC87DivisionName AS NVARCHAR(250))
             , VerticalIndustryId = CAST(NULL AS INT)
             , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
             , ParentId = 59001
             , LevelNum = 1
             , LevelName = CAST('SIC87DivisionName' AS NVARCHAR(30))
             , ParentLevelName = CAST('System' AS NVARCHAR(30))
             , CreatedDate = div87.ETLCreatedDate
             , UpdatedDate = div87.ETLUpdatedDate
             , div87.IsDeleted
        FROM
            Reference.SIC87Industry div87
        UNION ALL
        SELECT
            IndustryId = riss.IndustrySubSectorId
          , DataSourceInstanceId = 50355
          , IndustryCode = CAST(NULL AS NVARCHAR(20))
          , IndustryName = CAST(riss.IndustrySubsectorName AS NVARCHAR(250))
          , VerticalIndustryId = CAST(NULL AS INT)
          , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
          , ParentId = CAST(riss.IndustrySectorId AS NVARCHAR(30))
          , LevelNum = 3
          , LevelName = CAST('ReferenceIndustrySubSector' AS NVARCHAR(30))
          , ParentLevelName = CAST('ReferenceIndustrySector' AS NVARCHAR(30))
          , CreatedDate = riss.LastUpdateTime
          , UpdatedDate = riss.LastUpdateTime
          , riss.IsDeleted
        FROM
            Reference.IndustrySubSector riss
        UNION ALL
        SELECT
            IndustryId = ris.IndustrySectorId
          , DataSourceInstanceId = 50355
          , IndustryCode = CAST(NULL AS NVARCHAR(20))
          , IndustryName = CAST(ris.IndustrySectorName AS NVARCHAR(250))
          , VerticalIndustryId = CAST(NULL AS INT)
          , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
          , ParentId = CAST(ris.IndustryId AS NVARCHAR(30))
          , LevelNum = 2
          , LevelName = CAST('ReferenceIndustrySector' AS NVARCHAR(30))
          , ParentLevelName = CAST('ReferenceIndustry' AS NVARCHAR(30))
          , CreatedDate = ris.LastUpdateTime
          , UpdatedDate = ris.LastUpdateTime
          , ris.IsDeleted
        FROM
            Reference.IndustrySector ris
        UNION ALL
        SELECT
            IndustryId = ri.IndustryId
          , DataSourceInstanceId = 50355
          , IndustryCode = CAST(NULL AS NVARCHAR(20))
          , IndustryName = CAST(ri.IndustryName AS NVARCHAR(250))
          , VerticalIndustryId = CAST(NULL AS INT)
          , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
          , ParentID = 50355
          , LevelNum = 1
          , LevelName = CAST('ReferenceIndustry' AS NVARCHAR(30))
          , ParentLevelName = CAST('System' AS NVARCHAR(30))
          , CreatedDate = ri.LastUpdateTime
          , UpdatedDate = ri.LastUpdateTime
          , ri.IsDeleted
        FROM
            Reference.Industry ri
        UNION ALL
        SELECT
            IndustryId = L.Id
          , DataSourceInstanceId = 50003
          , IndustryCode = CAST(NULL AS NVARCHAR(20))
          , IndustryName = CAST(L.Name AS NVARCHAR(250))
          , VerticalIndustryId = CAST(NULL AS INT)
          , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
          , ParentId = 50003
          , LevelNum = 1
          , LevelName = CAST('CNAEIndustry' AS NVARCHAR(30))
          , ParentLevelName = CAST('System' AS NVARCHAR(30))
          , CreatedDate = L.CreatedUTCDate
          , UpdatedDate = L.LastUpdatedUTCDate
          , IsDeleted = CAST(CASE WHEN L.IsActive = 0
                                       OR L.IsDeleted = 1
                                      THEN 1
                                  ELSE 0 END AS BIT)
        FROM
            dbo.LookupGroup LG
            INNER JOIN dbo.Lookup L
                ON L.LookupGroupId = LG.Id
        WHERE
            LG.DataSourceInstanceId = 50003
            AND LG.LocalCode = '57'
        UNION ALL
        SELECT
            IndustryId = PSIndustryId
          , DataSourceInstanceId
          , IndustryCode
          , IndustryName
          , VerticalIndustryId = CAST(NULL AS INT)
          , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
          , ParentId = 50351
          , LevelNum = LevelNumber
          , LevelName = CAST('IndustryGroup' AS NVARCHAR(30))
          , ParentLevelName = CAST('System' AS NVARCHAR(30))
          , CreatedDate
          , UpdatedDate
          , IsDeleted
        FROM
            dbo.Industry
        WHERE
            IndustryTypeId = 1
        UNION ALL
        SELECT
            IndustryId = ind.PSIndustryId
          , ind.DataSourceInstanceId
          , ind.IndustryCode
          , ind.IndustryName
          , VerticalIndustryId = CAST(NULL AS INT)
          , VerticalIndustryName = CAST(NULL AS NVARCHAR(250))
          , ParentId = prnt.PSIndustryId
          , LevelNum = ind.LevelNumber
          , LevelName = CAST('Industry' AS NVARCHAR(30))
          , ParentLevelName = CAST('IndustryGroup' AS NVARCHAR(30))
          , ind.CreatedDate
          , ind.UpdatedDate
          , ind.IsDeleted
        FROM
            dbo.Industry ind
            INNER JOIN dbo.Industry prnt
                ON prnt.IndustryTypeId = 1
                   AND prnt.IndustryId = ind.ParentId
        WHERE
            ind.IndustryTypeId = 2
    ) child
         INNER JOIN Industries i
             ON child.ParentId = i.IndustryId
                AND child.ParentLevelName = i.LevelName
                AND child.DataSourceInstanceId = i.DataSourceInstanceId
)
SELECT
    IndustryID = IndustryId
  , DataSourceInstanceID = DataSourceInstanceId
  , IndustryCode
  , IndustryName
  , VerticalIndustryId
  , VerticalIndustryName
  , ParentID = ParentId
  , LevelNum
  , LevelName
  , ParentLevelName
  , IndustryNode
  , CreatedDate
  , UpdatedDate
  , IsDeleted
FROM
    Industries;
GO