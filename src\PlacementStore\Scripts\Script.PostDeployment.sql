/*
Post-Deployment Script Template
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be appended to the build script.
 Use SQLCMD syntax to include a file in the post-deployment script.
 Example:      :r ./myfile.sql
 Use SQLCMD syntax to reference a variable in the post-deployment script.
 Example:      :setvar TableName MyTable
               SELECT * FROM [$(TableName)]
--------------------------------------------------------------------------------------
*/

/**
	Validate schema conventions are met
	Note: 'dbo' removed from this check until we decide how to handle other system generated tables in dbo schema from replication
**/
IF (SELECT COUNT(*)FROM devops.AddMissing_PrimaryKeys(N'APIv1,Reference,PS,BP', 1) ) > 0
BEGIN
    SELECT * FROM devops.AddMissing_PrimaryKeys(N'APIv1,Reference,PS,BP', 1);

    THROW 50001, N'Schema issue: [devops].[AddMissing_PrimaryKeys]', 1;
END;

/**
	Set up Renewals
**/
:r ./reference/PactConfig/RunType.sql
:r ./reference/PactConfig/RuleType.sql
:r ./rules/IncludeForRenewal.sql
:r ./rules/Exclusion.sql
:r ./rules/Region.sql
:r ./rules/Segment.sql
:r ./rules/SubSegment.sql
:r ./rules/PlacementName.sql
:r ./rules/PlacementRenewal.sql
:r ./reference/PactConfig/SourceSystem.sql

:r ./rules/RenewalPlacementDataSourceInstance.sql

/**
	Set up Programme Store controlled data
**/
:r ./reference/reference/DataSource.sql
:r ./reference/reference/DataSourceInstance.sql
:r ./reference/reference/PartyRole.sql
:r ./reference/ref/AgencyType.sql
:r ./reference/ref/PremiumRange.sql
--:r "reference/ref/BrokingSubSegment.sql"
:r ./reference/ref/PartyRole.sql
:r ./reference/ref/RenewableOption.sql
:r ./reference/ref/SignetPartyRole.sql
:r ./reference/ref/OrganisationLegalEntity.sql
:r ./reference/ref/NegotiationType.sql
:r ./reference/ref/SourceQuery.sql
:r ./reference/ref/InsuredTypeMapping.sql

:r ./reference/dbo/BusinessContext.sql
:r ./reference/dbo/PlacementSystemStatus.sql
:r ./reference/dbo/PlacementCompletionStatus.sql
:r ./reference/dbo/PlacementSystemRoleMapping.sql
:r ./reference/PactConfig/rptAttribute.sql
:r ./reference/dbo/AddFMAProducts.sql
:r ./reference/dbo/CarrierAlias.sql
:r ./reference/dbo/CarrierType.sql
:r ./reference/dbo/CarrierMappingType.sql
:r ./reference/dbo/IndexationType.sql
:r ./reference/dbo/IndustryType.sql
:r ./reference/reference/SIC87IndustryMapping.sql

/**
Ensure that the dbo.PlacementPolicyRelationshipType has the record required to created PlacementPolicy records
if needed before the ETL fully loads the table from the Broking Platform.
**/
:r ./reference/dbo/PlacementPolicyRelationshipType.sql

/**
	Set up the translation ref config stuff
**/
:r ./reference/RefConfig/IncludedLocales.sql
:r ./reference/RefConfig/IncludedTables.sql
:r ./reference/RefConfig/IncludedColumns.sql
:r ./reference/RefConfig/CodeList.sql

/**
	Set up the translation of placement store ref data
**/
:r ./reference/dbo/RefTranslations.en-GB.sql

/**
	Set-up datamasking where it is required
**/
:r ./Script.PostDeployment.DataMasking.sql

/**
	Set up the security
**/
-- :r ./Script.PostDeployment.Security.sql						# This is not required as the database security is now handled by the yaml files.
:r ./Script.PostDeployment.PopulateSharedKeyAccess.sql

/**
	Set up Brazilian lookup values. This is just TEMPORARY. This will be replaced by SSIS
**/
:r ./reference/dbo/PopulateBrazilLookupData.sql

/*script to populate Data Mapping Table
*/
:r ./reference/dbo/DataSourceInstanceIdMapping.sql

/**
	Populate the Signet Carrier Status table (may be replaced by ETL eventually)
**/
:r ./reference/SignetStaging/CarrierStatus.sql

/**
	Ensure that the Override flags exist in the database to preven job failures.
**/
:r ./reference/PactConfig/EnsureOverrideFlagsExist.sql

/**
* Signet queries need Country and CountrySub from WillisCore.
* As it's static we can just load it here without need to ETL it in.
**/
:r ./reference/reference/WillisCoreCountry.sql
:r ./reference/reference/WillisCoreCountrySub.sql

/**
* Populate the Task (PRP) schema objects.
**/
:r ./Script.PostDeployment.PopulateTaskSchema.sql

/*Load Marine Mar Reference Data*/
:r ./reference/MarineMar/MarineDataMapping.sql
:r ./reference/MarineMar/SpecieDataMapping.sql

/*Populate Populate NotRemarketingReason Reference Data*/
:r ./reference/reference/NotRemarketingReason.sql

/*Populate Populate Group Data*/
:r ./reference/dbo/ReasonGroup.sql
:r ./reference/dbo/ReasonGroupMapping.sql

/*Populate rpt schema Reference Data*/
:r ./reference/rpt/Bands.sql
:r ./reference/rpt/Date.sql
:r ./reference/rpt/PremiumCategory.sql

/* The ADF configuration */
:r ./reference/ADF/Connection.sql
:r ./reference/ADF/ProcessType.sql
:r ./reference/ADF/ProcessSessionStatus.sql
:r ./reference/ADF/LoadType.sql
:r ./reference/ADF/PartitionConfig.sql
:r ./reference/ADF/Process.sql
:r ./reference/ADF/ProcessInterdependency.sql
:r ./reference/ADF/InstanceConfig.sql

/**
 Value Mapping Configuration
*/
IF devops.IsProdEnv() = 0
BEGIN
    :r ./reference/psapi/ValueMapping.sql
END;

/**
	Apply any data fixes required for releases.
	Below file contain all one off scripts which need to run as part of current release.
	This should be the last script executed here.
	This is not called for a Unit Test database.
**/

IF devops.IsUnitTestEnv() = 1
BEGIN
	PRINT 'Not calling Script.PostDeployment.OneOffScript for Unit Test database.';
END
ELSE
BEGIN
	:r ./Script.PostDeployment.OneOffScript.sql
END


/**
 Value Mapping Configuration
*/
IF devops.IsProdEnv() = 0 AND devops.IsIatEnv() = 0
BEGIN
	Exec devops.SetTemporalHistoryRetention;
END;
