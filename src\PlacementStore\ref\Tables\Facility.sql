CREATE TABLE ref.Facility (
    FacilityId            INT           NOT NULL IDENTITY(1, 1)
  , DataSourceInstanceId  INT           NOT NULL
  , FacilityKey           NVARCHAR(100) NOT NULL
  , TranslationKey        NVARCHAR(100) NULL
  , FacilityName          NVARCHAR(200) NOT NULL
  , FacilityReference     NVARCHAR(50)  NULL
  , InceptionDate         DATETIME2     NOT NULL
  , ExpiryDate            DATETIME2     NOT NULL
  , RenewedFromFacilityId INT           NULL
  , FacilityType          NVARCHAR(80)  NULL
  , RefPolicyStatusId     INT           NULL
  , BrokerCode            NVARCHAR(50)  NULL
  , PolicyReference       NVARCHAR(50)  NULL
  , PolicyStatusId        INT           NULL
  , RenewedFromPolicyId   BIGINT        NULL
  , ETLCreatedDate        DATETIME2(7)  NOT NULL
        DEFAULT (GETUTCDATE())
  , ETLUpdatedDate        DATETIME2(7)  NOT NULL
        DEFAULT (GETUTCDATE())
  , SourceUpdatedDate     DATETIME2(7)  NOT NULL
  , IsDeprecated          BIT           NOT NULL
        DEFAULT (0)
  , PSFacilityPolicyId    AS
        (CASE WHEN DataSourceInstanceId = 50000
                   AND ISNULL(FacilityType, '') NOT IN (
                  'CONSORTIUM', 'POOL'
              )
                  THEN CAST(FacilityKey AS INT) --> Eclipse (50000)
              ELSE FacilityId * -1 END
        )
  , PolicyStatusKey       NVARCHAR(100) NULL
  , CONSTRAINT PK_ref_Facility
        PRIMARY KEY
        (
            FacilityId
        )
);
GO

CREATE UNIQUE INDEX IXU_ref_Facility_DataSourceInstanceIdFacilityKey
ON ref.Facility
(
    DataSourceInstanceId
  , FacilityKey
);
GO