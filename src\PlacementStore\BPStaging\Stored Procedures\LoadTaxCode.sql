/*
Lineage
ref.TaxCode.TaxCodeId=BPStaging.TaxCode.Id
ref.TaxCode.TaxCodeKey=BPStaging.TaxCode.LabelTranslationKey
ref.TaxCode.TaxCode=BPStaging.TaxCode.Text
ref.TaxCode.SourceUpdatedDate=BPStaging.TaxCode.ValidFrom
ref.TaxCode.IsDeprecated=BPStaging.TaxCode.IsDeprecated
*/
CREATE PROCEDURE BPStaging.LoadTaxCode
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.TaxCode';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.TaxCode T
    USING (
        SELECT
            TaxCodeId = Id
          , DataSourceInstanceId = 50366
          , TaxCodeKey = LabelTranslationKey
          , TaxCode = Text
          , SourceUpdatedDate = ValidFrom
          , IsDeprecated
        FROM
            BPStaging.TaxCode
    ) S
    ON T.TaxCodeId = S.TaxCodeId
    WHEN NOT MATCHED
        THEN INSERT (
                 TaxCodeId
               , DataSourceInstanceId
               , TaxCodeKey
               , TaxCode
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.TaxCodeId
                   , S.DataSourceInstanceId
                   , S.TaxCodeKey
                   , S.TaxCode
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.TaxCodeId
                               , T.DataSourceInstanceId
                               , T.TaxCodeKey
                               , T.TaxCode
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.TaxCodeId
                               , S.DataSourceInstanceId
                               , S.TaxCodeKey
                               , S.TaxCode
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.TaxCodeId = S.TaxCodeId
               , T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.TaxCodeKey = S.TaxCodeKey
               , T.TaxCode = S.TaxCode
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.IsDeprecated = S.IsDeprecated
    WHEN NOT MATCHED BY SOURCE AND T.IsDeprecated = 0
        THEN UPDATE SET
                 T.IsDeprecated = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
