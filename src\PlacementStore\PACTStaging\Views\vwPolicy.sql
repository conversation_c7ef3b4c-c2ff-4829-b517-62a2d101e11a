/*
Lineage
PACTPolicyId=PACTStaging.rpt_vwPolicy.PolicyId
DataSourceInstanceId=PACTStaging.rpt_vwPolicy.DataSourceInstanceId
PolicyKey=PACTStaging.rpt_vwPolicy.PolicyKey
ClaimManagementApproach=PACTStaging.rpt_vwPolicy.ClaimManagementApproach
ExpiryDate=PACTStaging.rpt_vwPolicy.ExpiryDate
FirstInceptionDate=PACTStaging.rpt_vwPolicy.FirstInceptionDate
InceptionDate=PACTStaging.rpt_vwPolicy.InceptionDate
InsuranceTypeId=ref.InsuranceType.InsuranceTypeId
PACTInsuranceTypeId=PACTStaging.rpt_vwPolicy.InsuranceTypeId
IsWholeOrder=PACTStaging.rpt_vwPolicy.IsWholeOrder
MasterPolicyId=PACTStaging.rpt_vwPolicy.MasterPolicyId
OpportunityTypeId=ref.OpportunityType.OpportunityTypeId
PACTOpportunityTypeId=PACTStaging.rpt_vwPolicy.OpportunityTypeId
OwnershipOrganisationId=PACTStaging.rpt_vwPolicy.OwnershipOrganisationId
ParentId=dbo.Policy.PolicyId
ParentPolicyKey=dbo.Policy.PolicyKey
ParentPACTPolicyId=PACTStaging.rpt_vwPolicy.ParentId
RetentionStructure=PACTStaging.rpt_vwPolicy.RetentionStructure
PolicyDescription=PACTStaging.rpt_vwPolicy.PolicyDescription
PolicyReference=PACTStaging.rpt_vwPolicy.PolicyReference
PolicyStatusKey=PAS.PolicyStatus.PolicyStatusKey
PACTPolicyStatusId=PACTStaging.rpt_vwPolicy.PolicyStatusId
PolicyTypeKey=PAS.PolicyType.PolicyTypeKey
PACTPolicyTypeId=PACTStaging.rpt_vwPolicy.PolicyTypeId
PolicyIssued=PACTStaging.rpt_vwPolicy.PolicyIssued
PolicyIssuedDate=PACTStaging.rpt_vwPolicy.PolicyIssuedDate
RenewalDate=PACTStaging.rpt_vwPolicy.RenewalDate
RenewedFromPolicyId=dbo.Policy.PolicyId
RenewedFromPolicyKey=dbo.Policy.PolicyKey
RenewedFromPACTPolicyId=PACTStaging.rpt_vwPolicy.RenewedFromPolicyId
SumInsured=PACTStaging.rpt_vwPolicy.SumInsured
PACTSumInsuredCurrencyId=PACTStaging.rpt_vwPolicy.SumInsuredCurrencyId
SumInsuredGlobalCurrencyId=PAS.Currency.GlobalCurrencyId
WillisPercentageOfOrder=PACTStaging.rpt_vwPolicy.WillisPercentageOfOrder
Attributes=PACTStaging.rpt_vwPolicy.Attributes
SourceLastUpdateDate=PACTStaging.rpt_vwPolicy.SourceLastUpdateDate
SourceQueryId=PACTStaging.rpt_vwPolicy.SourceQueryId
ETLCreatedDate=PACTStaging.rpt_vwPolicy.ETLCreatedDate
ETLUpdatedDate=PACTStaging.rpt_vwPolicy.ETLUpdatedDate
IsDeleted=PACTStaging.rpt_vwPolicy.IsDeleted
IsDeleted=PACTStaging.rpt_vwPolicy.SegmentCode
RefInsuranceTypeId=PACTStaging.rpt_vwPolicy.RefInsuranceTypeId
RefPolicyStatusId=PACTStaging.rpt_vwPolicy.RefPolicyStatusId
SegmentCode=PACTStaging.rpt_vwPolicy.SegmentCode
AnnualizedCommission=PACTStaging.rpt_vwPolicy.AnnualizedCommission
AnnualizedPremium=PACTStaging.rpt_vwPolicy.AnnualizedPremium
CurrencyId=PACTStaging.rpt_vwPolicy.CurrencyId
LastRunDate=PactConfig.vwSourceSystemLastRunDate.LastRunDate
FinancialGeographyId=ref.FinancialGeography.FinancialGeographyId
FinancialLegalEntityId=ref.LegalEntity.LegalEntityId
FinancialSegmentId=ref.FinancialSegment.FinancialSegmentId
InInclusionCriteria=PACTStaging.rpt_vwPolicy.ExpiryDate
InInclusionCriteria=PactConfig.vwSourceSystemLastRunDate.ExpiringFrom
*/
CREATE VIEW PACTStaging.vwPolicy
AS
SELECT
    PACTPolicyId = p.PolicyId
  , p.DataSourceInstanceId
  , p.PolicyKey
  , p.ClaimManagementApproach
  , p.ExpiryDate
  , p.FirstInceptionDate
  , p.InceptionDate
  , it.InsuranceTypeId
  , PACTInsuranceTypeId = p.InsuranceTypeId
  , p.IsWholeOrder
  , p.MasterPolicyId
  , ot.OpportunityTypeId
  , PACTOpportunityTypeId = p.OpportunityTypeId
  , p.OwnershipOrganisationId
  , ParentId = pp.PolicyId
  , ParentPolicyKey = CONVERT(NVARCHAR(50), op.PolicyKey)
  , ParentPACTPolicyId = p.ParentId
  , p.RetentionStructure
  , p.PolicyDescription
  , PolicyReference = CAST(p.PolicyReference AS NVARCHAR(50))
  , ps.PolicyStatusKey
  , PACTPolicyStatusId = p.PolicyStatusId
  , pt.PolicyTypeKey
  , PACTPolicyTypeId = p.PolicyTypeId
  , p.PolicyIssued
  , p.PolicyIssuedDate
  , p.RenewalDate
  , RenewedFromPolicyId = op.PolicyId
  , RenewedFromPolicyKey = CONVERT(NVARCHAR(50), op.PolicyKey)
  , RenewedFromPACTPolicyId = p.RenewedFromPolicyId
  , p.SumInsured
  --, CU.CurrencyId AS SumInsuredCurrencyId --PASStaging.rpt_vwCurrency so same as PACTSumInsuredCurrencyId
  , PACTSumInsuredCurrencyId = p.SumInsuredCurrencyId
  , SumInsuredGlobalCurrencyId = cu.GlobalCurrencyId
  , p.WillisPercentageOfOrder
  , Attributes = TRY_CAST(p.Attributes AS XML)
  , p.SourceLastUpdateDate
  , p.SourceQueryId
  , p.ETLCreatedDate
  , p.ETLUpdatedDate
  , IsDeleted = CASE WHEN p.SegmentCode IS NULL
                         THEN 1
                     ELSE p.IsDeleted END
  , p.RefInsuranceTypeId
  , p.RefPolicyStatusId
  , p.SegmentCode
  , p.AnnualizedCommission
  , p.AnnualizedPremium
  , p.CurrencyId
  , lrd.LastRunDate
  , fg.FinancialGeographyId
  , FinancialLegalEntityId = le.LegalEntityId
  , FinancialSegmentId = fs.FinancialSegmentId
  /* Before we used to filter records. But if the data changed to be out-of-range we wouldn't update it. */
  /* This means we won't add the record if Included = false, but once it has been added we will update   */
  /* so we don't have an old version.                                                                    */
  , InInclusionCriteria = CASE WHEN CAST(p.ExpiryDate AS DATETIME2) >= CAST(lrd.ExpiringFrom AS DATETIME2)
                                   THEN 1
                               ELSE 0 END
FROM
    PACTStaging.rpt_vwPolicy p WITH (NOLOCK)
    LEFT JOIN dbo.Policy op WITH (NOLOCK) --old policy already should be loaded
        ON op.PACTPolicyId = p.RenewedFromPolicyId

    LEFT JOIN dbo.Policy pp WITH (NOLOCK) --Parent Policy
        ON pp.PACTPolicyId = p.ParentId

    LEFT JOIN PAS.Currency cu WITH (NOLOCK)
        ON cu.PASCurrencyId = p.SumInsuredCurrencyId
    -- This will change to CU.CurrencyKey = P.SumInsuredCurrencyKey AND CU.DataSourceInstance = P.DataSourceInstanceId

    LEFT JOIN PAS.PolicyStatus ps WITH (NOLOCK)
        ON ps.PASPolicyStatusId = p.PolicyStatusId

    LEFT JOIN ref.OpportunityType ot WITH (NOLOCK)
        ON ot.PASOpportunityTypeId = p.OpportunityTypeId

    LEFT JOIN ref.InsuranceType it WITH (NOLOCK)
        ON it.PASInsuranceTypeId = p.InsuranceTypeId

    LEFT JOIN PAS.PolicyType pt WITH (NOLOCK)
        ON pt.PASPolicyTypeId = p.PolicyTypeId

    INNER JOIN PactConfig.vwSourceSystemLastRunDate lrd WITH (NOLOCK)
        ON lrd.DataSourceInstanceId = p.DataSourceInstanceId

    LEFT JOIN ref.FinancialGeography fg WITH (NOLOCK)
        ON fg.PACTFinancialGeographyId = p.FinancialGeographyId

    LEFT JOIN ref.LegalEntity le WITH (NOLOCK)
        ON le.PACTLegalEntityId = p.FinancialLegalEntityId

    LEFT JOIN ref.FinancialSegment fs WITH (NOLOCK)
        ON fs.PACTFinancialSegmentId = p.FinancialSegmentId;
