/*
Lineage
PlacementId=dbo.Placement.PlacementId
GrossPremiumUSD=dbo.ClientUnderwriterPremium.GrossPremiumUSD
NetPaymentFromClientUSD=dbo.ClientUnderwriterPremium.NetPaymentFromClientUSD
NetPremiumtoUWUSD=dbo.ClientUnderwriterPremium.NetPremiumtoUWUSD
ClaimUSD=dbo.ClientUnderwriterPremium.ClaimUSD
OutstandingClaimUSD=dbo.ClientUnderwriterPremium.OutstandingClaimUSD
*/
CREATE VIEW APIv1.PlacementPremium_CUP
AS
SELECT
    pl.PlacementId
  , GrossPremiumUSD = SUM(cup.GrossPremiumUSD)
  , NetPaymentFromClientUSD = SUM(cup.NetPaymentFromClientUSD)
  , NetPremiumtoUWUSD = SUM(cup.NetPremiumtoUWUSD)
  , ClaimUSD = SUM(cup.ClaimUSD)
  , OutstandingClaimUSD = SUM(ISNULL(cup.OutstandingClaimUSD, 0))
FROM
    dbo.Placement pl
    INNER JOIN dbo.PlacementPolicy plpol
        ON pl.PlacementId = plpol.PlacementId
           AND plpol.IsDeleted = 0
           AND plpol.PlacementPolicyRelationshipTypeId = 1

    INNER JOIN dbo.ClientUnderwriterPremium cup
        ON plpol.PolicyId = cup.PolicyId
WHERE
    pl.InceptionDate >= '2019-01-01'
    AND pl.PlacementSystemId IS NOT NULL
    AND pl.DataSourceInstanceId = 50366
    AND cup.IsDeleted = 0
GROUP BY
    pl.PlacementId;