/*
Lineage
PS.RiskStructureSpecification.RiskStructureSpecificationKey=PS.vwSpecificationLayerFlat.SpecificationId
PS.RiskStructureSpecification.RiskStructureSpecificationKey=PS.RiskStructure.RiskStructureId
PS.RiskStructureSpecification.RiskStructureId=PS.RiskStructure.RiskStructureId
PS.RiskStructureSpecification.SpecificationId=PS.vwSpecificationLayerFlat.SpecificationId
PS.RiskStructureSpecification.SourceUpdatedDate=PS.RiskStructure.ETLUpdatedDate
PS.RiskStructureSpecification.SourceUpdatedDate=PS.vwSpecificationLayerFlat.ValidFrom
*/
CREATE PROCEDURE BPStaging.Load_PS_RiskStructureSpecification
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.RiskStructureSpecification';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #source;

    SELECT
        DataSourceInstanceId = 50366
      , RiskStructureSpecificationKey = CONCAT('MKTRES|', SL.SpecificationId, '|RS|', RS.RiskStructureId)
      , RS.RiskStructureId
      , SpecificationId = SL.SpecificationId
      , SourceUpdatedDate = (
            SELECT MAX(value.v)
            FROM (
                VALUES (
                    RS.ETLUpdatedDate
                )
                     , (
                    CASE WHEN SL.ValidFrom < '1900-01-01 00:00:00'
                              OR SL.ValidFrom IS NULL
                             THEN '1900-01-01 00:00:00.0000000'
                         ELSE SL.ValidFrom END
                )
            ) value (v)
        )
      , IsDeleted = 0
    INTO #source
    FROM
        PS.vwSpecificationLayerFlat SL
        INNER JOIN PS.RiskStructure RS
            ON RS.PlacementId = SL.PlacementId
               AND ISNULL(RS.RiskProfileId, 0) = ISNULL(SL.RiskProfileId, 0)
               AND ISNULL(RS.LayerTypeId, 0) = ISNULL(SL.LayerTypeId, 0)
               AND RS.RiskStructureKey NOT LIKE 'LAYER|%'
               AND ISNULL(RS.LimitCurrencyId, 0) = ISNULL(SL.LimitCurrencyId, 0)
               AND ISNULL(RS.Limit, -1) = ISNULL(SL.Limit, -1)
               AND ISNULL(RS.AttachmentPointCurrencyId, 0) = ISNULL(SL.AttachmentPointCurrencyId, 0)
               AND ISNULL(RS.AttachmentPoint, -1) = ISNULL(SL.AttachmentPoint, -1)
               AND ISNULL(RS.DeductibleCurrencyId, 0) = ISNULL(SL.DeductibleCurrencyId, 0)
               AND ISNULL(RS.Deductible, -1) = ISNULL(SL.Deductible, -1);

    MERGE PS.RiskStructureSpecification T
    USING (
        SELECT
            DataSourceInstanceId
          , RiskStructureSpecificationKey
          , RiskStructureId
          , SpecificationId
          , SourceUpdatedDate
          , IsDeleted
        FROM
            #source
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.RiskStructureId = S.RiskStructureId
       AND T.SpecificationId = S.SpecificationId
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , RiskStructureSpecificationKey
               , RiskStructureId
               , SpecificationId
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.RiskStructureSpecificationKey
                   , S.RiskStructureId
                   , S.SpecificationId
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.DataSourceInstanceId
                               , S.RiskStructureSpecificationKey
                               , S.RiskStructureId
                               , S.SpecificationId
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.DataSourceInstanceId
                               , T.RiskStructureSpecificationKey
                               , T.RiskStructureId
                               , T.SpecificationId
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.RiskStructureSpecificationKey = S.RiskStructureSpecificationKey
               , T.RiskStructureId = S.RiskStructureId
               , T.SpecificationId = S.SpecificationId
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
                                   AND T.DataSourceInstanceId = 50366
        THEN UPDATE SET
                 T.IsDeleted = 1
               , T.ETLUpdatedDate = GETUTCDATE()
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    DROP TABLE IF EXISTS #source;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;