#------------------------------------------------------------------------------
# Reporting Layer Deployment tool
#
# Version: 0.1
# Date:    12 Feb 2019
#------------------------------------------------------------------------------
# PARAMETERS:
param([Parameter(Mandatory)] [string]$Environment="",
       [Parameter(Mandatory)] [string]$analysisServicesUserName="",
       [Parameter(Mandatory)] [string]$analysisServicesPassword="",
       [Parameter(Mandatory)] [string]$ImpersonationUserId="",
       [Parameter(Mandatory)] [string]$ImpersonationPassword="",
	   [Parameter(Mandatory)] [string]$PlacementStoreServer="",
	   [Parameter(Mandatory)] [string]$PlacementStoreDB="",
	   [Parameter(Mandatory)] [string]$SSASCubeName="",
	   [Parameter(Mandatory)] [string]$SSASServer="",
	   [Parameter(Mandatory)] [string]$SSASModelName="")

# cls


# PowerShell by default uses TLS 1.0 for web requests.
# This will force the use of TLS 1.2 (you can also make it use 1.1 if you want for some reason).

[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

if(!( Get-PackageProvider -ListAvailable -Name Nuget -ErrorAction SilentlyContinue)) {
  Write-Host "Installing Nuget Package Provider"
  Install-PackageProvider -Name NuGet -Force -Scope CurrentUser
}
if (!(Get-Module -ListAvailable -Name SqlServer)) 
{
    Write-Host "Installing SqlServer Module"
    Install-Module -Name SqlServer -Scope CurrentUser -RequiredVersion 21.1.18218 -Force -Verbose -AllowClobber
    if($?) {
      Write-Host "Import SqlServer Module"
      Import-Module -Name SqlServer -RequiredVersion 21.1.18218 -Force -Verbose
    }
}

$modulePath = ";${env:UserProfile}\Documents\WindowsPowerShell\Modules"
$env:PSModulePath += $modulePath
# -----------------------------------------------------------------------------------------------------

# Check if the server URL already has the read-write endpoint suffix
If (-not ($SSASServer -like "*:rw")) {
  Write-Host "Adding :rw suffix to SSASServer for read-write endpoint"
  $SSASServer = $SSASServer + ":rw"
}

# Fix any relative paths
$Profilename =""
$Profilename= $PSScriptRoot+$Profilename
#$bim_file = $PSScriptRoot+"\"+$SSASModelName+".bim"

# Copy build outputs and deployment options to deployment directory
$deploymentDir = $PSScriptRoot + "\deployment"
if (Test-Path $deploymentDir) {
  Remove-Item $deploymentDir -Recurse -Force | Out-Null
}
mkdir -Force $deploymentDir
cp Azure*.* $deploymentDir
cp $PSScriptRoot\deploymentoptions\*.* $deploymentDir
cp $PSScriptRoot\xmla\Azure*.* $deploymentDir
cp $PSScriptRoot\*.asdatabase $deploymentDir

#list items for any debugging
Get-ChildItem -Path $deploymentDir
#Get-Content -Path $deploymentDir\deploy.log

Rename-Item -Path "$deploymentDir\Analytics.deploymenttargets" "$deploymentDir\$SSASModelName.deploymenttargets"
Rename-Item -Path "$deploymentDir\Analytics.deploymentoptions" "$deploymentDir\$SSASModelName.deploymentoptions" 
Rename-Item -Path "$deploymentDir\Azure_ImpersonateAccount.xmla" "$deploymentDir\Azure_ImpersonateAccount_$SSASModelName.xmla"

############# ALL NEW CONTENT
$AsDBpath = "$deploymentDir\$SSASModelName.asdatabase"
$DepTargetpath= "$deploymentDir\$SSASModelName.deploymenttargets"
$DeployOptionpath = "$deploymentDir\$SSASModelName.deploymentoptions" 
$DeployImperPath = "$deploymentDir\Azure_ImpersonateAccount_$SSASModelName.xmla"
$DwDbConntring = "DataSource=$SSASServer;Timeout=0;UID=$analysisServicesUserName;Password=$analysisServicesPassword;"
#$PLSConntring = "Provider=SQLOLEDB.1;Data Source=$PlacementStoreServer;Integrated Security=SSPI;Initial Catalog=$PlacementStoreDB"
$PLSConntring = "Data Source=$PlacementStoreServer;Initial Catalog=$PlacementStoreDB;Authentication=Active Directory Integrated"

Get-ChildItem -Path $DepTargetpath
Get-ChildItem -Path $DeployOptionpath

## New stuff
$secureStringRecreated = ConvertTo-SecureString -String $analysisServicesPassword -AsPlainText -Force
$cred = New-Object System.Management.Automation.PSCredential($analysisServicesUserName, $secureStringRecreated)

$SECURE_PASSWORD = ConvertTo-SecureString $analysisServicesPassword -AsPlainText -Force
$CREDENTIAL = New-Object System.Management.Automation.PSCredential ($analysisServicesUsername, $SECURE_PASSWORD)

#region begin Update Model.deploymenttargets
# Read Model.deploymenttargets
[xml]$deploymenttargets = Get-Content -Path $DepTargetpath

$deploymenttargets.DeploymentTarget.Database = $SSASCubeName
$deploymenttargets.DeploymentTarget.Server = $SSASServer
$deploymenttargets.DeploymentTarget.ConnectionString = $DwDbConntring
$deploymenttargets.Save($DepTargetpath);
#endregion

#region begin Update Model.deploymentoptions
# Read Model.deploymentoptions
[xml]$deploymentoptions = Get-Content -Path $DeployOptionpath

# Update ProcessingOption to DoNotProcess otherwise correct xmla file wont be generated.
$deploymentoptions.Deploymentoptions.ProcessingOption = 'DoNotProcess'
$deploymentoptions.Deploymentoptions.TransactionalDeployment = 'false'
$deploymentoptions.Save($DeployOptionpath);
#endregion

#region begin update to imperse file
##region begin Update .xmla
##Add passowrd in .xmla file.
$xmladata = Get-Content -Path $DeployImperPath | ConvertFrom-Json

foreach ($ds in $xmladata){
##$ds.Credential.AuthenticationKind = 'Windows'
$ds.createOrReplace.object.database = $SSASCubeName
$ds.createOrReplace.dataSource.connectionString = $PLSConntring
$ds.createOrReplace.dataSource.Provider = "System.Data.SqlClient"
$ds.createOrReplace.dataSource.account = $ImpersonationUserId
$ds.createOrReplace.dataSource.password = $ImpersonationPassword
if ($SSASModelName -eq "GA") {
   $ds.createOrReplace.dataSource.timeout = 3600
}

##Add password property to the object.
#$ds.credential | Add-Member -NotePropertyName Password -NotePropertyValue $analysisServicesPassword
}

$xmladata | ConvertTo-Json -depth 100 | Out-File $DeployImperPath
#endregion

Get-Content -Path $DepTargetpath
Get-Content -Path $DeployOptionpath
Get-Content -Path $DeployImperPath

$xmlaFilePath = "$deploymentDir\$SSASModelName.xmla"
$RoleXML= "$deploymentDir\Azure_$SSASCubeName.$Environment.Role.xmla";

# Create xmla deployment file.
Microsoft.AnalysisServices.Deployment.exe $AsDBpath /s:deploy.log /o:$xmlaFilePath

Get-ChildItem -Path $xmlaFilePath

#Deploy model xmla.
#drop the database for clean deployment 
$deleteDB =
'{
  "delete": {
    "object": {
      "database": "' + $SSASCubeName +'"
    }
  }
}';
Write-Host "$(Get-Date): Drop database $deleteDB";
try {
Invoke-ASCmd -Query $deleteDB -Server $SSASServer -Credential $CREDENTIAL
}
catch {
	Write-Host "$SSASCubeName does not exist on target server $SSASServer"
}

Invoke-ASCmd -InputFile $xmlaFilePath -Server $SSASServer -Credential $CREDENTIAL
Invoke-ASCmd -InputFile $RoleXML -Server $SSASServer -Credential $CREDENTIAL
Invoke-ASCmd -InputFile $DeployImperPath -Server $SSASServer -Credential $CREDENTIAL