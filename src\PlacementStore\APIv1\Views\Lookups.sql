/*
Lineage
LookupGroupID=dbo.LookupGroup.Id
DataSourceInstanceId=dbo.LookupGroup.DataSourceInstanceId
LocalGroupCode=dbo.LookupGroup.LocalCode
GroupName=dbo.LookupGroup.Name
LookupID=dbo.Lookup.Id
LocalCode=dbo.Lookup.LocalCode
GlobalCode=dbo.Lookup.GlobalCode
Name=dbo.Lookup.Name
*/
CREATE VIEW APIv1.Lookups
AS
SELECT
    LookupGroupID = LG.Id
  , LG.DataSourceInstanceId
  , LocalGroupCode = LG.LocalCode
  , GroupName = LG.Name
  , LookupID = L.Id
  , L.LocalCode
  , L.GlobalCode
  , L.Name
FROM
    dbo.LookupGroup LG
    JOIN dbo.Lookup L
        ON L.LookupGroupId = LG.Id
           AND L.IsActive = 1
           AND L.IsDeleted = 0
WHERE
    LG.IsActive = 1
    AND LG.IsDeleted = 0;