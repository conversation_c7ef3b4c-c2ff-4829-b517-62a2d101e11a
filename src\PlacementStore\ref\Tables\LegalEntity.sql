CREATE TABLE ref.LegalEntity (
    LegalEntityId          INT            IDENTITY(1, 1) PRIMARY KEY NOT NULL
  , DataSourceInstanceId   INT            NOT NULL
  , LegalEntityKey         NVARCHAR(100)  NOT NULL
  , LegalEntity            NVARCHAR(2000) NULL
  , GlobalLegalEntityId    INT            NULL
  , PACTLegalEntityId      INT            NULL
  , PASLegalEntityId       INT            NULL
  , SourceUpdatedDate      DATETIME2(7)   NOT NULL
  , ETLCreatedDate         DATETIME2(7)   NOT NULL
        DEFAULT (GETUTCDATE())
  , ETLUpdatedDate         DATETIME2(7)   NOT NULL
        DEFAULT (GETUTCDATE())
  , IsDeprecated           BIT            NOT NULL
        DEFAULT (0)
  , BrokerCode             NVARCHAR(50)   NULL
  , ScopeId                INT            NULL
  , LegalEntityCode        NVARCHAR(200)  NULL
  , LegalEntityDescription NVARCHAR(200)  NULL
);
GO

CREATE UNIQUE NONCLUSTERED INDEX IXU_ref_LegalEntity_PASLegalEntityId
ON ref.LegalEntity
(
    PASLegalEntityId
)
WHERE PASLegalEntityId IS NOT NULL;