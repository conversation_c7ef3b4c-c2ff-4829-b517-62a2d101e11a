/*
Lineage
ref.PlacementStatus.PlacementStatusKey=BPStaging.PlacementStatus.Id
ref.PlacementStatus.PlacementStatus=BPStaging.PlacementStatus.Text
ref.PlacementStatus.SourceUpdatedDate=BPStaging.PlacementStatus.ValidTo
ref.PlacementStatus.SourceUpdatedDate=BPStaging.PlacementStatus.ValidFrom
ref.PlacementStatus.IsDeprecated=BPStaging.PlacementStatus.IsDeprecated
ref.PlacementStatus.IsDeprecated=BPStaging.PlacementStatus.ValidTo
*/
CREATE PROCEDURE BPStaging.LoadPlacementStatus
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.PlacementStatus';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE ref.PlacementStatus T
    USING (
        SELECT
            PlacementStatusId = Id
          , DataSourceInstanceId = 50366
          , PlacementStatusKey = Id
          , PlacementStatus = Text
          , SourceUpdatedDate = IIF(YEAR(s.ValidTo) < 9999, s.ValidTo, s.ValidFrom)
          , IsDeprecated = IIF(YEAR(s.ValidTo) < 9999, 1, IsDeprecated)
        FROM (
        SELECT
            Id
          , Text
          , ValidTo
          , ValidFrom
          , IsDeprecated
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.PlacementStatus
    ) s
        WHERE
            s.RowNo = 1
    ) S
    ON T.DataSourceInstanceId = S.DataSourceInstanceId
       AND T.PlacementStatusKey = S.PlacementStatusKey
    WHEN NOT MATCHED
        THEN INSERT (
                 DataSourceInstanceId
               , PlacementStatusKey
               , PlacementStatus
               , SourceUpdatedDate
               , IsDeprecated
             )
             VALUES
                 (
                     S.DataSourceInstanceId
                   , S.PlacementStatusKey
                   , S.PlacementStatus
                   , S.SourceUpdatedDate
                   , S.IsDeprecated
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.DataSourceInstanceId
                               , T.PlacementStatusKey
                               , T.PlacementStatus
                               , T.SourceUpdatedDate
                               , T.IsDeprecated
                             INTERSECT
                             SELECT
                                 S.DataSourceInstanceId
                               , S.PlacementStatusKey
                               , S.PlacementStatus
                               , S.SourceUpdatedDate
                               , S.IsDeprecated
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.PlacementStatusKey = S.PlacementStatusKey
               , T.PlacementStatus = S.PlacementStatus
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeprecated = S.IsDeprecated
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);
