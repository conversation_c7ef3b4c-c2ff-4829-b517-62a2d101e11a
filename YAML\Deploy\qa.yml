﻿parameters:
  configOnly: false
  reporting: false
  enableReplica: false
  download: current
  variableGroupName: crbbro-ps-nonprod
  variableGroupNameShared: crbbro-bkt-nonprod
  microservice: ps
  appId: 002757
  billingCode: 'Placement Store'
  envName: qa
  regionName: em20
  azureServiceConnection: AKS-CRBBRO-DEV
  aksIdentityName: aks-q-em20-identity
  agentPoolName: Private-CRB-Linux-AKS-Q
  nightly: false
  resourceGroupRoleAssignment:
  - roleName: Reader                                                        # Azure role
    members:
    - memberName: R-AZC-CRBBRO-NONPROD-RGRP-READER                          # Cloud Group
  - roleName: 'Website Contributor'                                         # Azure role
    members:
    - memberName: R-AZC-CRBBRO-NONPROD-RGRP-CONTRIBUTOR                     # Cloud Group
  - roleName: 'Web Plan Contributor'                                        # Azure role
    members:
    - memberName: R-AZC-CRBBRO-NONPROD-RGRP-CONTRIBUTOR                     # Cloud Group
      type: Group
  - roleName: 'Monitoring Reader'                                           # Azure role
    members:
    - memberName: R-AZC-CRBBRO-NONPROD-RGRP-READER                          # Cloud Group
      type: Group
  - roleName: 'Data Factory ReadOnly'                                       # Data Factory ReadOnly
    members:
    - memberName: R-AZC-CRBBRO-NONPROD-RGRP-READER                          # Cloud Group

  - roleName: 'Storage Blob Data Contributor'                               # Storage Blob Data Contributor
    members:
    - memberName: crbbro-ps-##envLetter##-##regionName##-adf                # Data Factory
      type: ServicePrincipal
  storageAccount:
    create: true                                                            # Create Storage Account
    fileShares:
    - name: datasource-cam-analytics
    rbacAssignments:
    - roleName: 'Storage File Data SMB Share Reader'                        # Azure role
      members:
      - memberName: R-AZC-CRBBRO-NONPROD-RGRP-READER                        # Cloud Group
        type: Group
  virtualNetwork:
    resourceGroupName1: crbbro-bkt-##envLetter##-##regionName##-rgrp
    virtualNetworkName1: crbbro-bkt-##envLetter##-##regionName##-vnet2
    resourceGroupName2: crbbro-bkt-##envLetter##-##regionName##-rgrp
    virtualNetworkName2: crbbro-bkt-##envLetter##-##regionName##-vnet
  appServicePlans:
  - name: crbbro-ps-##envLetter##-##regionName##-asp
    shortName: dedicated
    sku: S1
    kind: app                                                            # app or linux
  keyVault:                                                              # Create KeyVault
    create: true
    vaultAccess:                                                         # Access list for KeyVault
      - displayName: aks-q-em20-identity
        type: servicePrincipal
        permissions:                                                     # Permissions to enable on the KeyVault
          keys: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore'
          secrets: 'Get,List,Set,Delete,Recover,Backup,Restore'
          certificates: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore,ManageContacts,ManageIssuers,GetIssuers,ListIssuers,SetIssuers,DeleteIssuers'
      - displayName: crbbro-ps-##envLetter##-em20-adf
        type: ServicePrincipal
        permissions:                                                     # Permissions to enable on the KeyVault
          secrets: 'Get,List'
      - displayName: R-AZC-CRBBRO-NONPROD-RGRP-CONTRIBUTOR               # Cloud Group
        type: Group
        permissions:                                                     # Permissions to enable on the KeyVault
          secrets: 'Get,List'
  vaultSecrets:                                                          # Secrets to automatically generate in KeyVault (if not already present)
  - name: ServiceAccountPassword--SVC-PSPACT-Q                           # Database connection
    type: value                                                          # Secret type
    value: $(ServiceAccountPassword-SVC-PSPACT-Q)
  - name: ServiceAccountPassword--SVC-W-GPPPACTETL-QA                    # SVC-W-GPPPACTETL-QA
    type: value                                                          # Secret type
    value: $(ServiceAccountPassword-SVC-W-GPPPACTETL-QA)
  webApps:
  - project: PsWeb                                                       # Name of project containing Web application
    dotnetVersion: 'v8.0'                                                # DotNet Framework Version
    appServicePlan: crbbro-ps-##envLetter##-##regionName##-asp           # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##envLetter##-##regionName##-subn1             # Subnet name to connect Web application
    clientAffinityEnabled: false
    use32BitWorkerProcess: false
    autoHealEnabled: true
    applicationGateway:                                                  # App Gateway network details
      resourceGroupName: crbbro-dvo-##envLetter##-##regionName##-rgrp
      virtualNetworkName: crbbro-dvo-##envLetter##-##regionName##-vnet
      subnetName: crbbro-dvo-##envLetter##-##regionName##-subn2
    applicationGatewayPrivate:                                           # App Gateway APIM network details
      resourceGroupName: crbbro-dvo-##envLetter##-##regionName##-rgrp
      virtualNetworkName: crbbro-dvo-##envLetter##-##regionName##-vnet
      subnetName: crbbro-dvo-##envLetter##-##regionName##-subn4
    artifactName: WebApp                                                 # Name of published artefact from build stage
    healthCheckPath: '/api/HealthCheck'                                  # Path of health check api to call to check health during deployment
    appSettingsName: webAppSettings                                      # Artefact name with common app settings
    overrideAppSettings:                                                 # Environment specific overrides for app.settings
    - name: AzureAd__ClientId
      value: 6f4770e5-1cca-42ef-ae31-bdece86f1458
    - name: AzureAd__Scope
      value: api://6f4770e5-1cca-42ef-ae31-bdece86f1458/.default
    - name: PlacementStore__Mappings__0__ApplicationId
      value: c144ec0e-f796-43f6-8666-6f02bfe618e0                        # PSA.Client.DevTest
    - name: PlacementStore__Mappings__0__SharedKeys__0
      value: AEA9660B-6A33-422B-9A1F-56B5AD2DFC47                        # 50000
    - name: PlacementStore__Mappings__1__ApplicationId
      value: 7a062428-a35b-4c2a-b70b-5eb98372adf3                        # PSA.Client.COL-PowerApps
    - name: PlacementStore__Mappings__1__SharedKeys__0
      value: EE498401-F715-490E-B77F-695A7DE4C678                        # 50003
    - name: PlacementStore__Mappings__2__ApplicationId
      value: 5e0625ca-6a20-4624-8eba-c3d8e97e2341                        # PSA.Client.Eclipse-PowerApps
    - name: PlacementStore__Mappings__2__SharedKeys__0
      value: AEA9660B-6A33-422B-9A1F-56B5AD2DFC47                        # 50000
    - name: PlacementStore__Mappings__3__ApplicationId
      value: 449e229e-cdf6-4b92-88e1-7ae2abbdeb74                        # PSA.Client.Broking-net-PowerApps
    - name: PlacementStore__Mappings__3__SharedKeys__0
      value: A6F51DE3-3DAA-4150-B965-29D0F56495E1                        # 50358
    - name: PlacementStore__Mappings__4__ApplicationId
      value: 5ee48321-072d-4580-a5c8-09e9f261c4dd                        # PSA.Client.Epic-US-PowerApps
    - name: PlacementStore__Mappings__4__SharedKeys__0
      value: E33E88B6-B96C-476F-9C3E-24A96D87E3B6                        # 50001
    - name: PlacementStore__Mappings__5__ApplicationId
      value: bedd15ec-079b-486c-8820-c8bb6f7b43b6                        # PSA.Client.CPT
    - name: PlacementStore__Mappings__5__SharedKeys__0
      value: E33E88B6-B96C-476F-9C3E-24A96D87E3B6                        # 50001
    - name: PlacementStore__Mappings__6__ApplicationId
      value: 5af227f4-a632-4ad1-8c28-8ce6118661a1                        # PSA.Client.CGW
    - name: PlacementStore__Mappings__7__ApplicationId
      value: 350667cb-1746-4f6c-afa0-c39a38e85a7c                        # PSA.Client.Eglobal-Australia-PowerApps
    - name: PlacementStore__Mappings__7__SharedKeys__0
      value: A868636C-E8D7-4702-8ADC-6BA665F66FD4                        # 50004
    - name: PlacementStore__Mappings__8__ApplicationId
      value: 82a9609e-af24-4ff7-b8fe-2f3eddb98f9c                        # PSA.Client.BPA
    - name: PlacementStore__Mappings__9__ApplicationId
      value: ebbe3178-4cb9-4318-9c35-e09aee042a27                        # PSA.Client.WIBS (Italy)
    - name: PlacementStore__Mappings__9__SharedKeys__0
      value: B571C362-946E-4616-93D4-95F018DF5BFD                        # 50045
    - name: PlacementStore__Mappings__10__ApplicationId
      value: ea7ad542-3373-4204-86b8-416d8b6692d7                        # PSA.Client.ASYS-Germany
    - name: PlacementStore__Mappings__10__SharedKeys__0
      value: 4193A151-7B78-437F-9BDB-B5C96A85D883                        # 50029
    - name: PlacementStore__Mappings__11__ApplicationId
      value: aeb264eb-5bec-4089-bb72-ebd9ff12448a                        # PSA.Client.Gras-Savoye-EGS (France)
    - name: PlacementStore__Mappings__11__SharedKeys__0
      value: 60159ECA-2FC1-4D20-A94D-112E4C47991E                        # 50364
    - name: PlacementStore__Mappings__12__ApplicationId
      value: d3100cee-8517-47a5-b944-f74c49e6e6fe                        # PSA.Client.VisualSeg-Spain
    - name: PlacementStore__Mappings__12__SharedKeys__0
      value: 560D3BF5-C452-4A80-9B47-ED8844F32EDE                        # 50044
    - name: PlacementStore__Mappings__13__ApplicationId
      value: 0aea92af-fc1d-46a5-99f0-f2ac49cf2c19                        # PSA.Client.eGlobal-Netherlands
    - name: PlacementStore__Mappings__13__SharedKeys__0
      value: 04C2761C-A1FF-4E7F-B32C-D72144AEB4BA                        # 50010
    - name: PlacementStore__Mappings__14__ApplicationId
      value: fe7dcd30-352e-49e8-88ce-9a11797d4f83                        # PSA.Client.eGlobal-HongKong
    - name: PlacementStore__Mappings__14__SharedKeys__0
      value: 615091E9-7C8A-4BFB-A8FC-BF9173740AC6                        # 50007
    - name: PlacementStore__Mappings__15__ApplicationId
      value: a02676ec-b5be-4e31-a803-cea6b59d93e4                        # PSA.Client.BisCore
    - name: PlacementStore__Mappings__15__SharedKeys__0
      value: 26BCD6CA-EFA5-4E9F-A866-4CAE248BAB3A                        # 50500
    - name: PlacementStore__Mappings__16__ApplicationId
      value: 5acecb6e-7651-41ac-8449-07e45e6470f4                        # PSA.Client.eGlobal-South-Africa
    - name: PlacementStore__Mappings__16__SharedKeys__0
      value: C430C693-7393-4529-9E05-6B07741F440A                        # 50015
    - name: PlacementStore__Mappings__17__ApplicationId
      value: 0a26adab-67d3-4b9d-9514-4805ac730eb2                        # PSA.Client.Reference-Data
    - name: PlacementStore__Mappings__17__SharedKeys__0
      value: DBD29121-9F23-4220-ACCD-477E5C540B8B                        # 50355
    - name: PlacementStore__Mappings__18__ApplicationId                  # PSA.Client.Carrier-Gateway
      value: 28c3a7f3-574f-44a5-9d9c-3e1d9ae631a9
    - name: PlacementStore__Mappings__19__ApplicationId
      value: 5f763e81-03ac-428f-80b3-c60e1550a100                        # PSA.Client.SegElevia-Portugal
    - name: PlacementStore__Mappings__19__SharedKeys__0
      value: BBB23E8A-508A-42F4-A42B-CFEF28A160EF                        # 50041
    - name: PlacementStore__Mappings__20__ApplicationId
      value: a4913d18-7ad6-4c16-aee4-ddd970b973c6                        # PSA.Client.eGlobal-China
    - name: PlacementStore__Mappings__20__SharedKeys__0
      value: FCEC8BA2-508E-4F5B-B0A7-DEFA6CEBD282                        # 50006
  funcApps:
  - project: PsFunc                                                      # Name of project containing Function application
    dotnetVersion: 'v8.0'                                                # DotNet Framework Version
    artifactName: FuncApp                                                # Name of published artefact from build stage
    appServicePlan: crbbro-ps-##envLetter##-##regionName##-asp           # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##envLetter##-##regionName##-subn1             # Subnet name to connect Web application
    healthCheckPath: '/api/HealthCheck'                                  # Path of Http Trigger Function to call to check health during deployment
    disabledFunctions:                                                   # Functions that should remain disabled
    - name: HealthCheckExt                                               # Extended healthcheck for manual run only
    appSettingsName: funcAppSettings                                     # Artefact name with common app settings
    overrideAppSettings:                                                 # Environment specific overrides for app.settings
    - name: ConnectionStrings__EventHubEndpoint__fullyQualifiedNamespace
      value: crb-data-placement-q-na20-ev.servicebus.windows.net         # Event Hub url
    - name: ConnectionStrings__BKPDatabase
      value: "Server=crbbro-dvo-q-em20-sql.database.windows.net;Database=crbbro-bkp-q-em20-db;Authentication=Active Directory Default"
    - name: ConnectionStrings__ServiceHub
      value: "Server=crbbro-dvo-q-em20-sql.database.windows.net;Database=crbbro-sh-q-em20-db;Authentication=Active Directory Default"
    - name: ConnectionStrings__ServiceHubMetadata
      value: "Server=crbbro-dvo-q-em20-sql.database.windows.net;Database=crbbro-sh-q-em20-db-metadata;Authentication=Active Directory Default"
    - name: AasServerName
      value: "crbbropsem20qssas"
    - name: AasRegionName
      value: "westeurope"
  sqlDatabases:
  - project: PlacementStoreDb.Build                                         # Project containing database
    name: crbbro-ps-##envLetter##-##regionName##-db
    shortName: ps
    useManagedIdentity: true                                                # Using Microsoft.Data.SqlClient instead of System.Data.SqlClient to allow automatic token authentication
    vaultSecretName: ConnectionStrings--Database
    dest:                                                                   # SQL Server / Elastic Pool which will host the database (to manage Azure costs)
      resourceGroupName: crbbro-dvo-q-em20-rgrp
      sqlServerName: crbbro-dvo-q-em20-sql
      elasticPoolName: crbbro-dvo-q-em-epool-ps
    artifactName: PlacementStore.Database                                   # Name of published artefact from build stage
    roleMembers:
    - roleName: db_owner                                                    # Database role
      members:
      - memberName: R-AZC-CRBBRO-NONPROD-SQL-CONTRIBUTOR                    # Cloud Group
        type: Group
    - roleName: db_datareader                                               # Database role
      members:
      - memberName: R-AZC-CRBBRO-NONPROD-SQL-READER                         # Cloud Group
        type: Group
      - memberName: ITCEDS-D-NA20-PurviewAccount                            # PurviewAccount
        type: ServicePrincipal
    - roleName: DevReadOnly                                                 # Database role
      members:
      - memberName: R-AZC-CRBBRO-DATAENGINEER                               # Cloud group
        type: Group
    - roleName: PlacementStoreAPIConsumerRole                               # Database role
      members:
      - memberName: crbbro-ps-##envLetter##-##regionName##-wa               # PS Web App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-wa/slots/staging # PS Staging Web App
        type: ServicePrincipal
    - roleName: ProgrammestoreODSConsumerRole                               # Database role
      members:
      - memberName: R-AZC-CRBBRO-PS-ODS-NONPROD-CONSUMER-READ               # Database role
        type: ServicePrincipal
    - roleName: ProgrammeStoreMarineMarConsumerRole                         # Database role
      members:
      - memberName: R-AZC-CRBBRO-PS-MARINEMAR-NONPROD-CONSUMER-READ         # Database role
        type: ServicePrincipal
    - roleName: ETL_Role                                                    # Database role
      members:
      - memberName: crbbro-ps-##envLetter##-##regionName##-fa               # PS Func App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-fa/slots/staging # PS Staging Func App
        type: ServicePrincipal
      - memberName: crbbro-ps-##envLetter##-##regionName##-adf              # PS ADF
        type: ServicePrincipal
      - memberName: <EMAIL>                # PS Service Account
        type: User
    - roleName: ProgrammeStoreQueryRole                                     # Database role
      members:
      - memberName: <EMAIL>            # PS Service Account
        type: User
      - memberName: E20-CB-BKT001Q
        type: ServicePrincipal
    - roleName: TaskControllerRole                                          # Database role
      members:                                                              
      - memberName: <EMAIL>            # PS Service Account
        type: User
      - memberName: E20-CB-BKT001Q
        type: ServicePrincipal
  ssas:
    ssasServer: 'asazure://westeurope.asazure.windows.net/crbbropsem20qssas'
    resourceGroupName: crbbro-ps-q-em20-rgrp
    models:
    - name: 'GA'
      databaseName: 'GeneralAnalytics_AS_TAB'
    - name: 'Usage'
      databaseName: 'BrokingPlatformUsage_AS_TAB'
    - name: 'Carrier'
      databaseName: 'CarrierAnalytics_AS_TAB'
    impersonationUserId: 'int\svc-w-psbroker-qa'
    ssasAdminUserPassword: $(SSASAdminUserPassword)
    analysisServicesUserName: '<EMAIL>'
    envName: qa
    placementStoreSqlServer: 'crbbro-dvo-q-em20-sql.database.windows.net'
    placementStoreDatabaseName: crbbro-ps-q-em20-db
  dataFactory:
    create: true
    subscriptionId: f3a96a8b-6a8c-45f1-8e43-25768f5427ec
    artefacts: PS.ADF
    psKeyVaultUrl: 'https://crbbro-ps-q-em20-kv.vault.azure.net/'
    selfHostedIntegrationRuntimeResourceId: '/subscriptions/f3a96a8b-6a8c-45f1-8e43-25768f5427ec/resourcegroups/crbbro-bkt-q-em20-rgrp/providers/Microsoft.DataFactory/factories/crbbro-bkt-q-em20-adf/integrationruntimes/SharedSelfHostedIntegrationRuntime'
    mpeSqlServerResourceId: '/subscriptions/f3a96a8b-6a8c-45f1-8e43-25768f5427ec/resourceGroups/crbbro-dvo-q-em20-rgrp/providers/Microsoft.Sql/servers/crbbro-dvo-q-em20-sql'   # SQL Server Managed Private End point
    mpePactServerResourceId: '/subscriptions/92ae2673-1f32-4529-aeb0-197f7b14d0f7/resourceGroups/PAS-U-EM22-RGRP/providers/Microsoft.Synapse/workspaces/pas-u-em22-saws'        # PACT Server Managed Private End point
    mpeWillisReferenceServerResourceId: '/subscriptions/103aaa2d-b0cd-43f8-86df-a12b6f9340b6/resourceGroups/DPSERD-SQL-EM20-I-RGRP/providers/Microsoft.Sql/managedInstances/e20-eds-erdmd1i'        # Willis Reference Server Managed Private End point
    mpeCrbDataLakeResourceId: '/subscriptions/af06586d-f5ca-4abd-a6bb-1fdda2f4bb8b/resourceGroups/CRB-DATA-D-NA20-RGRP/providers/Microsoft.Storage/storageAccounts/crbdatadna20sa'        # CRB data lake Managed Private End point
    triggers:
    - Name : 'Every3Hours'
      enabled: false
    - Name: 'Every24Hours'
      enabled: false
    - Name: 'SetPactLoad'
      enabled: false
stages:
- stage: ${{parameters.envName}}
  dependsOn:
  - start
  jobs:
  - deployment: approve
    displayName: 'Approve ${{parameters.envName}}'
    pool: server
    environment: y-deploy-crbbro-${{parameters.microservice}}-${{parameters.envName}}

  - template: deploy_infra.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      resourceGroupRoleAssignment: ${{parameters.resourceGroupRoleAssignment}}
      virtualNetwork: ${{parameters.virtualNetwork}}
      storageAccount: ${{parameters.storageAccount}}
      keyVault: ${{parameters.keyVault}}
      vaultSecrets: ${{parameters.vaultSecrets}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      appServicePlans: ${{parameters.appServicePlans}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      dataFactory: ${{parameters.dataFactory}}

- stage: ${{parameters.envName}}_apps
  ${{ if or(eq(parameters.configOnly,'true'),eq(parameters.reporting,'true')) }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
  ${{ else }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
    - build_apps
    - build_adf
  variables:
  - name: virtualNetworkResourceGroupName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkResourceGroupName'] ]
  - name: virtualNetworkName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkName'] ]
  - name: appSubnetNamesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['appSubnets.appSubnetNamesCSV'] ]
  - name: appIdentitiesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['identities.appIdentitiesCSV'] ]
  - name: dataFactoryName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_dedicated.outputs['dataFactory.dataFactoryName'] ]
  - name: dataFactoryresourceGroupName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_dedicated.outputs['dataFactory.resourceGroupName'] ]
  jobs:
  - template: deploy_apps.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      ssas: ${{ parameters.ssas }}
      virtualNetworkResourceGroupName: $(virtualNetworkResourceGroupName)
      virtualNetworkName: $(virtualNetworkName)
      appSubnetNamesCSV: $(appSubnetNamesCSV)
      appIdentitiesCSV: $(appIdentitiesCSV)
      dataFactory: ${{parameters.dataFactory}}
      dataFactoryName: $(dataFactoryName)
      dataFactoryResourceGroupName: $(dataFactoryResourceGroupName)
      enableReplica: ${{parameters.enableReplica}}

  # Scale down after deployment
- ${{ if eq(parameters.enableReplica,'true') }}:
  - stage: ${{parameters.envName}}_ssas_scale_down
    condition: succeededOrFailed()
    dependsOn: ${{parameters.envName}}_apps
    jobs:
    - deployment: approve
      displayName: 'Approve ${{parameters.envName}}'
      pool: server
      environment: y-deploy-crbbro-${{parameters.microservice}}-${{parameters.envName}}
    - template: deploy_reporting_scale_sync.yml
      parameters:
        envName: ${{parameters.envName}}
        microservice: ${{parameters.microservice}}
        variableGroupName: ${{parameters.variableGroupName}}
        variableGroupNameShared: ${{parameters.variableGroupNameShared}}
        download: ${{parameters.download}}
        azureServiceConnection: ${{parameters.azureServiceConnection}}
        ssas: ${{parameters.ssas}}
        replicaCount: 0
        operation: 'scale_down'
        enableReplica: ${{parameters.enableReplica}}
