/*
Lineage
PlacementSystemId=dbo.Placement.PlacementSystemId
PolicyId=dbo.Policy.PolicyId
CreatedUTCDate=dbo.Policy.ETLCreatedDate
LastUpdatedUTCDate=dbo.Policy.ETLUpdatedDate
ExpiryDate=dbo.Policy.ExpiryDate
*/
CREATE VIEW APIv1.BPCurrentPlacementPolicy
AS

SELECT DISTINCT
       PlacementSystemId
     , PolicyId
     , Association
     , CreatedUTCDate
     , LastUpdatedUTCDate
     , ExpiryDate
FROM (
    /*Policies*/
    SELECT
        PlacementSystemId = CAST(PL.PlacementSystemId AS NVARCHAR(100))
      , PL.PlacementId
      , PO.PolicyId
      , PO.PolicyReference
      , PO.InceptionDate
      , PO.ExpiryDate
      , PO.RenewedFromPolicyId
      , OpportunityTypeID = ISNULL(PO.OpportunityTypeId, 3)
      , CreatedUTCDate = PO.ETLCreatedDate
      , LastUpdatedUTCDate = PO.ETLUpdatedDate
      , PO.PolicyDescription
      , PO.IsFacility
      , PO.DataSourceInstanceId
      , PO.PolicyKey
      , Association = CAST('Current' AS NVARCHAR(10))
    FROM
        dbo.Policy PO
        INNER JOIN dbo.PlacementPolicy ppcur
            ON ppcur.PolicyId = PO.PolicyId
               AND ppcur.IsDeleted = 0
               AND ppcur.PlacementPolicyRelationshipTypeId = 1
               AND ppcur.DataSourceInstanceId = 50366

        INNER JOIN dbo.Placement PL
            ON PL.PlacementId = ppcur.PlacementId
    WHERE
        PL.PlacementSystemId IS NOT NULL
        AND PL.DataSourceInstanceId = 50366
        AND PO.IsDeleted = 0
        AND PO.RuleId > 0
        AND PO.SuppressPlacementLink = 0
) A;
