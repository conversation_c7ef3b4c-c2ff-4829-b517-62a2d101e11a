/*
Lineage
PS.MarketQuoteResponse.SourceMarketQuoteResponseKey=BP.ExpiringResponseGroup.Id
PS.MarketQuoteResponse.SourceMarketQuoteResponseKey=BP.ExpiringResponseElement.Id
PS.MarketQuoteResponse.SourceMarketQuoteResponseKey=BP.AdjustmentResponseGroup.Id
PS.MarketQuoteResponse.SourceMarketQuoteResponseKey=BP.AdjustmentResponseElement.Id
PS.MarketQuoteResponse.MarketResponseId=PS.MarketResponse.MarketResponseId
PS.MarketQuoteResponse.PremiumCurrencyId=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.Premium=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.CommissionRate=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.QuotedToLead=BP.ExpiringResponseElement.QuotedToLead
PS.MarketQuoteResponse.QuotedToLead=BP.AdjustmentResponseElement.QuotedToLead
PS.MarketQuoteResponse.FollowTypeId=BP.ExpiringResponseElement.FollowTypeId
PS.MarketQuoteResponse.FollowTypeId=BP.AdjustmentResponseElement.FollowTypeId
PS.MarketQuoteResponse.AdditionalPolicyCostCurrencyId=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.AdditionalPolicyCost=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.CommissionCurrencyId=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.Commission=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.QuoteExpiryDate=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.SignedLineRate=dbo.ElementAttributeCache.Value
PS.MarketQuoteResponse.SourceUpdatedDate=BP.ExpiringResponseGroup.ETLUpdatedDate
PS.MarketQuoteResponse.SourceUpdatedDate=BP.AdjustmentResponseGroup.ETLUpdatedDate
*/

CREATE PROCEDURE BPStaging.Load_PS_MarketQuoteResponseHistoric
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.MarketQuoteResponse';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY

    /* Expiring Response attributes */
    DROP TABLE IF EXISTS #ExpiringResponseAttribute;

    SELECT
        P.ExpiringResponseGroupId
      , P.ExpiringResponseElementId
      , P.FollowTypeId
      , P.QuotedToLead
      , QuoteExpiryDate = P.[Quote Expiry Date]
      , PremiumCurrencyId = P.[100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) currency]
      , Premium = P.[100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) amount]
      , CommissionCurrencyId = P.[Commission currency]
      , Commission = P.[Commission amount]
      , CommissionRate = P.[Commission percentage]
      , AdditionalPolicyCost = P.[Additional Policy Cost amount]
      , AdditionalPolicyCostCurrencyId = P.[Additional Policy Cost currency]
      , SignedLineRate = P.[Signed Line % percentage]
    INTO #ExpiringResponseAttribute
    FROM (
        SELECT
            ExpiringResponseGroupId = erg.Id
          , ExpiringResponseElementId = ere.Id
          , FollowTypeId = ere.FollowTypeId
          , QuotedToLead = ere.QuotedToLead
          , ElementType = CASE WHEN LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', '')) IN (
                                   'currency', 'percentage', 'amount'
                               )
                                   THEN et.ElementType + ' '
                                        + LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', ''))
                               ELSE et.ElementType END
          , eattr.Value
        FROM
            BP.ExpiringResponseGroup erg
            INNER JOIN BP.ExpiringResponseElement ere
                ON erg.Id = ere.ExpiringResponseGroupId

            INNER JOIN dbo.ElementBranch eb
                ON eb.ElementBranchId = ere.ElementBranchId
            --   AND eb.ClonedFromElementBranchId IS NULL --> Include only they haven't been cloned from last years placement.

            INNER JOIN BP.ResponseManagementElement rme
                ON rme.Id = ere.ResponseManagementElementId

            INNER JOIN dbo.ElementCache ec
                ON ec.RootElementId = rme.ElementId
                   AND ec.ElementBranchId = ere.ElementBranchId

            INNER JOIN ref.ElementType et
                ON et.ElementTypeId = ec.ElementTypeId

            INNER JOIN dbo.ElementDelta ed
                ON ed.ElementDeltaId = ec.ElementDeltaId

            INNER JOIN dbo.ElementAttributeCache eattr
                ON eattr.ElementId = ec.ElementId
                   AND eattr.ElementBranchId = ec.ElementBranchId

            INNER JOIN dbo.ElementAttributeType eatt
                ON eatt.ElementAttributeTypeId = eattr.ElementAttributeTypeId
        WHERE
            eatt.ElementAttributeType <> 'includeinhighlevelcapture'
            AND et.ElementType IN (
                    'Quote Expiry Date'
                  , '100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments)', 'Commission'
                  , 'Additional Policy Cost', 'Signed Line %'
                )
    ) n
    PIVOT (
        MAX(Value)
        FOR ElementType IN (
            [Quote Expiry Date]
          , [100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) amount]
          , [100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) currency]
          , [Commission amount], [Commission currency], [Commission percentage], [Additional Policy Cost amount]
          , [Additional Policy Cost currency], [Signed Line % percentage]
        )
    ) P;

    /* Adjustment Response attributes */
    DROP TABLE IF EXISTS #AdjustmentResponseAttribute;

    SELECT
        P.AdjustmentResponseGroupId
      , P.AdjustmentResponseElementId
      , P.FollowTypeId
      , P.QuotedToLead
      , QuoteExpiryDate = P.[Quote Expiry Date]
      , PremiumCurrencyId = P.[100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) currency]
      , Premium = P.[100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) amount]
      , CommissionCurrencyId = P.[Commission currency]
      , Commission = P.[Commission amount]
      , CommissionRate = P.[Commission percentage]
      , AdditionalPolicyCost = P.[Additional Policy Cost amount]
      , AdditionalPolicyCostCurrencyId = P.[Additional Policy Cost currency]
    INTO #AdjustmentResponseAttribute
    FROM (
        SELECT
            AdjustmentResponseGroupId = arg.Id
          , AdjustmentResponseElementId = are.Id
          , FollowTypeId = are.FollowTypeId
          , QuotedToLead = are.QuotedToLead
          , ElementType = CASE WHEN LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', '')) IN (
                                   'currency', 'percentage', 'amount'
                               )
                                   THEN et.ElementType + ' '
                                        + LOWER(REPLACE(eatt.ElementAttributeTypeKey, 'EAT_RESPONSECAPTURE_', ''))
                               ELSE et.ElementType END
          , eattr.Value
        FROM
            BP.AdjustmentResponseGroup arg
            INNER JOIN BP.AdjustmentResponseElement are
                ON arg.Id = are.AdjustmentResponseGroupId

            INNER JOIN dbo.ElementBranch eb
                ON eb.ElementBranchId = are.ElementBranchId

            INNER JOIN BP.ResponseManagementElement rme
                ON rme.Id = are.ResponseManagementElementId

            INNER JOIN dbo.ElementCache ec
                ON ec.RootElementId = rme.ElementId
                   AND ec.ElementBranchId = are.ElementBranchId

            INNER JOIN ref.ElementType et
                ON et.ElementTypeId = ec.ElementTypeId

            INNER JOIN dbo.ElementDelta ed
                ON ed.ElementDeltaId = ec.ElementDeltaId

            INNER JOIN dbo.ElementAttributeCache eattr
                ON eattr.ElementId = ec.ElementId
                   AND eattr.ElementBranchId = ec.ElementBranchId

            INNER JOIN dbo.ElementAttributeType eatt
                ON eatt.ElementAttributeTypeId = eattr.ElementAttributeTypeId
        WHERE
            eatt.ElementAttributeType <> 'includeinhighlevelcapture'
            AND et.ElementType IN (
                    'Quote Expiry Date'
                  , '100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments)', 'Commission'
                  , 'Additional Policy Cost'
                )
    ) n
    PIVOT (
        MAX(Value)
        FOR ElementType IN (
            [Quote Expiry Date]
          , [100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) amount]
          , [100% Layer Premium (excluding Surplus Lines Tax, & Taxes, Surcharges & Assessments) currency]
          , [Commission amount], [Commission currency], [Commission percentage], [Additional Policy Cost amount]
          , [Additional Policy Cost currency]
        )
    ) P;

    MERGE PS.MarketQuoteResponse T
    USING (
        SELECT
            SourceKey = CONCAT('EXPMQR|', era.ExpiringResponseGroupId, '|', era.ExpiringResponseElementId)
          , Id = era.ExpiringResponseElementId
          , DataSourceInstanceId = 50366
          , MR.MarketResponseId
          , era.PremiumCurrencyId
          , Premium = TRY_CAST(era.Premium AS DECIMAL(19, 4))
          , PremiumRate = NULL
          , era.CommissionRate
          , Subjectivity = NULL
          , IsPremiumRatePerMille = NULL
          , OfferedLineRate = NULL
          , OfferedLine = NULL
          , OutcomeStatusId = 1 -- Assumed to have been accepted
          , QuotedToLead = era.QuotedToLead
          , FollowTypeId = era.FollowTypeId
          , OutcomeReasonId = NULL
          , era.AdditionalPolicyCostCurrencyId
          , AdditionalPolicyCost = TRY_CAST(era.AdditionalPolicyCost AS DECIMAL(19, 4))
          , FrontingCarrierId = NULL
          , NetPremium = NULL
          , era.CommissionCurrencyId
          , Commission = TRY_CAST(era.Commission AS DECIMAL(19, 4))
          , era.QuoteExpiryDate
          , BindRequestedDate = NULL
          , BoundDate = NULL
          , SignedLine = NULL
          , SignedLineRate = TRY_CAST(era.SignedLineRate AS DECIMAL(21, 6))
          , TriaRequired = NULL
          , TriaIncluded = NULL
          , TriaPremium = NULL
          , TriaPremiumCurrencyId = NULL
          , DeductibleCurrencyId = NULL
          , Deductible = NULL
          , IsOverride = 0
          , AdditionalDetails = NULL
          , PrimaryRate = NULL
          , PrimaryExposureValue = NULL
          , SourceUpdatedDate = erg.ETLUpdatedDate
          , IsDeleted = 0
        FROM
            #ExpiringResponseAttribute era
            INNER JOIN BP.ExpiringResponseGroup erg
                ON erg.Id = era.ExpiringResponseGroupId

            INNER JOIN PS.MarketResponse MR
                ON MR.MarketResponseKey = CONCAT(
                                              'EXPRESP|'
                                            , era.ExpiringResponseGroupId
                                            , '|'
                                            , era.ExpiringResponseElementId
                                          )
        UNION ALL
        SELECT
            SourceKey = CONCAT('ADJMQR|', era.AdjustmentResponseGroupId, '|', era.AdjustmentResponseElementId)
          , Id = era.AdjustmentResponseElementId
          , DataSourceInstanceId = 50366
          , MR.MarketResponseId
          , era.PremiumCurrencyId
          , Premium = TRY_CAST(era.Premium AS DECIMAL(19, 4))
          , PremiumRate = NULL
          , era.CommissionRate
          , Subjectivity = NULL
          , IsPremiumRatePerMille = NULL
          , OfferedLineRate = NULL
          , OfferedLine = NULL
          , OutcomeStatusId = 1 -- Assumed to be Accepted
          , QuotedToLead = era.QuotedToLead
          , FollowTypeId = era.FollowTypeId
          , OutcomeReasonId = NULL
          , era.AdditionalPolicyCostCurrencyId
          , era.AdditionalPolicyCost
          , FrontingCarrierId = NULL
          , NetPremium = NULL
          , era.CommissionCurrencyId
          , era.Commission
          , era.QuoteExpiryDate
          , BindRequestedDate = NULL
          , BoundDate = NULL
          , SignedLine = NULL
          , SignedLineRate = NULL
          , TriaRequired = NULL
          , TriaIncluded = NULL
          , TriaPremium = NULL
          , TriaPremiumCurrencyId = NULL
          , DeductibleCurrencyId = NULL
          , Deductible = NULL
          , IsOverride = 0
          , AdditionalDetails = NULL
          , PrimaryRate = NULL
          , PrimaryExposureValue = NULL
          , SourceUpdatedDate = arg.ETLUpdatedDate
          , IsDeleted = 0
        FROM
            #AdjustmentResponseAttribute era
            INNER JOIN BP.AdjustmentResponseGroup arg
                ON arg.Id = era.AdjustmentResponseGroupId

            INNER JOIN PS.MarketResponse MR
                ON MR.MarketResponseKey = CONCAT(
                                              'ADJRESP|'
                                            , era.AdjustmentResponseGroupId
                                            , '|'
                                            , era.AdjustmentResponseElementId
                                          )
    ) S
    ON S.SourceKey = T.SourceMarketQuoteResponseKey
       AND T.DataSourceInstanceId = S.DataSourceInstanceId
    WHEN NOT MATCHED
        THEN INSERT (
                 SourceMarketQuoteResponseKey
               , DataSourceInstanceId
               , MarketResponseId
               , PremiumCurrencyId
               , Premium
               , OfferedLine
               , PremiumRate
               , CommissionRate
               , Subjectivity
               , IsPremiumRatePerMille
               , OfferedLineRate
               , OutcomeStatusId
               , QuotedToLead
               , FollowTypeId
               , OutcomeReasonId
               , AdditionalPolicyCostCurrencyId
               , AdditionalPolicyCost
               , FrontingCarrierId
               , NetPremium
               , CommissionCurrencyId
               , Commission
               , QuoteExpiryDate
               , BindRequestedDate
               , BoundDate
               , SignedLine
               , SignedLineRate
               , TriaRequired
               , TriaIncluded
               , TriaPremium
               , TriaPremiumCurrencyId
               , DeductibleCurrencyId
               , Deductible
               , IsOverride
               , AdditionalDetails
               , PrimaryRate
               , PrimaryExposureValue
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.SourceKey
                   , S.DataSourceInstanceId
                   , S.MarketResponseId
                   , S.PremiumCurrencyId
                   , S.Premium
                   , S.OfferedLine
                   , S.PremiumRate
                   , S.CommissionRate
                   , S.Subjectivity
                   , S.IsPremiumRatePerMille
                   , S.OfferedLineRate
                   , S.OutcomeStatusId
                   , S.QuotedToLead
                   , S.FollowTypeId
                   , S.OutcomeReasonId
                   , S.AdditionalPolicyCostCurrencyId
                   , S.AdditionalPolicyCost
                   , S.FrontingCarrierId
                   , S.NetPremium
                   , S.CommissionCurrencyId
                   , S.Commission
                   , S.QuoteExpiryDate
                   , S.BindRequestedDate
                   , S.BoundDate
                   , S.SignedLine
                   , S.SignedLineRate
                   , S.TriaRequired
                   , S.TriaIncluded
                   , S.TriaPremium
                   , S.TriaPremiumCurrencyId
                   , S.DeductibleCurrencyId
                   , S.Deductible
                   , S.IsOverride
                   , S.AdditionalDetails
                   , S.PrimaryRate
                   , S.PrimaryExposureValue
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.MarketResponseId
                               , S.PremiumCurrencyId
                               , S.Premium
                               , S.OfferedLine
                               , S.PremiumRate
                               , S.CommissionRate
                               , S.Subjectivity
                               , S.IsPremiumRatePerMille
                               , S.OfferedLineRate
                               , S.OutcomeStatusId
                               , S.QuotedToLead
                               , S.FollowTypeId
                               , S.OutcomeReasonId
                               , S.AdditionalPolicyCostCurrencyId
                               , S.AdditionalPolicyCost
                               , S.FrontingCarrierId
                               , S.NetPremium
                               , S.CommissionCurrencyId
                               , S.Commission
                               , S.QuoteExpiryDate
                               , S.BindRequestedDate
                               , S.BoundDate
                               , S.SignedLine
                               , S.SignedLineRate
                               , S.TriaRequired
                               , S.TriaIncluded
                               , S.TriaPremium
                               , S.TriaPremiumCurrencyId
                               , S.DeductibleCurrencyId
                               , S.Deductible
                               , S.IsOverride
                               , S.AdditionalDetails
                               , S.PrimaryRate
                               , S.PrimaryExposureValue
                               , S.SourceUpdatedDate
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.MarketResponseId
                               , T.PremiumCurrencyId
                               , T.Premium
                               , T.OfferedLine
                               , T.PremiumRate
                               , T.CommissionRate
                               , T.Subjectivity
                               , T.IsPremiumRatePerMille
                               , T.OfferedLineRate
                               , T.OutcomeStatusId
                               , T.QuotedToLead
                               , T.FollowTypeId
                               , T.OutcomeReasonId
                               , T.AdditionalPolicyCostCurrencyId
                               , T.AdditionalPolicyCost
                               , T.FrontingCarrierId
                               , T.NetPremium
                               , T.CommissionCurrencyId
                               , T.Commission
                               , T.QuoteExpiryDate
                               , T.BindRequestedDate
                               , T.BoundDate
                               , T.SignedLine
                               , T.SignedLineRate
                               , T.TriaRequired
                               , T.TriaIncluded
                               , T.TriaPremium
                               , T.TriaPremiumCurrencyId
                               , T.DeductibleCurrencyId
                               , T.Deductible
                               , T.IsOverride
                               , T.AdditionalDetails
                               , T.PrimaryRate
                               , T.PrimaryExposureValue
                               , T.SourceUpdatedDate
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.MarketResponseId = S.MarketResponseId
               , T.PremiumCurrencyId = S.PremiumCurrencyId
               , T.Premium = S.Premium
               , T.OfferedLine = S.OfferedLine
               , T.PremiumRate = S.PremiumRate
               , T.CommissionRate = S.CommissionRate
               , T.Subjectivity = S.Subjectivity
               , T.IsPremiumRatePerMille = S.IsPremiumRatePerMille
               , T.OfferedLineRate = S.OfferedLineRate
               , T.OutcomeStatusId = S.OutcomeStatusId
               , T.QuotedToLead = S.QuotedToLead
               , T.FollowTypeId = S.FollowTypeId
               , T.OutcomeReasonId = S.OutcomeReasonId
               , T.AdditionalPolicyCostCurrencyId = S.AdditionalPolicyCostCurrencyId
               , T.AdditionalPolicyCost = S.AdditionalPolicyCost
               , T.FrontingCarrierId = S.FrontingCarrierId
               , T.NetPremium = S.NetPremium
               , T.CommissionCurrencyId = S.CommissionCurrencyId
               , T.Commission = S.Commission
               , T.QuoteExpiryDate = S.QuoteExpiryDate
               , T.BindRequestedDate = S.BindRequestedDate
               , T.BoundDate = S.BoundDate
               , T.SignedLine = S.SignedLine
               , T.SignedLineRate = S.SignedLineRate
               , T.TriaRequired = S.TriaRequired
               , T.TriaIncluded = S.TriaIncluded
               , T.TriaPremium = S.TriaPremium
               , T.TriaPremiumCurrencyId = S.TriaPremiumCurrencyId
               , T.DeductibleCurrencyId = S.DeductibleCurrencyId
               , T.Deductible = S.Deductible
               , T.IsOverride = S.IsOverride
               , T.AdditionalDetails = S.AdditionalDetails
               , T.PrimaryRate = S.PrimaryRate
               , T.PrimaryExposureValue = S.PrimaryExposureValue
               , T.ETLUpdatedDate = GETUTCDATE()
               , T.SourceUpdatedDate = S.SourceUpdatedDate
               , T.IsDeleted = S.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    /*Not attempting to do logical deletion of this as it could potentially remove the records from the other load*/
    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;