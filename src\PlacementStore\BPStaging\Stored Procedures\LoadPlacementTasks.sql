/*
Lineage
dbo.PlacementTasks.PlacementId=dbo.Placement.PlacementId
dbo.PlacementTasks.RiskRequestId=BPStaging.PlacementTasks.RiskRequestId
dbo.PlacementTasks.TaskId=BPStaging.PlacementTasks.TaskId
dbo.PlacementTasks.TaskType=BPStaging.PlacementTasks.TaskType
dbo.PlacementTasks.TaskStatus=BPStaging.PlacementTasks.TaskStatus
dbo.PlacementTasks.AssignedTo=BPStaging.PlacementTasks.AssignedTo
dbo.PlacementTasks.ApprovedBy=BPStaging.PlacementTasks.ApprovedBy
dbo.PlacementTasks.RejectedBy=BPStaging.PlacementTasks.RejectedBy
dbo.PlacementTasks.RejectionReason=BPStaging.PlacementTasks.RejectionReason
dbo.PlacementTasks.Comments=BPStaging.PlacementTasks.Comments
dbo.PlacementTasks.ModifiedDate=BPStaging.PlacementTasks.ModifiedDate
dbo.PlacementTasks.UserLogOn=BPStaging.PlacementTasks.UserLogOn
dbo.PlacementTasks.UserFullName=BPStaging.PlacementTasks.UserFullName
dbo.PlacementTasks.CreatedUTCDate=BPStaging.PlacementTasks.ValidFrom
dbo.PlacementTasks.ExtensibleMetadata=BPStaging.PlacementTasks.ExtensibleMetadata
*/
CREATE PROCEDURE BPStaging.LoadPlacementTasks
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.PlacementTasks';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.PlacementTasks T
    USING (
        SELECT
            pl.PlacementId
          , pt.RiskRequestId
          , pt.TaskId
          , pt.TaskType
          , pt.TaskStatus
          , pt.AssignedTo
          , pt.ApprovedBy
          , pt.RejectedBy
          , pt.RejectionReason
          , pt.Comments
          , pt.ModifiedDate
          , pt.UserLogOn
          , pt.UserFullName
          , CreatedUTCDate = pt.ValidFrom
          , LastUpdatedUTCDate = GETUTCDATE()
          , pt.ExtensibleMetadata
        FROM
            BPStaging.PlacementTasks pt
            INNER JOIN
            -- To get the PlacementId and ensure we don't have a missing foreign key.
            dbo.Placement pl
                ON pl.PlacementSystemId = pt.PlacementId
                   AND pl.DataSourceInstanceId = 50366
    ) S
    ON T.PlacementId = S.PlacementId
       AND T.TaskId = S.TaskId
    WHEN NOT MATCHED
        THEN INSERT (
                 PlacementId
               , RiskRequestId
               , TaskId
               , TaskType
               , TaskStatus
               , AssignedTo
               , ApprovedBy
               , RejectedBy
               , RejectionReason
               , Comments
               , ModifiedDate
               , UserLogOn
               , UserFullName
               , CreatedUTCDate
               , LastUpdatedUTCDate
               , ExtensibleMetadata
             )
             VALUES
                 (
                     S.PlacementId
                   , S.RiskRequestId
                   , S.TaskId
                   , S.TaskType
                   , S.TaskStatus
                   , S.AssignedTo
                   , S.ApprovedBy
                   , S.RejectedBy
                   , S.RejectionReason
                   , S.Comments
                   , S.ModifiedDate
                   , S.UserLogOn
                   , S.UserFullName
                   , S.CreatedUTCDate
                   , S.LastUpdatedUTCDate
                   , S.ExtensibleMetadata
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.PlacementId
                               , T.RiskRequestId
                               , T.TaskId
                               , T.TaskType
                               , T.TaskStatus
                               , T.AssignedTo
                               , T.ApprovedBy
                               , T.RejectedBy
                               , T.RejectionReason
                               , T.Comments
                               , T.ModifiedDate
                               , T.UserLogOn
                               , T.UserFullName
                               , T.ExtensibleMetadata
                             INTERSECT
                             SELECT
                                 S.PlacementId
                               , S.RiskRequestId
                               , S.TaskId
                               , S.TaskType
                               , S.TaskStatus
                               , S.AssignedTo
                               , S.ApprovedBy
                               , S.RejectedBy
                               , S.RejectionReason
                               , S.Comments
                               , S.ModifiedDate
                               , S.UserLogOn
                               , S.UserFullName
                               , S.ExtensibleMetadata
                         )
        THEN UPDATE SET
                 T.RiskRequestId = S.RiskRequestId
               , T.TaskType = S.TaskType
               , T.TaskStatus = S.TaskStatus
               , T.AssignedTo = S.AssignedTo
               , T.ApprovedBy = S.ApprovedBy
               , T.RejectedBy = S.RejectedBy
               , T.RejectionReason = S.RejectionReason
               , T.Comments = S.Comments
               , T.ModifiedDate = S.ModifiedDate
               , T.UserLogOn = S.UserLogOn
               , T.UserFullName = S.UserFullName
               , T.LastUpdatedUTCDate = S.LastUpdatedUTCDate
               , T.ExtensibleMetadata = S.ExtensibleMetadata
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);