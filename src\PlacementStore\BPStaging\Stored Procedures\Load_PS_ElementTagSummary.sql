/*
Lineage
PS.ElementTagSummary.ElementId=dbo.ElementTagCache.ElementId
PS.ElementTagSummary.DataSourceInstanceId=ref.ElementTagType.DataSourceInstanceId
PS.ElementTagSummary.ElementBranchId=dbo.ElementTagCache.ElementBranchId
PS.ElementTagSummary.ElementTagTypeId=ref.ElementTagType.ElementTagTypeId
PS.ElementTagSummary.ElementTagType=ref.ElementTagType.ElementTagType
PS.ElementTagSummary.ElementTagTypeKey=ref.ElementTagType.ElementTagTypeKey
PS.ElementTagSummary.ElementTagTypeTranslationKey=ref.ElementTagType.TranslationKey
PS.ElementTagSummary.ElementTagGroupId=ref.ElementTagGroup.ElementTagGroupId
PS.ElementTagSummary.ElementTagGroup=ref.ElementTagGroup.ElementTagGroup
PS.ElementTagSummary.ElementTagGroupKey=ref.ElementTagGroup.ElementTagGroupKey
PS.ElementTagSummary.ElementTagGroupTranslationKey=ref.ElementTagGroup.TranslationKey
PS.ElementTagSummary.SourceUpdatedDate=dbo.ElementTagCache.ETLUpdatedDate
PS.ElementTagSummary.SourceUpdatedDate=ref.ElementTagType.ETLUpdatedDate
PS.ElementTagSummary.SourceUpdatedDate=ref.ElementTagGroup.ETLUpdatedDate
PS.ElementTagSummary.IsDeleted=dbo.ElementTagCache.IsDeleted
*/
CREATE PROCEDURE BPStaging.Load_PS_ElementTagSummary
    @LastUpdatedDate DATETIME2(7)
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'PS.ElementTagSummary';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @MaxLastUpdatedUTCDate DATETIME2(7) = NULL;
DECLARE @RecordsPicked INT = 0;

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #intermediateResults;

    CREATE TABLE #intermediateResults (
        ElementId                     INT           NOT NULL
      , DataSourceInstanceId          INT           NOT NULL
      , ElementBranchId               INT           NOT NULL
      , ElementTagTypeId              INT           NOT NULL
      , ElementTagType                NVARCHAR(MAX) NULL
      , ElementTagTypeKey             NVARCHAR(100) NOT NULL
      , ElementTagTypeTranslationKey  NVARCHAR(255) NULL
      , ElementTagGroupId             INT           NOT NULL
      , ElementTagGroup               NVARCHAR(MAX) NULL
      , ElementTagGroupKey            NVARCHAR(100) NOT NULL
      , ElementTagGroupTranslationKey NVARCHAR(255) NULL
      , IsDeleted                     BIT           NOT NULL
      , SourceUpdatedDate             DATETIME2(7)  NOT NULL
      , ElementGroupRowNumber         INT           NOT NULL
      , ElementTypeGroupRowNumber     INT           NOT NULL
      ,
      UNIQUE CLUSTERED
      (
          ElementId ASC
        , ElementBranchId ASC
        , ElementTagTypeId ASC
        , ElementTagGroupId ASC
      )
    );

    ----------------------------------------------------------------------------------------------------------------------------------------
    INSERT INTO
        #intermediateResults
        (
            ElementId
          , DataSourceInstanceId
          , ElementBranchId
          , ElementTagTypeId
          , ElementTagType
          , ElementTagTypeKey
          , ElementTagTypeTranslationKey
          , ElementTagGroupId
          , ElementTagGroup
          , ElementTagGroupKey
          , ElementTagGroupTranslationKey
          , IsDeleted
          , SourceUpdatedDate
          , ElementGroupRowNumber
          , ElementTypeGroupRowNumber
        )
    SELECT
        main.ElementId
      , main.DataSourceInstanceId
      , main.ElementBranchId
      , main.ElementTagTypeId
      , main.ElementTagType
      , main.ElementTagTypeKey
      , main.ElementTagTypeTranslationKey
      , main.ElementTagGroupId
      , main.ElementTagGroup
      , main.ElementTagGroupKey
      , main.ElementTagGroupTranslationKey
      , main.IsDeleted
      , main.SourceUpdatedDate
      , main.ElementGroupRowNumber
      , main.ElementTypeGroupRowNumber
    FROM (
        SELECT
            tag.ElementId
          , tt.DataSourceInstanceId
          , tag.ElementBranchId
          , tt.ElementTagTypeId
          , tt.ElementTagType
          , tt.ElementTagTypeKey
          , ElementTagTypeTranslationKey = tt.TranslationKey
          , tg.ElementTagGroupId
          , tg.ElementTagGroup
          , tg.ElementTagGroupKey
          , ElementTagGroupTranslationKey = tg.TranslationKey
          , tag.IsDeleted
          , SourceUpdatedDate = GREATEST(tag.ETLUpdatedDate, tt.ETLUpdatedDate, tg.ETLUpdatedDate)
          , ElementGroupRowNumber = ROW_NUMBER() OVER (PARTITION BY tag.ElementId, tg.ElementTagGroupId ORDER BY tag.ElementTagDeltaId DESC)
          , ElementTypeGroupRowNumber = ROW_NUMBER() OVER (PARTITION BY
                                                               tag.ElementId
                                                             , tag.ElementTagTypeId /* tt */
                                                             , tg.ElementTagGroupId
                                                           ORDER BY
                                                               tag.ElementTagDeltaId DESC
                                                     )
          , ElementBranchTypeGroupRowNumber = ROW_NUMBER() OVER (PARTITION BY
                                                                     tag.ElementId
                                                                   , tag.ElementBranchId
                                                                   , tag.ElementTagTypeId /* tt */
                                                                   , tg.ElementTagGroupId
                                                                 ORDER BY
                                                                     tag.ElementTagDeltaId DESC
                                                           )
        FROM
            dbo.ElementTagCache tag --WITH (NOLOCK)
            INNER JOIN ref.ElementTagType tt --WITH (NOLOCK)
                ON tag.ElementTagTypeId = tt.ElementTagTypeId

            INNER JOIN ref.ElementTagGroup tg --WITH (NOLOCK)
                ON tg.ElementTagGroupId = tt.ElementTagGroupId
    ) main
    WHERE
        main.ElementBranchTypeGroupRowNumber = 1
        AND main.SourceUpdatedDate > @LastUpdatedDate
    ORDER BY
        main.ElementId ASC
      , main.ElementBranchId ASC
      , main.ElementTagTypeId ASC
      , main.ElementTagGroupId ASC;

    ----------------------------------------------------------------------------------------------------------------------------------------
    DECLARE @UpdateDate DATETIME2(7) = GETUTCDATE();

    DECLARE @Actions TABLE (
        Change VARCHAR(20) NOT NULL
    );

    MERGE PS.ElementTagSummary WITH (HOLDLOCK) T
    USING (
        SELECT
            ElementId
          , DataSourceInstanceId
          , ElementBranchId
          , ElementTagTypeId
          , ElementTagType
          , ElementTagTypeKey
          , ElementTagTypeTranslationKey
          , ElementTagGroupId
          , ElementTagGroup
          , ElementTagGroupKey
          , ElementTagGroupTranslationKey
          , IsDeleted
          , SourceUpdatedDate
          , ElementGroupRowNumber
          , ElementTypeGroupRowNumber
        FROM
            #intermediateResults
    ) S
    ON T.ElementId = S.ElementId
       AND T.ElementBranchId = S.ElementBranchId
       AND T.ElementTagTypeId = S.ElementTagTypeId
       AND T.ElementTagGroupId = S.ElementTagGroupId
    WHEN NOT MATCHED
        THEN INSERT (
                 ElementId
               , DataSourceInstanceId
               , ElementBranchId
               , ElementTagTypeId
               , ElementTagType
               , ElementTagTypeKey
               , ElementTagTypeTranslationKey
               , ElementTagGroupId
               , ElementTagGroup
               , ElementTagGroupKey
               , ElementTagGroupTranslationKey
               , ElementGroupRowNumber
               , ElementTypeGroupRowNumber
               , ETLCreatedDate
               , ETLUpdatedDate
               , SourceUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     S.ElementId
                   , S.DataSourceInstanceId
                   , S.ElementBranchId
                   , S.ElementTagTypeId
                   , S.ElementTagType
                   , S.ElementTagTypeKey
                   , S.ElementTagTypeTranslationKey
                   , S.ElementTagGroupId
                   , S.ElementTagGroup
                   , S.ElementTagGroupKey
                   , S.ElementTagGroupTranslationKey
                   , S.ElementGroupRowNumber
                   , S.ElementTypeGroupRowNumber
                   , @UpdateDate
                   , @UpdateDate
                   , S.SourceUpdatedDate
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 S.DataSourceInstanceId
                               , S.ElementTagType
                               , S.ElementTagTypeKey
                               , S.ElementTagTypeTranslationKey
                               , S.ElementTagGroup
                               , S.ElementTagGroupKey
                               , S.ElementTagGroupTranslationKey
                               , S.ElementGroupRowNumber
                               , S.ElementTypeGroupRowNumber
                               , S.IsDeleted
                             INTERSECT
                             SELECT
                                 T.DataSourceInstanceId
                               , T.ElementTagType
                               , T.ElementTagTypeKey
                               , T.ElementTagTypeTranslationKey
                               , T.ElementTagGroup
                               , T.ElementTagGroupKey
                               , T.ElementTagGroupTranslationKey
                               , T.ElementGroupRowNumber
                               , T.ElementTypeGroupRowNumber
                               , T.IsDeleted
                         )
        THEN UPDATE SET
                 T.DataSourceInstanceId = S.DataSourceInstanceId
               , T.ElementTagType = S.ElementTagType
               , T.ElementTagTypeKey = S.ElementTagTypeKey
               , T.ElementTagTypeTranslationKey = S.ElementTagTypeTranslationKey
               , T.ElementTagGroup = S.ElementTagGroup
               , T.ElementTagGroupKey = S.ElementTagGroupKey
               , T.ElementTagGroupTranslationKey = S.ElementTagGroupTranslationKey
               , T.ElementGroupRowNumber = S.ElementGroupRowNumber
               , T.ElementTypeGroupRowNumber = S.ElementTypeGroupRowNumber
               , T.IsDeleted = S.IsDeleted
               , T.ETLUpdatedDate = @UpdateDate
    OUTPUT $ACTION
    INTO @Actions;

    -- Get the date as that will be stored in ProcessSession.
    -- Also get the count as that will be interesting to see what is fed into the MERGE. 
    -- Without date filter will be more than 5 million.
    SELECT
        @MaxLastUpdatedUTCDate = MAX(SourceUpdatedDate)
      , @RecordsPicked = COUNT(*)
    FROM
        #intermediateResults;

    ----------------------------------------------------------------------------------------------------------------------------------------
    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action =
    CONCAT(
        N'Merge '
      , @TargetTable
      , N'. @LastUpdatedDate = '
      , @LastUpdatedDate
      , N'. Records used by Merge = '
      , @RecordsPicked
      , N'.'
    );

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0)
  , MaxLastUpdatedUTCDate = ISNULL(@MaxLastUpdatedUTCDate, @LastUpdatedDate);

RETURN 0;