/*
Lineage
DataSourceInstanceID=ref.ServicingRole.DataSourceInstanceId
RoleID=ref.ServicingRole.ServicingRoleId
RoleDescription=ref.ServicingRole.ServicingRole
RoleKey=ref.ServicingRole.DataSourceInstanceId
RoleKey=ref.ServicingRole.ServicingRoleId
*/
CREATE VIEW APIv1.PlacementSystemRole
AS
SELECT
    DataSourceInstanceID = DataSourceInstanceId
  , RoleID = ServicingRoleId
  , RoleDescription = ServicingRole
  , RoleKey = CAST(DataSourceInstanceId AS NVARCHAR(6)) + '|' + CAST(ServicingRoleId AS NVARCHAR(6))
FROM
    ref.ServicingRole;