/*
Lineage
PlacementId=dbo.Placement.PlacementId
PlacementSystemId=dbo.Placement.PlacementSystemId
PlacementCompletionStatusId=dbo.Placement.PlacementCompletionStatusId
DataSourceInstanceId=dbo.Placement.ServicingPlatformId
PartyID=dbo.Party.PartyId
PartyKey=dbo.Party.PartyKey
PartyName=dbo.Party.PartyName
CompCode=dbo.Party.CompCode
IsIncumbent=PS.NegotiationMarket.IsIncumbent
QuotedToLead=PS.MarketQuoteResponse.QuotedToLead
OfferedLineRate=PS.MarketQuoteResponse.OfferedLineRate
OutcomeStatus=ref.OutcomeStatus.OutcomeStatus
Premium=PS.MarketQuoteResponse.Premium
CommissionRate=PS.MarketQuoteResponse.CommissionRate
ProductId=PS.RiskProfile.COLProductId
ProductId=PS.RiskProfile.ProductId
PremiumCurrencyId=PS.MarketQuoteResponse.PremiumCurrencyId
ResponseDate=PS.MarketResponse.ResponseDate
*/
/*
Used for COL Integration
*/

CREATE VIEW APIv1.PlacementMarketInteractions
AS
WITH CTE AS (
    SELECT
        P.PlacementId
      , PlacementSystemId = CAST(P.PlacementSystemId AS NVARCHAR(100))
      , P.PlacementCompletionStatusId
      , DataSourceInstanceId = P.ServicingPlatformId
      , MI.IsIncumbent
      , mqr.QuotedToLead
      , mqr.OfferedLineRate
      , OS.OutcomeStatus
      , PartyId = CASE WHEN MI.CarrierId IS NOT NULL
                            AND MI.CarrierId < 10000000
                           THEN MI.SourceCarrierId
                       ELSE NULL END
      , CarrierId = CASE WHEN MI.CarrierId IS NOT NULL
                              AND c.CarrierTypeId NOT IN (
                             2, 6
                         )
                             THEN c.CarrierId
                         ELSE NULL END
      , CarrierGroupId = CASE WHEN MI.CarrierId IS NOT NULL
                                   AND c.CarrierTypeId IN (
                                  2, 6
                              )
                                  THEN c.CarrierId
                              ELSE NULL END
      , mqr.Premium
      , mqr.CommissionRate
      , ProductId = COALESCE(rp.COLProductId, rp.ProductId, rp1.ProductId)
      , mqr.PremiumCurrencyId
      , mr.ResponseDate
    FROM
        dbo.Placement P
        INNER JOIN PS.Negotiation MS
            ON MS.PlacementId = P.PlacementId
               AND MS.IsDeleted = 0
               AND MS.NegotiationTypeKey IN (
                       1, 2
                   ) -- Current and Placement

        INNER JOIN PS.NegotiationMarket MI
            ON MI.NegotiationId = MS.NegotiationId
               AND MI.IsDeleted = 0

        INNER JOIN dbo.Carrier c
            ON MI.CarrierId = c.CarrierId

        INNER JOIN PS.MarketResponse mr
            ON MI.NegotiationMarketId = mr.NegotiationMarketId
               AND mr.IsDeleted = 0

        INNER JOIN PS.MarketQuoteResponse mqr
            ON mr.MarketResponseId = mqr.MarketResponseId
               AND mqr.IsDeleted = 0

        INNER JOIN PS.MarketResponseBasis mrb
            ON mrb.MarketResponseId = mr.MarketResponseId
               AND mrb.IsDeleted = 0

        LEFT JOIN PS.RiskProfile rp1
            ON rp1.RiskProfileKey = CONCAT('SPEC|', mrb.SpecificationId)

        LEFT JOIN PS.RiskProfile rp
            ON mrb.RiskProfileId = rp.RiskProfileId

        INNER JOIN ref.OutcomeStatus OS
            ON OS.OutcomeStatusId = mqr.OutcomeStatusId
               AND OS.IsDeprecated = 0
    WHERE
        ISNULL(P.DataSourceInstanceId, 50366) <> 50351
)
SELECT
    CTE.PlacementId
  , CTE.PlacementSystemId
  , CTE.PlacementCompletionStatusId
  , CTE.DataSourceInstanceId
  , PartyID = PY.PartyId
  , PY.PartyKey
  , PY.PartyName
  , PY.CompCode
  , CTE.IsIncumbent
  , CTE.QuotedToLead
  , CTE.OfferedLineRate
  , CTE.OutcomeStatus
  , CTE.Premium
  , CTE.CommissionRate
  , CTE.ProductId
  , CTE.PremiumCurrencyId
  , CTE.ResponseDate
FROM
    CTE
    JOIN dbo.Party PY
        ON PY.PartyId = CTE.PartyId
           AND PY.IsDeleted = 0
UNION
SELECT
    CTE.PlacementId
  , CTE.PlacementSystemId
  , CTE.PlacementCompletionStatusId
  , CTE.DataSourceInstanceId
  , PartyID = PY.PartyId
  , PY.PartyKey
  , PY.PartyName
  , PY.CompCode
  , CTE.IsIncumbent
  , CTE.QuotedToLead
  , CTE.OfferedLineRate
  , CTE.OutcomeStatus
  , CTE.Premium
  , CTE.CommissionRate
  , CTE.ProductId
  , CTE.PremiumCurrencyId
  , CTE.ResponseDate
FROM
    CTE
    JOIN dbo.Carrier C
        ON C.CarrierId = CTE.CarrierId
           AND C.IsActive = 1

    JOIN dbo.Party PY
        ON PY.DataSourceInstanceId = CTE.DataSourceInstanceId
           AND PY.IsDeleted = 0
           AND PY.CompCode = C.CarrierEntityCompCode
UNION
SELECT
    CTE.PlacementId
  , CTE.PlacementSystemId
  , CTE.PlacementCompletionStatusId
  , CTE.DataSourceInstanceId
  , PartyID = PY.PartyId
  , PY.PartyKey
  , PY.PartyName
  , PY.CompCode
  , CTE.IsIncumbent
  , CTE.QuotedToLead
  , CTE.OfferedLineRate
  , CTE.OutcomeStatus
  , CTE.Premium
  , CTE.CommissionRate
  , CTE.ProductId
  , CTE.PremiumCurrencyId
  , CTE.ResponseDate
FROM
    CTE
    JOIN dbo.Carrier CG
        ON CG.SourceCarrierId = CTE.CarrierGroupId
           AND CG.IsActive = 1
           AND CG.CarrierTypeId = 6 -- Carrier Group

    JOIN dbo.Party PY
        ON PY.DataSourceInstanceId = CTE.DataSourceInstanceId
           AND PY.IsDeleted = 0
           AND PY.CompCode = CG.CarrierEntityCompCode;